﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_VicinityLootUI

#include "Basic.hpp"

#include "W_VicinityLootUI_classes.hpp"
#include "W_VicinityLootUI_parameters.hpp"


namespace SDK
{

// Function W_VicinityLootUI.W_VicinityLootUI_C.AddPickups
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UW_VicinityLootUI_C::AddPickups()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_VicinityLootUI_C", "AddPickups");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_VicinityLootUI.W_VicinityLootUI_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_VicinityLootUI_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_VicinityLootUI_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_VicinityLootUI.W_VicinityLootUI_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_VicinityLootUI_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_VicinityLootUI_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_VicinityLootUI.W_VicinityLootUI_C.Drop_ContainerOnContainerUnhandled_Event_0
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSIContainer_C*                  FromContainer                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSIContainer_C*                  ToContainer                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSI_Slot_C*                      DroppedSlotRef                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSI_Slot_C*                      SlotReceiverRef                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// int32                                   ToSlotIndex                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Rotated_                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_VicinityLootUI_C::Drop_ContainerOnContainerUnhandled_Event_0(class UJSIContainer_C* FromContainer, class UJSIContainer_C* ToContainer, class UJSI_Slot_C* DroppedSlotRef, class UJSI_Slot_C* SlotReceiverRef, int32 ToSlotIndex, bool Rotated_)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_VicinityLootUI_C", "Drop_ContainerOnContainerUnhandled_Event_0");

	Params::W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0 Parms{};

	Parms.FromContainer = FromContainer;
	Parms.ToContainer = ToContainer;
	Parms.DroppedSlotRef = DroppedSlotRef;
	Parms.SlotReceiverRef = SlotReceiverRef;
	Parms.ToSlotIndex = ToSlotIndex;
	Parms.Rotated_ = Rotated_;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_VicinityLootUI.W_VicinityLootUI_C.ExecuteUbergraph_W_VicinityLootUI
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_VicinityLootUI_C::ExecuteUbergraph_W_VicinityLootUI(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_VicinityLootUI_C", "ExecuteUbergraph_W_VicinityLootUI");

	Params::W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_VicinityLootUI.W_VicinityLootUI_C.GetActorFromKey
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UJSI_Slot_C*                      JigRef                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class AActor*                           ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)
// class UBP_JigPickupComponent_C**        Comp                                                   (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

class AActor* UW_VicinityLootUI_C::GetActorFromKey(class UJSI_Slot_C* JigRef, class UBP_JigPickupComponent_C** Comp)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_VicinityLootUI_C", "GetActorFromKey");

	Params::W_VicinityLootUI_C_GetActorFromKey Parms{};

	Parms.JigRef = JigRef;

	UObject::ProcessEvent(Func, &Parms);

	if (Comp != nullptr)
		*Comp = Parms.Comp;

	return Parms.ReturnValue;
}


// Function W_VicinityLootUI.W_VicinityLootUI_C.OnInitialized
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_VicinityLootUI_C::OnInitialized()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_VicinityLootUI_C", "OnInitialized");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_VicinityLootUI.W_VicinityLootUI_C.OnItemStackRequest_Event_0
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSI_Slot_C*                      DroppedItem                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSI_Slot_C*                      ReceiverItem                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_VicinityLootUI_C::OnItemStackRequest_Event_0(class UJSI_Slot_C* DroppedItem, class UJSI_Slot_C* ReceiverItem)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_VicinityLootUI_C", "OnItemStackRequest_Event_0");

	Params::W_VicinityLootUI_C_OnItemStackRequest_Event_0 Parms{};

	Parms.DroppedItem = DroppedItem;
	Parms.ReceiverItem = ReceiverItem;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_VicinityLootUI.W_VicinityLootUI_C.OnSlotMouseButtonDown_Event_0
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSIContainer_C*                  Container                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSI_Slot_C*                      SlotRef                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// const struct FKey&                      Button                                                 (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)

void UW_VicinityLootUI_C::OnSlotMouseButtonDown_Event_0(class UJSIContainer_C* Container, class UJSI_Slot_C* SlotRef, const struct FKey& Button)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_VicinityLootUI_C", "OnSlotMouseButtonDown_Event_0");

	Params::W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0 Parms{};

	Parms.Container = Container;
	Parms.SlotRef = SlotRef;
	Parms.Button = std::move(Button);

	UObject::ProcessEvent(Func, &Parms);
}

}

