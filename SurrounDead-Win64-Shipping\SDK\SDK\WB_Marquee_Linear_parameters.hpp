﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Marquee_Linear

#include "Basic.hpp"

#include "EMarqueeMethod_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WB_Marquee_Linear.WB_Marquee_Linear_C.ExecuteUbergraph_WB_Marquee_Linear
// 0x00F0 (0x00F0 - 0x0000)
struct WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EHorizontalAlignment                          Temp_byte_Variable;                                // 0x0004(0x0001)(ZeroConstructor, IsPlainOldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               CallFunc_GetDynamicMaterial_ReturnValue;           // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	EHorizontalAlignment                          Temp_byte_Variable_1;                              // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EHorizontalAlignment                          Temp_byte_Variable_2;                              // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13[0x5];                                       // 0x0013(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              K2Node_CustomEvent_Size;                           // 0x0018(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMarqueeMethod                                Temp_byte_Variable_3;                              // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_CustomEvent_Value_1;                        // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_CustomEvent_Tiling;                         // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Blueprint_GetSizeX_ReturnValue;           // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Blueprint_GetSizeY_ReturnValue;           // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_44[0x4];                                       // 0x0044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue_1;           // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_59[0x7];                                       // 0x0059(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0060(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Value;                          // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Percent;                        // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetDynamicMaterial_ReturnValue_1;         // 0x0088(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_91[0x7];                                       // 0x0091(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSize_Width;                            // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetSize_Height;                           // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EHorizontalAlignment                          Temp_byte_Variable_4;                              // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A9[0x7];                                       // 0x00A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_1;            // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UOverlaySlot*                           CallFunc_SlotAsOverlaySlot_ReturnValue;            // 0x00C8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	EMarqueeMethod                                K2Node_CustomEvent_MarqueeMethod;                  // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EHorizontalAlignment                          K2Node_Select_Default;                             // 0x00D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D2[0x6];                                       // 0x00D2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X_1;                        // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_1;                        // 0x00E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear) == 0x000008, "Wrong alignment on WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear");
static_assert(sizeof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear) == 0x0000F0, "Wrong size on WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, EntryPoint) == 0x000000, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, Temp_byte_Variable) == 0x000004, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_GetDynamicMaterial_ReturnValue) == 0x000008, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_GetDynamicMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, Temp_byte_Variable_1) == 0x000010, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_IsValid_ReturnValue) == 0x000011, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, Temp_byte_Variable_2) == 0x000012, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::Temp_byte_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, K2Node_CustomEvent_Size) == 0x000018, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::K2Node_CustomEvent_Size' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, Temp_byte_Variable_3) == 0x000028, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::Temp_byte_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, K2Node_CustomEvent_Value_1) == 0x000030, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::K2Node_CustomEvent_Value_1' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, K2Node_CustomEvent_Tiling) == 0x000038, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::K2Node_CustomEvent_Tiling' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_Blueprint_GetSizeX_ReturnValue) == 0x00003C, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_Blueprint_GetSizeX_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_Blueprint_GetSizeY_ReturnValue) == 0x000040, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_Blueprint_GetSizeY_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000048, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_Conv_IntToDouble_ReturnValue_1) == 0x000050, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_Conv_IntToDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_IsValid_ReturnValue_1) == 0x000058, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_MakeVector2D_ReturnValue) == 0x000060, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, K2Node_CustomEvent_Value) == 0x000070, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::K2Node_CustomEvent_Value' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_MapRangeClamped_ReturnValue) == 0x000078, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, K2Node_CustomEvent_Percent) == 0x000080, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::K2Node_CustomEvent_Percent' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_GetDynamicMaterial_ReturnValue_1) == 0x000088, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_GetDynamicMaterial_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_IsValid_ReturnValue_2) == 0x000090, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_GetSize_Width) == 0x000098, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_GetSize_Width' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_GetSize_Height) == 0x0000A0, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_GetSize_Height' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, Temp_byte_Variable_4) == 0x0000A8, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::Temp_byte_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_BreakVector2D_X) == 0x0000B0, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_BreakVector2D_Y) == 0x0000B8, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_MapRangeClamped_ReturnValue_1) == 0x0000C0, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_MapRangeClamped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_SlotAsOverlaySlot_ReturnValue) == 0x0000C8, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_SlotAsOverlaySlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, K2Node_CustomEvent_MarqueeMethod) == 0x0000D0, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::K2Node_CustomEvent_MarqueeMethod' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, K2Node_Select_Default) == 0x0000D1, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_BreakVector2D_X_1) == 0x0000D8, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_BreakVector2D_Y_1) == 0x0000E0, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x0000E8, "Member 'WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeMask
// 0x0008 (0x0008 - 0x0000)
struct WB_Marquee_Linear_C_SetMarqueeMask final
{
public:
	class UTexture2D*                             Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Marquee_Linear_C_SetMarqueeMask) == 0x000008, "Wrong alignment on WB_Marquee_Linear_C_SetMarqueeMask");
static_assert(sizeof(WB_Marquee_Linear_C_SetMarqueeMask) == 0x000008, "Wrong size on WB_Marquee_Linear_C_SetMarqueeMask");
static_assert(offsetof(WB_Marquee_Linear_C_SetMarqueeMask, Value) == 0x000000, "Member 'WB_Marquee_Linear_C_SetMarqueeMask::Value' has a wrong offset!");

// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_Marquee_Linear_C_SetMarqueeMethod final
{
public:
	EMarqueeMethod                                MarqueeMethod;                                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Marquee_Linear_C_SetMarqueeMethod) == 0x000001, "Wrong alignment on WB_Marquee_Linear_C_SetMarqueeMethod");
static_assert(sizeof(WB_Marquee_Linear_C_SetMarqueeMethod) == 0x000001, "Wrong size on WB_Marquee_Linear_C_SetMarqueeMethod");
static_assert(offsetof(WB_Marquee_Linear_C_SetMarqueeMethod, MarqueeMethod) == 0x000000, "Member 'WB_Marquee_Linear_C_SetMarqueeMethod::MarqueeMethod' has a wrong offset!");

// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeSize
// 0x0010 (0x0010 - 0x0000)
struct WB_Marquee_Linear_C_SetMarqueeSize final
{
public:
	struct FVector2D                              Size_0;                                            // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Marquee_Linear_C_SetMarqueeSize) == 0x000008, "Wrong alignment on WB_Marquee_Linear_C_SetMarqueeSize");
static_assert(sizeof(WB_Marquee_Linear_C_SetMarqueeSize) == 0x000010, "Wrong size on WB_Marquee_Linear_C_SetMarqueeSize");
static_assert(offsetof(WB_Marquee_Linear_C_SetMarqueeSize, Size_0) == 0x000000, "Member 'WB_Marquee_Linear_C_SetMarqueeSize::Size_0' has a wrong offset!");

// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeSpeed
// 0x0008 (0x0008 - 0x0000)
struct WB_Marquee_Linear_C_SetMarqueeSpeed final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Marquee_Linear_C_SetMarqueeSpeed) == 0x000008, "Wrong alignment on WB_Marquee_Linear_C_SetMarqueeSpeed");
static_assert(sizeof(WB_Marquee_Linear_C_SetMarqueeSpeed) == 0x000008, "Wrong size on WB_Marquee_Linear_C_SetMarqueeSpeed");
static_assert(offsetof(WB_Marquee_Linear_C_SetMarqueeSpeed, Value) == 0x000000, "Member 'WB_Marquee_Linear_C_SetMarqueeSpeed::Value' has a wrong offset!");

// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeTiling
// 0x0001 (0x0001 - 0x0000)
struct WB_Marquee_Linear_C_SetMarqueeTiling final
{
public:
	ESlateBrushTileType                           Tiling;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Marquee_Linear_C_SetMarqueeTiling) == 0x000001, "Wrong alignment on WB_Marquee_Linear_C_SetMarqueeTiling");
static_assert(sizeof(WB_Marquee_Linear_C_SetMarqueeTiling) == 0x000001, "Wrong size on WB_Marquee_Linear_C_SetMarqueeTiling");
static_assert(offsetof(WB_Marquee_Linear_C_SetMarqueeTiling, Tiling) == 0x000000, "Member 'WB_Marquee_Linear_C_SetMarqueeTiling::Tiling' has a wrong offset!");

// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Marquee_Linear_C_SetPercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Marquee_Linear_C_SetPercent) == 0x000008, "Wrong alignment on WB_Marquee_Linear_C_SetPercent");
static_assert(sizeof(WB_Marquee_Linear_C_SetPercent) == 0x000008, "Wrong size on WB_Marquee_Linear_C_SetPercent");
static_assert(offsetof(WB_Marquee_Linear_C_SetPercent, Percent) == 0x000000, "Member 'WB_Marquee_Linear_C_SetPercent::Percent' has a wrong offset!");

}

