﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_Van

#include "Basic.hpp"


namespace SDK::Params
{

// Function Vehicle_Van.Vehicle_Van_C.UserConstructionScript
// 0x0010 (0x0010 - 0x0000)
struct Vehicle_Van_C_UserConstructionScript final
{
public:
	TArray<class USpotLightComponent*>            K2Node_MakeArray_Array;                            // 0x0000(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(Vehicle_Van_C_UserConstructionScript) == 0x000008, "Wrong alignment on Vehicle_Van_C_UserConstructionScript");
static_assert(sizeof(Vehicle_Van_C_UserConstructionScript) == 0x000010, "Wrong size on Vehicle_Van_C_UserConstructionScript");
static_assert(offsetof(Vehicle_Van_C_UserConstructionScript, K2Node_MakeArray_Array) == 0x000000, "Member 'Vehicle_Van_C_UserConstructionScript::K2Node_MakeArray_Array' has a wrong offset!");

}

