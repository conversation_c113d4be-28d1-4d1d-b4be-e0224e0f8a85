﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_BatteryChargerUI

#include "Basic.hpp"

#include "GameplayTags_structs.hpp"


namespace SDK::Params
{

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.ExecuteUbergraph_W_BatteryChargerUI
// 0x0018 (0x0018 - 0x0000)
struct W_BatteryChargerUI_C_ExecuteUbergraph_W_BatteryChargerUI final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSIContainer_C*>                K2Node_MakeArray_Array;                            // 0x0008(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(W_BatteryChargerUI_C_ExecuteUbergraph_W_BatteryChargerUI) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_ExecuteUbergraph_W_BatteryChargerUI");
static_assert(sizeof(W_BatteryChargerUI_C_ExecuteUbergraph_W_BatteryChargerUI) == 0x000018, "Wrong size on W_BatteryChargerUI_C_ExecuteUbergraph_W_BatteryChargerUI");
static_assert(offsetof(W_BatteryChargerUI_C_ExecuteUbergraph_W_BatteryChargerUI, EntryPoint) == 0x000000, "Member 'W_BatteryChargerUI_C_ExecuteUbergraph_W_BatteryChargerUI::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_BatteryChargerUI_C_ExecuteUbergraph_W_BatteryChargerUI, K2Node_MakeArray_Array) == 0x000008, "Member 'W_BatteryChargerUI_C_ExecuteUbergraph_W_BatteryChargerUI::K2Node_MakeArray_Array' has a wrong offset!");

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.GetAllAttachments
// 0x0010 (0x0010 - 0x0000)
struct W_BatteryChargerUI_C_GetAllAttachments final
{
public:
	TArray<class FName>                           Attachments;                                       // 0x0000(0x0010)(Parm, OutParm)
};
static_assert(alignof(W_BatteryChargerUI_C_GetAllAttachments) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_GetAllAttachments");
static_assert(sizeof(W_BatteryChargerUI_C_GetAllAttachments) == 0x000010, "Wrong size on W_BatteryChargerUI_C_GetAllAttachments");
static_assert(offsetof(W_BatteryChargerUI_C_GetAllAttachments, Attachments) == 0x000000, "Member 'W_BatteryChargerUI_C_GetAllAttachments::Attachments' has a wrong offset!");

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.GetContainerByAttachmentType
// 0x0018 (0x0018 - 0x0000)
struct W_BatteryChargerUI_C_GetContainerByAttachmentType final
{
public:
	struct FGameplayTag                           Type;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        JigContainer;                                      // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         ContainerIndex;                                    // 0x0010(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_BatteryChargerUI_C_GetContainerByAttachmentType) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_GetContainerByAttachmentType");
static_assert(sizeof(W_BatteryChargerUI_C_GetContainerByAttachmentType) == 0x000018, "Wrong size on W_BatteryChargerUI_C_GetContainerByAttachmentType");
static_assert(offsetof(W_BatteryChargerUI_C_GetContainerByAttachmentType, Type) == 0x000000, "Member 'W_BatteryChargerUI_C_GetContainerByAttachmentType::Type' has a wrong offset!");
static_assert(offsetof(W_BatteryChargerUI_C_GetContainerByAttachmentType, JigContainer) == 0x000008, "Member 'W_BatteryChargerUI_C_GetContainerByAttachmentType::JigContainer' has a wrong offset!");
static_assert(offsetof(W_BatteryChargerUI_C_GetContainerByAttachmentType, ContainerIndex) == 0x000010, "Member 'W_BatteryChargerUI_C_GetContainerByAttachmentType::ContainerIndex' has a wrong offset!");

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.GetDropWidget
// 0x0008 (0x0008 - 0x0000)
struct W_BatteryChargerUI_C_GetDropWidget final
{
public:
	class UDropItemBackGwidget_C*                 DropWRef;                                          // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_BatteryChargerUI_C_GetDropWidget) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_GetDropWidget");
static_assert(sizeof(W_BatteryChargerUI_C_GetDropWidget) == 0x000008, "Wrong size on W_BatteryChargerUI_C_GetDropWidget");
static_assert(offsetof(W_BatteryChargerUI_C_GetDropWidget, DropWRef) == 0x000000, "Member 'W_BatteryChargerUI_C_GetDropWidget::DropWRef' has a wrong offset!");

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.GetJSIContainerByPlayerSlots
// 0x0020 (0x0020 - 0x0000)
struct W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots final
{
public:
	struct FGameplayTag                           Slot_0;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        Container;                                         // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            EquippedItem;                                      // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          IsPending_;                                        // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots");
static_assert(sizeof(W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots) == 0x000020, "Wrong size on W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots");
static_assert(offsetof(W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots, Slot_0) == 0x000000, "Member 'W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots::Slot_0' has a wrong offset!");
static_assert(offsetof(W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots, Container) == 0x000008, "Member 'W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots::Container' has a wrong offset!");
static_assert(offsetof(W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots, EquippedItem) == 0x000010, "Member 'W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots::EquippedItem' has a wrong offset!");
static_assert(offsetof(W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots, IsPending_) == 0x000018, "Member 'W_BatteryChargerUI_C_GetJSIContainerByPlayerSlots::IsPending_' has a wrong offset!");

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.GetListOfNonAddContainers
// 0x0010 (0x0010 - 0x0000)
struct W_BatteryChargerUI_C_GetListOfNonAddContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_BatteryChargerUI_C_GetListOfNonAddContainers) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_GetListOfNonAddContainers");
static_assert(sizeof(W_BatteryChargerUI_C_GetListOfNonAddContainers) == 0x000010, "Wrong size on W_BatteryChargerUI_C_GetListOfNonAddContainers");
static_assert(offsetof(W_BatteryChargerUI_C_GetListOfNonAddContainers, Containers) == 0x000000, "Member 'W_BatteryChargerUI_C_GetListOfNonAddContainers::Containers' has a wrong offset!");

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.GetLootContent
// 0x0008 (0x0008 - 0x0000)
struct W_BatteryChargerUI_C_GetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_BatteryChargerUI_C_GetLootContent) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_GetLootContent");
static_assert(sizeof(W_BatteryChargerUI_C_GetLootContent) == 0x000008, "Wrong size on W_BatteryChargerUI_C_GetLootContent");
static_assert(offsetof(W_BatteryChargerUI_C_GetLootContent, Widget) == 0x000000, "Member 'W_BatteryChargerUI_C_GetLootContent::Widget' has a wrong offset!");

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.GetValidReloadContainers
// 0x0010 (0x0010 - 0x0000)
struct W_BatteryChargerUI_C_GetValidReloadContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_BatteryChargerUI_C_GetValidReloadContainers) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_GetValidReloadContainers");
static_assert(sizeof(W_BatteryChargerUI_C_GetValidReloadContainers) == 0x000010, "Wrong size on W_BatteryChargerUI_C_GetValidReloadContainers");
static_assert(offsetof(W_BatteryChargerUI_C_GetValidReloadContainers, Containers) == 0x000000, "Member 'W_BatteryChargerUI_C_GetValidReloadContainers::Containers' has a wrong offset!");

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.SetActionbarFollower
// 0x0010 (0x0010 - 0x0000)
struct W_BatteryChargerUI_C_SetActionbarFollower final
{
public:
	class UJSI_Slot_C*                            JigRef;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Return;                                            // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_BatteryChargerUI_C_SetActionbarFollower) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_SetActionbarFollower");
static_assert(sizeof(W_BatteryChargerUI_C_SetActionbarFollower) == 0x000010, "Wrong size on W_BatteryChargerUI_C_SetActionbarFollower");
static_assert(offsetof(W_BatteryChargerUI_C_SetActionbarFollower, JigRef) == 0x000000, "Member 'W_BatteryChargerUI_C_SetActionbarFollower::JigRef' has a wrong offset!");
static_assert(offsetof(W_BatteryChargerUI_C_SetActionbarFollower, Return) == 0x000008, "Member 'W_BatteryChargerUI_C_SetActionbarFollower::Return' has a wrong offset!");

// Function W_BatteryChargerUI.W_BatteryChargerUI_C.GetListOfContainers
// 0x0020 (0x0020 - 0x0000)
struct W_BatteryChargerUI_C_GetListOfContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
	TArray<class UJSIContainer_C*>                K2Node_MakeArray_Array;                            // 0x0010(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(W_BatteryChargerUI_C_GetListOfContainers) == 0x000008, "Wrong alignment on W_BatteryChargerUI_C_GetListOfContainers");
static_assert(sizeof(W_BatteryChargerUI_C_GetListOfContainers) == 0x000020, "Wrong size on W_BatteryChargerUI_C_GetListOfContainers");
static_assert(offsetof(W_BatteryChargerUI_C_GetListOfContainers, Containers) == 0x000000, "Member 'W_BatteryChargerUI_C_GetListOfContainers::Containers' has a wrong offset!");
static_assert(offsetof(W_BatteryChargerUI_C_GetListOfContainers, K2Node_MakeArray_Array) == 0x000010, "Member 'W_BatteryChargerUI_C_GetListOfContainers::K2Node_MakeArray_Array' has a wrong offset!");

}

