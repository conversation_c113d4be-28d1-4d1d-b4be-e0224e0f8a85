﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_Truck

#include "Basic.hpp"

#include "Vehicle_Truck_classes.hpp"
#include "Vehicle_Truck_parameters.hpp"


namespace SDK
{

// Function Vehicle_Truck.Vehicle_Truck_C.UserConstructionScript
// (Event, Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AVehicle_Truck_C::UserConstructionScript()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_Truck_C", "UserConstructionScript");

	UObject::ProcessEvent(Func, nullptr);
}

}

