﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Container_Linear

#include "Basic.hpp"

#include "WB_Container_Linear_classes.hpp"
#include "WB_Container_Linear_parameters.hpp"


namespace SDK
{

// Function WB_Container_Linear.WB_Container_Linear_C.AddBackground
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bUseBackgroundBlur                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  BlurStrength                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::AddBackground(const struct FLinearColor& Color, bool bUseBackgroundBlur, double BlurStrength)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "AddBackground");

	Params::WB_Container_Linear_C_AddBackground Parms{};

	Parms.Color = std::move(Color);
	Parms.bUseBackgroundBlur = bUseBackgroundBlur;
	Parms.BlurStrength = BlurStrength;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.AddSegments
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   NumSegments                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Spacing                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Size_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              FillColor_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EProgressBarFillType                    FillType                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bIsFillFromCenter                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bUseShader                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::AddSegments(int32 NumSegments, double Spacing, const struct FVector2D& Size_0, const struct FLinearColor& FillColor_0, EProgressBarFillType FillType, bool bIsFillFromCenter, bool bUseShader)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "AddSegments");

	Params::WB_Container_Linear_C_AddSegments Parms{};

	Parms.NumSegments = NumSegments;
	Parms.Spacing = Spacing;
	Parms.Size_0 = std::move(Size_0);
	Parms.FillColor_0 = std::move(FillColor_0);
	Parms.FillType = FillType;
	Parms.bIsFillFromCenter = bIsFillFromCenter;
	Parms.bUseShader = bUseShader;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.ExecuteUbergraph_WB_Container_Linear
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::ExecuteUbergraph_WB_Container_Linear(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "ExecuteUbergraph_WB_Container_Linear");

	Params::WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.FindHighestPercentValue
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Container_Linear_C::FindHighestPercentValue()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "FindHighestPercentValue");

	Params::WB_Container_Linear_C_FindHighestPercentValue Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Linear.WB_Container_Linear_C.FindMarquee
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UWB_Marquee_Linear_C*             ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)

class UWB_Marquee_Linear_C* UWB_Container_Linear_C::FindMarquee()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "FindMarquee");

	Params::WB_Container_Linear_C_FindMarquee Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Linear.WB_Container_Linear_C.FindTargetFillColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor UWB_Container_Linear_C::FindTargetFillColor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "FindTargetFillColor");

	Params::WB_Container_Linear_C_FindTargetFillColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Linear.WB_Container_Linear_C.FindTargetPercentValue
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Container_Linear_C::FindTargetPercentValue()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "FindTargetPercentValue");

	Params::WB_Container_Linear_C_FindTargetPercentValue Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Linear.WB_Container_Linear_C.FindTargetProgressBarPosition
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Percent                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::FindTargetProgressBarPosition(double Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "FindTargetProgressBarPosition");

	Params::WB_Container_Linear_C_FindTargetProgressBarPosition Parms{};

	Parms.Percent = Percent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.GetAbsoluteTargetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Container_Linear_C::GetAbsoluteTargetPercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "GetAbsoluteTargetPercent");

	Params::WB_Container_Linear_C_GetAbsoluteTargetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Linear.WB_Container_Linear_C.GetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Percent                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::GetPercent(double* Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "GetPercent");

	Params::WB_Container_Linear_C_GetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Percent != nullptr)
		*Percent = Parms.Percent;
}


// Function WB_Container_Linear.WB_Container_Linear_C.GetPercent_Separated
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Percent                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::GetPercent_Separated(double* Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "GetPercent_Separated");

	Params::WB_Container_Linear_C_GetPercent_Separated Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Percent != nullptr)
		*Percent = Parms.Percent;
}


// Function WB_Container_Linear.WB_Container_Linear_C.GetSizeX
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Current_Size_X                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::GetSizeX(double* Current_Size_X)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "GetSizeX");

	Params::WB_Container_Linear_C_GetSizeX Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Current_Size_X != nullptr)
		*Current_Size_X = Parms.Current_Size_X;
}


// Function WB_Container_Linear.WB_Container_Linear_C.GetSizeY
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Current_Size_Y                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::GetSizeY(double* Current_Size_Y)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "GetSizeY");

	Params::WB_Container_Linear_C_GetSizeY Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Current_Size_Y != nullptr)
		*Current_Size_Y = Parms.Current_Size_Y;
}


// Function WB_Container_Linear.WB_Container_Linear_C.GetTargetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Percent                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::GetTargetPercent(double* Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "GetTargetPercent");

	Params::WB_Container_Linear_C_GetTargetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Percent != nullptr)
		*Percent = Parms.Percent;
}


// Function WB_Container_Linear.WB_Container_Linear_C.Handle_SetUseSeparation
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bUseSeparation_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::Handle_SetUseSeparation(bool bUseSeparation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "Handle_SetUseSeparation");

	Params::WB_Container_Linear_C_Handle_SetUseSeparation Parms{};

	Parms.bUseSeparation_0 = bUseSeparation_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.Hide_AllMarquees
// (Public, BlueprintCallable, BlueprintEvent)

void UWB_Container_Linear_C::Hide_AllMarquees()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "Hide_AllMarquees");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Linear.WB_Container_Linear_C.Interp_BasePB_Color
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  InterpSpeed                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bIsChanging                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::Interp_BasePB_Color(const struct FLinearColor& Target, double InterpSpeed, bool bIsChanging)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "Interp_BasePB_Color");

	Params::WB_Container_Linear_C_Interp_BasePB_Color Parms{};

	Parms.Target = std::move(Target);
	Parms.InterpSpeed = InterpSpeed;
	Parms.bIsChanging = bIsChanging;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.IsMarqueeMethod
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// EMarqueeMethod                          Method                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Container_Linear_C::IsMarqueeMethod(EMarqueeMethod Method)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "IsMarqueeMethod");

	Params::WB_Container_Linear_C_IsMarqueeMethod Parms{};

	Parms.Method = Method;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Linear.WB_Container_Linear_C.IsNegativeFillValue
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Container_Linear_C::IsNegativeFillValue()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "IsNegativeFillValue");

	Params::WB_Container_Linear_C_IsNegativeFillValue Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Linear.WB_Container_Linear_C.IsProgressMethodInterpolate
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Container_Linear_C::IsProgressMethodInterpolate()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "IsProgressMethodInterpolate");

	Params::WB_Container_Linear_C_IsProgressMethodInterpolate Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Linear.WB_Container_Linear_C.IsProgressMethodStatic
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Container_Linear_C::IsProgressMethodStatic()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "IsProgressMethodStatic");

	Params::WB_Container_Linear_C_IsProgressMethodStatic Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Linear.WB_Container_Linear_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "PreConstruct");

	Params::WB_Container_Linear_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.Set_DefaultMarquee_Visibility
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bVisible                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::Set_DefaultMarquee_Visibility(bool bVisible)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "Set_DefaultMarquee_Visibility");

	Params::WB_Container_Linear_C_Set_DefaultMarquee_Visibility Parms{};

	Parms.bVisible = bVisible;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.Set_MarqueeMask
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EMarqueeMask                            MaskType                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UTexture2D*                       CustomMask                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::Set_MarqueeMask(EMarqueeMask MaskType, class UTexture2D* CustomMask)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "Set_MarqueeMask");

	Params::WB_Container_Linear_C_Set_MarqueeMask Parms{};

	Parms.MaskType = MaskType;
	Parms.CustomMask = CustomMask;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.Set_Mirror_OV_BasePB_Marquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Mirror                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::Set_Mirror_OV_BasePB_Marquee(bool Mirror)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "Set_Mirror_OV_BasePB_Marquee");

	Params::WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee Parms{};

	Parms.Mirror = Mirror;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.Set_Mirror_OV_OnTopMarquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Mirror                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::Set_Mirror_OV_OnTopMarquee(bool Mirror)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "Set_Mirror_OV_OnTopMarquee");

	Params::WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee Parms{};

	Parms.Mirror = Mirror;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.Set_Mirror_OV_TargetPB_Marquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Mirror                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::Set_Mirror_OV_TargetPB_Marquee(bool Mirror)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "Set_Mirror_OV_TargetPB_Marquee");

	Params::WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee Parms{};

	Parms.Mirror = Mirror;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetBarFillType
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressBarFillType                    FillType                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bUseShader                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetBarFillType(EProgressBarFillType FillType, bool bUseShader)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetBarFillType");

	Params::WB_Container_Linear_C_SetBarFillType Parms{};

	Parms.FillType = FillType;
	Parms.bUseShader = bUseShader;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueeAppearance
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bIsMarquee                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EMarqueeMethod                          MarqueeMethod_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetCustomMarqueeAppearance(bool bIsMarquee, EMarqueeMethod MarqueeMethod_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetCustomMarqueeAppearance");

	Params::WB_Container_Linear_C_SetCustomMarqueeAppearance Parms{};

	Parms.bIsMarquee = bIsMarquee;
	Parms.MarqueeMethod_0 = MarqueeMethod_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueeColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColorAndOpacity                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetCustomMarqueeColor(const struct FLinearColor& InColorAndOpacity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetCustomMarqueeColor");

	Params::WB_Container_Linear_C_SetCustomMarqueeColor Parms{};

	Parms.InColorAndOpacity = std::move(InColorAndOpacity);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueeMask
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EMarqueeMask                            MaskType                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UTexture2D*                       CustomMask                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// ESlateBrushTileType                     MaskTiling                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetCustomMarqueeMask(EMarqueeMask MaskType, class UTexture2D* CustomMask, ESlateBrushTileType MaskTiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetCustomMarqueeMask");

	Params::WB_Container_Linear_C_SetCustomMarqueeMask Parms{};

	Parms.MaskType = MaskType;
	Parms.CustomMask = CustomMask;
	Parms.MaskTiling = MaskTiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueePercent_Current
// (BlueprintCallable, BlueprintEvent)

void UWB_Container_Linear_C::SetCustomMarqueePercent_Current()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetCustomMarqueePercent_Current");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueePercent_CurrentAndTarget
// (BlueprintCallable, BlueprintEvent)

void UWB_Container_Linear_C::SetCustomMarqueePercent_CurrentAndTarget()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetCustomMarqueePercent_CurrentAndTarget");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueePercent_Target
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Percent                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetCustomMarqueePercent_Target(double Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetCustomMarqueePercent_Target");

	Params::WB_Container_Linear_C_SetCustomMarqueePercent_Target Parms{};

	Parms.Percent = Percent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueeSpeed
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Speed                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetCustomMarqueeSpeed(double Speed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetCustomMarqueeSpeed");

	Params::WB_Container_Linear_C_SetCustomMarqueeSpeed Parms{};

	Parms.Speed = Speed;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_DrawAs
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushDrawType                     Draw_As                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetDefaultMarquee_DrawAs(ESlateBrushDrawType Draw_As)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetDefaultMarquee_DrawAs");

	Params::WB_Container_Linear_C_SetDefaultMarquee_DrawAs Parms{};

	Parms.Draw_As = Draw_As;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_Image
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture2D*                       Image                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetDefaultMarquee_Image(class UTexture2D* Image)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetDefaultMarquee_Image");

	Params::WB_Container_Linear_C_SetDefaultMarquee_Image Parms{};

	Parms.Image = Image;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_ImageSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 Image_Size                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetDefaultMarquee_ImageSize(const struct FVector2D& Image_Size)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetDefaultMarquee_ImageSize");

	Params::WB_Container_Linear_C_SetDefaultMarquee_ImageSize Parms{};

	Parms.Image_Size = std::move(Image_Size);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_IsMarquee
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsMarquee                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetDefaultMarquee_IsMarquee(bool IsMarquee)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetDefaultMarquee_IsMarquee");

	Params::WB_Container_Linear_C_SetDefaultMarquee_IsMarquee Parms{};

	Parms.IsMarquee = IsMarquee;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_Tiling
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetDefaultMarquee_Tiling(ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetDefaultMarquee_Tiling");

	Params::WB_Container_Linear_C_SetDefaultMarquee_Tiling Parms{};

	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_Tint
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetDefaultMarquee_Tint(const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetDefaultMarquee_Tint");

	Params::WB_Container_Linear_C_SetDefaultMarquee_Tint Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetFillColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColor                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  GradientPower                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetFillColor(const struct FLinearColor& InColor, double GradientPower, ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetFillColor");

	Params::WB_Container_Linear_C_SetFillColor Parms{};

	Parms.InColor = std::move(InColor);
	Parms.GradientPower = GradientPower;
	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetFillColorMask
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetFillColorMask(class UObject* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetFillColorMask");

	Params::WB_Container_Linear_C_SetFillColorMask Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetGradientMask
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture2D*                       GradientTexture                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetGradientMask(class UTexture2D* GradientTexture)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetGradientMask");

	Params::WB_Container_Linear_C_SetGradientMask Parms{};

	Parms.GradientTexture = GradientTexture;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetMirrorX_OV_Target
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    MirrorX                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetMirrorX_OV_Target(bool MirrorX)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetMirrorX_OV_Target");

	Params::WB_Container_Linear_C_SetMirrorX_OV_Target Parms{};

	Parms.MirrorX = MirrorX;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetMirrorY_OV_Target
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    MirrorY                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetMirrorY_OV_Target(bool MirrorY)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetMirrorY_OV_Target");

	Params::WB_Container_Linear_C_SetMirrorY_OV_Target Parms{};

	Parms.MirrorY = MirrorY;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  InPercent                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetPercent(double InPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetPercent");

	Params::WB_Container_Linear_C_SetPercent Parms{};

	Parms.InPercent = InPercent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetProgressMethod
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressMethod                         ProgressMethod_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetProgressMethod(EProgressMethod ProgressMethod_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetProgressMethod");

	Params::WB_Container_Linear_C_SetProgressMethod Parms{};

	Parms.ProgressMethod_0 = ProgressMethod_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 Size_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetSize(const struct FVector2D& Size_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetSize");

	Params::WB_Container_Linear_C_SetSize Parms{};

	Parms.Size_0 = std::move(Size_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetTargetFillColorNegative
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetTargetFillColorNegative(const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetTargetFillColorNegative");

	Params::WB_Container_Linear_C_SetTargetFillColorNegative Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetTargetFillColorPositive
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetTargetFillColorPositive(const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetTargetFillColorPositive");

	Params::WB_Container_Linear_C_SetTargetFillColorPositive Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetTargetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  TargetPercent_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetTargetPercent(double TargetPercent_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetTargetPercent");

	Params::WB_Container_Linear_C_SetTargetPercent Parms{};

	Parms.TargetPercent_0 = TargetPercent_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetUseAbsoluteFillMethod
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bAbsoluteFill                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetUseAbsoluteFillMethod(bool bAbsoluteFill)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetUseAbsoluteFillMethod");

	Params::WB_Container_Linear_C_SetUseAbsoluteFillMethod Parms{};

	Parms.bAbsoluteFill = bAbsoluteFill;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetUseGradient
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    UseGradient                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetUseGradient(bool UseGradient)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetUseGradient");

	Params::WB_Container_Linear_C_SetUseGradient Parms{};

	Parms.UseGradient = UseGradient;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetUseSeparation
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bUseSeparation_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetUseSeparation(bool bUseSeparation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetUseSeparation");

	Params::WB_Container_Linear_C_SetUseSeparation Parms{};

	Parms.bUseSeparation_0 = bUseSeparation_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.SetUseTargetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    UseTargetPercent                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::SetUseTargetPercent(bool UseTargetPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "SetUseTargetPercent");

	Params::WB_Container_Linear_C_SetUseTargetPercent Parms{};

	Parms.UseTargetPercent = UseTargetPercent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.StartTriggerProgressChangeColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              ProgressChangeColor_0                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::StartTriggerProgressChangeColor(const struct FLinearColor& ProgressChangeColor_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "StartTriggerProgressChangeColor");

	Params::WB_Container_Linear_C_StartTriggerProgressChangeColor Parms{};

	Parms.ProgressChangeColor_0 = std::move(ProgressChangeColor_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.StopTriggerProgressChangeColor
// (BlueprintCallable, BlueprintEvent)

void UWB_Container_Linear_C::StopTriggerProgressChangeColor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "StopTriggerProgressChangeColor");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Linear.WB_Container_Linear_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "Tick");

	Params::WB_Container_Linear_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.UpdateMarqueeFillType
// (BlueprintCallable, BlueprintEvent)

void UWB_Container_Linear_C::UpdateMarqueeFillType()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "UpdateMarqueeFillType");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Linear.WB_Container_Linear_C.UpdatePercent
// (BlueprintCallable, BlueprintEvent)

void UWB_Container_Linear_C::UpdatePercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "UpdatePercent");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Linear.WB_Container_Linear_C.UpdateSeparationPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Percent                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Linear_C::UpdateSeparationPercent(double Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "UpdateSeparationPercent");

	Params::WB_Container_Linear_C_UpdateSeparationPercent Parms{};

	Parms.Percent = Percent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Linear.WB_Container_Linear_C.UpdateStaticPercent
// (Public, BlueprintCallable, BlueprintEvent)

void UWB_Container_Linear_C::UpdateStaticPercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "UpdateStaticPercent");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Linear.WB_Container_Linear_C.UpdateTargetPercent
// (BlueprintCallable, BlueprintEvent)

void UWB_Container_Linear_C::UpdateTargetPercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Linear_C", "UpdateTargetPercent");

	UObject::ProcessEvent(Func, nullptr);
}

}

