﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_RV

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "BP_VehicleMaster_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass Vehicle_RV.Vehicle_RV_C
// 0x0008 (0x04C8 - 0x04C0)
class AVehicle_RV_C final : public ABP_VehicleMaster_C
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame_Vehicle_RV_C;                       // 0x04C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)

public:
	void ExecuteUbergraph_Vehicle_RV(int32 EntryPoint);
	void SleepInteractionOption(class AActor* InteractingActor);
	void UserConstructionScript();
	void GetInteractOptions(TMap<struct FGameplayTag, class FText>* Options);
	void ReceiveBeginPlay();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Vehicle_RV_C">();
	}
	static class AVehicle_RV_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<AVehicle_RV_C>();
	}
};
static_assert(alignof(AVehicle_RV_C) == 0x000008, "Wrong alignment on AVehicle_RV_C");
static_assert(sizeof(AVehicle_RV_C) == 0x0004C8, "Wrong size on AVehicle_RV_C");
static_assert(offsetof(AVehicle_RV_C, UberGraphFrame_Vehicle_RV_C) == 0x0004C0, "Member 'AVehicle_RV_C::UberGraphFrame_Vehicle_RV_C' has a wrong offset!");

}

