﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UltraDynamicWeather_Functions

#include "Basic.hpp"

#include "Engine_classes.hpp"
#include "UDS_Season_structs.hpp"
#include "UDS_Weather_Display_Names_structs.hpp"
#include "UDS_Temperature_Sample_Location_structs.hpp"
#include "UDS_TemperatureType_structs.hpp"


namespace SDK
{

// BlueprintGeneratedClass UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C
// 0x0000 (0x0028 - 0x0028)
class UUltraDynamicWeather_Functions_C final : public UBlueprintFunctionLibrary
{
public:
	static void Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶(double Time_to_Transition_to_Random_Weather__Seconds_, class UObject* __WorldContext);
	static void Change_Weather_·_𝖴𝖣𝖶(class UUDS_Weather_Settings_C* New_Weather_Type, double Time_To_Transition_To_New_Weather__Seconds_, class UObject* __WorldContext);
	static void Change_Wind_Direction_·_𝖴𝖣𝖶(double New_Wind_Direction, double Change_Duration, class UObject* __WorldContext);
	static void Flash_Lightning_·_𝖴𝖣𝖶(bool Use_Custom_Lightning_Location, const struct FVector& Custom_Lightning_Location, const struct FVector& Custom_Target_Location, int32 Lightning_Bolt_Seed, class UObject* __WorldContext);
	static void Get_Cloud_Coverage_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Cloud_Coverage);
	static void Get_Current_Temperature_·_𝖴𝖣𝖶(EUDS_Temperature_Sample_Location Sample_Location, const struct FVector& Custom_Sample_Location, EUDS_TemperatureType Scale, class UObject* __WorldContext, double* Temperature);
	static void Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶(class UObject* __WorldContext, class FString* As_String, EUDS_Weather_Display_Names* As_Enumerator);
	static void Sand_Amount_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Dust);
	static void Get_Fog_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Fog);
	static void Get_Local_Weather_State_·_𝖴𝖣𝖶(class UObject* __WorldContext, class UUDS_Weather_Settings_C** Local_Weather_State);
	static void Get_Material_Dust_Coverage_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Material_Dust_Coverage);
	static void Get_Material_Snow_Coverage_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Material_Snow_Coverage);
	static void Get_Material_Wetness_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Material_Wetness);
	static void Get_Rain_Amount_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Rain);
	static void Get_Season_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Season, EUDS_Season* Season_Enum);
	static void Get_Snow_Amount_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Snow);
	static void Lightning_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Lightning);
	static void Get_Ultra_Dynamic_Weather(class UObject* __WorldContext, class AUltra_Dynamic_Weather_C** UDW, bool* Valid);
	static void Get_Wind_Direction_Vector_·_𝖴𝖣𝖶(class UObject* __WorldContext, struct FVector* Wind_Direction);
	static void Get_Wind_Intensity_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Wind_Intensity);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"UltraDynamicWeather_Functions_C">();
	}
	static class UUltraDynamicWeather_Functions_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UUltraDynamicWeather_Functions_C>();
	}
};
static_assert(alignof(UUltraDynamicWeather_Functions_C) == 0x000008, "Wrong alignment on UUltraDynamicWeather_Functions_C");
static_assert(sizeof(UUltraDynamicWeather_Functions_C) == 0x000028, "Wrong size on UUltraDynamicWeather_Functions_C");

}

