﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_DisassemblyWorkbenchUI

#include "Basic.hpp"

#include "GameplayTags_structs.hpp"
#include "ContainerPickupsInfo_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"


namespace SDK::Params
{

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.ExecuteUbergraph_W_DisassemblyWorkbenchUI
// 0x0160 (0x0160 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_RandomFloatInRange_ReturnValue;           // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A[0x6];                                       // 0x001A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            K2Node_Event_ItemRef;                              // 0x0020(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 K2Node_Event_ActorRef;                             // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_NewWeight;                            // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUserWidget*                            K2Node_Event_Widget;                               // 0x0038(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   K2Node_Event_Name;                                 // 0x0040(0x0018)()
	class UBP_InspectorWindowWidget_C*            K2Node_Event_Inspector;                            // 0x0058(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_64[0x4];                                       // 0x0064(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UBP_JigMultiplayer_C*                   K2Node_DynamicCast_AsBP_Jig_Multiplayer;           // 0x0068(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_71[0x3];                                       // 0x0071(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0074(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FContainerPickupsInfo>          CallFunc_GetContainerItems_AllItems;               // 0x0078(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UBP_JigMultiplayer_C*                   K2Node_DynamicCast_AsBP_Jig_Multiplayer_1;         // 0x0088(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_91[0x3];                                       // 0x0091(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_LastIndex_ReturnValue;              // 0x0094(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_99[0x7];                                       // 0x0099(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x00A0(0x0050)(HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x00F0(0x0010)(ReferenceParm)
	int32                                         CallFunc_RandomIntegerInRange_ReturnValue;         // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_104[0x4];                                      // 0x0104(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0108(0x0018)()
	int32                                         CallFunc_Multiply_IntInt_ReturnValue;              // 0x0120(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JigTryAddItemSomewhere_Added_;            // 0x0124(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_125[0x3];                                      // 0x0125(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGuid                                  CallFunc_JigTryAddItemSomewhere_UID;               // 0x0128(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue;       // 0x0138(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_139[0x3];                                      // 0x0139(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_LastIndex_ReturnValue_1;            // 0x013C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0141(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0142(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x0143(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_144[0x4];                                      // 0x0144(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSIContainer_C*>                K2Node_MakeArray_Array_1;                          // 0x0148(0x0010)(ReferenceParm, ContainsInstancedReference)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI) == 0x000160, "Wrong size on W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, EntryPoint) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_RandomFloatInRange_ReturnValue) == 0x000008, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_RandomFloatInRange_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, Temp_int_Variable) == 0x000010, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_Add_IntInt_ReturnValue) == 0x000014, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, Temp_bool_Variable) == 0x000018, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_Not_PreBool_ReturnValue) == 0x000019, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_Event_ItemRef) == 0x000020, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_Event_ItemRef' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_Event_ActorRef) == 0x000028, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_Event_ActorRef' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_Event_NewWeight) == 0x000030, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_Event_NewWeight' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_Event_Widget) == 0x000038, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_Event_Widget' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_Event_Name) == 0x000040, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_Event_Name' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_Event_Inspector) == 0x000058, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_Event_Inspector' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, Temp_int_Variable_1) == 0x000060, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_DynamicCast_AsBP_Jig_Multiplayer) == 0x000068, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_DynamicCast_AsBP_Jig_Multiplayer' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_DynamicCast_bSuccess) == 0x000070, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_Add_IntInt_ReturnValue_1) == 0x000074, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_GetContainerItems_AllItems) == 0x000078, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_GetContainerItems_AllItems' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_DynamicCast_AsBP_Jig_Multiplayer_1) == 0x000088, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_DynamicCast_AsBP_Jig_Multiplayer_1' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_DynamicCast_bSuccess_1) == 0x000090, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_Array_LastIndex_ReturnValue) == 0x000094, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_Array_LastIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000098, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_MakeStruct_FormatArgumentData) == 0x0000A0, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_MakeArray_Array) == 0x0000F0, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_RandomIntegerInRange_ReturnValue) == 0x000100, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_RandomIntegerInRange_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_Format_ReturnValue) == 0x000108, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_Multiply_IntInt_ReturnValue) == 0x000120, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_Multiply_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_JigTryAddItemSomewhere_Added_) == 0x000124, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_JigTryAddItemSomewhere_Added_' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_JigTryAddItemSomewhere_UID) == 0x000128, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_JigTryAddItemSomewhere_UID' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_LessEqual_DoubleDouble_ReturnValue) == 0x000138, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_LessEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_Array_LastIndex_ReturnValue_1) == 0x00013C, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_Array_LastIndex_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x000140, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_BooleanAND_ReturnValue) == 0x000141, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, Temp_bool_Variable_1) == 0x000142, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_Not_PreBool_ReturnValue_1) == 0x000143, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, K2Node_MakeArray_Array_1) == 0x000148, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::K2Node_MakeArray_Array_1' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI, CallFunc_BooleanAND_ReturnValue_1) == 0x000158, "Member 'W_DisassemblyWorkbenchUI_C_ExecuteUbergraph_W_DisassemblyWorkbenchUI::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.GetAllAttachments
// 0x0010 (0x0010 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_GetAllAttachments final
{
public:
	TArray<class FName>                           Attachments;                                       // 0x0000(0x0010)(Parm, OutParm)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_GetAllAttachments) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_GetAllAttachments");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_GetAllAttachments) == 0x000010, "Wrong size on W_DisassemblyWorkbenchUI_C_GetAllAttachments");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetAllAttachments, Attachments) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_GetAllAttachments::Attachments' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.GetContainerByAttachmentType
// 0x0018 (0x0018 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType final
{
public:
	struct FGameplayTag                           Type;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        JigContainer;                                      // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         ContainerIndex;                                    // 0x0010(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType) == 0x000018, "Wrong size on W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType, Type) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType::Type' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType, JigContainer) == 0x000008, "Member 'W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType::JigContainer' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType, ContainerIndex) == 0x000010, "Member 'W_DisassemblyWorkbenchUI_C_GetContainerByAttachmentType::ContainerIndex' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.GetDropWidget
// 0x0008 (0x0008 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_GetDropWidget final
{
public:
	class UDropItemBackGwidget_C*                 DropWRef;                                          // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_GetDropWidget) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_GetDropWidget");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_GetDropWidget) == 0x000008, "Wrong size on W_DisassemblyWorkbenchUI_C_GetDropWidget");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetDropWidget, DropWRef) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_GetDropWidget::DropWRef' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.GetJSIContainerByPlayerSlots
// 0x0020 (0x0020 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots final
{
public:
	struct FGameplayTag                           Slot_0;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        Container;                                         // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            EquippedItem;                                      // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          IsPending_;                                        // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots) == 0x000020, "Wrong size on W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots, Slot_0) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots::Slot_0' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots, Container) == 0x000008, "Member 'W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots::Container' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots, EquippedItem) == 0x000010, "Member 'W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots::EquippedItem' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots, IsPending_) == 0x000018, "Member 'W_DisassemblyWorkbenchUI_C_GetJSIContainerByPlayerSlots::IsPending_' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.GetListOfNonAddContainers
// 0x0010 (0x0010 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_GetListOfNonAddContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_GetListOfNonAddContainers) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_GetListOfNonAddContainers");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_GetListOfNonAddContainers) == 0x000010, "Wrong size on W_DisassemblyWorkbenchUI_C_GetListOfNonAddContainers");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetListOfNonAddContainers, Containers) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_GetListOfNonAddContainers::Containers' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.GetLootContent
// 0x0008 (0x0008 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_GetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_GetLootContent) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_GetLootContent");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_GetLootContent) == 0x000008, "Wrong size on W_DisassemblyWorkbenchUI_C_GetLootContent");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetLootContent, Widget) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_GetLootContent::Widget' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.GetValidReloadContainers
// 0x0010 (0x0010 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_GetValidReloadContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_GetValidReloadContainers) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_GetValidReloadContainers");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_GetValidReloadContainers) == 0x000010, "Wrong size on W_DisassemblyWorkbenchUI_C_GetValidReloadContainers");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetValidReloadContainers, Containers) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_GetValidReloadContainers::Containers' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.JigSetLootContent
// 0x0020 (0x0020 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_JigSetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   Name_0;                                            // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_JigSetLootContent) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_JigSetLootContent");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_JigSetLootContent) == 0x000020, "Wrong size on W_DisassemblyWorkbenchUI_C_JigSetLootContent");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_JigSetLootContent, Widget) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_JigSetLootContent::Widget' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_JigSetLootContent, Name_0) == 0x000008, "Member 'W_DisassemblyWorkbenchUI_C_JigSetLootContent::Name_0' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.JSIOnWeightUpdated
// 0x0008 (0x0008 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_JSIOnWeightUpdated final
{
public:
	double                                        NewWeight;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_JSIOnWeightUpdated");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong size on W_DisassemblyWorkbenchUI_C_JSIOnWeightUpdated");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_JSIOnWeightUpdated, NewWeight) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_JSIOnWeightUpdated::NewWeight' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.SetActionbarFollower
// 0x0010 (0x0010 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_SetActionbarFollower final
{
public:
	class UJSI_Slot_C*                            JigRef;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Return;                                            // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_SetActionbarFollower) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_SetActionbarFollower");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_SetActionbarFollower) == 0x000010, "Wrong size on W_DisassemblyWorkbenchUI_C_SetActionbarFollower");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_SetActionbarFollower, JigRef) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_SetActionbarFollower::JigRef' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_SetActionbarFollower, Return) == 0x000008, "Member 'W_DisassemblyWorkbenchUI_C_SetActionbarFollower::Return' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.SetActorOwner
// 0x0008 (0x0008 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_SetActorOwner final
{
public:
	class AActor*                                 ActorRef;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_SetActorOwner) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_SetActorOwner");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_SetActorOwner) == 0x000008, "Wrong size on W_DisassemblyWorkbenchUI_C_SetActorOwner");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_SetActorOwner, ActorRef) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_SetActorOwner::ActorRef' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.SetInspectorRef
// 0x0008 (0x0008 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_SetInspectorRef final
{
public:
	class UBP_InspectorWindowWidget_C*            Inspector;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_SetInspectorRef) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_SetInspectorRef");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_SetInspectorRef) == 0x000008, "Wrong size on W_DisassemblyWorkbenchUI_C_SetInspectorRef");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_SetInspectorRef, Inspector) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_SetInspectorRef::Inspector' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.SetItemReference
// 0x0008 (0x0008 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_SetItemReference final
{
public:
	class UJSI_Slot_C*                            ItemRef;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_SetItemReference) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_SetItemReference");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_SetItemReference) == 0x000008, "Wrong size on W_DisassemblyWorkbenchUI_C_SetItemReference");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_SetItemReference, ItemRef) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_SetItemReference::ItemRef' has a wrong offset!");

// Function W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C.GetListOfContainers
// 0x0020 (0x0020 - 0x0000)
struct W_DisassemblyWorkbenchUI_C_GetListOfContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
	TArray<class UJSIContainer_C*>                K2Node_MakeArray_Array;                            // 0x0010(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(W_DisassemblyWorkbenchUI_C_GetListOfContainers) == 0x000008, "Wrong alignment on W_DisassemblyWorkbenchUI_C_GetListOfContainers");
static_assert(sizeof(W_DisassemblyWorkbenchUI_C_GetListOfContainers) == 0x000020, "Wrong size on W_DisassemblyWorkbenchUI_C_GetListOfContainers");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetListOfContainers, Containers) == 0x000000, "Member 'W_DisassemblyWorkbenchUI_C_GetListOfContainers::Containers' has a wrong offset!");
static_assert(offsetof(W_DisassemblyWorkbenchUI_C_GetListOfContainers, K2Node_MakeArray_Array) == 0x000010, "Member 'W_DisassemblyWorkbenchUI_C_GetListOfContainers::K2Node_MakeArray_Array' has a wrong offset!");

}

