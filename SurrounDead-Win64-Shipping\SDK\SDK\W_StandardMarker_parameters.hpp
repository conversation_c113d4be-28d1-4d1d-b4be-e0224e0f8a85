﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_StandardMarker

#include "Basic.hpp"

#include "UMG_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function W_StandardMarker.W_StandardMarker_C.Construct Marker
// 0x0018 (0x0018 - 0x0000)
struct W_StandardMarker_C_Construct_Marker final
{
public:
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0000(0x0014)()
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_StandardMarker_C_Construct_Marker) == 0x000004, "Wrong alignment on W_StandardMarker_C_Construct_Marker");
static_assert(sizeof(W_StandardMarker_C_Construct_Marker) == 0x000018, "Wrong size on W_StandardMarker_C_Construct_Marker");
static_assert(offsetof(W_StandardMarker_C_Construct_Marker, K2Node_MakeStruct_SlateColor) == 0x000000, "Member 'W_StandardMarker_C_Construct_Marker::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_Construct_Marker, CallFunc_IsValid_ReturnValue) == 0x000014, "Member 'W_StandardMarker_C_Construct_Marker::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function W_StandardMarker.W_StandardMarker_C.ExecuteUbergraph_W_StandardMarker
// 0x0028 (0x0028 - 0x0000)
struct W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_CustomEvent_PlayMode;                       // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_Destroy_On_Hide;                // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x000B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x000D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E[0x2];                                        // 0x000E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_GetEndTime_ReturnValue;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMapMarker_ToolTip_C*                   CallFunc_Create_ReturnValue;                       // 0x0020(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker) == 0x000008, "Wrong alignment on W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker");
static_assert(sizeof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker) == 0x000028, "Wrong size on W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, EntryPoint) == 0x000000, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, K2Node_CustomEvent_PlayMode) == 0x000004, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::K2Node_CustomEvent_PlayMode' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, K2Node_CustomEvent_Destroy_On_Hide) == 0x000005, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::K2Node_CustomEvent_Destroy_On_Hide' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, CallFunc_Not_PreBool_ReturnValue) == 0x000006, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000007, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000008, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, CallFunc_BooleanAND_ReturnValue) == 0x000009, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, CallFunc_BooleanAND_ReturnValue_1) == 0x00000A, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, K2Node_SwitchEnum_CmpSuccess) == 0x00000B, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, CallFunc_BooleanOR_ReturnValue) == 0x00000C, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, K2Node_SwitchEnum_CmpSuccess_1) == 0x00000D, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, CallFunc_GetEndTime_ReturnValue) == 0x000010, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::CallFunc_GetEndTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, CallFunc_PlayAnimation_ReturnValue) == 0x000018, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker, CallFunc_Create_ReturnValue) == 0x000020, "Member 'W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker::CallFunc_Create_ReturnValue' has a wrong offset!");

// Function W_StandardMarker.W_StandardMarker_C.OnMouseButtonDoubleClick
// 0x0230 (0x0230 - 0x0000)
struct W_StandardMarker_C_OnMouseButtonDoubleClick final
{
public:
	struct FGeometry                              InMyGeometry;                                      // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          InMouseEvent;                                      // 0x0038(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00B0(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0168(0x00B8)()
	class UBPC_MinimapSystem_C*                   CallFunc_Get_Minimap_Component_ReturnValue;        // 0x0220(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0228(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_StandardMarker_C_OnMouseButtonDoubleClick) == 0x000008, "Wrong alignment on W_StandardMarker_C_OnMouseButtonDoubleClick");
static_assert(sizeof(W_StandardMarker_C_OnMouseButtonDoubleClick) == 0x000230, "Wrong size on W_StandardMarker_C_OnMouseButtonDoubleClick");
static_assert(offsetof(W_StandardMarker_C_OnMouseButtonDoubleClick, InMyGeometry) == 0x000000, "Member 'W_StandardMarker_C_OnMouseButtonDoubleClick::InMyGeometry' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_OnMouseButtonDoubleClick, InMouseEvent) == 0x000038, "Member 'W_StandardMarker_C_OnMouseButtonDoubleClick::InMouseEvent' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_OnMouseButtonDoubleClick, ReturnValue) == 0x0000B0, "Member 'W_StandardMarker_C_OnMouseButtonDoubleClick::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_OnMouseButtonDoubleClick, CallFunc_Handled_ReturnValue) == 0x000168, "Member 'W_StandardMarker_C_OnMouseButtonDoubleClick::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_OnMouseButtonDoubleClick, CallFunc_Get_Minimap_Component_ReturnValue) == 0x000220, "Member 'W_StandardMarker_C_OnMouseButtonDoubleClick::CallFunc_Get_Minimap_Component_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_OnMouseButtonDoubleClick, CallFunc_IsValid_ReturnValue) == 0x000228, "Member 'W_StandardMarker_C_OnMouseButtonDoubleClick::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function W_StandardMarker.W_StandardMarker_C.Play Hide Animation
// 0x0002 (0x0002 - 0x0000)
struct W_StandardMarker_C_Play_Hide_Animation final
{
public:
	EUMGSequencePlayMode                          PlayMode;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Destroy_On_Hide;                                   // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_StandardMarker_C_Play_Hide_Animation) == 0x000001, "Wrong alignment on W_StandardMarker_C_Play_Hide_Animation");
static_assert(sizeof(W_StandardMarker_C_Play_Hide_Animation) == 0x000002, "Wrong size on W_StandardMarker_C_Play_Hide_Animation");
static_assert(offsetof(W_StandardMarker_C_Play_Hide_Animation, PlayMode) == 0x000000, "Member 'W_StandardMarker_C_Play_Hide_Animation::PlayMode' has a wrong offset!");
static_assert(offsetof(W_StandardMarker_C_Play_Hide_Animation, Destroy_On_Hide) == 0x000001, "Member 'W_StandardMarker_C_Play_Hide_Animation::Destroy_On_Hide' has a wrong offset!");

}

