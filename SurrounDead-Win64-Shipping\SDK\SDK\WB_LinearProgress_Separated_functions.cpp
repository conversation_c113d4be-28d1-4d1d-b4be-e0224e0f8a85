﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_LinearProgress_Separated

#include "Basic.hpp"

#include "WB_LinearProgress_Separated_classes.hpp"
#include "WB_LinearProgress_Separated_parameters.hpp"


namespace SDK
{

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.AddSegmentsBackground
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bUseBackgroundBlur_0                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  BlurStrength_0                                         (BlueprintVisible, BlueprintReadOnly, <PERSON>rm, ZeroCons<PERSON><PERSON>, <PERSON><PERSON><PERSON>OldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::AddSegmentsBackground(const struct FLinearColor& Color, bool bUseBackgroundBlur_0, double BlurStrength_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "AddSegmentsBackground");

	Params::WB_LinearProgress_Separated_C_AddSegmentsBackground Parms{};

	Parms.Color = std::move(Color);
	Parms.bUseBackgroundBlur_0 = bUseBackgroundBlur_0;
	Parms.BlurStrength_0 = BlurStrength_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.AddSegmentsMarquee
// (BlueprintCallable, BlueprintEvent)

void UWB_LinearProgress_Separated_C::AddSegmentsMarquee()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "AddSegmentsMarquee");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.AddSegmentsProgressBar
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   NumSegments_0                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Spacing_0                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Size_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              FillColor_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EProgressBarFillType                    FillType_0                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bIsFillFromCenter_0                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bUseShader_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::AddSegmentsProgressBar(int32 NumSegments_0, double Spacing_0, const struct FVector2D& Size_0, const struct FLinearColor& FillColor_0, EProgressBarFillType FillType_0, bool bIsFillFromCenter_0, bool bUseShader_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "AddSegmentsProgressBar");

	Params::WB_LinearProgress_Separated_C_AddSegmentsProgressBar Parms{};

	Parms.NumSegments_0 = NumSegments_0;
	Parms.Spacing_0 = Spacing_0;
	Parms.Size_0 = std::move(Size_0);
	Parms.FillColor_0 = std::move(FillColor_0);
	Parms.FillType_0 = FillType_0;
	Parms.bIsFillFromCenter_0 = bIsFillFromCenter_0;
	Parms.bUseShader_0 = bUseShader_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.ClearSegments_Background
// (BlueprintCallable, BlueprintEvent)

void UWB_LinearProgress_Separated_C::ClearSegments_Background()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "ClearSegments_Background");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.ClearSegments_Marquee
// (BlueprintCallable, BlueprintEvent)

void UWB_LinearProgress_Separated_C::ClearSegments_Marquee()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "ClearSegments_Marquee");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.ClearSegments_ProgressBar
// (BlueprintCallable, BlueprintEvent)

void UWB_LinearProgress_Separated_C::ClearSegments_ProgressBar()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "ClearSegments_ProgressBar");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.ExecuteUbergraph_WB_LinearProgress_Separated
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::ExecuteUbergraph_WB_LinearProgress_Separated(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "ExecuteUbergraph_WB_LinearProgress_Separated");

	Params::WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.FindCurrentSegmentPercentValue
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_LinearProgress_Separated_C::FindCurrentSegmentPercentValue()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "FindCurrentSegmentPercentValue");

	Params::WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.FindMaxPaddingValue_Horizontal
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Tolerance                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_LinearProgress_Separated_C::FindMaxPaddingValue_Horizontal(double Tolerance)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "FindMaxPaddingValue_Horizontal");

	Params::WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal Parms{};

	Parms.Tolerance = Tolerance;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.FindMaxPaddingValue_Vertical
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Tolerance                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_LinearProgress_Separated_C::FindMaxPaddingValue_Vertical(double Tolerance)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "FindMaxPaddingValue_Vertical");

	Params::WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical Parms{};

	Parms.Tolerance = Tolerance;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.FindPercentCurrentSegment
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

int32 UWB_LinearProgress_Separated_C::FindPercentCurrentSegment()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "FindPercentCurrentSegment");

	Params::WB_LinearProgress_Separated_C_FindPercentCurrentSegment Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.GetSegmentSteps
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_LinearProgress_Separated_C::GetSegmentSteps()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "GetSegmentSteps");

	Params::WB_LinearProgress_Separated_C_GetSegmentSteps Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetAbsoluteFillMethod
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bAbsoluteFill_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::SetAbsoluteFillMethod(bool bAbsoluteFill_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "SetAbsoluteFillMethod");

	Params::WB_LinearProgress_Separated_C_SetAbsoluteFillMethod Parms{};

	Parms.bAbsoluteFill_0 = bAbsoluteFill_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeDrawAs
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushDrawType                     DrawAs                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::SetMarqueeDrawAs(ESlateBrushDrawType DrawAs)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "SetMarqueeDrawAs");

	Params::WB_LinearProgress_Separated_C_SetMarqueeDrawAs Parms{};

	Parms.DrawAs = DrawAs;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeImage
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          Image                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::SetMarqueeImage(class UObject* Image)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "SetMarqueeImage");

	Params::WB_LinearProgress_Separated_C_SetMarqueeImage Parms{};

	Parms.Image = Image;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeImageSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 ImageSize                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::SetMarqueeImageSize(const struct FVector2D& ImageSize)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "SetMarqueeImageSize");

	Params::WB_LinearProgress_Separated_C_SetMarqueeImageSize Parms{};

	Parms.ImageSize = std::move(ImageSize);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeTiling
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::SetMarqueeTiling(ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "SetMarqueeTiling");

	Params::WB_LinearProgress_Separated_C_SetMarqueeTiling Parms{};

	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeTint
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Tint                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::SetMarqueeTint(const struct FLinearColor& Tint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "SetMarqueeTint");

	Params::WB_LinearProgress_Separated_C_SetMarqueeTint Parms{};

	Parms.Tint = std::move(Tint);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Percent_0                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::SetPercent(double Percent_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "SetPercent");

	Params::WB_LinearProgress_Separated_C_SetPercent Parms{};

	Parms.Percent_0 = Percent_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetUseMarquee
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bSetUseMarquee                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::SetUseMarquee(bool bSetUseMarquee)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "SetUseMarquee");

	Params::WB_LinearProgress_Separated_C_SetUseMarquee Parms{};

	Parms.bSetUseMarquee = bSetUseMarquee;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.UpdateProgressChangeColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              NewColor                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  InterpSpeed                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_LinearProgress_Separated_C::UpdateProgressChangeColor(const struct FLinearColor& NewColor, double InterpSpeed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_LinearProgress_Separated_C", "UpdateProgressChangeColor");

	Params::WB_LinearProgress_Separated_C_UpdateProgressChangeColor Parms{};

	Parms.NewColor = std::move(NewColor);
	Parms.InterpSpeed = InterpSpeed;

	UObject::ProcessEvent(Func, &Parms);
}

}

