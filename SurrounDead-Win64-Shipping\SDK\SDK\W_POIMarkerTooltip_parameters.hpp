﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_POIMarkerTooltip

#include "Basic.hpp"

#include "Engine_structs.hpp"


namespace SDK::Params
{

// Function W_POIMarkerTooltip.W_POIMarkerTooltip_C.ExecuteUbergraph_W_POIMarkerTooltip
// 0x00F0 (0x00F0 - 0x0000)
struct W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0008(0x0018)()
	int64                                         CallFunc_Conv_IntToInt64_ReturnValue;              // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0028(0x0050)(HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_IntInt_ReturnValue;              // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0080(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0090(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x00A8(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_2;          // 0x00C0(0x0018)()
	class FText                                   CallFunc_TextToUpper_ReturnValue;                  // 0x00D8(0x0018)()
};
static_assert(alignof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip) == 0x000008, "Wrong alignment on W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip");
static_assert(sizeof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip) == 0x0000F0, "Wrong size on W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, EntryPoint) == 0x000000, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, CallFunc_Conv_StringToText_ReturnValue) == 0x000008, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, CallFunc_Conv_IntToInt64_ReturnValue) == 0x000020, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::CallFunc_Conv_IntToInt64_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, K2Node_MakeStruct_FormatArgumentData) == 0x000028, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, CallFunc_NotEqual_IntInt_ReturnValue) == 0x000078, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::CallFunc_NotEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, K2Node_MakeArray_Array) == 0x000080, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, CallFunc_Format_ReturnValue) == 0x000090, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, CallFunc_Conv_StringToText_ReturnValue_1) == 0x0000A8, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, CallFunc_Conv_StringToText_ReturnValue_2) == 0x0000C0, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::CallFunc_Conv_StringToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip, CallFunc_TextToUpper_ReturnValue) == 0x0000D8, "Member 'W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip::CallFunc_TextToUpper_ReturnValue' has a wrong offset!");

}

