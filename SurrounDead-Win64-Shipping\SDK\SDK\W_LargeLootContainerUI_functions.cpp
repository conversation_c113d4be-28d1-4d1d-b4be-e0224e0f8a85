﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_LargeLootContainerUI

#include "Basic.hpp"

#include "W_LargeLootContainerUI_classes.hpp"
#include "W_LargeLootContainerUI_parameters.hpp"


namespace SDK
{

// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.BndEvt__LargeContainer_Widget_Button_66_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UW_LargeLootContainerUI_C::BndEvt__LargeContainer_Widget_Button_66_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "BndEvt__LargeContainer_Widget_Button_66_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.ExecuteUbergraph_W_LargeLootContainerUI
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::ExecuteUbergraph_W_LargeLootContainerUI(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "ExecuteUbergraph_W_LargeLootContainerUI");

	Params::W_LargeLootContainerUI_C_ExecuteUbergraph_W_LargeLootContainerUI Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.ForceInitSpecialcontainer
// (Public, BlueprintCallable, BlueprintEvent)

void UW_LargeLootContainerUI_C::ForceInitSpecialcontainer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "ForceInitSpecialcontainer");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.GetAllAttachments
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class FName>*                    Attachments                                            (Parm, OutParm)

void UW_LargeLootContainerUI_C::GetAllAttachments(TArray<class FName>* Attachments)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "GetAllAttachments");

	Params::W_LargeLootContainerUI_C_GetAllAttachments Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Attachments != nullptr)
		*Attachments = std::move(Parms.Attachments);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.GetContainerByAttachmentType
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGameplayTag&              Type                                                   (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
// class UJSIContainer_C**                 JigContainer                                           (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// int32*                                  ContainerIndex                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::GetContainerByAttachmentType(const struct FGameplayTag& Type, class UJSIContainer_C** JigContainer, int32* ContainerIndex)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "GetContainerByAttachmentType");

	Params::W_LargeLootContainerUI_C_GetContainerByAttachmentType Parms{};

	Parms.Type = std::move(Type);

	UObject::ProcessEvent(Func, &Parms);

	if (JigContainer != nullptr)
		*JigContainer = Parms.JigContainer;

	if (ContainerIndex != nullptr)
		*ContainerIndex = Parms.ContainerIndex;
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.GetDropWidget
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UDropItemBackGwidget_C**          DropWRef                                               (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::GetDropWidget(class UDropItemBackGwidget_C** DropWRef)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "GetDropWidget");

	Params::W_LargeLootContainerUI_C_GetDropWidget Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (DropWRef != nullptr)
		*DropWRef = Parms.DropWRef;
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.GetJSIContainerByPlayerSlots
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGameplayTag&              Slot_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
// class UJSIContainer_C**                 Container                                              (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSI_Slot_C**                     EquippedItem                                           (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// bool*                                   IsPending_                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::GetJSIContainerByPlayerSlots(const struct FGameplayTag& Slot_0, class UJSIContainer_C** Container, class UJSI_Slot_C** EquippedItem, bool* IsPending_)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "GetJSIContainerByPlayerSlots");

	Params::W_LargeLootContainerUI_C_GetJSIContainerByPlayerSlots Parms{};

	Parms.Slot_0 = std::move(Slot_0);

	UObject::ProcessEvent(Func, &Parms);

	if (Container != nullptr)
		*Container = Parms.Container;

	if (EquippedItem != nullptr)
		*EquippedItem = Parms.EquippedItem;

	if (IsPending_ != nullptr)
		*IsPending_ = Parms.IsPending_;
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.GetListOfNonAddContainers
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class UJSIContainer_C*>*         Containers                                             (Parm, OutParm, ContainsInstancedReference)

void UW_LargeLootContainerUI_C::GetListOfNonAddContainers(TArray<class UJSIContainer_C*>* Containers)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "GetListOfNonAddContainers");

	Params::W_LargeLootContainerUI_C_GetListOfNonAddContainers Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Containers != nullptr)
		*Containers = std::move(Parms.Containers);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.GetLootContent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUserWidget**                     Widget                                                 (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::GetLootContent(class UUserWidget** Widget)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "GetLootContent");

	Params::W_LargeLootContainerUI_C_GetLootContent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Widget != nullptr)
		*Widget = Parms.Widget;
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.GetValidReloadContainers
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class UJSIContainer_C*>*         Containers                                             (Parm, OutParm, ContainsInstancedReference)

void UW_LargeLootContainerUI_C::GetValidReloadContainers(TArray<class UJSIContainer_C*>* Containers)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "GetValidReloadContainers");

	Params::W_LargeLootContainerUI_C_GetValidReloadContainers Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Containers != nullptr)
		*Containers = std::move(Parms.Containers);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.JigSetLootContent
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUserWidget*                      Widget                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// const class FText&                      Name_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm)

void UW_LargeLootContainerUI_C::JigSetLootContent(class UUserWidget* Widget, const class FText& Name_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "JigSetLootContent");

	Params::W_LargeLootContainerUI_C_JigSetLootContent Parms{};

	Parms.Widget = Widget;
	Parms.Name_0 = std::move(Name_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.JSICheckStatus
// (Public, BlueprintCallable, BlueprintEvent)

void UW_LargeLootContainerUI_C::JSICheckStatus()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "JSICheckStatus");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.JSIOnWeightUpdated
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  NewWeight                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::JSIOnWeightUpdated(double NewWeight)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "JSIOnWeightUpdated");

	Params::W_LargeLootContainerUI_C_JSIOnWeightUpdated Parms{};

	Parms.NewWeight = NewWeight;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.OnCreatedFromUtility
// (Public, BlueprintCallable, BlueprintEvent)

void UW_LargeLootContainerUI_C::OnCreatedFromUtility()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "OnCreatedFromUtility");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.OnInitialized
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_LargeLootContainerUI_C::OnInitialized()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "OnInitialized");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.SetActionbarFollower
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSI_Slot_C*                      JigRef                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// bool*                                   Return                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::SetActionbarFollower(class UJSI_Slot_C* JigRef, bool* Return)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "SetActionbarFollower");

	Params::W_LargeLootContainerUI_C_SetActionbarFollower Parms{};

	Parms.JigRef = JigRef;

	UObject::ProcessEvent(Func, &Parms);

	if (Return != nullptr)
		*Return = Parms.Return;
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.SetActorOwner
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AActor*                           ActorRef                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::SetActorOwner(class AActor* ActorRef)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "SetActorOwner");

	Params::W_LargeLootContainerUI_C_SetActorOwner Parms{};

	Parms.ActorRef = ActorRef;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.SetInspectorRef
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UBP_InspectorWindowWidget_C*      Inspector                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::SetInspectorRef(class UBP_InspectorWindowWidget_C* Inspector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "SetInspectorRef");

	Params::W_LargeLootContainerUI_C_SetInspectorRef Parms{};

	Parms.Inspector = Inspector;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.SetItemReference
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSI_Slot_C*                      ItemRef                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::SetItemReference(class UJSI_Slot_C* ItemRef)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "SetItemReference");

	Params::W_LargeLootContainerUI_C_SetItemReference Parms{};

	Parms.ItemRef = ItemRef;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_LargeLootContainerUI_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "Tick");

	Params::W_LargeLootContainerUI_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_LargeLootContainerUI.W_LargeLootContainerUI_C.GetListOfContainers
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, Const)
// Parameters:
// TArray<class UJSIContainer_C*>*         Containers                                             (Parm, OutParm, ContainsInstancedReference)

void UW_LargeLootContainerUI_C::GetListOfContainers(TArray<class UJSIContainer_C*>* Containers) const
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_LargeLootContainerUI_C", "GetListOfContainers");

	Params::W_LargeLootContainerUI_C_GetListOfContainers Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Containers != nullptr)
		*Containers = std::move(Parms.Containers);
}

}

