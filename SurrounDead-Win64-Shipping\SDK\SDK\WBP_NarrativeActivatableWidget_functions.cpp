﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeActivatableWidget

#include "Basic.hpp"

#include "WBP_NarrativeActivatableWidget_classes.hpp"
#include "WBP_NarrativeActivatableWidget_parameters.hpp"


namespace SDK
{

// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.BP_OnActivated
// (Event, Protected, BlueprintEvent)

void UWBP_NarrativeActivatableWidget_C::BP_OnActivated()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeActivatableWidget_C", "BP_OnActivated");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.BP_OnDeactivated
// (Event, Protected, BlueprintEvent)

void UWBP_NarrativeActivatableWidget_C::BP_OnDeactivated()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeActivatableWidget_C", "BP_OnDeactivated");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.BP_OnHandleBackAction
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWBP_NarrativeActivatableWidget_C::BP_OnHandleBackAction()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeActivatableWidget_C", "BP_OnHandleBackAction");

	Params::WBP_NarrativeActivatableWidget_C_BP_OnHandleBackAction Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWBP_NarrativeActivatableWidget_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeActivatableWidget_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.ExecuteUbergraph_WBP_NarrativeActivatableWidget
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_NarrativeActivatableWidget_C::ExecuteUbergraph_WBP_NarrativeActivatableWidget(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeActivatableWidget_C", "ExecuteUbergraph_WBP_NarrativeActivatableWidget");

	Params::WBP_NarrativeActivatableWidget_C_ExecuteUbergraph_WBP_NarrativeActivatableWidget Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.HandleFocus
// (Public, BlueprintCallable, BlueprintEvent)

void UWBP_NarrativeActivatableWidget_C::HandleFocus()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeActivatableWidget_C", "HandleFocus");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.RegisterActions
// (Public, BlueprintCallable, BlueprintEvent)

void UWBP_NarrativeActivatableWidget_C::RegisterActions()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeActivatableWidget_C", "RegisterActions");

	UObject::ProcessEvent(Func, nullptr);
}

}

