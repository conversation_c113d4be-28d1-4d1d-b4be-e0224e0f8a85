@echo off
echo Installing Call of Duty Controller Mod for SurrounDead...
echo.

REM Get the game directory
set GAME_DIR=@CMAKE_INSTALL_PREFIX@
if "%GAME_DIR%"=="@CMAKE_INSTALL_PREFIX@" (
    set GAME_DIR=C:\Program Files (x86)\Steam\steamapps\common\SurrounDead
)

echo Game directory: %GAME_DIR%
echo.

REM Check if game directory exists
if not exist "%GAME_DIR%\SurrounDead.exe" (
    echo ERROR: SurrounDead.exe not found in %GAME_DIR%
    echo Please make sure the game is installed correctly.
    pause
    exit /b 1
)

REM Create backup directory
if not exist "%GAME_DIR%\Backups" mkdir "%GAME_DIR%\Backups"

REM Copy the mod DLL
echo Copying mod files...
copy /Y "bin\COD_Controller_Mod.dll" "%GAME_DIR%\"
if errorlevel 1 (
    echo ERROR: Failed to copy mod DLL
    pause
    exit /b 1
)

REM Create mod configuration directory
if not exist "%GAME_DIR%\COD_Controller_Config" mkdir "%GAME_DIR%\COD_Controller_Config"

REM Copy configuration files
echo Creating configuration files...
echo # Call of Duty Controller Mod Settings > "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo [Controller] >> "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo LookSensitivity=2.5 >> "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo MoveSensitivity=1.0 >> "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo ADSSensitivityMultiplier=0.6 >> "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo Deadzone=0.25 >> "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo TriggerDeadzone=0.1 >> "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo InvertY=false >> "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo VibrationEnabled=true >> "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo AimAssistEnabled=true >> "%GAME_DIR%\COD_Controller_Config\settings.ini"
echo AimAssistStrength=0.6 >> "%GAME_DIR%\COD_Controller_Config\settings.ini"

REM Create DLL injection script
echo Creating launcher script...
echo @echo off > "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo echo Starting SurrounDead with Call of Duty Controller Mod... >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo echo. >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo echo Make sure your Xbox controller is connected! >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo echo. >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo pause >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo. >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo REM Inject the DLL and start the game >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo start "" "SurrounDead.exe" >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo. >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo REM Wait a moment then inject the DLL >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo timeout /t 5 /nobreak ^>nul >> "%GAME_DIR%\Launch_With_COD_Controls.bat"
echo rundll32.exe COD_Controller_Mod.dll,DllMain >> "%GAME_DIR%\Launch_With_COD_Controls.bat"

REM Create uninstaller
echo Creating uninstaller...
echo @echo off > "%GAME_DIR%\Uninstall_COD_Controller_Mod.bat"
echo echo Uninstalling Call of Duty Controller Mod... >> "%GAME_DIR%\Uninstall_COD_Controller_Mod.bat"
echo echo. >> "%GAME_DIR%\Uninstall_COD_Controller_Mod.bat"
echo del /Q "COD_Controller_Mod.dll" 2^>nul >> "%GAME_DIR%\Uninstall_COD_Controller_Mod.bat"
echo del /Q "Launch_With_COD_Controls.bat" 2^>nul >> "%GAME_DIR%\Uninstall_COD_Controller_Mod.bat"
echo rmdir /S /Q "COD_Controller_Config" 2^>nul >> "%GAME_DIR%\Uninstall_COD_Controller_Mod.bat"
echo echo Mod uninstalled successfully! >> "%GAME_DIR%\Uninstall_COD_Controller_Mod.bat"
echo pause >> "%GAME_DIR%\Uninstall_COD_Controller_Mod.bat"
echo del "%%~f0" >> "%GAME_DIR%\Uninstall_COD_Controller_Mod.bat"

echo.
echo ========================================
echo  INSTALLATION COMPLETE!
echo ========================================
echo.
echo The Call of Duty Controller Mod has been installed successfully!
echo.
echo TO USE THE MOD:
echo 1. Connect your Xbox controller to your PC
echo 2. Run "Launch_With_COD_Controls.bat" in the game directory
echo 3. The game will start with COD-style controller support
echo.
echo CONTROLS:
echo   Left Stick: Movement
echo   Right Stick: Camera/Look
echo   A Button: Jump
echo   B Button: Reload
echo   X Button: Use/Interact
echo   Y Button: Switch Weapon
echo   LT: Aim Down Sights
echo   RT: Fire
echo   LB: Grenade
echo   RB: Tactical
echo   LS Click: Sprint
echo   RS Click: Melee
echo   D-Pad: Various functions
echo.
echo CONFIGURATION:
echo - Edit settings in: COD_Controller_Config\settings.ini
echo - Adjust sensitivity, deadzone, aim assist, etc.
echo.
echo TO UNINSTALL:
echo - Run "Uninstall_COD_Controller_Mod.bat"
echo.
echo Enjoy your Call of Duty-style controls in SurrounDead!
echo.
pause
