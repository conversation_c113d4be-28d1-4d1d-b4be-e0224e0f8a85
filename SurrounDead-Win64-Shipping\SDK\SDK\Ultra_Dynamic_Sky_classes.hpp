﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Ultra_Dynamic_Sky

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "Engine_classes.hpp"
#include "UDS_RenderingFeatureLevel_structs.hpp"
#include "UDS_ColorMode_structs.hpp"
#include "UDS_SkyLightMode_structs.hpp"
#include "RadialStorm_CoverageBrush_structs.hpp"
#include "UDS_SkyMode_structs.hpp"
#include "UDS_Project_Mode_structs.hpp"
#include "UDS_VolRT_Mode_structs.hpp"
#include "UDS_CityPresets_structs.hpp"
#include "UDS_Modifier_Color_Property_structs.hpp"
#include "UDS_PropertyType_structs.hpp"
#include "UDS_LensFlareType_structs.hpp"
#include "UDS_FogColorMode_structs.hpp"
#include "UDS_Post_Process_Stage_structs.hpp"
#include "UDS_Space_Planet_structs.hpp"
#include "UDS_CachedProperties_structs.hpp"
#include "UDS_RunContext_structs.hpp"
#include "UDS_Modifier_Float_Property_structs.hpp"
#include "UDS_Cache_Group_structs.hpp"
#include "UDS_Planet_Lightsource_structs.hpp"
#include "UDS_Space_Parent_structs.hpp"


namespace SDK
{

// BlueprintGeneratedClass Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C
// 0x2728 (0x29C0 - 0x0298)
class AUltra_Dynamic_Sky_C final : public AActor
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0298(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UPostProcessComponent*                  PostProcess;                                       // 0x02A0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UBillboardComponent*                    Sun_Icon;                                          // 0x02A8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UBillboardComponent*                    Moon_Icon;                                         // 0x02B0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class USkyLightComponent*                     Captured_Scene_Sky_Light;                          // 0x02B8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UBillboardComponent*                    Root;                                              // 0x02C0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UVolumetricCloudComponent*              VolumetricCloud;                                   // 0x02C8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class USkyLightComponent*                     Cubemap_Sky_Light;                                 // 0x02D0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UExponentialHeightFogComponent*         HeightFog;                                         // 0x02D8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UDirectionalLightComponent*             Moon;                                              // 0x02E0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UDirectionalLightComponent*             Sun;                                               // 0x02E8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class USkyAtmosphereComponent*                SkyAtmosphere;                                     // 0x02F0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UStaticMeshComponent*                   Sky_Sphere_Mesh;                                   // 0x02F8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Sky_Sphere_MID;                                    // 0x0300(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_speed;                                       // 0x0308(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Wisps_Opacity__Clear_;                       // 0x0310(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Refresh_Settings;                                  // 0x0318(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_319[0x7];                                      // 0x0319(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Time_of_Day;                                       // 0x0320(0x0008)(Edit, BlueprintVisible, ZeroConstructor, SaveGame, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	EUDS_SkyMode                                  Sky_Mode;                                          // 0x0328(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_ColorMode                                Color_Mode;                                        // 0x0329(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_Project_Mode                             Project_Mode;                                      // 0x032A(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_RenderingFeatureLevel                    Feature_Level;                                     // 0x032B(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_32C[0x4];                                      // 0x032C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cloud_Coverage;                                    // 0x0330(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog;                                               // 0x0338(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Overall_Intensity;                                 // 0x0340(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Direction;                                   // 0x0348(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Phase;                                       // 0x0350(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Manually_Position_Moon_Target;                     // 0x0358(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Render_Moon;                                       // 0x0359(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_35A[0x6];                                      // 0x035A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Moon_Light_Intensity;                              // 0x0360(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	EComponentMobility                            Moon_Mobility;                                     // 0x0368(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_369[0x7];                                      // 0x0369(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Moon_Scale;                                        // 0x0370(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Phase;                                        // 0x0378(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Yaw;                                          // 0x0380(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Pitch;                                        // 0x0388(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Vertical_Offset;                              // 0x0390(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Orbit_Offset;                                 // 0x0398(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Moon_Casts_Shadows;                                // 0x03A0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Moon_Transmission;                                 // 0x03A1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3A2[0x2];                                      // 0x03A2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           Moon_Light_Color;                                  // 0x03A4(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3B4[0x4];                                      // 0x03B4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TSoftObjectPtr<class UTexture2D>              Moon_Texture;                                      // 0x03B8(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	struct FLinearColor                           Moon_Material_Color;                               // 0x03E0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Texture_Intensity__Night_;                    // 0x03F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Texture_Intensity__Day_;                      // 0x03F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Glow_Intensity;                               // 0x0400(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Render_Moon_Phases;                                // 0x0408(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_409[0x7];                                      // 0x0409(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Moon_Phase_Contrast;                               // 0x0410(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Moon_Phase_Changes_Over_Time;                      // 0x0418(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_419[0x7];                                      // 0x0419(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Dark_Side_Brightness;                              // 0x0420(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              Moon_Phase_Normal;                                 // 0x0428(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	bool                                          Manually_Position_Sun_Target;                      // 0x0450(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_451[0x7];                                      // 0x0451(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Sun_Light_Intensity;                               // 0x0458(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	EComponentMobility                            Sun_Mobility;                                      // 0x0460(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_461[0x7];                                      // 0x0461(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Sun_Yaw;                                           // 0x0468(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Contrast;                                          // 0x0470(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sun_Pitch;                                         // 0x0478(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Sun_Vertical_Offset;                               // 0x0480(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Sun_Casts_Shadows;                                 // 0x0488(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Sun_Transmission;                                  // 0x0489(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_48A[0x2];                                      // 0x048A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           Sun_Light_Color;                                   // 0x048C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_49C[0x4];                                      // 0x049C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Soften_Cloud_Layer_1;                              // 0x04A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Soften_Cloud_Layer_2;                              // 0x04A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sharpen_Outer_Edge;                                // 0x04B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Stars_Intensity;                                   // 0x04B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Stars_Color;                                       // 0x04C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Sun_Scale;                                         // 0x04D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Cloud_Shadows_MID;                                 // 0x04D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          Use_Cloud_Shadows;                                 // 0x04E0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4E1[0x7];                                      // 0x04E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cloud_Shadows_Intensity_when_Sunny;                // 0x04E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Stars_Speed;                                       // 0x04F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        New_Moon_Light_Brightness;                         // 0x04F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Source_Angle_Scale;                           // 0x0500(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Texture_Rotation;                             // 0x0508(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	EUDS_SkyLightMode                             Sky_Light_Mode;                                    // 0x0510(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Use_Auroras;                                       // 0x0511(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_512[0x6];                                      // 0x0512(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Aurora_Intensity;                                  // 0x0518(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Aurora_Speed;                                      // 0x0520(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Shadows_Intensity_when_Overcast;             // 0x0528(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Shadows_Softness_When_Sunny;                 // 0x0530(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Shadows_Softness_when_Overcast;              // 0x0538(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Tiling;                                      // 0x0540(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Soften_Horizon;                                    // 0x0548(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          One_Cloud_Layer;                                   // 0x0550(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_551[0x7];                                      // 0x0551(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cloud_Height;                                      // 0x0558(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Overcast_Swirling_Texture;                         // 0x0560(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Saturation;                                        // 0x0568(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Directional_Balance;                               // 0x0570(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Dawn_Time;                                         // 0x0578(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dusk_Time;                                         // 0x0580(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Night_Brightness;                                  // 0x0588(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Sun_Softness;                                      // 0x0590(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Aurora_Phase;                                      // 0x0598(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UDirectionalLightComponent*             Sun_LightComponent;                                // 0x05A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UDirectionalLightComponent*             Moon_LightComponent;                               // 0x05A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class USkyLightComponent*                     SkyLightComponent;                                 // 0x05B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UExponentialHeightFogComponent*         Height_Fog_Component;                              // 0x05B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Apply_Exposure_Settings;                           // 0x05C0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EAutoExposureMethod                           Exposure_Metering_Mode;                            // 0x05C1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5C2[0x6];                                      // 0x05C2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UCurveFloat*                            Exposure_Compensation_Curve;                       // 0x05C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          Animate_Time_of_Day;                               // 0x05D0(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5D1[0x7];                                      // 0x05D1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Day_Length;                                        // 0x05D8(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Night_Length;                                      // 0x05E0(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Extend_Dawn_And_Dusk;                              // 0x05E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Base_Fog_Density;                                  // 0x05F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Foggy_Density_Contribution;                        // 0x05F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cloudy_Density_Contribution;                       // 0x0600(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Density_Contribution;                         // 0x0608(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Stars_Tiling;                                      // 0x0610(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Stars_Daytime_Intensity;                           // 0x0618(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Twinkle_Amount;                                    // 0x0620(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Twinkle_Speed;                                     // 0x0628(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              Tiling_Stars_Texture;                              // 0x0630(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              Real_Stars_Texture;                                // 0x0658(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	double                                        Stars_Phase;                                       // 0x0680(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Sky_Light_Intensity;                               // 0x0688(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	EComponentMobility                            Sky_Light_Mobility;                                // 0x0690(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_691[0x7];                                      // 0x0691(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Sky_Light_Temperature;                             // 0x0698(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Sky_Light_Color_Multiplier__Day_;                  // 0x06A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dusk_;                                             // 0x06B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Sky_Light_Color_Multiplier__Night_;                // 0x06C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Real_Time_Capture;                                 // 0x06D0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Move_Sky_Light_Capture_with_Camera_Location;       // 0x06D1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6D2[0x2];                                      // 0x06D2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           Sky_Light_Lower_Hemisphere_Tint__Capture_Based_;   // 0x06D4(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Sky_Light_Lower_Hemisphere_Tint__Cubemap_;         // 0x06E4(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6F4[0x4];                                      // 0x06F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TSoftObjectPtr<class UTextureCube>            Sky_Light_Cubemap;                                 // 0x06F8(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	double                                        Sky_Light_Cubemap_Angle;                           // 0x0720(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Render_Sky_Light;                                  // 0x0728(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_729[0x7];                                      // 0x0729(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ASkyLight*                              Custom_Sky_Light_Actor;                            // 0x0730(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Real_Time_Capture_Uses_Time_Slicing;               // 0x0738(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Stationary_Sky_Light_Casts_Shadows;                // 0x0739(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Movable_Sky_Light_Casts_Shadows__Enable_DFAO_;     // 0x073A(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Recapture_Sky_Light_Periodically;                  // 0x073B(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_73C[0x4];                                      // 0x073C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Sky_Light_Recapture_Period__Seconds_;              // 0x0740(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Random_Starting_Time;                              // 0x0748(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_749[0x7];                                      // 0x0749(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Sunrise_Event_Offset;                              // 0x0750(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sunset_Event_Offset;                               // 0x0758(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              Sunset;                                            // 0x0760(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              Sunrise;                                           // 0x0770(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	double                                        Cloud_Wisps_Opacity__Cloudy_;                      // 0x0780(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Cloud_Wisps_Texture;                               // 0x0788(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              TwoD_Dynamic_Cloud_Formation_Texture;              // 0x0790(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	double                                        Sun_Shader_Intensity;                              // 0x07B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Directional_Lighting_Intensity;                    // 0x07C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Sun_Highlight_Radius_Scale;                        // 0x07C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Cloud_opacity;                                     // 0x07D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Horizon_Density_Multiplier;                        // 0x07D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Zenith_Density_Multiplier;                         // 0x07E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Latitude_Gradient_Position;                        // 0x07E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Latitude_Gradient_Width;                           // 0x07F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Around_Sun_Density_Multiplier;                     // 0x07F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Around_Sun_Density_Exponent;                       // 0x0800(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Around_Moon_Density_Multiplier;                    // 0x0808(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Around_Moon_Density_Exponent;                      // 0x0810(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        TwoD_Clouds_Shading_Offset;                        // 0x0818(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UCurveFloat*                            Shine_Intensity_Curve;                             // 0x0820(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UCurveFloat*                            Sun_Highlight_Intensity_Curve;                     // 0x0828(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UCurveFloat*                            Sun_Highlight_Radius_Curve;                        // 0x0830(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Adjust_for_Path_Tracer;                            // 0x0838(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_839[0x7];                                      // 0x0839(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Lights_Update_Period;                              // 0x0840(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Lights_Update_Degree_Threshold;                    // 0x0848(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Dim_Directional_Lights_with_Cloud_Coverage;        // 0x0850(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_851[0x7];                                      // 0x0851(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Extra_Night_Brightness_when_Cloudy;                // 0x0858(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Directional_Lights_Absent_Brightness;              // 0x0860(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Vol_Clouds_MID_Complex;                            // 0x0868(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	EUDS_VolRT_Mode                               Volumetric_Cloud_Rendering_Mode;                   // 0x0870(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_871[0x7];                                      // 0x0871(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Bottom_Altitude;                                   // 0x0878(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Volumetric_Clouds_Scale;                           // 0x0880(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Layer_Height_Scale;                                // 0x0888(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Formation_Texture_Scale;                     // 0x0890(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        ThreeD_Noise_Scale;                                // 0x0898(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        ThreeD_Erosion_Intensity;                          // 0x08A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        High_Frequency_Noise_Amount;                       // 0x08A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	int32                                         High_Frequency_Levels;                             // 0x08B0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8B4[0x4];                                      // 0x08B4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Distort_High_Frequency_Noise;                      // 0x08B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Extinction_Scale;                                  // 0x08C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Base_Floor_Variation;                              // 0x08C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Overcast_Floor_Variation;                          // 0x08D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Floor_Luminance_Scale;                             // 0x08D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        View_Sample_Scale__Day_;                           // 0x08E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        View_Sample_Scale__Night_;                         // 0x08E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Shadow_Sample_Scale;                               // 0x08F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Shadow_Tracing_Distance;                           // 0x08F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Tracing_Max_Distance__Inside_Cloud_Layer_;         // 0x0900(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Tracing_Max_Distance__Outside_Cloud_Layer_;        // 0x0908(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Volumetric_Cloud_Color;                            // 0x0910(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture>                Cloud_Profile_LUT;                                 // 0x0920(0x0028)(Edit, BlueprintVisible, AdvancedDisplay, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              Volumetric_Cloud_Formation_Texture;                // 0x0948(0x0028)(Edit, BlueprintVisible, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Minimum_Erosion;                                   // 0x0970(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Distance_to_Sample_MaxCount;                       // 0x0978(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Cloud_Formation_Texture_Mip_Level;                 // 0x0980(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Reflection_Sample_Count_Scale;                     // 0x0988(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Reflection_Shadow_Sample_Count_Scale;              // 0x0990(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Volumetric_Cloud_Ambient_Light_Intensity;          // 0x0998(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Volumetric_Cloud_Ambient_Light__Day_;              // 0x09A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dusk__0;                                           // 0x09B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Volumetric_Cloud_Ambient_Light__Night_;            // 0x09C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Volumetric_Cloud_Ambient_Light_Saturation;         // 0x09D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Multiscattering_Light_Intensity;                   // 0x09D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Multiscattering_Light_Occlusion;                   // 0x09E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Vol_Clouds_MID_Simple;                             // 0x09E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Scale_Fog_Density;                                 // 0x09F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Start_Distance_when_Clear;                     // 0x09F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Density_Where_Start_Distance_Reaches_Zero;     // 0x0A00(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	float                                         Formation_Change_Speed;                            // 0x0A08(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Randomize_Cloud_Formation_on_Run;                  // 0x0A0C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A0D[0x3];                                      // 0x0A0D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        ThreeD_Noise_Vertical_Movement;                    // 0x0A10(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Macro_Variation;                                   // 0x0A18(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Macro_Scale;                                       // 0x0A20(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Multiscattering_Phase_1;                           // 0x0A28(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Multiscattering_Phase_2;                           // 0x0A30(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Multiscattering_Eccentricity;                      // 0x0A38(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Time_Event_Dispatcher_Check_Period;                // 0x0A40(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Time_Speed;                                        // 0x0A48(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Time_Cycle_Degrees;                                // 0x0A50(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 Ultra_Dynamic_Weather;                             // 0x0A58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, Transient, NoDestructor, HasGetValueTypeHash)
	double                                        Tracing_Max_Start_Distance;                        // 0x0A60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Aurora_Shape_Change_Speed;                         // 0x0A68(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Aurora_Texture_Scale;                              // 0x0A70(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Daytime_Aurora_Intensity;                          // 0x0A78(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Aurora_Color_1;                                    // 0x0A80(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Aurora_Color_2;                                    // 0x0A90(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Aurora_Color_3;                                    // 0x0AA0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Volumetric_Aurora_MID;                             // 0x0AB0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          Half_Rate_Tick;                                    // 0x0AB8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AB9[0x3];                                      // 0x0AB9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Half_Rate_Tick_Framerate_Threshold;                // 0x0ABC(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Disable_All_Runtime_Updating;                      // 0x0AC0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Use_Sky_Mode_Scalability_Map;                      // 0x0AC1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AC2[0x6];                                      // 0x0AC2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TMap<int32, EUDS_SkyMode>                     Sky_Mode_Scalability_Map;                          // 0x0AC8(0x0050)(Edit, BlueprintVisible)
	double                                        Sun_Disk_Intensity;                                // 0x0B18(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Inside_Clouds_Fog_MID;                             // 0x0B20(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Fog_Inside_Clouds;                          // 0x0B28(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B29[0x7];                                      // 0x0B29(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TSoftObjectPtr<class UTexture2D>              Aurora_Texture;                                    // 0x0B30(0x0028)(Edit, BlueprintVisible, AdvancedDisplay, HasGetValueTypeHash)
	double                                        TwoD_Aurora_Max_Samples;                           // 0x0B58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        TwoD_Aurora_Sample_Step_Size;                      // 0x0B60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        TwoD_Aurora_Line_Exponent;                         // 0x0B68(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        TwoD_Aurora_Mask_Exponent;                         // 0x0B70(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Volumetric_Aurora_Sample_Count_Scale;              // 0x0B78(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Volumetric_Aurora_Layer_Height;                    // 0x0B80(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Volumetric_Aurora_Bottom_Altitude;                 // 0x0B88(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Density_Daytime_Mutliplier;                    // 0x0B90(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Density_Nighttime_Multiplier;                  // 0x0B98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Base_Height_Fog_Falloff;                           // 0x0BA0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cloudy_Height_Fog_Falloff;                         // 0x0BA8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Foggy_Height_Fog_Falloff;                          // 0x0BB0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Inside_Cloud_Fog_Strength;                         // 0x0BB8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Inside_Cloud_Fog_Color;                            // 0x0BC0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        TwoD_Overcast_Turbulence;                          // 0x0BD0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Close_Fade_Distance;                               // 0x0BD8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Close_Fade_Offset;                                 // 0x0BE0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Two_Layers;                                        // 0x0BE8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_BE9[0x7];                                      // 0x0BE9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Layer_2_Coverage_Scale;                            // 0x0BF0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Layer_2_Extinction_Scale;                          // 0x0BF8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Layer_2_Cloud_Scale;                               // 0x0C00(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Layer_2_Mip_Offset;                                // 0x0C10(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Distance_Between_Layers;                           // 0x0C18(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Sun_Light_Shaft_Bloom;                      // 0x0C20(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C21[0x7];                                      // 0x0C21(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              Sun_Light_Shaft_Bloom_Scale;                       // 0x0C28(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Sun_Light_Shaft_Max_Brightness;                    // 0x0C38(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Sun_Light_Shaft_Bloom_Threshold;                   // 0x0C48(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Sun_Light_Shaft_Tint_Color;                        // 0x0C58(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Moon_Light_Shaft_Bloom;                     // 0x0C68(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C69[0x7];                                      // 0x0C69(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              Moon_Light_Shaft_Bloom_Scale;                      // 0x0C70(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Moon_Light_Shaft_Max_Brightness;                   // 0x0C80(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Moon_Light_Shaft_Bloom_Threshold;                  // 0x0C90(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Moon_Light_Shaft_Tint_Color;                       // 0x0CA0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_SkyMode                                  Volumetric_Clouds_Replacement;                     // 0x0CB0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_SkyLightMode                             Real_Time_Capture_Sky_Light_Replacement;           // 0x0CB1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_SkyMode                                  Volumetric_Aurora_Replacement;                     // 0x0CB2(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_CB3[0x5];                                      // 0x0CB3(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	TMap<class FString, EUDS_RenderingFeatureLevel> Platform_Feature_Levels;                         // 0x0CB8(0x0050)(Edit, BlueprintVisible)
	bool                                          Simulate_Real_Sun;                                 // 0x0D08(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Simulate_Real_Moon;                                // 0x0D09(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Simulate_Real_Stars;                               // 0x0D0A(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_CityPresets                              Location_Preset;                                   // 0x0D0B(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D0C[0x4];                                      // 0x0D0C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Latitude;                                          // 0x0D10(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Longitude;                                         // 0x0D18(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Time_Zone;                                         // 0x0D20(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	int32                                         Year;                                              // 0x0D28(0x0004)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	int32                                         Month;                                             // 0x0D2C(0x0004)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	int32                                         Day;                                               // 0x0D30(0x0004)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D34[0x4];                                      // 0x0D34(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        North_Yaw;                                         // 0x0D38(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Simulation_Speed;                                  // 0x0D40(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Use_System_Time;                                   // 0x0D48(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Apply_Daylight_Savings_Time;                       // 0x0D49(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D4A[0x6];                                      // 0x0D4A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Calendar_C*                        Calendar;                                          // 0x0D50(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         DST_Start_Month;                                   // 0x0D58(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	int32                                         DST_Start_Day;                                     // 0x0D5C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	int32                                         DST_End_Month;                                     // 0x0D60(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	int32                                         DST_End_Day;                                       // 0x0D64(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	int32                                         DST_Change_Hour;                                   // 0x0D68(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_D6C[0x4];                                      // 0x0D6C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Stellar_Calibration;                               // 0x0D70(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Lunar_Orbit_Calibration;                           // 0x0D78(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Lunar_Inclination_Calibration;                     // 0x0D80(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Daylight_Savings_Time;                             // 0x0D88(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D89[0x3];                                      // 0x0D89(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Total_Days_Elapsed;                                // 0x0D8C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Clouds_Move_with_Time_of_Day;                      // 0x0D90(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D91[0x7];                                      // 0x0D91(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Time_of_Day_Movement_Multiplier;                   // 0x0D98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	TMap<class FString, TSoftObjectPtr<class UMaterialInstance>> Sky_MID_Parent_Instances;           // 0x0DA0(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	double                                        Fog_Shadows;                                       // 0x0DF0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Shadows;                                      // 0x0DF8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        TwoD_Cloud_Shadows_Speed;                          // 0x0E00(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        TwoD_Cloud_Shadows_Scale;                          // 0x0E08(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Disable_Directional_Light_Shadows_When_Fully_Shadowed_By_Clouds; // 0x0E10(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_E11[0x7];                                      // 0x0E11(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInterface*                     Custom_Light_Function_Material;                    // 0x0E18(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Force_Light_Functions_On;                          // 0x0E20(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_E21[0x7];                                      // 0x0E21(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Time_Of_Day_Replication_Period;                    // 0x0E28(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Replicated_Time_of_Day;                            // 0x0E30(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, RepNotify, NoDestructor, HasGetValueTypeHash)
	bool                                          Render_Moon_Directional_Light;                     // 0x0E38(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_E39[0x7];                                      // 0x0E39(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ADirectionalLight*                      Custom_Moon_Light_Actor;                           // 0x0E40(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Scale_Moon_Radius_As_It_Nears_Horizon;             // 0x0E48(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Fog_Colors_from_Sky_Atmosphere;                    // 0x0E50(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E51[0x7];                                      // 0x0E51(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Dusty_Height_Fog_Falloff;                          // 0x0E58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Specular_Scale;                               // 0x0E60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Ambient_Light_From_Sky_Atmosphere;                 // 0x0E68(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Volumetric_Cloud_Ambient_Light_Bottom_Scale;       // 0x0E70(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Volumetric_Cloud_Ambient_Light_Top_Scale;          // 0x0E78(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        High_Frequency_Disable_Distance;                   // 0x0E80(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Change_To_Simplified_Material_When_Overcast;       // 0x0E88(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_E89[0x7];                                      // 0x0E89(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Simplified_Material_Cloud_Coverage_Threshold;      // 0x0E90(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Simplified_Material_Fade_Length;                   // 0x0E98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Scale_View_Samples_when_Overcast;                  // 0x0EA0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	int32                                         Current_Volumetric_Clouds_MID;                     // 0x0EA8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_EAC[0x4];                                      // 0x0EAC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Clouds_Position;                                   // 0x0EB0(0x0018)(Edit, BlueprintVisible, Net, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Cloud_Phase_Vector_Multiplier;                     // 0x0EC8(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Rayleigh_Scattering_Color__Day_;                   // 0x0EE0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dusk__1;                                           // 0x0EF0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Rayleigh_Scattering_Color__Night_;                 // 0x0F00(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Desaturate_Rayleigh_Scattering_when_Cloudy;        // 0x0F10(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Current_Time_of_Day_Offset;                        // 0x0F18(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Last_Frame_Time_of_Day;                            // 0x0F20(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Time_of_Day_Change_Speed;                          // 0x0F28(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMap<class FName, EUDS_PropertyType>          Properties;                                        // 0x0F30(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	double                                        Night_Sky_Glow;                                    // 0x0F80(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Night_Sky_Glow_Color;                              // 0x0F88(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Light_Pollution_Intensity;                         // 0x0F98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Light_Pollution_Color;                             // 0x0FA0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Cloud_Fog_Post_Process_MID;                        // 0x0FB0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	struct FFloatRange                            Dimming_Range;                                     // 0x0FB8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dimming_Range_Exponent;                            // 0x0FC8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UMaterialInterface*                     Custom_Sky_Sphere_Material;                        // 0x0FD0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Fog_Snow_Contribution;                             // 0x0FD8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              Midnight;                                          // 0x0FE0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FLinearColor                           Sunrise_Color__Absorption_;                        // 0x0FF0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Sunrise_Color_Intensity__Absorption_Scale_;        // 0x1000(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Simulated_Sunrise_Time;                            // 0x1008(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Simulated_Sunset_Time;                             // 0x1010(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Base_Cloud_Height;                                 // 0x1018(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Sun_Lens_Flare;                             // 0x1020(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_LensFlareType                            Lens_Flare_Type;                                   // 0x1021(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1022[0x6];                                     // 0x1022(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Lens_Flare_Strength;                               // 0x1028(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Lens_Flare_Tint;                                   // 0x1030(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Lens_Flare_MID;                                    // 0x1040(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Lens_Flare_Brightness_Threshold;                   // 0x1048(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UMaterialInterface*                     Custom_Lens_Flare_Parent_Instance;                 // 0x1050(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Scale_Flare_Elements;                              // 0x1058(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dynamic_Sky_Light_Transition_Time;                 // 0x1060(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Target_Sky_Light_Multiplier;                       // 0x1068(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dynamic_Sky_Light_Multiplier;                      // 0x1070(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Coverage_0_3;                                // 0x1078(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Global_Overcast_0_1;                               // 0x1080(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Local_Overcast_0_1;                                // 0x1088(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Override_with_New_Changes;                         // 0x1090(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1091[0x7];                                     // 0x1091(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               Overcast_Turbulence_MID;                           // 0x1098(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Scale_View_Samples_when_Camera_is_In_Cloud_Layer;  // 0x10A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Low_Material_Quality_Sample_Count_Scale;           // 0x10A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Medium_Material_Quality_Sample_Count_Scale;        // 0x10B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Epic_Material_Quality_Sample_Count_Scale;          // 0x10B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	TSoftObjectPtr<class UVolumeTexture>          Epic_Quality_Volume_Texture;                       // 0x10C0(0x0028)(Edit, BlueprintVisible, AdvancedDisplay, HasGetValueTypeHash)
	TSoftObjectPtr<class UVolumeTexture>          High_Quality_Volume_Texture;                       // 0x10E8(0x0028)(Edit, BlueprintVisible, AdvancedDisplay, HasGetValueTypeHash)
	TSoftObjectPtr<class UVolumeTexture>          Low_Quality_Volume_Texture;                        // 0x1110(0x0028)(Edit, BlueprintVisible, AdvancedDisplay, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Inside_Cloud_Fog_Parent_Material;                  // 0x1138(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Post_Process_Cloud_Fog_Parent_Material;            // 0x1160(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TArray<class UMaterialInstanceDynamic*>       Volumetric_Cloud_MIDs;                             // 0x1188(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	double                                        Moon_Light_Current_Intensity_Value;                // 0x1198(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Twilight_Color__Absorption_;                       // 0x11A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Twilight_Color_Intensity__Absorption_Scale_;       // 0x11B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Base_Sky_Color__Day_;                              // 0x11B8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dusk__2;                                           // 0x11C8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Base_Sky_Color__Night_;                            // 0x11D8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Cloud_Light_Color__Day_;                           // 0x11E8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dusk__3;                                           // 0x11F8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Cloud_Light_Color__Night_;                         // 0x1208(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Cloud_Dark_Color__Day_;                            // 0x1218(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dusk__4;                                           // 0x1228(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Cloud_Dark_Color__Night_;                          // 0x1238(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Sun_Glow_Color;                                    // 0x1248(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Moon_Glow_Tint;                                    // 0x1258(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCurveFloat*                            Directional_Intensity_Curve;                       // 0x1268(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UCurveLinearColor*                      Directional_Light_Scattering_Curve;                // 0x1270(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Atmospheric_Scattering_LUT_Texture;                // 0x1278(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Render_Exponential_Height_Fog;                     // 0x1280(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1281[0x7];                                     // 0x1281(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AExponentialHeightFog*                  Custom_Height_Fog_Actor;                           // 0x1288(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	EUDS_FogColorMode                             Fog_Color_Mode;                                    // 0x1290(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_1291[0x7];                                     // 0x1291(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Fog_Color_Intensity_Scale;                         // 0x1298(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Overcast_Brightness_Day;                           // 0x12A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Overcast_Brightness_Night;                         // 0x12A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Sky_Atmosphere_Fog_Influence_Sun;                  // 0x12B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sky_Atmosphere_Fog_Influence_Moon;                 // 0x12B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sky_Atmosphere_Fog_Influence_None;                 // 0x12C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCurveLinearColor*                      Fog_Scattering_Color_Curve;                        // 0x12C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	struct FLinearColor                           Fog_Inscattering_Sun;                              // 0x12D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Fog_Inscattering_Moon;                             // 0x12E0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	class UCurveLinearColor*                      Fog_Directional_Scattering_Color_Curve;            // 0x12F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	struct FLinearColor                           Fog_Directional_Inscattering;                      // 0x12F8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Foggy_Desaturation;                                // 0x1308(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Overcast_Desaturation;                             // 0x1310(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           All_Fog_Colors_Multiplier;                         // 0x1318(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Amount;                                       // 0x1328(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dust_Color;                                        // 0x1330(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Use_Volumetric_Fog;                                // 0x1340(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1341[0x7];                                     // 0x1341(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Volumetric_Fog_Distance;                           // 0x1348(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Volumetric_Fog_Extinction;                         // 0x1350(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sun_Volumetric_Scattering_Intensity;               // 0x1358(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Volumetric_Scattering_Intensity;              // 0x1360(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Disable_Height_Fog_Above_Volumetric_Cloud_Layer;   // 0x1368(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1369[0x3];                                     // 0x1369(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         Cached_Height_Fog_Density;                         // 0x136C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Using_Cloud_Fog_Post_Process;                      // 0x1370(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1371[0x7];                                     // 0x1371(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Current_Camera_Location;                           // 0x1378(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Ambient_Fog_Color;                                 // 0x1390(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Directional_Base_Fog_Color;                        // 0x13A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Scaled_Directional_Base_Fog_Color;                 // 0x13B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Volumetric_Aurora_Parent_Material;                 // 0x13C0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	bool                                          Using_Inside_Cloud_Fog;                            // 0x13E8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13E9[0x7];                                     // 0x13E9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TSoftObjectPtr<class UMaterialInterface>      Overcast_Turbulence_Parent_Material;               // 0x13F0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	bool                                          Forward_Shading;                                   // 0x1418(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1419[0x7];                                     // 0x1419(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(int32 Hour)>    Hourly;                                            // 0x1420(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	int32                                         Event_Hour;                                        // 0x1430(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1434[0x4];                                     // 0x1434(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cloud_Wisps_Color_Intensity;                       // 0x1438(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Moon_Source_Angle_Scale;                       // 0x1440(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Transition_Time_A;                                 // 0x1448(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Transition_Time_B;                                 // 0x1450(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Transitioning_Time;                                // 0x1458(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEasingFunc                                   Transition_Easing_Function;                        // 0x1459(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_145A[0x6];                                     // 0x145A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Transition_Easing_Exponent;                        // 0x1460(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           Transition_Timer;                                  // 0x1468(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Transition_Duration;                               // 0x1470(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Transition_Days_Counted;                           // 0x1478(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Transition_Can_Go_Backwards;                       // 0x147C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_147D[0x3];                                     // 0x147D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Moon_Source_Angle_Softness;                        // 0x1480(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Exposure_Bias_Day;                                 // 0x1488(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Exposure_Bias_Night;                               // 0x1490(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Exposure_Bias_Cloudy;                              // 0x1498(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Exposure_Bias_Foggy;                               // 0x14A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Exposure_Bias_Dusty;                               // 0x14A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FFloatRange                            Exposure_Brightness_Range;                         // 0x14B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Apply_Interior_Adjustments;                        // 0x14C0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14C1[0x7];                                     // 0x14C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Extra_Fog_Start_Distance_In_Interior;              // 0x14C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Density_Multiplier_in_Interior;                // 0x14D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Exposure_Bias_in_Interior;                         // 0x14D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Sky_Light_Intensity_Multiplier_in_Interiors;       // 0x14E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Sun_Light_Intensity_Multiplier_In_Interiors;       // 0x14E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Moon_Light_Intensity_Multiplier_in_Interiors;      // 0x14F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Extra_Sun_Volumetric_Scattering_In_Interiors;      // 0x14F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Extra_Moon_Volumetric_Scattering_In_Interiors;     // 0x1500(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cached_Inverted_Global_Occlusion;                  // 0x1508(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUDS_OcclusionSettings_C*               Occlusion_Settings;                                // 0x1510(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Shadow_Disable_Threshold;                    // 0x1518(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Bottom_World_Height;                         // 0x1520(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cached_Night_Filter;                               // 0x1528(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<double>                                Cached_Floats_Old;                                 // 0x1530(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<double>                                Cached_Floats_New;                                 // 0x1540(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	double                                        Cache_Alpha;                                       // 0x1550(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Filling_Starting_Cache;                            // 0x1558(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1559[0x7];                                     // 0x1559(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FLinearColor>                   Cached_Colors_Old;                                 // 0x1560(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<struct FLinearColor>                   Cached_Colors_New;                                 // 0x1570(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<struct FVector>                        Cached_Vectors_Old;                                // 0x1580(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<struct FVector>                        Cached_Vectors_New;                                // 0x1590(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FVector                                Cached_Sun_Vector;                                 // 0x15A0(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Cached_Moon_Vector;                                // 0x15B8(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<bool>                                  Cached_Value_Changing;                             // 0x15D0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	double                                        Max_Property_Cache_Period;                         // 0x15E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMap<EUDS_CityPresets, struct FVector>        Preset_Location_Coordinates;                       // 0x15E8(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          Disable_Image_Based_Lens_Flares_when_Enabled;      // 0x1638(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_1639[0x7];                                     // 0x1639(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Starting_Sky_Atmosphere_Height;                    // 0x1640(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FUDS_Post_Process_Stage>        Post_Process_Components;                           // 0x1648(0x0010)(Edit, BlueprintVisible)
	uint8                                         Pad_1658[0x8];                                     // 0x1658(0x0008)(Fixing Size After Last Property [ Dumper-7 ])
	struct FPostProcessSettings                   Static_Post_Process_Settings;                      // 0x1660(0x06E0)(Edit, BlueprintVisible)
	TArray<class UPostProcessComponent*>          User_Post_Process_Components;                      // 0x1D40(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	bool                                          Using_Post_Process_Components;                     // 0x1D50(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D51[0x3];                                     // 0x1D51(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           Cloud_Wisps_Tint__Day_;                            // 0x1D54(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dusk__5;                                           // 0x1D64(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Cloud_Wisps_Tint__Night_;                          // 0x1D74(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D84[0x4];                                     // 0x1D84(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Increase_Wisps_Brightness_Around_Sun;              // 0x1D88(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Increase_Wisps_Brightness_Around_Moon;             // 0x1D90(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Sun_Source_Angle_Scale;                            // 0x1D98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Solar_Eclipse;                                     // 0x1DA0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1DA1[0x3];                                     // 0x1DA1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           Solar_Eclipse_Tint_Color;                          // 0x1DA4(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1DB4[0x4];                                     // 0x1DB4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Solar_Eclipse_Intensity_Multiplier;                // 0x1DB8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sun_Specular_Scale;                                // 0x1DC0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Render_Sun_Directional_Light;                      // 0x1DC8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_1DC9[0x7];                                     // 0x1DC9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ADirectionalLight*                      Custom_Sun_Light_Actor;                            // 0x1DD0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Max_Sun_Source_Angle_Scale;                        // 0x1DD8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Sun_Source_Angle_Softness;                         // 0x1DE0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Scale_Sun_Radius_As_It_Nears_Horizon;              // 0x1DE8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Fade_Down_High_Sun_Light_Intensity_Below_Horizon;  // 0x1DF0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Support_Sky_Atmo_Affecting_Height_Fog;             // 0x1DF1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1DF2[0x6];                                     // 0x1DF2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 UDS_Version;                                       // 0x1DF8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, HasGetValueTypeHash)
	class UUDS_VersionInfo_C*                     UDS_Version_Info;                                  // 0x1E08(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Clouds_B_Time;                                     // 0x1E10(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sky_Atmosphere_Overcast_Luminance;                 // 0x1E18(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Keep_Planet_Top_At_Camera_XY_Location;             // 0x1E20(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Control_Sky_Atmosphere_Settings;                   // 0x1E21(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_1E22[0x6];                                     // 0x1E22(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Old_Composite_Weather;                             // 0x1E28(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Old_Composite_Context;                             // 0x1E40(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Composite_Weather_Change_Speed;                    // 0x1E58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Composite_Context_Change_Speed;                    // 0x1E60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Old_Moon_Target;                                   // 0x1E68(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Old_Sun_Target;                                    // 0x1E80(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cache_Current_Timer;                               // 0x1E98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Last_Low_Frequency_Update_Location;                // 0x1EA0(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUDS_Planet_Preset_C*                   Moon_Preset;                                       // 0x1EB8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TArray<struct FUDS_Space_Planet>              Moons;                                             // 0x1EC0(0x0010)(Edit, BlueprintVisible)
	double                                        Space_Layer_Brightness__Night_;                    // 0x1ED0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Space_Layer_Brightness__Day_;                      // 0x1ED8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Space_Glow_Brightness;                             // 0x1EE0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class UMaterialInstanceDynamic*>       Space_Planet_MIDs;                                 // 0x1EE8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<class USceneComponent*>                Space_Scene_Components;                            // 0x1EF8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	TArray<class UMaterialInstanceDynamic*>       Space_Ring_MIDs;                                   // 0x1F08(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<class UMaterialInstanceDynamic*>       Space_Glow_MIDs;                                   // 0x1F18(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<class UStaticMeshComponent*>           Space_Planet_Components;                           // 0x1F28(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	bool                                          Space_Layer_Active;                                // 0x1F38(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1F39[0x3];                                     // 0x1F39(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           Cached_Solar_Eclipse_Tint;                         // 0x1F3C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1F4C[0x4];                                     // 0x1F4C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cached_Sun_Scale;                                  // 0x1F50(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cached_Moon_Scale;                                 // 0x1F58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Sun___Lighting_Channel_0;                          // 0x1F60(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Sun___Lighting_Channel_1;                          // 0x1F61(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Sun___Lighting_Channel_2;                          // 0x1F62(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Moon___Lighting_Channel_0;                         // 0x1F63(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Moon___Lighting_Channel_1;                         // 0x1F64(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Moon___Lighting_Channel_2;                         // 0x1F65(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_1F66[0x2];                                     // 0x1F66(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class USceneComponent*>                Space_Roots;                                       // 0x1F68(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	double                                        Water_Level;                                       // 0x1F78(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Render_Water_Caustics;                             // 0x1F80(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1F81[0x7];                                     // 0x1F81(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Caustics_Intensity;                                // 0x1F88(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Caustics_Falloff;                                  // 0x1F90(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Deep_Water_Falloff;                                // 0x1F98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Caustics_Texture_Scale;                            // 0x1FA0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Moons_Cloud_Mask;                                  // 0x1FA8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Blur_Caustics_With_Depth;                          // 0x1FB0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Blur_Caustics_In_Cloud_Shadow;                     // 0x1FB8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Blur_Caustics_With_Camera_Distance;                // 0x1FC0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	int32                                         Event_Minute;                                      // 0x1FC8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Sunset_Event_State;                                // 0x1FCC(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Fog_Cloudiness_Above_Cloud_Layer;                  // 0x1FD0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_1FD1[0x7];                                     // 0x1FD1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Camera_Cloud_Layer_Normalized_Height;              // 0x1FD8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Camera_Sky_Atmosphere_Normalized_Density;          // 0x1FE0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Moon_Fog_Base_Color;                               // 0x1FE8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Sun_Fog_Base_Color;                                // 0x1FF8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Last_High_Frequency_Update_Location;               // 0x2008(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Old_Cloud_Layer_Camera_Level;                      // 0x2020(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Camera_Relative_Change_Speed;                // 0x2028(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Cached_Sky_Atmosphere_Absorption_Color;            // 0x2030(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Cloud_Coverage_Target_Resolution;                  // 0x2040(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2044[0x4];                                     // 0x2044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UTextureRenderTarget2D*                 Cloud_Coverage_Render_Target;                      // 0x2048(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, NoDestructor, HasGetValueTypeHash)
	int32                                         Cloud_Coverage_Target_Size;                        // 0x2050(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2054[0x4];                                     // 0x2054(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Painted_Cloud_Coverage_Opacity;                    // 0x2058(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Layer_2_Painted_Cloud_Coverage_Opacity;            // 0x2060(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Painted_Coverage_Affects_Global_Values;            // 0x2068(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMap<struct FIntPoint, class UUDS_Cloud_Paint_Cell_C*> Local_Painting_Cell_Data;                 // 0x2070(0x0050)(Edit, BlueprintVisible, Transient, DisableEditOnInstance)
	int32                                         Local_Painted_Cell_Size;                           // 0x20C0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cloud_Painting_Active;                             // 0x20C4(0x0001)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_20C5[0x3];                                     // 0x20C5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              Cloud_Coverage_Target_Location;                    // 0x20C8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Current_Cloud_Coverage_Render_Target_Mapping;      // 0x20D8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cloud_Coverage_Target_in_Use;                      // 0x20E8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_20E9[0x7];                                     // 0x20E9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<TSoftObjectPtr<class UObject>>         Async_Loading_Queue;                               // 0x20F0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<class UObject*>                        Async_Loaded_Objects;                              // 0x2100(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          Async_Loading_Active;                              // 0x2110(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2111[0x7];                                     // 0x2111(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cloud_Coverage_After_Painting;                     // 0x2118(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cloud_Paint_Can_Add_Coverage;                      // 0x2120(0x0001)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cloud_Paint_Can_Subtract_Coverage;                 // 0x2121(0x0001)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Volumetric_Cloud_Light_Rays;                // 0x2122(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2123[0x5];                                     // 0x2123(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Light_Ray_Intensity;                               // 0x2128(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Light_Ray_Tint_Color;                              // 0x2130(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Light_Rays_Point_Spacing;                          // 0x2140(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Light_Ray_Length;                                  // 0x2148(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Individual_Clouds_Light_Rays;                      // 0x2150(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Scale_Light_Ray_Width;                             // 0x2158(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Light_Rays_Max_Distance__Km_;                      // 0x2160(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Light_Rays_Depth_Fade_Distance;                    // 0x2168(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Light_Rays_3D_Noise_Scale;                         // 0x2170(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UNiagaraSystem>          Volumetric_Cloud_Light_Rays_Niagara_System;        // 0x2178(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	double                                        Max_Light_Ray_Length;                              // 0x21A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Top_World_Height;                            // 0x21A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Cached_Light_Ray_Vector;                           // 0x21B0(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Light_Rays_Using_Sun;                              // 0x21C8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21C9[0x7];                                     // 0x21C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cached_Light_Ray_Strength;                         // 0x21D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Cloud_Paint_Draw_MID;                              // 0x21D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Volumetric_Fog_Albedo__Day_;                       // 0x21E0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dusk__6;                                           // 0x21F0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Volumetric_Fog_Albedo__Night_;                     // 0x2200(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Render_Global_Volumetric_Material;                 // 0x2210(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2211[0x7];                                     // 0x2211(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Fog_Noise_Intensity;                               // 0x2218(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Noise_Scale;                                   // 0x2220(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Noise_Max_Samples;                             // 0x2228(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Noise_Speed_Scale;                             // 0x2230(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Mask_Fog_with_Weather_Masks;                       // 0x2238(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Render_Ground_Fog__Fog_Above_Distance_Fields_;     // 0x2240(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2241[0x7];                                     // 0x2241(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Ground_Fog_Extinction__Foggy_;                     // 0x2248(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Ground_Fog_Extinction__Dusty_;                     // 0x2250(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Ground_Fog_Extinction__Rainy_;                     // 0x2258(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Ground_Fog_Extinction__Snowy_;                     // 0x2260(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Ground_Fog_Extinction__Manual_;                    // 0x2268(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Ground_Fog_Height;                                 // 0x2270(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Ground_Fog_Height_Noise;                           // 0x2278(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Ground_Fog_Angle_Threshold;                        // 0x2280(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Ground_Fog_Hardness;                               // 0x2288(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Apply_Water_Fog_Values;                            // 0x2290(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2291[0x7];                                     // 0x2291(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Water_Extinction;                                  // 0x2298(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Water_Albedo;                                      // 0x22A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Global_Volumetric_Fog_Parent_Material;             // 0x22B0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Global_Volumetric_Fog_MID;                         // 0x22D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Global_Volumetric_Fog__Ground_Fog___Parent_Material; // 0x22E0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	struct FVector                                Fog_Position;                                      // 0x2308(0x0018)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCurveLinearColor*                      Sun_Disk_Color_Curve;                              // 0x2320(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	struct FVector                                Sun_Target;                                        // 0x2328(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	struct FVector                                Moon_Target;                                       // 0x2340(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              Static_Clouds_Texture;                             // 0x2358(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	double                                        Static_Clouds_Rotation;                            // 0x2380(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Static_Clouds_Rotation_Speed;                      // 0x2388(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Static_Clouds_Color_Intensity;                     // 0x2390(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2394[0x4];                                     // 0x2394(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cloud_Movement_Timer_Length;                       // 0x2398(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           Cloud_Movement_Cache_Timer;                        // 0x23A0(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Clouds_B_Time_Old;                                 // 0x23A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Clouds_Position_Color_A;                           // 0x23B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Clouds_Position_Color_B;                           // 0x23C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Fog_Position_Color_A;                              // 0x23D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Fog_Position_Color_B;                              // 0x23E0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Clouds_Time_Elapsed_Last_Update;                   // 0x23F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Next_Cache_Step;                                   // 0x23F8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_23FC[0x4];                                     // 0x23FC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<bool>                                  Cache_Group_Booleans;                              // 0x2400(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	double                                        Current_Cache_Timer_Speed;                         // 0x2410(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Low_Priority_Update_Step;                          // 0x2418(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Active_Update_Speed;                               // 0x241C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         High_Priority_Update_Step;                         // 0x2420(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Non_Cached_Update_Step;                            // 0x2424(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Property_Cache_Period___Time_Of_Day_Transition; // 0x2428(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Minimum_Active_Update_Speed;                       // 0x2430(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2434[0x4];                                     // 0x2434(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<EUDS_CachedProperties>                 Cache_Group_Moon;                                  // 0x2438(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<EUDS_CachedProperties>                 Cache_Group_Fog;                                   // 0x2448(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<EUDS_CachedProperties>                 Cache_Group_Sky_Material;                          // 0x2458(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<EUDS_CachedProperties>                 Cache_Group_Sky_Atmosphere;                        // 0x2468(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<EUDS_CachedProperties>                 Cache_Group_Simplified_Color;                      // 0x2478(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<EUDS_CachedProperties>                 Cache_Group_Sky_Light;                             // 0x2488(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<EUDS_CachedProperties>                 Cache_Group_Sun;                                   // 0x2498(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<EUDS_CachedProperties>                 Cache_Group_Volumetric_Clouds;                     // 0x24A8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<EUDS_CachedProperties>                 Cache_Group_2D_Clouds;                             // 0x24B8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	double                                        Wind_Speed_Multiplier;                             // 0x24C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Fog_Vertical_Velocity;                             // 0x24D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Volumetric_Aurora_Fade_Distance__km_;              // 0x24D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Cinematic_Clouds_View_Sample_Scale;                // 0x24E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cinematic_Clouds_Shadow_Sample_Scale;              // 0x24E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cinematic_Clouds_Tracing_Max_Distance;             // 0x24F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Cinematic_Clouds_View_Ray_Sample_Max_Count;        // 0x24F8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24FC[0x4];                                     // 0x24FC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UUDS_Modifier_C*>                Current_Modifiers;                                 // 0x2500(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<double>                                Modifier_Alphas;                                   // 0x2510(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<double>                                Modifier_Targets;                                  // 0x2520(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<double>                                Modifier_Speeds;                                   // 0x2530(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<class UUDS_Modifier_C*>                Starting_Modifiers;                                // 0x2540(0x0010)(Edit, BlueprintVisible)
	bool                                          Preview_Starting_Modifiers_in_Editor;              // 0x2550(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Replicate_Modifiers_To_Clients;                    // 0x2551(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Using_Volumetric_Light_Rays;                       // 0x2552(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2553[0x1];                                     // 0x2553(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Last_Material_Quality_Level;                       // 0x2554(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Last_Effects_Quality_Level;                        // 0x2558(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Instant_Exposure_Adjustment_on_Begin_Play;         // 0x255C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_255D[0x3];                                     // 0x255D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UPostProcessComponent*                  Instant_Exposure_Post_Process;                     // 0x2560(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Editor_Tick_Handler;                               // 0x2568(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, NoDestructor, HasGetValueTypeHash)
	double                                        Tick_Delta_Seconds;                                // 0x2570(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Editor_Camera_Position;                            // 0x2578(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Editor_Camera_Position_Offset;                     // 0x2590(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Last_Editor_Tick_Time;                             // 0x25A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_RunContext                               Run_Context;                                       // 0x25B0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_25B1[0x7];                                     // 0x25B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Slow_Falling_Combined_Change_Speed;                // 0x25B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Combined_Change_Speed;                             // 0x25C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UStaticMesh*                            Custom_Sky_Sphere_Static_Mesh;                     // 0x25C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Fallback_Cloud_Layer_Altitude;                     // 0x25D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Level_Editor_Tick;                                 // 0x25D8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_25D9[0x7];                                     // 0x25D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Time_of_Last_Cloud_Cache;                          // 0x25E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUDS_OcclusionState_C*                  Occlusion_State;                                   // 0x25E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Last_Static_Clouds_Update_Time;                    // 0x25F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Static_Clouds_Dynamic_Rotation;                    // 0x25F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               Editor_Camera_Rotation;                            // 0x2600(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	double                                        Cached_Cloud_Shadows_Cancel_Value;                 // 0x2618(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Interior_Occlusion_Change_Speed;                   // 0x2620(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Time_of_Last_Cache_Timing_Update;                  // 0x2628(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Old_Interior_Occlusion;                            // 0x2630(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Transitioning_Sky_Light_Intensity;                 // 0x2638(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cache_Sun_Cast_Shadows;                            // 0x2639(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_263A[0x6];                                     // 0x263A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Eclipse_Percent;                                   // 0x2640(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sun_Shadows_Cutoff_Z;                              // 0x2648(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(int32 Hour)>    Current_Hour_Changed;                              // 0x2650(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(int32 Minute__0_59_)> Every_Minute;                                // 0x2660(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	bool                                          Called_Starting_Event_Dispatchers;                 // 0x2670(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2671[0x7];                                     // 0x2671(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                World_Origin_Location;                             // 0x2678(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftClassPtr<class UClass>                   Editor_Tick_Handler_Class;                         // 0x2690(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftClassPtr<class UClass>                   Editor_Utility_Opener_Class;                       // 0x26B8(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	struct FDateTime                              Event_Date;                                        // 0x26E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              Date_Changed;                                      // 0x26E8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	bool                                          Initial_Replication;                               // 0x26F8(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_26F9[0x7];                                     // 0x26F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDateTime                              Starting_Date;                                     // 0x2700(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Clouds_Time_Based_Movement_Offset;                 // 0x2708(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<double>                                Unmodified_Floats;                                 // 0x2710(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<struct FLinearColor>                   Unmodified_Colors;                                 // 0x2720(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          Modifiers_Ticking;                                 // 0x2730(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2731[0x7];                                     // 0x2731(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<EUDS_Modifier_Float_Property>          Modified_Float_Properties;                         // 0x2738(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<EUDS_Modifier_Color_Property>          Modified_Color_Properties;                         // 0x2748(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          Move_Sky_Light_Location;                           // 0x2758(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Sky_Light_Movement_uses_Z_Axis;                    // 0x2759(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_275A[0x6];                                     // 0x275A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Weather_Cloud_Coverage;                            // 0x2760(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Weather_Fog;                                       // 0x2768(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Weather_Dust_Amount;                               // 0x2770(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Sky_Glow_Fog_Color;                                // 0x2778(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Cached_Night_Sky_Glow;                             // 0x2788(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Cached_Light_Pollution;                            // 0x2798(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Local_Cloud_Coverage;                              // 0x27A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Storm_Clouds_Draw_MID;                             // 0x27B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          Cloud_Painting_Present;                            // 0x27B8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Radial_Storm_Clouds_Present;                       // 0x27B9(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_27BA[0x6];                                     // 0x27BA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FRadialStorm_CoverageBrush>     Radial_Storm_Cloud_Coverage_Cache;                 // 0x27C0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TMap<class FString, double>                   Cvar_Cache;                                        // 0x27D0(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	class AUDS_Cloud_Paint_Container_C*           Cloud_Paint_Container;                             // 0x2820(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, Transient, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	TArray<class AActor*>                         Radial_Storms;                                     // 0x2828(0x0010)(Edit, BlueprintVisible, DisableEditOnTemplate, Transient, DisableEditOnInstance)
	struct FRotator                               Sun_World_Rotation;                                // 0x2838(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	struct FRotator                               Moon_World_Rotation;                               // 0x2850(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	class USkyLightComponent*                     Path_Tracer_Sky_Light;                             // 0x2868(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UStaticMeshComponent*                   Compass_Mesh;                                      // 0x2870(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UStaticMeshComponent*                   Global_Volumetric_Fog_Mesh;                        // 0x2878(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UUDS_PlayerOcclusion_C*                 Player_Occlusion;                                  // 0x2880(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UStaticMeshComponent*                   TwoD_Turbulence_Disk;                              // 0x2888(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UNiagaraComponent*                      Volumetric_Cloud_Light_Rays;                       // 0x2890(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UStaticMeshComponent*                   Inside_Cloud_Fog_Mesh;                             // 0x2898(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        Volumetric_Cloud_Shadow_Altitude;                  // 0x28A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<bool>                                  Post_Process_Mats_Toggle_State;                    // 0x28A8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FWeightedBlendable                     Lens_Flare_WB;                                     // 0x28B8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	struct FWeightedBlendable                     Cloud_Fog_PP_WB;                                   // 0x28C8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	class UPostProcessComponent*                  Static_PPM_Component;                              // 0x28D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Path_Tracer_Fog_Parent_Mat;                        // 0x28E0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Path_Tracer_Fog_MID;                               // 0x2908(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	struct FWeightedBlendable                     Path_Tracer_Fog_WB;                                // 0x2910(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	bool                                          Render_Height_Fog_in_Path_Tracer_using_Post_Process; // 0x2920(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_2921[0x7];                                     // 0x2921(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture*                               Cloud_Profile_LUT_Preview;                         // 0x2928(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, NoDestructor, HasGetValueTypeHash)
	bool                                          Previewing_Cloud_Profile_Editor;                   // 0x2930(0x0001)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Alternate_Tick;                                    // 0x2931(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2932[0x6];                                     // 0x2932(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UCurveFloat*                            Sky_Atmosphere_Density_Curve;                      // 0x2938(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          Allow_Disabling_Directional_Shadows_with_Coverage; // 0x2940(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Apply_Flat_Cloudiness;                             // 0x2941(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Using_Sky_Atmosphere;                              // 0x2942(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2943[0x5];                                     // 0x2943(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cached_Moon_Effective_Illumination_0_1;            // 0x2948(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cached_Absent_Directional_Lights_Brightness;       // 0x2950(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cached_Current_Moon_Lit_Percent;                   // 0x2958(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Using_Space_Mode;                                  // 0x2960(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Using_Volumetric_Clouds;                           // 0x2961(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Using_Static_Clouds;                               // 0x2962(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Using_2D_Dynamic_Clouds;                           // 0x2963(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Using_Volumetric_Aurora;                           // 0x2964(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2965[0x3];                                     // 0x2965(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cached_Lit_Intensity;                              // 0x2968(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cached_Directional_Light_Dimming;                  // 0x2970(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Modifiers_Animating;                               // 0x2978(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2979[0x7];                                     // 0x2979(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cached_Directional_Inscattering_Multiplier;        // 0x2980(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Moon_Renders_Behind_Space_Layer;                   // 0x2988(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2989[0x3];                                     // 0x2989(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           Cached_Fog_Directional_Inscattering;               // 0x298C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_299C[0x4];                                     // 0x299C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Sun_Moon_Delta;                                    // 0x29A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ECollisionChannel                             Weather_Particle_Collision_Channel;                // 0x29A8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Use_Legacy_Cloud_Coverage_Range;                   // 0x29A9(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_29AA[0x6];                                     // 0x29AA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<int32>                                 Cache_Group_Countdowns;                            // 0x29B0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)

public:
	void TwoD_Clouds_Shading_Offset_Vector(struct FLinearColor* LinearColor);
	void Three_Color_Time_Blend(const struct FLinearColor& Day_Color, const struct FLinearColor& Dusk_Color, const struct FLinearColor& Night_Color, struct FLinearColor* Out);
	double Absent_Directional_Lights_Brightness();
	void Add_Constructed_Components();
	void Add_Modifier(class UUDS_Modifier_C* Modifier, double Fade_In_Time);
	void Add_Object_to_Async_Loading_Queue(TSoftObjectPtr<class UObject> Object, bool High_Priority);
	void Add_Static_Post_Process_Materials();
	void Adjust_Base_Sun_Light_Intensity(const struct FVector& Sun_Vector, double* Intensity, double* Multiplier);
	void Adjust_for_World_Origin_Rebasing();
	void All_Volumetric_Cloud_MIDs(TArray<class UMaterialInstanceDynamic*>* MIDs);
	void Applied_Cloud_Speed(double* Out);
	void Apply_Console_Variable_with_Check(const class FString& CVar, double setting, int32 Type);
	void Apply_Editor_Weather_Override();
	void Apply_Feature_Level_Mode_Changes(bool* Made_Changes);
	void Apply_Light_Shaft_Settings(class UDirectionalLightComponent* Light, const struct FVector2D& Max_Brightness, const struct FVector2D& Bloom_Threshold, const struct FVector2D& Bloom_Scale, const struct FVector& Forward_Vector);
	void Apply_Location_Preset(EUDS_CityPresets Location);
	void Apply_Modifier_Property_Overrides(class UUDS_Modifier_C* Modifier, double Alpha);
	void Apply_Modifiers();
	void Apply_Saved_UDS_and_UDW_State(const struct FUDS_and_UDW_State& State);
	void Apply_Starting_Modifiers();
	void Apply_System_Time();
	void Apply_Volumetric_Mode(EUDS_VolRT_Mode Mode);
	void Approximate_Real_Sun_Moon_and_Stars(double Time_of_Day_0, bool Only_Calculate_Sun, struct FVector* Sun_Vector, struct FVector* Moon_Vector, double* Real_Phase, struct FVector* Phase_Alignment, struct FLinearColor* Celestial_Yaw, double* Celestial_Orbit);
	void Cache_Color(EUDS_CachedProperties Property, const struct FLinearColor& Set_Value, double Change_Tolerance);
	void Cache_Float(EUDS_CachedProperties Property, double Set_Value, double Change_Tolerance);
	void Cache_Properties(int32 Cache_Group, bool Starting_Cache_Fill);
	void Cache_Sun_and_Moon_Orientation();
	void Cache_Timer_And_Update_Speed(bool* Hard_Cache_Reset);
	void Cache_Vector(EUDS_CachedProperties Property, const struct FVector& Set_Value, double Change_Tolerance);
	void Camera_Location_Dependent_Updates();
	void Change_Sky_Mode_at_Runtime(EUDS_SkyMode New_Sky_Mode);
	void Check_for_Cloud_Coverage_Target_Recenter();
	void Check_for_Daylight_Savings_Time(int32 Hour);
	void Check_for_Time_Event_Dispatchers();
	void Check_if_Point_is_Exposed_to_Sun_or_Moon_Light(const struct FVector& Location, double Distance_to_Trace, TArray<class AActor*>& Actors_to_Ignore_in_Trace, bool* Exposed_to_Sun, bool* Exposed_to_Moon, bool* Exposed_to_Either, double* Light_Intensity__Lux_);
	void Check_If_Year_is_Leap_Year(int32 Year_0, bool* Leap_Year);
	void Check_to_Start_Volumetric_Cloud_Light_Rays();
	void Cinematic_Mode_Startup();
	void Clear_Modifiers();
	void Client_Check_Initial_Replication();
	void Cloud_Coverage_Target_Mapping(struct FVector* Mapping);
	void Cloud_Layer_Top_and_Bottom_World_Height(double* Bottom_World_Height, double* Top_World_Height);
	double Cloud_Shadows_Cloud_Density();
	void Cloud_Shadows_Light_Vector_And_Cancel_Value(struct FLinearColor* Vector, double* Cancel_Value);
	void Cloud_Shadows_Parent_Material(TSoftObjectPtr<class UMaterialInterface>* Mat);
	void Cloud_Texture_Pan_Scale(double* Scale);
	struct FVector Cloud_Texture_Velocity();
	void Cloud_Wisp_Gradient_Vector(struct FLinearColor* Out);
	double Clouds_Time_of_Day_Factor();
	void Combined_Night_Brightness(double* Out);
	void Composite_Context_Vector(struct FVector* Out);
	void Composite_Weather_Vector(struct FVector* Out);
	void Configure_Directional_Light_with_Feature_Toggle(bool Toggle, class UDirectionalLightComponent*& Selected_Component_Variable, class UDirectionalLightComponent* Built_in_Light_Component, class ADirectionalLight* Custom_Light_Actor, int32 Atmospheric_Index, EComponentMobility Mobility, bool Light_Visibility);
	void Configure_Height_Fog_with_Feature_Toggle();
	void Configure_Sky_Light_with_Feature_Toggle();
	void Construct_Cloud_Shadows_MID();
	void Construct_Inside_Cloud_Fog();
	void Construct_Lens_Flare();
	void Construct_Overcast_Turbulence();
	void Construct_Path_Tracer_Fog();
	void Construct_Sky_Sphere_and_Material();
	void Construct_Space_Layer();
	void Construct_Volumetric_Aurora();
	void Construct_Volumetric_Clouds();
	void Construction_Script_Function();
	void Create_Post_Process_Components();
	void Create_UDS_and_UDW_State_for_Saving(struct FUDS_and_UDW_State* Packaged_State);
	struct FLinearColor Current_2D_Cloud_Tint();
	double Current_Aurora_Intensity();
	double Current_Base_Clouds_Scale();
	struct FLinearColor Current_Cloud_Fog_Post_Process_Color();
	void Current_Cloud_Wisps_Color(struct FLinearColor* Out);
	double Current_Directional_Inscattering_Exponent();
	struct FDateTime Current_Event_Date();
	void Current_Exposure_Bias(double* Bias);
	double Current_Fog_Density();
	void Current_Fog_Directional_Inscattering_Color(struct FLinearColor* Directional_Inscattering_Color);
	void Current_Fog_Inscattering_Color(struct FLinearColor* Inscattering_Color);
	void Current_Hour_as_Integer(int32* Hour);
	void Current_Lerp_to_Simplified_Clouds(double* Alpha);
	struct FLinearColor Current_Light_Pollution();
	double Current_Lit_Intensity();
	void Current_Max_Trace_Distance(double* Out);
	double Current_Mie_Anisotropy();
	double Current_Mie_Scattering_Scale();
	void Current_Minute_as_Integer(int32* Minute);
	void Current_Month_Lengths(int32 Year_0, TArray<int32>* Lengths);
	void Current_Moon_Light_Color(struct FLinearColor* Color);
	struct FLinearColor Current_Moon_Light_Material_Color();
	double Current_Moon_Lit_Percent();
	double Current_Moon_Material_Intensity();
	struct FLinearColor Current_Moon_Phase_Angle();
	void Current_Moon_Scale(double* Scale);
	double Current_Moons_Cloud_Mask();
	struct FLinearColor Current_Night_Sky_Glow();
	double Current_Overall_Intensity();
	double Current_Overcast_Turbulence_Strength();
	struct FLinearColor Current_Rayleigh_Scattering_Color();
	void Current_Sky_Ambient_Color(struct FLinearColor* Sky_Ambient_Color);
	void Current_Sky_Atmosphere_Absorption_Color(struct FLinearColor* Out);
	struct FLinearColor Current_Sky_Atmosphere_Luminance();
	struct FLinearColor Current_Sky_Light_Lower_Hemisphere_Color();
	void Current_Solar_Eclipse_Values(double* Eclipse_Percent_0, struct FLinearColor* Tint_Color);
	double Current_Space_Layer_Brightness();
	struct FLinearColor Current_Stars_Color();
	struct FLinearColor Current_Sun_Disk_Color();
	double Current_Sun_Disk_Intensity();
	struct FLinearColor Current_Sun_Light_Color();
	double Current_Sun_Light_Intensity();
	double Current_Sun_Moon_Cache_Delta();
	double Current_Sun_Radius();
	double Current_Sun_Specular_Scale();
	double Current_Sunrise_Event_Time();
	double Current_Sunset_Event_Time();
	void Sunrise_Event_State(bool* Sun_Up);
	void Current_View_Sample_Scale(double* Out);
	struct FLinearColor Current_Volumetric_Cloud_Albedo();
	void Current_Volumetric_Cloud_Inner_Emit_Limit(double* Out);
	double Current_Volumetric_Cloud_Macro_Variation();
	void Current_Volumetric_Cloud_Multiscattering_Intensity(double* Out);
	void Current_Volumetric_Cloud_Multiscattering_Occlusion(double* Occlusion);
	void Current_Volumetric_Cloud_Shadow_Tracing_Distance(double* Out);
	double Current_Volumetric_Cloud_Sky_Atmo_Contribution();
	void Current_Volumetric_Clouds_Density(bool Layer_1, double* Out);
	double Current_Volumetric_Multiscattering_Phase_1();
	double Current_Wisps_Opacity();
	void Date_and_Time_to_Year_Progress(int32 Month_0, int32 Day_0, double Time, double* Days_from_Start_of_Year);
	void Day_Count_at_the_Start_of_a_Month(int32 Year_0, int32 Month_0, int32* Count);
	void Day_Ended();
	void Days_Since_J2000(int32 Input_Day, int32 Input_Month, int32 Input_Year, int32* Days);
	void Days_Since_Y1D1M1(int32 Input_Day, int32 Input_Month, int32 Input_Year, int32* Days);
	bool Dimming_Directional_Lights();
	void Directional_Inscattering_Multiplier(double* Multiplier);
	void Directional_Light_Dimming(double* Out);
	void Directional_Source_Angle(double Disk_Scale, double Max_Scale_Factor, double Scale_Setting, double* Out);
	void Disable_Instant_Exposure();
	void Editor_Tick(const struct FVector& Editor_Camera_Location, const struct FRotator& Editor_Camera_Rotation_0, bool Sequencer_Open);
	void Editor_Update_from_Weather();
	void ExecuteUbergraph_Ultra_Dynamic_Sky(int32 EntryPoint);
	void Filtered_Moon_Light_Intensity(double Unfiltered, double* Intensity);
	void Sunrise_Times();
	void Finish_Time_Transition();
	void Fire_Editor_Dispatchers();
	double Fog_and_Dust_Shadow_Value();
	double Fog_Height_Falloff();
	double Fog_Start_Distance();
	void Force_Startup();
	void Force_Valid_Day();
	void Get_Cached_Color(EUDS_CachedProperties Property, struct FLinearColor* Value);
	void Get_Cached_Float(EUDS_CachedProperties Property, double* Value);
	void Get_Cached_Vector(EUDS_CachedProperties Property, struct FVector* Value);
	void Get_Cloud_Coverage_0_10(double* Cloud_Coverage_0);
	void Get_Cloud_Coverage_0_3(double* Cloud_Coverage_0);
	void Get_Cloud_Coverage_Local(double* Local_Height);
	void Get_Current_Sky_Light_Color_and_Intensity(double* Out_Intensity, struct FLinearColor* Out_Color);
	void Get_Current_Volumetric_Cloud_Extinction_Scale(double* Top);
	void Get_DateTime(struct FDateTime* Current_Date_and_Time);
	void Get_Day_of_the_Week(int32* Index_0, class FString* Name_0);
	void Get_Editor_Camera_Location();
	double Get_Inverted_Global_Occlusion();
	void Get_Manual_Target_Change_Speed(struct FVector& Old_Vector, const struct FVector& New_Vector);
	void Get_Nearby_Cloud_Cells_To_Load_Asynchronously();
	void Get_Runtime_Camera_Location();
	TSoftObjectPtr<class UMaterialInterface> Get_Sky_MID_Parent_Material_Instance();
	void Get_Starting_Cloud_Painting_Actors();
	void Get_TimeCode(struct FTimecode* Time);
	void Get_UDW_Reference();
	void Get_Volumetric_Cloud_Emissive_Colors(struct FLinearColor* Bottom, struct FLinearColor* Top);
	void MS_to_Time_of_Day(int32 Hours, int32 Minutes, int32 Seconds, int32 Miliseconds, double* Time);
	void Hard_Reset_Cache();
	void Increment_Cache_Timer();
	void Increment_Cloud_Movement_Cache();
	void Increment_Time_of_Day_Forward(double Amount);
	void Initialize_Occlusion();
	double Inside_Outside_Cloud_Layer();
	void Is_Cached_Value_Changing(EUDS_CachedProperties Enum, bool* Yes);
	bool Is_Date_and_Time_in_Daylight_Savings_Time();
	void Is_Directional_Light_Casting_Shadows(bool Force_Disabled, const struct FVector& Light_Vector, bool* Yes);
	void Is_it_Daytime_(bool* Yes);
	void Lens_Flare_Parent_Material(TSoftObjectPtr<class UMaterialInterface>* Mat);
	bool Lights_Update_Degree_Threshold_Test(const struct FRotator& World_Rotation, class USceneComponent* Light);
	void Load_Required_Assets();
	void Monitor_for_Changes();
	double Moon_Effective_Illumination_0_1();
	double Moon_Light_Specular_Scale();
	double Moon_Light_Volumetric_Scattering_Intensity();
	double Moon_Phase_Light_Multiplier(double* Without_Light_Brightness);
	void Moon_Z_Vector(struct FVector* Out);
	double Night_Filter(bool Cached);
	void Night_Filtered_Night_Brightness(double* Multiplier);
	void Notify_of_Removed_Cloud_Paint_Container();
	void Number_of_Days_in_a_Year(int32 Year_0, int32* Count);
	void Offset_Date(int32 Offset);
	void Offset_Date_by_a_Number_of_Days(int32 Input_Month, int32 Input_Day, int32 Input_Year, int32 Offset, int32* Output_Month, int32* Output_Day, int32* Output_Year);
	void OnLoaded_AA91001A446E22425F2F54AAB2159C50(class UObject* Loaded);
	void OnRep_Replicated_Time_of_Day();
	void Open_Editor_Readme_Entry(const class FString& Entry);
	void Open_Editor_Readme_Entry_Set(TArray<class FName>& Entry);
	double Overcast_Brightness();
	void Overcast_Luminance_Boost(double Multiplier, double* Out);
	void Query_Project_Settings_And_UDS_Version();
	void Randomize_Time_Of_Day();
	void Recapture_Sky_Light();
	void ReceiveBeginPlay();
	void ReceiveEndPlay(EEndPlayReason EndPlayReason);
	void ReceiveTick(float DeltaSeconds);
	void Release_Async_Loaded_Object(TSoftObjectPtr<class UObject> Object);
	void Remove_Modifier(class UUDS_Modifier_C* Modifier, double Fade_Out_Time);
	void Replicate_Modifier_State(const TArray<class UUDS_Modifier_C*>& Modifiers, const TArray<double>& Modifier_Alphas_0, const TArray<double>& Modifier_Targets_0, const TArray<double>& Modifier_Speeds_0, const bool Hard_Reset);
	void Restart_Real_Time_Sky_Light_Capture();
	void Reverse_Day_Ended();
	void Revert_Changed_Console_Variables();
	void Revert_Modified_Properties();
	bool Runtime_Or_Initializing();
	void Save_Calendar_Data();
	void Scale_Sample_Count(double In, double* Out);
	void Season_Value_for_Weather_from_Date_and_Time(int32 Day_Offset, double* Season);
	void Set_Cloud_Coverage(double Cloud_Coverage_0);
	void Set_Current_Fog_Base_Colors();
	void Set_Date_and_Time(const struct FDateTime& Date_Time);
	void Set_Dust_Amount(double Dust_Amount_0);
	void Set_Fog(double Fog_0);
	void Set_Modifier_State(class UUDS_Modifier_C* Modifier, double Alpha);
	void Set_Previous_Weather_Variables();
	void Set_Startup_Variables();
	void Set_Sun_and_Moon_Root_Rotation();
	void Set_Time_Cycle_Degrees();
	void Set_Time_with_Time_Code(const struct FTimecode& Time_Code);
	void Set_Unmodified_Property_Values();
	void Set_Up_Global_Volumetric_Fog_Material();
	void Set_Variables_Controlled_by_Weather();
	void Show_Editor_Warning(const class FString& Title, const class FString& Message);
	struct FLinearColor Simplfied_Color_Sun_Scattering();
	void Simplified_Cloud_Light_Color(struct FLinearColor* Out);
	struct FLinearColor Simplified_Moon_Scattering_Color();
	void Simplified_Sun_Glow_Color(struct FLinearColor* Out);
	void Size_Cache_Arrays();
	void Sky_Atmosphere_Fog_Contribution(double* Output);
	void Sky_Startup_Functions();
	void Solar_Eclipse_Circle_Mask(double Sun_Angular_Diameter, double Moon_Angular_Diameter, const struct FVector& Moon_Vector, double Moon_Softness, double* Fraction_Showing);
	void Space_Planet_Parent_MID(const struct FUDS_Space_Planet& Planet, TSoftObjectPtr<class UMaterialInterface>* Out);
	void Start_Active_Timers();
	void Start_Async_Loader();
	void Start_Editor_Tick_Handler();
	void Start_Instant_Exposure();
	void Start_Up_UDW_If_it_Exists();
	void Starting_Animate_Time_of_Day_Offset();
	void Starting_Cloud_Formation();
	void Startup_Tick();
	void Static_Clouds_Lighting_Mask(struct FLinearColor* XY_Mask);
	void Static_Clouds_Tint_Color(struct FLinearColor* Light, struct FLinearColor* Shadow);
	void Static_Mode_Cloud_Tick();
	void Static_Mode_Startup();
	void Static_Properties___2D_Dynamic_Clouds();
	void Static_Properties___Aurora();
	void Static_Properties___Cloud_Movement();
	void Static_Properties___Cloud_Shadows();
	void Static_Properties___Height_Fog();
	void Static_Properties___Lens_Flare();
	void Static_Properties___Misc();
	void Static_Properties___Mode_Derivatives();
	void Static_Properties___Moon();
	void Static_Properties___Occlusion();
	void Static_Properties___Post_Processing();
	void Static_Properties___Simplified_Color();
	void Static_Properties___Sky_Atmosphere();
	void Static_Properties___Sky_Light();
	void Static_Properties___Sky_Material();
	void Static_Properties___Space_Layer();
	void Static_Properties___Stars();
	void Static_Properties___Static_Clouds();
	void Static_Properties___Sun();
	void Static_Properties___Volumetric_Cloud_Light_Rays();
	void Static_Properties___Volumetric_Clouds();
	void Static_Properties___Water_Caustics();
	void Sun_height(bool Cached, double* Z);
	void Sun_Z_Vector(struct FVector* Out);
	void Swap_with_Cinematic_Runtime_Value(double In, double Cine, bool Use_Higher, double* Out);
	void Test_Point_for_Painted_Cloud_Coverage(const struct FVector& Location, double* Cloud_Coverage_with_Painting);
	void Three_Time_Floats(double Day_0, double Dusk, double Night, double* Out);
	void Tick_Function();
	void Tick_Time_Transition();
	void Time_of_Day_Animation();
	void Time_of_Day_Offset(double* Per_Second);
	void S(double Time, int32* Hour, int32* Minute, int32* Second, double* Second_Fraction);
	void Timed_Override_with_New_Changes();
	void Toggle_Post_Process_Material(int32 Index_0, bool Enabled);
	double Total_Time_Elapsed();
	void Transition_Sky_Light_Intensity(double New_Sky_Light_Intensity_Multiplier, double Transition_Time);
	void Transition_Time_of_Day(double New_Time_of_Day, double Transition_Duration__Seconds_, EEasingFunc Easing_Function, double Easing_Exponent, bool Allow_Time_Going_Backwards);
	void Twilight_Brightness_Falloff(double Z, double* Scale);
	void UDW_Editor_Update();
	void UDW_Instant_Update();
	void Unfiltered_Moon_Light_Intensity(double* Out);
	void Update_Active_Variables();
	void Update_Cache_Group_Boolean(EUDS_Cache_Group Group, TArray<EUDS_CachedProperties>& Properties_0);
	void Update_Cloud_Coverage_After_Painting();
	void Update_Cloud_Movement();
	void Update_Common_Derivatives();
	void Update_Current_Volumetric_Clouds_MID();
	void Update_Directional_Light_Rotations();
	void Update_Dynamic_Sky_Light_Multiplier();
	void Update_Global_Volumetric_Fog_Material();
	void Update_High_Priority_Properties();
	void Update_Lens_Flare();
	void Update_Low_Priority_Properties();
	void Update_Lunar_Phase(int32 Hour);
	void Update_Non_Cached_Active_Properties();
	void Update_Overcast_Turbulence();
	void Update_Painted_Cloud_Coverage_Target();
	void Update_Path_Tracer_Fog();
	void Update_Post_Process_Blend_Weights();
	void Update_Replicated_Time();
	void Update_Settings_Based_on_Scalability();
	void Update_Sky_Atmosphere_Location(const struct FVector& Location);
	void Update_Space_Layer_Vectors();
	void Update_Static_Variables();
	void Update_Total_Days_Elapsed();
	void Update_Volumetric_Cloud_Light_Rays();
	void Update_Water_Level_Parameter();
	void UserConstructionScript();
	void Volumetric_Cloud_Ambient_Light_Color(struct FLinearColor* Bottom, struct FLinearColor* Top);
	void Volumetric_Cloud_Base_Cloud_Height(double* Base_Cloud_Height_0);
	void Volumetric_Cloud_First_Layer_Top_Altitude(double* Cloud_Top_Altitude);
	void Volumetric_Cloud_Floor_Variation(double* Height_Clear, double* Height_Cloudy, double* Color);
	void Volumetric_Cloud_Layer_Height(double Base_Cloud_Height_0, double* Layer_Height);
	void Volumetric_Cloud_Layer_Scale(double* Layer_Scale);
	void Volumetric_Clouds_Parent_Materials(TSoftObjectPtr<class UMaterialInterface>* Simplified, TSoftObjectPtr<class UMaterialInterface>* Complex);
	void Volumetric_Clouds_SubNoise_Scales(struct FLinearColor* High, struct FLinearColor* Low);
	void Volumetric_Light_Ray_Strength_and_Color(double* Ray_Strength, struct FLinearColor* Ray_Color);
	void World_Space_to_Drawn_Target_Pixel_Space(const struct FVector2D& In, struct FVector2D* Out);
	void __Aurora();
	void _Offline_Rendering();
	void __Cloud_Movement();
	void __Cloud_Shadows();
	void __Cloud_Wisps();
	void __Configuring_for_Performance();
	void __Considerations_for_Mobile();
	void __Directional_Light();
	void __Dust();
	void __Exposure();
	void __Fog_Color();
	void __Fog_Density();
	void __Interior_Adjustments();
	void __Moon();
	void __Post_Processing();
	void __Screen_Space_Light_Shafts();
	void __Simplified_Color();
	void __Simulation();
	void __Sky_Atmosphere();
	void __Sky_Glow();
	void __Sky_Light();
	void __Sky_Modifiers();
	void __Space_Layer();
	void __Stars();
	void __Sun();
	void __Sun_Lens_Flare();
	void __UDS_Documentation();
	void __Underwater_Caustics();
	void __Volumetric_Cloud_Light_Rays();
	void __Volumetric_Cloud_Painting();
	void __Volumetric_Fog();
	void _2D_Dynamic_Clouds();
	void _Static_Clouds();
	void _Time_of_Day();
	void _Volumetric_Clouds();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Ultra_Dynamic_Sky_C">();
	}
	static class AUltra_Dynamic_Sky_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<AUltra_Dynamic_Sky_C>();
	}
};
static_assert(alignof(AUltra_Dynamic_Sky_C) == 0x000010, "Wrong alignment on AUltra_Dynamic_Sky_C");
static_assert(sizeof(AUltra_Dynamic_Sky_C) == 0x0029C0, "Wrong size on AUltra_Dynamic_Sky_C");
static_assert(offsetof(AUltra_Dynamic_Sky_C, UberGraphFrame) == 0x000298, "Member 'AUltra_Dynamic_Sky_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, PostProcess) == 0x0002A0, "Member 'AUltra_Dynamic_Sky_C::PostProcess' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Icon) == 0x0002A8, "Member 'AUltra_Dynamic_Sky_C::Sun_Icon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Icon) == 0x0002B0, "Member 'AUltra_Dynamic_Sky_C::Moon_Icon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Captured_Scene_Sky_Light) == 0x0002B8, "Member 'AUltra_Dynamic_Sky_C::Captured_Scene_Sky_Light' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Root) == 0x0002C0, "Member 'AUltra_Dynamic_Sky_C::Root' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, VolumetricCloud) == 0x0002C8, "Member 'AUltra_Dynamic_Sky_C::VolumetricCloud' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cubemap_Sky_Light) == 0x0002D0, "Member 'AUltra_Dynamic_Sky_C::Cubemap_Sky_Light' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, HeightFog) == 0x0002D8, "Member 'AUltra_Dynamic_Sky_C::HeightFog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon) == 0x0002E0, "Member 'AUltra_Dynamic_Sky_C::Moon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun) == 0x0002E8, "Member 'AUltra_Dynamic_Sky_C::Sun' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, SkyAtmosphere) == 0x0002F0, "Member 'AUltra_Dynamic_Sky_C::SkyAtmosphere' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Sphere_Mesh) == 0x0002F8, "Member 'AUltra_Dynamic_Sky_C::Sky_Sphere_Mesh' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Sphere_MID) == 0x000300, "Member 'AUltra_Dynamic_Sky_C::Sky_Sphere_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_speed) == 0x000308, "Member 'AUltra_Dynamic_Sky_C::Cloud_speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Wisps_Opacity__Clear_) == 0x000310, "Member 'AUltra_Dynamic_Sky_C::Cloud_Wisps_Opacity__Clear_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Refresh_Settings) == 0x000318, "Member 'AUltra_Dynamic_Sky_C::Refresh_Settings' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_of_Day) == 0x000320, "Member 'AUltra_Dynamic_Sky_C::Time_of_Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Mode) == 0x000328, "Member 'AUltra_Dynamic_Sky_C::Sky_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Color_Mode) == 0x000329, "Member 'AUltra_Dynamic_Sky_C::Color_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Project_Mode) == 0x00032A, "Member 'AUltra_Dynamic_Sky_C::Project_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Feature_Level) == 0x00032B, "Member 'AUltra_Dynamic_Sky_C::Feature_Level' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Coverage) == 0x000330, "Member 'AUltra_Dynamic_Sky_C::Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog) == 0x000338, "Member 'AUltra_Dynamic_Sky_C::Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Overall_Intensity) == 0x000340, "Member 'AUltra_Dynamic_Sky_C::Overall_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Direction) == 0x000348, "Member 'AUltra_Dynamic_Sky_C::Cloud_Direction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Phase) == 0x000350, "Member 'AUltra_Dynamic_Sky_C::Cloud_Phase' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Manually_Position_Moon_Target) == 0x000358, "Member 'AUltra_Dynamic_Sky_C::Manually_Position_Moon_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Moon) == 0x000359, "Member 'AUltra_Dynamic_Sky_C::Render_Moon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Light_Intensity) == 0x000360, "Member 'AUltra_Dynamic_Sky_C::Moon_Light_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Mobility) == 0x000368, "Member 'AUltra_Dynamic_Sky_C::Moon_Mobility' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Scale) == 0x000370, "Member 'AUltra_Dynamic_Sky_C::Moon_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Phase) == 0x000378, "Member 'AUltra_Dynamic_Sky_C::Moon_Phase' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Yaw) == 0x000380, "Member 'AUltra_Dynamic_Sky_C::Moon_Yaw' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Pitch) == 0x000388, "Member 'AUltra_Dynamic_Sky_C::Moon_Pitch' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Vertical_Offset) == 0x000390, "Member 'AUltra_Dynamic_Sky_C::Moon_Vertical_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Orbit_Offset) == 0x000398, "Member 'AUltra_Dynamic_Sky_C::Moon_Orbit_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Casts_Shadows) == 0x0003A0, "Member 'AUltra_Dynamic_Sky_C::Moon_Casts_Shadows' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Transmission) == 0x0003A1, "Member 'AUltra_Dynamic_Sky_C::Moon_Transmission' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Light_Color) == 0x0003A4, "Member 'AUltra_Dynamic_Sky_C::Moon_Light_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Texture) == 0x0003B8, "Member 'AUltra_Dynamic_Sky_C::Moon_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Material_Color) == 0x0003E0, "Member 'AUltra_Dynamic_Sky_C::Moon_Material_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Texture_Intensity__Night_) == 0x0003F0, "Member 'AUltra_Dynamic_Sky_C::Moon_Texture_Intensity__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Texture_Intensity__Day_) == 0x0003F8, "Member 'AUltra_Dynamic_Sky_C::Moon_Texture_Intensity__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Glow_Intensity) == 0x000400, "Member 'AUltra_Dynamic_Sky_C::Moon_Glow_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Moon_Phases) == 0x000408, "Member 'AUltra_Dynamic_Sky_C::Render_Moon_Phases' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Phase_Contrast) == 0x000410, "Member 'AUltra_Dynamic_Sky_C::Moon_Phase_Contrast' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Phase_Changes_Over_Time) == 0x000418, "Member 'AUltra_Dynamic_Sky_C::Moon_Phase_Changes_Over_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dark_Side_Brightness) == 0x000420, "Member 'AUltra_Dynamic_Sky_C::Dark_Side_Brightness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Phase_Normal) == 0x000428, "Member 'AUltra_Dynamic_Sky_C::Moon_Phase_Normal' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Manually_Position_Sun_Target) == 0x000450, "Member 'AUltra_Dynamic_Sky_C::Manually_Position_Sun_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Light_Intensity) == 0x000458, "Member 'AUltra_Dynamic_Sky_C::Sun_Light_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Mobility) == 0x000460, "Member 'AUltra_Dynamic_Sky_C::Sun_Mobility' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Yaw) == 0x000468, "Member 'AUltra_Dynamic_Sky_C::Sun_Yaw' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Contrast) == 0x000470, "Member 'AUltra_Dynamic_Sky_C::Contrast' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Pitch) == 0x000478, "Member 'AUltra_Dynamic_Sky_C::Sun_Pitch' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Vertical_Offset) == 0x000480, "Member 'AUltra_Dynamic_Sky_C::Sun_Vertical_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Casts_Shadows) == 0x000488, "Member 'AUltra_Dynamic_Sky_C::Sun_Casts_Shadows' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Transmission) == 0x000489, "Member 'AUltra_Dynamic_Sky_C::Sun_Transmission' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Light_Color) == 0x00048C, "Member 'AUltra_Dynamic_Sky_C::Sun_Light_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Soften_Cloud_Layer_1) == 0x0004A0, "Member 'AUltra_Dynamic_Sky_C::Soften_Cloud_Layer_1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Soften_Cloud_Layer_2) == 0x0004A8, "Member 'AUltra_Dynamic_Sky_C::Soften_Cloud_Layer_2' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sharpen_Outer_Edge) == 0x0004B0, "Member 'AUltra_Dynamic_Sky_C::Sharpen_Outer_Edge' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Stars_Intensity) == 0x0004B8, "Member 'AUltra_Dynamic_Sky_C::Stars_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Stars_Color) == 0x0004C0, "Member 'AUltra_Dynamic_Sky_C::Stars_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Scale) == 0x0004D0, "Member 'AUltra_Dynamic_Sky_C::Sun_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Shadows_MID) == 0x0004D8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Shadows_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Use_Cloud_Shadows) == 0x0004E0, "Member 'AUltra_Dynamic_Sky_C::Use_Cloud_Shadows' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Shadows_Intensity_when_Sunny) == 0x0004E8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Shadows_Intensity_when_Sunny' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Stars_Speed) == 0x0004F0, "Member 'AUltra_Dynamic_Sky_C::Stars_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, New_Moon_Light_Brightness) == 0x0004F8, "Member 'AUltra_Dynamic_Sky_C::New_Moon_Light_Brightness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Source_Angle_Scale) == 0x000500, "Member 'AUltra_Dynamic_Sky_C::Moon_Source_Angle_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Texture_Rotation) == 0x000508, "Member 'AUltra_Dynamic_Sky_C::Moon_Texture_Rotation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Mode) == 0x000510, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Use_Auroras) == 0x000511, "Member 'AUltra_Dynamic_Sky_C::Use_Auroras' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Aurora_Intensity) == 0x000518, "Member 'AUltra_Dynamic_Sky_C::Aurora_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Aurora_Speed) == 0x000520, "Member 'AUltra_Dynamic_Sky_C::Aurora_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Shadows_Intensity_when_Overcast) == 0x000528, "Member 'AUltra_Dynamic_Sky_C::Cloud_Shadows_Intensity_when_Overcast' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Shadows_Softness_When_Sunny) == 0x000530, "Member 'AUltra_Dynamic_Sky_C::Cloud_Shadows_Softness_When_Sunny' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Shadows_Softness_when_Overcast) == 0x000538, "Member 'AUltra_Dynamic_Sky_C::Cloud_Shadows_Softness_when_Overcast' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Tiling) == 0x000540, "Member 'AUltra_Dynamic_Sky_C::Cloud_Tiling' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Soften_Horizon) == 0x000548, "Member 'AUltra_Dynamic_Sky_C::Soften_Horizon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, One_Cloud_Layer) == 0x000550, "Member 'AUltra_Dynamic_Sky_C::One_Cloud_Layer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Height) == 0x000558, "Member 'AUltra_Dynamic_Sky_C::Cloud_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Overcast_Swirling_Texture) == 0x000560, "Member 'AUltra_Dynamic_Sky_C::Overcast_Swirling_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Saturation) == 0x000568, "Member 'AUltra_Dynamic_Sky_C::Saturation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Directional_Balance) == 0x000570, "Member 'AUltra_Dynamic_Sky_C::Directional_Balance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dawn_Time) == 0x000578, "Member 'AUltra_Dynamic_Sky_C::Dawn_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusk_Time) == 0x000580, "Member 'AUltra_Dynamic_Sky_C::Dusk_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Night_Brightness) == 0x000588, "Member 'AUltra_Dynamic_Sky_C::Night_Brightness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Softness) == 0x000590, "Member 'AUltra_Dynamic_Sky_C::Sun_Softness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Aurora_Phase) == 0x000598, "Member 'AUltra_Dynamic_Sky_C::Aurora_Phase' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_LightComponent) == 0x0005A0, "Member 'AUltra_Dynamic_Sky_C::Sun_LightComponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_LightComponent) == 0x0005A8, "Member 'AUltra_Dynamic_Sky_C::Moon_LightComponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, SkyLightComponent) == 0x0005B0, "Member 'AUltra_Dynamic_Sky_C::SkyLightComponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Height_Fog_Component) == 0x0005B8, "Member 'AUltra_Dynamic_Sky_C::Height_Fog_Component' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Apply_Exposure_Settings) == 0x0005C0, "Member 'AUltra_Dynamic_Sky_C::Apply_Exposure_Settings' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Exposure_Metering_Mode) == 0x0005C1, "Member 'AUltra_Dynamic_Sky_C::Exposure_Metering_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Exposure_Compensation_Curve) == 0x0005C8, "Member 'AUltra_Dynamic_Sky_C::Exposure_Compensation_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Animate_Time_of_Day) == 0x0005D0, "Member 'AUltra_Dynamic_Sky_C::Animate_Time_of_Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Day_Length) == 0x0005D8, "Member 'AUltra_Dynamic_Sky_C::Day_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Night_Length) == 0x0005E0, "Member 'AUltra_Dynamic_Sky_C::Night_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Extend_Dawn_And_Dusk) == 0x0005E8, "Member 'AUltra_Dynamic_Sky_C::Extend_Dawn_And_Dusk' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Base_Fog_Density) == 0x0005F0, "Member 'AUltra_Dynamic_Sky_C::Base_Fog_Density' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Foggy_Density_Contribution) == 0x0005F8, "Member 'AUltra_Dynamic_Sky_C::Foggy_Density_Contribution' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloudy_Density_Contribution) == 0x000600, "Member 'AUltra_Dynamic_Sky_C::Cloudy_Density_Contribution' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dust_Density_Contribution) == 0x000608, "Member 'AUltra_Dynamic_Sky_C::Dust_Density_Contribution' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Stars_Tiling) == 0x000610, "Member 'AUltra_Dynamic_Sky_C::Stars_Tiling' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Stars_Daytime_Intensity) == 0x000618, "Member 'AUltra_Dynamic_Sky_C::Stars_Daytime_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Twinkle_Amount) == 0x000620, "Member 'AUltra_Dynamic_Sky_C::Twinkle_Amount' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Twinkle_Speed) == 0x000628, "Member 'AUltra_Dynamic_Sky_C::Twinkle_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Tiling_Stars_Texture) == 0x000630, "Member 'AUltra_Dynamic_Sky_C::Tiling_Stars_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Real_Stars_Texture) == 0x000658, "Member 'AUltra_Dynamic_Sky_C::Real_Stars_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Stars_Phase) == 0x000680, "Member 'AUltra_Dynamic_Sky_C::Stars_Phase' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Intensity) == 0x000688, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Mobility) == 0x000690, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Mobility' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Temperature) == 0x000698, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Temperature' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Color_Multiplier__Day_) == 0x0006A0, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Color_Multiplier__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusk_) == 0x0006B0, "Member 'AUltra_Dynamic_Sky_C::Dusk_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Color_Multiplier__Night_) == 0x0006C0, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Color_Multiplier__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Real_Time_Capture) == 0x0006D0, "Member 'AUltra_Dynamic_Sky_C::Real_Time_Capture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Move_Sky_Light_Capture_with_Camera_Location) == 0x0006D1, "Member 'AUltra_Dynamic_Sky_C::Move_Sky_Light_Capture_with_Camera_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Lower_Hemisphere_Tint__Capture_Based_) == 0x0006D4, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Lower_Hemisphere_Tint__Capture_Based_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Lower_Hemisphere_Tint__Cubemap_) == 0x0006E4, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Lower_Hemisphere_Tint__Cubemap_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Cubemap) == 0x0006F8, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Cubemap' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Cubemap_Angle) == 0x000720, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Cubemap_Angle' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Sky_Light) == 0x000728, "Member 'AUltra_Dynamic_Sky_C::Render_Sky_Light' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Custom_Sky_Light_Actor) == 0x000730, "Member 'AUltra_Dynamic_Sky_C::Custom_Sky_Light_Actor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Real_Time_Capture_Uses_Time_Slicing) == 0x000738, "Member 'AUltra_Dynamic_Sky_C::Real_Time_Capture_Uses_Time_Slicing' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Stationary_Sky_Light_Casts_Shadows) == 0x000739, "Member 'AUltra_Dynamic_Sky_C::Stationary_Sky_Light_Casts_Shadows' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Movable_Sky_Light_Casts_Shadows__Enable_DFAO_) == 0x00073A, "Member 'AUltra_Dynamic_Sky_C::Movable_Sky_Light_Casts_Shadows__Enable_DFAO_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Recapture_Sky_Light_Periodically) == 0x00073B, "Member 'AUltra_Dynamic_Sky_C::Recapture_Sky_Light_Periodically' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Recapture_Period__Seconds_) == 0x000740, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Recapture_Period__Seconds_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Random_Starting_Time) == 0x000748, "Member 'AUltra_Dynamic_Sky_C::Random_Starting_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sunrise_Event_Offset) == 0x000750, "Member 'AUltra_Dynamic_Sky_C::Sunrise_Event_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sunset_Event_Offset) == 0x000758, "Member 'AUltra_Dynamic_Sky_C::Sunset_Event_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sunset) == 0x000760, "Member 'AUltra_Dynamic_Sky_C::Sunset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sunrise) == 0x000770, "Member 'AUltra_Dynamic_Sky_C::Sunrise' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Wisps_Opacity__Cloudy_) == 0x000780, "Member 'AUltra_Dynamic_Sky_C::Cloud_Wisps_Opacity__Cloudy_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Wisps_Texture) == 0x000788, "Member 'AUltra_Dynamic_Sky_C::Cloud_Wisps_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Dynamic_Cloud_Formation_Texture) == 0x000790, "Member 'AUltra_Dynamic_Sky_C::TwoD_Dynamic_Cloud_Formation_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Shader_Intensity) == 0x0007B8, "Member 'AUltra_Dynamic_Sky_C::Sun_Shader_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Directional_Lighting_Intensity) == 0x0007C0, "Member 'AUltra_Dynamic_Sky_C::Directional_Lighting_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Highlight_Radius_Scale) == 0x0007C8, "Member 'AUltra_Dynamic_Sky_C::Sun_Highlight_Radius_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_opacity) == 0x0007D0, "Member 'AUltra_Dynamic_Sky_C::Cloud_opacity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Horizon_Density_Multiplier) == 0x0007D8, "Member 'AUltra_Dynamic_Sky_C::Horizon_Density_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Zenith_Density_Multiplier) == 0x0007E0, "Member 'AUltra_Dynamic_Sky_C::Zenith_Density_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Latitude_Gradient_Position) == 0x0007E8, "Member 'AUltra_Dynamic_Sky_C::Latitude_Gradient_Position' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Latitude_Gradient_Width) == 0x0007F0, "Member 'AUltra_Dynamic_Sky_C::Latitude_Gradient_Width' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Around_Sun_Density_Multiplier) == 0x0007F8, "Member 'AUltra_Dynamic_Sky_C::Around_Sun_Density_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Around_Sun_Density_Exponent) == 0x000800, "Member 'AUltra_Dynamic_Sky_C::Around_Sun_Density_Exponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Around_Moon_Density_Multiplier) == 0x000808, "Member 'AUltra_Dynamic_Sky_C::Around_Moon_Density_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Around_Moon_Density_Exponent) == 0x000810, "Member 'AUltra_Dynamic_Sky_C::Around_Moon_Density_Exponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Clouds_Shading_Offset) == 0x000818, "Member 'AUltra_Dynamic_Sky_C::TwoD_Clouds_Shading_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Shine_Intensity_Curve) == 0x000820, "Member 'AUltra_Dynamic_Sky_C::Shine_Intensity_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Highlight_Intensity_Curve) == 0x000828, "Member 'AUltra_Dynamic_Sky_C::Sun_Highlight_Intensity_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Highlight_Radius_Curve) == 0x000830, "Member 'AUltra_Dynamic_Sky_C::Sun_Highlight_Radius_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Adjust_for_Path_Tracer) == 0x000838, "Member 'AUltra_Dynamic_Sky_C::Adjust_for_Path_Tracer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lights_Update_Period) == 0x000840, "Member 'AUltra_Dynamic_Sky_C::Lights_Update_Period' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lights_Update_Degree_Threshold) == 0x000848, "Member 'AUltra_Dynamic_Sky_C::Lights_Update_Degree_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dim_Directional_Lights_with_Cloud_Coverage) == 0x000850, "Member 'AUltra_Dynamic_Sky_C::Dim_Directional_Lights_with_Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Extra_Night_Brightness_when_Cloudy) == 0x000858, "Member 'AUltra_Dynamic_Sky_C::Extra_Night_Brightness_when_Cloudy' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Directional_Lights_Absent_Brightness) == 0x000860, "Member 'AUltra_Dynamic_Sky_C::Directional_Lights_Absent_Brightness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Vol_Clouds_MID_Complex) == 0x000868, "Member 'AUltra_Dynamic_Sky_C::Vol_Clouds_MID_Complex' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Rendering_Mode) == 0x000870, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Rendering_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Bottom_Altitude) == 0x000878, "Member 'AUltra_Dynamic_Sky_C::Bottom_Altitude' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Clouds_Scale) == 0x000880, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Clouds_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Layer_Height_Scale) == 0x000888, "Member 'AUltra_Dynamic_Sky_C::Layer_Height_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Formation_Texture_Scale) == 0x000890, "Member 'AUltra_Dynamic_Sky_C::Cloud_Formation_Texture_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, ThreeD_Noise_Scale) == 0x000898, "Member 'AUltra_Dynamic_Sky_C::ThreeD_Noise_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, ThreeD_Erosion_Intensity) == 0x0008A0, "Member 'AUltra_Dynamic_Sky_C::ThreeD_Erosion_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, High_Frequency_Noise_Amount) == 0x0008A8, "Member 'AUltra_Dynamic_Sky_C::High_Frequency_Noise_Amount' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, High_Frequency_Levels) == 0x0008B0, "Member 'AUltra_Dynamic_Sky_C::High_Frequency_Levels' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Distort_High_Frequency_Noise) == 0x0008B8, "Member 'AUltra_Dynamic_Sky_C::Distort_High_Frequency_Noise' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Extinction_Scale) == 0x0008C0, "Member 'AUltra_Dynamic_Sky_C::Extinction_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Base_Floor_Variation) == 0x0008C8, "Member 'AUltra_Dynamic_Sky_C::Base_Floor_Variation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Overcast_Floor_Variation) == 0x0008D0, "Member 'AUltra_Dynamic_Sky_C::Overcast_Floor_Variation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Floor_Luminance_Scale) == 0x0008D8, "Member 'AUltra_Dynamic_Sky_C::Floor_Luminance_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, View_Sample_Scale__Day_) == 0x0008E0, "Member 'AUltra_Dynamic_Sky_C::View_Sample_Scale__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, View_Sample_Scale__Night_) == 0x0008E8, "Member 'AUltra_Dynamic_Sky_C::View_Sample_Scale__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Shadow_Sample_Scale) == 0x0008F0, "Member 'AUltra_Dynamic_Sky_C::Shadow_Sample_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Shadow_Tracing_Distance) == 0x0008F8, "Member 'AUltra_Dynamic_Sky_C::Shadow_Tracing_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Tracing_Max_Distance__Inside_Cloud_Layer_) == 0x000900, "Member 'AUltra_Dynamic_Sky_C::Tracing_Max_Distance__Inside_Cloud_Layer_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Tracing_Max_Distance__Outside_Cloud_Layer_) == 0x000908, "Member 'AUltra_Dynamic_Sky_C::Tracing_Max_Distance__Outside_Cloud_Layer_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Color) == 0x000910, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Profile_LUT) == 0x000920, "Member 'AUltra_Dynamic_Sky_C::Cloud_Profile_LUT' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Formation_Texture) == 0x000948, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Formation_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Minimum_Erosion) == 0x000970, "Member 'AUltra_Dynamic_Sky_C::Minimum_Erosion' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Distance_to_Sample_MaxCount) == 0x000978, "Member 'AUltra_Dynamic_Sky_C::Distance_to_Sample_MaxCount' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Formation_Texture_Mip_Level) == 0x000980, "Member 'AUltra_Dynamic_Sky_C::Cloud_Formation_Texture_Mip_Level' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Reflection_Sample_Count_Scale) == 0x000988, "Member 'AUltra_Dynamic_Sky_C::Reflection_Sample_Count_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Reflection_Shadow_Sample_Count_Scale) == 0x000990, "Member 'AUltra_Dynamic_Sky_C::Reflection_Shadow_Sample_Count_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Ambient_Light_Intensity) == 0x000998, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Ambient_Light_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Ambient_Light__Day_) == 0x0009A0, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Ambient_Light__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusk__0) == 0x0009B0, "Member 'AUltra_Dynamic_Sky_C::Dusk__0' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Ambient_Light__Night_) == 0x0009C0, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Ambient_Light__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Ambient_Light_Saturation) == 0x0009D0, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Ambient_Light_Saturation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Multiscattering_Light_Intensity) == 0x0009D8, "Member 'AUltra_Dynamic_Sky_C::Multiscattering_Light_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Multiscattering_Light_Occlusion) == 0x0009E0, "Member 'AUltra_Dynamic_Sky_C::Multiscattering_Light_Occlusion' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Vol_Clouds_MID_Simple) == 0x0009E8, "Member 'AUltra_Dynamic_Sky_C::Vol_Clouds_MID_Simple' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Scale_Fog_Density) == 0x0009F0, "Member 'AUltra_Dynamic_Sky_C::Scale_Fog_Density' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Start_Distance_when_Clear) == 0x0009F8, "Member 'AUltra_Dynamic_Sky_C::Fog_Start_Distance_when_Clear' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Density_Where_Start_Distance_Reaches_Zero) == 0x000A00, "Member 'AUltra_Dynamic_Sky_C::Fog_Density_Where_Start_Distance_Reaches_Zero' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Formation_Change_Speed) == 0x000A08, "Member 'AUltra_Dynamic_Sky_C::Formation_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Randomize_Cloud_Formation_on_Run) == 0x000A0C, "Member 'AUltra_Dynamic_Sky_C::Randomize_Cloud_Formation_on_Run' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, ThreeD_Noise_Vertical_Movement) == 0x000A10, "Member 'AUltra_Dynamic_Sky_C::ThreeD_Noise_Vertical_Movement' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Macro_Variation) == 0x000A18, "Member 'AUltra_Dynamic_Sky_C::Macro_Variation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Macro_Scale) == 0x000A20, "Member 'AUltra_Dynamic_Sky_C::Macro_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Multiscattering_Phase_1) == 0x000A28, "Member 'AUltra_Dynamic_Sky_C::Multiscattering_Phase_1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Multiscattering_Phase_2) == 0x000A30, "Member 'AUltra_Dynamic_Sky_C::Multiscattering_Phase_2' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Multiscattering_Eccentricity) == 0x000A38, "Member 'AUltra_Dynamic_Sky_C::Multiscattering_Eccentricity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_Event_Dispatcher_Check_Period) == 0x000A40, "Member 'AUltra_Dynamic_Sky_C::Time_Event_Dispatcher_Check_Period' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_Speed) == 0x000A48, "Member 'AUltra_Dynamic_Sky_C::Time_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_Cycle_Degrees) == 0x000A50, "Member 'AUltra_Dynamic_Sky_C::Time_Cycle_Degrees' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ultra_Dynamic_Weather) == 0x000A58, "Member 'AUltra_Dynamic_Sky_C::Ultra_Dynamic_Weather' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Tracing_Max_Start_Distance) == 0x000A60, "Member 'AUltra_Dynamic_Sky_C::Tracing_Max_Start_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Aurora_Shape_Change_Speed) == 0x000A68, "Member 'AUltra_Dynamic_Sky_C::Aurora_Shape_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Aurora_Texture_Scale) == 0x000A70, "Member 'AUltra_Dynamic_Sky_C::Aurora_Texture_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Daytime_Aurora_Intensity) == 0x000A78, "Member 'AUltra_Dynamic_Sky_C::Daytime_Aurora_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Aurora_Color_1) == 0x000A80, "Member 'AUltra_Dynamic_Sky_C::Aurora_Color_1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Aurora_Color_2) == 0x000A90, "Member 'AUltra_Dynamic_Sky_C::Aurora_Color_2' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Aurora_Color_3) == 0x000AA0, "Member 'AUltra_Dynamic_Sky_C::Aurora_Color_3' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Aurora_MID) == 0x000AB0, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Aurora_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Half_Rate_Tick) == 0x000AB8, "Member 'AUltra_Dynamic_Sky_C::Half_Rate_Tick' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Half_Rate_Tick_Framerate_Threshold) == 0x000ABC, "Member 'AUltra_Dynamic_Sky_C::Half_Rate_Tick_Framerate_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Disable_All_Runtime_Updating) == 0x000AC0, "Member 'AUltra_Dynamic_Sky_C::Disable_All_Runtime_Updating' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Use_Sky_Mode_Scalability_Map) == 0x000AC1, "Member 'AUltra_Dynamic_Sky_C::Use_Sky_Mode_Scalability_Map' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Mode_Scalability_Map) == 0x000AC8, "Member 'AUltra_Dynamic_Sky_C::Sky_Mode_Scalability_Map' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Disk_Intensity) == 0x000B18, "Member 'AUltra_Dynamic_Sky_C::Sun_Disk_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Inside_Clouds_Fog_MID) == 0x000B20, "Member 'AUltra_Dynamic_Sky_C::Inside_Clouds_Fog_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Enable_Fog_Inside_Clouds) == 0x000B28, "Member 'AUltra_Dynamic_Sky_C::Enable_Fog_Inside_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Aurora_Texture) == 0x000B30, "Member 'AUltra_Dynamic_Sky_C::Aurora_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Aurora_Max_Samples) == 0x000B58, "Member 'AUltra_Dynamic_Sky_C::TwoD_Aurora_Max_Samples' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Aurora_Sample_Step_Size) == 0x000B60, "Member 'AUltra_Dynamic_Sky_C::TwoD_Aurora_Sample_Step_Size' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Aurora_Line_Exponent) == 0x000B68, "Member 'AUltra_Dynamic_Sky_C::TwoD_Aurora_Line_Exponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Aurora_Mask_Exponent) == 0x000B70, "Member 'AUltra_Dynamic_Sky_C::TwoD_Aurora_Mask_Exponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Aurora_Sample_Count_Scale) == 0x000B78, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Aurora_Sample_Count_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Aurora_Layer_Height) == 0x000B80, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Aurora_Layer_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Aurora_Bottom_Altitude) == 0x000B88, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Aurora_Bottom_Altitude' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Density_Daytime_Mutliplier) == 0x000B90, "Member 'AUltra_Dynamic_Sky_C::Fog_Density_Daytime_Mutliplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Density_Nighttime_Multiplier) == 0x000B98, "Member 'AUltra_Dynamic_Sky_C::Fog_Density_Nighttime_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Base_Height_Fog_Falloff) == 0x000BA0, "Member 'AUltra_Dynamic_Sky_C::Base_Height_Fog_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloudy_Height_Fog_Falloff) == 0x000BA8, "Member 'AUltra_Dynamic_Sky_C::Cloudy_Height_Fog_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Foggy_Height_Fog_Falloff) == 0x000BB0, "Member 'AUltra_Dynamic_Sky_C::Foggy_Height_Fog_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Inside_Cloud_Fog_Strength) == 0x000BB8, "Member 'AUltra_Dynamic_Sky_C::Inside_Cloud_Fog_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Inside_Cloud_Fog_Color) == 0x000BC0, "Member 'AUltra_Dynamic_Sky_C::Inside_Cloud_Fog_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Overcast_Turbulence) == 0x000BD0, "Member 'AUltra_Dynamic_Sky_C::TwoD_Overcast_Turbulence' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Close_Fade_Distance) == 0x000BD8, "Member 'AUltra_Dynamic_Sky_C::Close_Fade_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Close_Fade_Offset) == 0x000BE0, "Member 'AUltra_Dynamic_Sky_C::Close_Fade_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Two_Layers) == 0x000BE8, "Member 'AUltra_Dynamic_Sky_C::Two_Layers' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Layer_2_Coverage_Scale) == 0x000BF0, "Member 'AUltra_Dynamic_Sky_C::Layer_2_Coverage_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Layer_2_Extinction_Scale) == 0x000BF8, "Member 'AUltra_Dynamic_Sky_C::Layer_2_Extinction_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Layer_2_Cloud_Scale) == 0x000C00, "Member 'AUltra_Dynamic_Sky_C::Layer_2_Cloud_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Layer_2_Mip_Offset) == 0x000C10, "Member 'AUltra_Dynamic_Sky_C::Layer_2_Mip_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Distance_Between_Layers) == 0x000C18, "Member 'AUltra_Dynamic_Sky_C::Distance_Between_Layers' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Enable_Sun_Light_Shaft_Bloom) == 0x000C20, "Member 'AUltra_Dynamic_Sky_C::Enable_Sun_Light_Shaft_Bloom' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Light_Shaft_Bloom_Scale) == 0x000C28, "Member 'AUltra_Dynamic_Sky_C::Sun_Light_Shaft_Bloom_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Light_Shaft_Max_Brightness) == 0x000C38, "Member 'AUltra_Dynamic_Sky_C::Sun_Light_Shaft_Max_Brightness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Light_Shaft_Bloom_Threshold) == 0x000C48, "Member 'AUltra_Dynamic_Sky_C::Sun_Light_Shaft_Bloom_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Light_Shaft_Tint_Color) == 0x000C58, "Member 'AUltra_Dynamic_Sky_C::Sun_Light_Shaft_Tint_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Enable_Moon_Light_Shaft_Bloom) == 0x000C68, "Member 'AUltra_Dynamic_Sky_C::Enable_Moon_Light_Shaft_Bloom' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Light_Shaft_Bloom_Scale) == 0x000C70, "Member 'AUltra_Dynamic_Sky_C::Moon_Light_Shaft_Bloom_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Light_Shaft_Max_Brightness) == 0x000C80, "Member 'AUltra_Dynamic_Sky_C::Moon_Light_Shaft_Max_Brightness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Light_Shaft_Bloom_Threshold) == 0x000C90, "Member 'AUltra_Dynamic_Sky_C::Moon_Light_Shaft_Bloom_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Light_Shaft_Tint_Color) == 0x000CA0, "Member 'AUltra_Dynamic_Sky_C::Moon_Light_Shaft_Tint_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Clouds_Replacement) == 0x000CB0, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Clouds_Replacement' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Real_Time_Capture_Sky_Light_Replacement) == 0x000CB1, "Member 'AUltra_Dynamic_Sky_C::Real_Time_Capture_Sky_Light_Replacement' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Aurora_Replacement) == 0x000CB2, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Aurora_Replacement' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Platform_Feature_Levels) == 0x000CB8, "Member 'AUltra_Dynamic_Sky_C::Platform_Feature_Levels' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Simulate_Real_Sun) == 0x000D08, "Member 'AUltra_Dynamic_Sky_C::Simulate_Real_Sun' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Simulate_Real_Moon) == 0x000D09, "Member 'AUltra_Dynamic_Sky_C::Simulate_Real_Moon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Simulate_Real_Stars) == 0x000D0A, "Member 'AUltra_Dynamic_Sky_C::Simulate_Real_Stars' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Location_Preset) == 0x000D0B, "Member 'AUltra_Dynamic_Sky_C::Location_Preset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Latitude) == 0x000D10, "Member 'AUltra_Dynamic_Sky_C::Latitude' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Longitude) == 0x000D18, "Member 'AUltra_Dynamic_Sky_C::Longitude' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_Zone) == 0x000D20, "Member 'AUltra_Dynamic_Sky_C::Time_Zone' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Year) == 0x000D28, "Member 'AUltra_Dynamic_Sky_C::Year' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Month) == 0x000D2C, "Member 'AUltra_Dynamic_Sky_C::Month' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Day) == 0x000D30, "Member 'AUltra_Dynamic_Sky_C::Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, North_Yaw) == 0x000D38, "Member 'AUltra_Dynamic_Sky_C::North_Yaw' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Simulation_Speed) == 0x000D40, "Member 'AUltra_Dynamic_Sky_C::Simulation_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Use_System_Time) == 0x000D48, "Member 'AUltra_Dynamic_Sky_C::Use_System_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Apply_Daylight_Savings_Time) == 0x000D49, "Member 'AUltra_Dynamic_Sky_C::Apply_Daylight_Savings_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Calendar) == 0x000D50, "Member 'AUltra_Dynamic_Sky_C::Calendar' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, DST_Start_Month) == 0x000D58, "Member 'AUltra_Dynamic_Sky_C::DST_Start_Month' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, DST_Start_Day) == 0x000D5C, "Member 'AUltra_Dynamic_Sky_C::DST_Start_Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, DST_End_Month) == 0x000D60, "Member 'AUltra_Dynamic_Sky_C::DST_End_Month' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, DST_End_Day) == 0x000D64, "Member 'AUltra_Dynamic_Sky_C::DST_End_Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, DST_Change_Hour) == 0x000D68, "Member 'AUltra_Dynamic_Sky_C::DST_Change_Hour' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Stellar_Calibration) == 0x000D70, "Member 'AUltra_Dynamic_Sky_C::Stellar_Calibration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lunar_Orbit_Calibration) == 0x000D78, "Member 'AUltra_Dynamic_Sky_C::Lunar_Orbit_Calibration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lunar_Inclination_Calibration) == 0x000D80, "Member 'AUltra_Dynamic_Sky_C::Lunar_Inclination_Calibration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Daylight_Savings_Time) == 0x000D88, "Member 'AUltra_Dynamic_Sky_C::Daylight_Savings_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Total_Days_Elapsed) == 0x000D8C, "Member 'AUltra_Dynamic_Sky_C::Total_Days_Elapsed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Clouds_Move_with_Time_of_Day) == 0x000D90, "Member 'AUltra_Dynamic_Sky_C::Clouds_Move_with_Time_of_Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_of_Day_Movement_Multiplier) == 0x000D98, "Member 'AUltra_Dynamic_Sky_C::Time_of_Day_Movement_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_MID_Parent_Instances) == 0x000DA0, "Member 'AUltra_Dynamic_Sky_C::Sky_MID_Parent_Instances' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Shadows) == 0x000DF0, "Member 'AUltra_Dynamic_Sky_C::Fog_Shadows' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dust_Shadows) == 0x000DF8, "Member 'AUltra_Dynamic_Sky_C::Dust_Shadows' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Cloud_Shadows_Speed) == 0x000E00, "Member 'AUltra_Dynamic_Sky_C::TwoD_Cloud_Shadows_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Cloud_Shadows_Scale) == 0x000E08, "Member 'AUltra_Dynamic_Sky_C::TwoD_Cloud_Shadows_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Disable_Directional_Light_Shadows_When_Fully_Shadowed_By_Clouds) == 0x000E10, "Member 'AUltra_Dynamic_Sky_C::Disable_Directional_Light_Shadows_When_Fully_Shadowed_By_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Custom_Light_Function_Material) == 0x000E18, "Member 'AUltra_Dynamic_Sky_C::Custom_Light_Function_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Force_Light_Functions_On) == 0x000E20, "Member 'AUltra_Dynamic_Sky_C::Force_Light_Functions_On' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_Of_Day_Replication_Period) == 0x000E28, "Member 'AUltra_Dynamic_Sky_C::Time_Of_Day_Replication_Period' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Replicated_Time_of_Day) == 0x000E30, "Member 'AUltra_Dynamic_Sky_C::Replicated_Time_of_Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Moon_Directional_Light) == 0x000E38, "Member 'AUltra_Dynamic_Sky_C::Render_Moon_Directional_Light' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Custom_Moon_Light_Actor) == 0x000E40, "Member 'AUltra_Dynamic_Sky_C::Custom_Moon_Light_Actor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Scale_Moon_Radius_As_It_Nears_Horizon) == 0x000E48, "Member 'AUltra_Dynamic_Sky_C::Scale_Moon_Radius_As_It_Nears_Horizon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Colors_from_Sky_Atmosphere) == 0x000E50, "Member 'AUltra_Dynamic_Sky_C::Fog_Colors_from_Sky_Atmosphere' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusty_Height_Fog_Falloff) == 0x000E58, "Member 'AUltra_Dynamic_Sky_C::Dusty_Height_Fog_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Specular_Scale) == 0x000E60, "Member 'AUltra_Dynamic_Sky_C::Moon_Specular_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ambient_Light_From_Sky_Atmosphere) == 0x000E68, "Member 'AUltra_Dynamic_Sky_C::Ambient_Light_From_Sky_Atmosphere' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Ambient_Light_Bottom_Scale) == 0x000E70, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Ambient_Light_Bottom_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Ambient_Light_Top_Scale) == 0x000E78, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Ambient_Light_Top_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, High_Frequency_Disable_Distance) == 0x000E80, "Member 'AUltra_Dynamic_Sky_C::High_Frequency_Disable_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Change_To_Simplified_Material_When_Overcast) == 0x000E88, "Member 'AUltra_Dynamic_Sky_C::Change_To_Simplified_Material_When_Overcast' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Simplified_Material_Cloud_Coverage_Threshold) == 0x000E90, "Member 'AUltra_Dynamic_Sky_C::Simplified_Material_Cloud_Coverage_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Simplified_Material_Fade_Length) == 0x000E98, "Member 'AUltra_Dynamic_Sky_C::Simplified_Material_Fade_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Scale_View_Samples_when_Overcast) == 0x000EA0, "Member 'AUltra_Dynamic_Sky_C::Scale_View_Samples_when_Overcast' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Current_Volumetric_Clouds_MID) == 0x000EA8, "Member 'AUltra_Dynamic_Sky_C::Current_Volumetric_Clouds_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Clouds_Position) == 0x000EB0, "Member 'AUltra_Dynamic_Sky_C::Clouds_Position' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Phase_Vector_Multiplier) == 0x000EC8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Phase_Vector_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Rayleigh_Scattering_Color__Day_) == 0x000EE0, "Member 'AUltra_Dynamic_Sky_C::Rayleigh_Scattering_Color__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusk__1) == 0x000EF0, "Member 'AUltra_Dynamic_Sky_C::Dusk__1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Rayleigh_Scattering_Color__Night_) == 0x000F00, "Member 'AUltra_Dynamic_Sky_C::Rayleigh_Scattering_Color__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Desaturate_Rayleigh_Scattering_when_Cloudy) == 0x000F10, "Member 'AUltra_Dynamic_Sky_C::Desaturate_Rayleigh_Scattering_when_Cloudy' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Current_Time_of_Day_Offset) == 0x000F18, "Member 'AUltra_Dynamic_Sky_C::Current_Time_of_Day_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Last_Frame_Time_of_Day) == 0x000F20, "Member 'AUltra_Dynamic_Sky_C::Last_Frame_Time_of_Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_of_Day_Change_Speed) == 0x000F28, "Member 'AUltra_Dynamic_Sky_C::Time_of_Day_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Properties) == 0x000F30, "Member 'AUltra_Dynamic_Sky_C::Properties' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Night_Sky_Glow) == 0x000F80, "Member 'AUltra_Dynamic_Sky_C::Night_Sky_Glow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Night_Sky_Glow_Color) == 0x000F88, "Member 'AUltra_Dynamic_Sky_C::Night_Sky_Glow_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Pollution_Intensity) == 0x000F98, "Member 'AUltra_Dynamic_Sky_C::Light_Pollution_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Pollution_Color) == 0x000FA0, "Member 'AUltra_Dynamic_Sky_C::Light_Pollution_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Fog_Post_Process_MID) == 0x000FB0, "Member 'AUltra_Dynamic_Sky_C::Cloud_Fog_Post_Process_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dimming_Range) == 0x000FB8, "Member 'AUltra_Dynamic_Sky_C::Dimming_Range' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dimming_Range_Exponent) == 0x000FC8, "Member 'AUltra_Dynamic_Sky_C::Dimming_Range_Exponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Custom_Sky_Sphere_Material) == 0x000FD0, "Member 'AUltra_Dynamic_Sky_C::Custom_Sky_Sphere_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Snow_Contribution) == 0x000FD8, "Member 'AUltra_Dynamic_Sky_C::Fog_Snow_Contribution' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Midnight) == 0x000FE0, "Member 'AUltra_Dynamic_Sky_C::Midnight' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sunrise_Color__Absorption_) == 0x000FF0, "Member 'AUltra_Dynamic_Sky_C::Sunrise_Color__Absorption_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sunrise_Color_Intensity__Absorption_Scale_) == 0x001000, "Member 'AUltra_Dynamic_Sky_C::Sunrise_Color_Intensity__Absorption_Scale_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Simulated_Sunrise_Time) == 0x001008, "Member 'AUltra_Dynamic_Sky_C::Simulated_Sunrise_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Simulated_Sunset_Time) == 0x001010, "Member 'AUltra_Dynamic_Sky_C::Simulated_Sunset_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Base_Cloud_Height) == 0x001018, "Member 'AUltra_Dynamic_Sky_C::Base_Cloud_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Enable_Sun_Lens_Flare) == 0x001020, "Member 'AUltra_Dynamic_Sky_C::Enable_Sun_Lens_Flare' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lens_Flare_Type) == 0x001021, "Member 'AUltra_Dynamic_Sky_C::Lens_Flare_Type' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lens_Flare_Strength) == 0x001028, "Member 'AUltra_Dynamic_Sky_C::Lens_Flare_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lens_Flare_Tint) == 0x001030, "Member 'AUltra_Dynamic_Sky_C::Lens_Flare_Tint' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lens_Flare_MID) == 0x001040, "Member 'AUltra_Dynamic_Sky_C::Lens_Flare_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lens_Flare_Brightness_Threshold) == 0x001048, "Member 'AUltra_Dynamic_Sky_C::Lens_Flare_Brightness_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Custom_Lens_Flare_Parent_Instance) == 0x001050, "Member 'AUltra_Dynamic_Sky_C::Custom_Lens_Flare_Parent_Instance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Scale_Flare_Elements) == 0x001058, "Member 'AUltra_Dynamic_Sky_C::Scale_Flare_Elements' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dynamic_Sky_Light_Transition_Time) == 0x001060, "Member 'AUltra_Dynamic_Sky_C::Dynamic_Sky_Light_Transition_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Target_Sky_Light_Multiplier) == 0x001068, "Member 'AUltra_Dynamic_Sky_C::Target_Sky_Light_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dynamic_Sky_Light_Multiplier) == 0x001070, "Member 'AUltra_Dynamic_Sky_C::Dynamic_Sky_Light_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Coverage_0_3) == 0x001078, "Member 'AUltra_Dynamic_Sky_C::Cloud_Coverage_0_3' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Global_Overcast_0_1) == 0x001080, "Member 'AUltra_Dynamic_Sky_C::Global_Overcast_0_1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Local_Overcast_0_1) == 0x001088, "Member 'AUltra_Dynamic_Sky_C::Local_Overcast_0_1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Override_with_New_Changes) == 0x001090, "Member 'AUltra_Dynamic_Sky_C::Override_with_New_Changes' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Overcast_Turbulence_MID) == 0x001098, "Member 'AUltra_Dynamic_Sky_C::Overcast_Turbulence_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Scale_View_Samples_when_Camera_is_In_Cloud_Layer) == 0x0010A0, "Member 'AUltra_Dynamic_Sky_C::Scale_View_Samples_when_Camera_is_In_Cloud_Layer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Low_Material_Quality_Sample_Count_Scale) == 0x0010A8, "Member 'AUltra_Dynamic_Sky_C::Low_Material_Quality_Sample_Count_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Medium_Material_Quality_Sample_Count_Scale) == 0x0010B0, "Member 'AUltra_Dynamic_Sky_C::Medium_Material_Quality_Sample_Count_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Epic_Material_Quality_Sample_Count_Scale) == 0x0010B8, "Member 'AUltra_Dynamic_Sky_C::Epic_Material_Quality_Sample_Count_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Epic_Quality_Volume_Texture) == 0x0010C0, "Member 'AUltra_Dynamic_Sky_C::Epic_Quality_Volume_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, High_Quality_Volume_Texture) == 0x0010E8, "Member 'AUltra_Dynamic_Sky_C::High_Quality_Volume_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Low_Quality_Volume_Texture) == 0x001110, "Member 'AUltra_Dynamic_Sky_C::Low_Quality_Volume_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Inside_Cloud_Fog_Parent_Material) == 0x001138, "Member 'AUltra_Dynamic_Sky_C::Inside_Cloud_Fog_Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Post_Process_Cloud_Fog_Parent_Material) == 0x001160, "Member 'AUltra_Dynamic_Sky_C::Post_Process_Cloud_Fog_Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_MIDs) == 0x001188, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_MIDs' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Light_Current_Intensity_Value) == 0x001198, "Member 'AUltra_Dynamic_Sky_C::Moon_Light_Current_Intensity_Value' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Twilight_Color__Absorption_) == 0x0011A0, "Member 'AUltra_Dynamic_Sky_C::Twilight_Color__Absorption_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Twilight_Color_Intensity__Absorption_Scale_) == 0x0011B0, "Member 'AUltra_Dynamic_Sky_C::Twilight_Color_Intensity__Absorption_Scale_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Base_Sky_Color__Day_) == 0x0011B8, "Member 'AUltra_Dynamic_Sky_C::Base_Sky_Color__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusk__2) == 0x0011C8, "Member 'AUltra_Dynamic_Sky_C::Dusk__2' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Base_Sky_Color__Night_) == 0x0011D8, "Member 'AUltra_Dynamic_Sky_C::Base_Sky_Color__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Light_Color__Day_) == 0x0011E8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Light_Color__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusk__3) == 0x0011F8, "Member 'AUltra_Dynamic_Sky_C::Dusk__3' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Light_Color__Night_) == 0x001208, "Member 'AUltra_Dynamic_Sky_C::Cloud_Light_Color__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Dark_Color__Day_) == 0x001218, "Member 'AUltra_Dynamic_Sky_C::Cloud_Dark_Color__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusk__4) == 0x001228, "Member 'AUltra_Dynamic_Sky_C::Dusk__4' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Dark_Color__Night_) == 0x001238, "Member 'AUltra_Dynamic_Sky_C::Cloud_Dark_Color__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Glow_Color) == 0x001248, "Member 'AUltra_Dynamic_Sky_C::Sun_Glow_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Glow_Tint) == 0x001258, "Member 'AUltra_Dynamic_Sky_C::Moon_Glow_Tint' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Directional_Intensity_Curve) == 0x001268, "Member 'AUltra_Dynamic_Sky_C::Directional_Intensity_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Directional_Light_Scattering_Curve) == 0x001270, "Member 'AUltra_Dynamic_Sky_C::Directional_Light_Scattering_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Atmospheric_Scattering_LUT_Texture) == 0x001278, "Member 'AUltra_Dynamic_Sky_C::Atmospheric_Scattering_LUT_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Exponential_Height_Fog) == 0x001280, "Member 'AUltra_Dynamic_Sky_C::Render_Exponential_Height_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Custom_Height_Fog_Actor) == 0x001288, "Member 'AUltra_Dynamic_Sky_C::Custom_Height_Fog_Actor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Color_Mode) == 0x001290, "Member 'AUltra_Dynamic_Sky_C::Fog_Color_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Color_Intensity_Scale) == 0x001298, "Member 'AUltra_Dynamic_Sky_C::Fog_Color_Intensity_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Overcast_Brightness_Day) == 0x0012A0, "Member 'AUltra_Dynamic_Sky_C::Overcast_Brightness_Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Overcast_Brightness_Night) == 0x0012A8, "Member 'AUltra_Dynamic_Sky_C::Overcast_Brightness_Night' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Atmosphere_Fog_Influence_Sun) == 0x0012B0, "Member 'AUltra_Dynamic_Sky_C::Sky_Atmosphere_Fog_Influence_Sun' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Atmosphere_Fog_Influence_Moon) == 0x0012B8, "Member 'AUltra_Dynamic_Sky_C::Sky_Atmosphere_Fog_Influence_Moon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Atmosphere_Fog_Influence_None) == 0x0012C0, "Member 'AUltra_Dynamic_Sky_C::Sky_Atmosphere_Fog_Influence_None' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Scattering_Color_Curve) == 0x0012C8, "Member 'AUltra_Dynamic_Sky_C::Fog_Scattering_Color_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Inscattering_Sun) == 0x0012D0, "Member 'AUltra_Dynamic_Sky_C::Fog_Inscattering_Sun' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Inscattering_Moon) == 0x0012E0, "Member 'AUltra_Dynamic_Sky_C::Fog_Inscattering_Moon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Directional_Scattering_Color_Curve) == 0x0012F0, "Member 'AUltra_Dynamic_Sky_C::Fog_Directional_Scattering_Color_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Directional_Inscattering) == 0x0012F8, "Member 'AUltra_Dynamic_Sky_C::Fog_Directional_Inscattering' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Foggy_Desaturation) == 0x001308, "Member 'AUltra_Dynamic_Sky_C::Foggy_Desaturation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Overcast_Desaturation) == 0x001310, "Member 'AUltra_Dynamic_Sky_C::Overcast_Desaturation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, All_Fog_Colors_Multiplier) == 0x001318, "Member 'AUltra_Dynamic_Sky_C::All_Fog_Colors_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dust_Amount) == 0x001328, "Member 'AUltra_Dynamic_Sky_C::Dust_Amount' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dust_Color) == 0x001330, "Member 'AUltra_Dynamic_Sky_C::Dust_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Use_Volumetric_Fog) == 0x001340, "Member 'AUltra_Dynamic_Sky_C::Use_Volumetric_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Fog_Distance) == 0x001348, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Fog_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Fog_Extinction) == 0x001350, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Fog_Extinction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Volumetric_Scattering_Intensity) == 0x001358, "Member 'AUltra_Dynamic_Sky_C::Sun_Volumetric_Scattering_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Volumetric_Scattering_Intensity) == 0x001360, "Member 'AUltra_Dynamic_Sky_C::Moon_Volumetric_Scattering_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Disable_Height_Fog_Above_Volumetric_Cloud_Layer) == 0x001368, "Member 'AUltra_Dynamic_Sky_C::Disable_Height_Fog_Above_Volumetric_Cloud_Layer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Height_Fog_Density) == 0x00136C, "Member 'AUltra_Dynamic_Sky_C::Cached_Height_Fog_Density' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_Cloud_Fog_Post_Process) == 0x001370, "Member 'AUltra_Dynamic_Sky_C::Using_Cloud_Fog_Post_Process' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Current_Camera_Location) == 0x001378, "Member 'AUltra_Dynamic_Sky_C::Current_Camera_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ambient_Fog_Color) == 0x001390, "Member 'AUltra_Dynamic_Sky_C::Ambient_Fog_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Directional_Base_Fog_Color) == 0x0013A0, "Member 'AUltra_Dynamic_Sky_C::Directional_Base_Fog_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Scaled_Directional_Base_Fog_Color) == 0x0013B0, "Member 'AUltra_Dynamic_Sky_C::Scaled_Directional_Base_Fog_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Aurora_Parent_Material) == 0x0013C0, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Aurora_Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_Inside_Cloud_Fog) == 0x0013E8, "Member 'AUltra_Dynamic_Sky_C::Using_Inside_Cloud_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Overcast_Turbulence_Parent_Material) == 0x0013F0, "Member 'AUltra_Dynamic_Sky_C::Overcast_Turbulence_Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Forward_Shading) == 0x001418, "Member 'AUltra_Dynamic_Sky_C::Forward_Shading' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Hourly) == 0x001420, "Member 'AUltra_Dynamic_Sky_C::Hourly' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Event_Hour) == 0x001430, "Member 'AUltra_Dynamic_Sky_C::Event_Hour' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Wisps_Color_Intensity) == 0x001438, "Member 'AUltra_Dynamic_Sky_C::Cloud_Wisps_Color_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Max_Moon_Source_Angle_Scale) == 0x001440, "Member 'AUltra_Dynamic_Sky_C::Max_Moon_Source_Angle_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transition_Time_A) == 0x001448, "Member 'AUltra_Dynamic_Sky_C::Transition_Time_A' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transition_Time_B) == 0x001450, "Member 'AUltra_Dynamic_Sky_C::Transition_Time_B' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transitioning_Time) == 0x001458, "Member 'AUltra_Dynamic_Sky_C::Transitioning_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transition_Easing_Function) == 0x001459, "Member 'AUltra_Dynamic_Sky_C::Transition_Easing_Function' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transition_Easing_Exponent) == 0x001460, "Member 'AUltra_Dynamic_Sky_C::Transition_Easing_Exponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transition_Timer) == 0x001468, "Member 'AUltra_Dynamic_Sky_C::Transition_Timer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transition_Duration) == 0x001470, "Member 'AUltra_Dynamic_Sky_C::Transition_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transition_Days_Counted) == 0x001478, "Member 'AUltra_Dynamic_Sky_C::Transition_Days_Counted' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transition_Can_Go_Backwards) == 0x00147C, "Member 'AUltra_Dynamic_Sky_C::Transition_Can_Go_Backwards' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Source_Angle_Softness) == 0x001480, "Member 'AUltra_Dynamic_Sky_C::Moon_Source_Angle_Softness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Exposure_Bias_Day) == 0x001488, "Member 'AUltra_Dynamic_Sky_C::Exposure_Bias_Day' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Exposure_Bias_Night) == 0x001490, "Member 'AUltra_Dynamic_Sky_C::Exposure_Bias_Night' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Exposure_Bias_Cloudy) == 0x001498, "Member 'AUltra_Dynamic_Sky_C::Exposure_Bias_Cloudy' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Exposure_Bias_Foggy) == 0x0014A0, "Member 'AUltra_Dynamic_Sky_C::Exposure_Bias_Foggy' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Exposure_Bias_Dusty) == 0x0014A8, "Member 'AUltra_Dynamic_Sky_C::Exposure_Bias_Dusty' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Exposure_Brightness_Range) == 0x0014B0, "Member 'AUltra_Dynamic_Sky_C::Exposure_Brightness_Range' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Apply_Interior_Adjustments) == 0x0014C0, "Member 'AUltra_Dynamic_Sky_C::Apply_Interior_Adjustments' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Extra_Fog_Start_Distance_In_Interior) == 0x0014C8, "Member 'AUltra_Dynamic_Sky_C::Extra_Fog_Start_Distance_In_Interior' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Density_Multiplier_in_Interior) == 0x0014D0, "Member 'AUltra_Dynamic_Sky_C::Fog_Density_Multiplier_in_Interior' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Exposure_Bias_in_Interior) == 0x0014D8, "Member 'AUltra_Dynamic_Sky_C::Exposure_Bias_in_Interior' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Intensity_Multiplier_in_Interiors) == 0x0014E0, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Intensity_Multiplier_in_Interiors' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Light_Intensity_Multiplier_In_Interiors) == 0x0014E8, "Member 'AUltra_Dynamic_Sky_C::Sun_Light_Intensity_Multiplier_In_Interiors' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Light_Intensity_Multiplier_in_Interiors) == 0x0014F0, "Member 'AUltra_Dynamic_Sky_C::Moon_Light_Intensity_Multiplier_in_Interiors' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Extra_Sun_Volumetric_Scattering_In_Interiors) == 0x0014F8, "Member 'AUltra_Dynamic_Sky_C::Extra_Sun_Volumetric_Scattering_In_Interiors' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Extra_Moon_Volumetric_Scattering_In_Interiors) == 0x001500, "Member 'AUltra_Dynamic_Sky_C::Extra_Moon_Volumetric_Scattering_In_Interiors' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Inverted_Global_Occlusion) == 0x001508, "Member 'AUltra_Dynamic_Sky_C::Cached_Inverted_Global_Occlusion' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Occlusion_Settings) == 0x001510, "Member 'AUltra_Dynamic_Sky_C::Occlusion_Settings' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Shadow_Disable_Threshold) == 0x001518, "Member 'AUltra_Dynamic_Sky_C::Cloud_Shadow_Disable_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Bottom_World_Height) == 0x001520, "Member 'AUltra_Dynamic_Sky_C::Cloud_Bottom_World_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Night_Filter) == 0x001528, "Member 'AUltra_Dynamic_Sky_C::Cached_Night_Filter' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Floats_Old) == 0x001530, "Member 'AUltra_Dynamic_Sky_C::Cached_Floats_Old' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Floats_New) == 0x001540, "Member 'AUltra_Dynamic_Sky_C::Cached_Floats_New' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Alpha) == 0x001550, "Member 'AUltra_Dynamic_Sky_C::Cache_Alpha' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Filling_Starting_Cache) == 0x001558, "Member 'AUltra_Dynamic_Sky_C::Filling_Starting_Cache' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Colors_Old) == 0x001560, "Member 'AUltra_Dynamic_Sky_C::Cached_Colors_Old' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Colors_New) == 0x001570, "Member 'AUltra_Dynamic_Sky_C::Cached_Colors_New' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Vectors_Old) == 0x001580, "Member 'AUltra_Dynamic_Sky_C::Cached_Vectors_Old' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Vectors_New) == 0x001590, "Member 'AUltra_Dynamic_Sky_C::Cached_Vectors_New' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Sun_Vector) == 0x0015A0, "Member 'AUltra_Dynamic_Sky_C::Cached_Sun_Vector' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Moon_Vector) == 0x0015B8, "Member 'AUltra_Dynamic_Sky_C::Cached_Moon_Vector' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Value_Changing) == 0x0015D0, "Member 'AUltra_Dynamic_Sky_C::Cached_Value_Changing' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Max_Property_Cache_Period) == 0x0015E0, "Member 'AUltra_Dynamic_Sky_C::Max_Property_Cache_Period' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Preset_Location_Coordinates) == 0x0015E8, "Member 'AUltra_Dynamic_Sky_C::Preset_Location_Coordinates' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Disable_Image_Based_Lens_Flares_when_Enabled) == 0x001638, "Member 'AUltra_Dynamic_Sky_C::Disable_Image_Based_Lens_Flares_when_Enabled' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Starting_Sky_Atmosphere_Height) == 0x001640, "Member 'AUltra_Dynamic_Sky_C::Starting_Sky_Atmosphere_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Post_Process_Components) == 0x001648, "Member 'AUltra_Dynamic_Sky_C::Post_Process_Components' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Static_Post_Process_Settings) == 0x001660, "Member 'AUltra_Dynamic_Sky_C::Static_Post_Process_Settings' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, User_Post_Process_Components) == 0x001D40, "Member 'AUltra_Dynamic_Sky_C::User_Post_Process_Components' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_Post_Process_Components) == 0x001D50, "Member 'AUltra_Dynamic_Sky_C::Using_Post_Process_Components' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Wisps_Tint__Day_) == 0x001D54, "Member 'AUltra_Dynamic_Sky_C::Cloud_Wisps_Tint__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusk__5) == 0x001D64, "Member 'AUltra_Dynamic_Sky_C::Dusk__5' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Wisps_Tint__Night_) == 0x001D74, "Member 'AUltra_Dynamic_Sky_C::Cloud_Wisps_Tint__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Increase_Wisps_Brightness_Around_Sun) == 0x001D88, "Member 'AUltra_Dynamic_Sky_C::Increase_Wisps_Brightness_Around_Sun' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Increase_Wisps_Brightness_Around_Moon) == 0x001D90, "Member 'AUltra_Dynamic_Sky_C::Increase_Wisps_Brightness_Around_Moon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Source_Angle_Scale) == 0x001D98, "Member 'AUltra_Dynamic_Sky_C::Sun_Source_Angle_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Solar_Eclipse) == 0x001DA0, "Member 'AUltra_Dynamic_Sky_C::Solar_Eclipse' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Solar_Eclipse_Tint_Color) == 0x001DA4, "Member 'AUltra_Dynamic_Sky_C::Solar_Eclipse_Tint_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Solar_Eclipse_Intensity_Multiplier) == 0x001DB8, "Member 'AUltra_Dynamic_Sky_C::Solar_Eclipse_Intensity_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Specular_Scale) == 0x001DC0, "Member 'AUltra_Dynamic_Sky_C::Sun_Specular_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Sun_Directional_Light) == 0x001DC8, "Member 'AUltra_Dynamic_Sky_C::Render_Sun_Directional_Light' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Custom_Sun_Light_Actor) == 0x001DD0, "Member 'AUltra_Dynamic_Sky_C::Custom_Sun_Light_Actor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Max_Sun_Source_Angle_Scale) == 0x001DD8, "Member 'AUltra_Dynamic_Sky_C::Max_Sun_Source_Angle_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Source_Angle_Softness) == 0x001DE0, "Member 'AUltra_Dynamic_Sky_C::Sun_Source_Angle_Softness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Scale_Sun_Radius_As_It_Nears_Horizon) == 0x001DE8, "Member 'AUltra_Dynamic_Sky_C::Scale_Sun_Radius_As_It_Nears_Horizon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fade_Down_High_Sun_Light_Intensity_Below_Horizon) == 0x001DF0, "Member 'AUltra_Dynamic_Sky_C::Fade_Down_High_Sun_Light_Intensity_Below_Horizon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Support_Sky_Atmo_Affecting_Height_Fog) == 0x001DF1, "Member 'AUltra_Dynamic_Sky_C::Support_Sky_Atmo_Affecting_Height_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, UDS_Version) == 0x001DF8, "Member 'AUltra_Dynamic_Sky_C::UDS_Version' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, UDS_Version_Info) == 0x001E08, "Member 'AUltra_Dynamic_Sky_C::UDS_Version_Info' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Clouds_B_Time) == 0x001E10, "Member 'AUltra_Dynamic_Sky_C::Clouds_B_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Atmosphere_Overcast_Luminance) == 0x001E18, "Member 'AUltra_Dynamic_Sky_C::Sky_Atmosphere_Overcast_Luminance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Keep_Planet_Top_At_Camera_XY_Location) == 0x001E20, "Member 'AUltra_Dynamic_Sky_C::Keep_Planet_Top_At_Camera_XY_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Control_Sky_Atmosphere_Settings) == 0x001E21, "Member 'AUltra_Dynamic_Sky_C::Control_Sky_Atmosphere_Settings' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Old_Composite_Weather) == 0x001E28, "Member 'AUltra_Dynamic_Sky_C::Old_Composite_Weather' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Old_Composite_Context) == 0x001E40, "Member 'AUltra_Dynamic_Sky_C::Old_Composite_Context' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Composite_Weather_Change_Speed) == 0x001E58, "Member 'AUltra_Dynamic_Sky_C::Composite_Weather_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Composite_Context_Change_Speed) == 0x001E60, "Member 'AUltra_Dynamic_Sky_C::Composite_Context_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Old_Moon_Target) == 0x001E68, "Member 'AUltra_Dynamic_Sky_C::Old_Moon_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Old_Sun_Target) == 0x001E80, "Member 'AUltra_Dynamic_Sky_C::Old_Sun_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Current_Timer) == 0x001E98, "Member 'AUltra_Dynamic_Sky_C::Cache_Current_Timer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Last_Low_Frequency_Update_Location) == 0x001EA0, "Member 'AUltra_Dynamic_Sky_C::Last_Low_Frequency_Update_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Preset) == 0x001EB8, "Member 'AUltra_Dynamic_Sky_C::Moon_Preset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moons) == 0x001EC0, "Member 'AUltra_Dynamic_Sky_C::Moons' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Layer_Brightness__Night_) == 0x001ED0, "Member 'AUltra_Dynamic_Sky_C::Space_Layer_Brightness__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Layer_Brightness__Day_) == 0x001ED8, "Member 'AUltra_Dynamic_Sky_C::Space_Layer_Brightness__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Glow_Brightness) == 0x001EE0, "Member 'AUltra_Dynamic_Sky_C::Space_Glow_Brightness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Planet_MIDs) == 0x001EE8, "Member 'AUltra_Dynamic_Sky_C::Space_Planet_MIDs' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Scene_Components) == 0x001EF8, "Member 'AUltra_Dynamic_Sky_C::Space_Scene_Components' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Ring_MIDs) == 0x001F08, "Member 'AUltra_Dynamic_Sky_C::Space_Ring_MIDs' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Glow_MIDs) == 0x001F18, "Member 'AUltra_Dynamic_Sky_C::Space_Glow_MIDs' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Planet_Components) == 0x001F28, "Member 'AUltra_Dynamic_Sky_C::Space_Planet_Components' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Layer_Active) == 0x001F38, "Member 'AUltra_Dynamic_Sky_C::Space_Layer_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Solar_Eclipse_Tint) == 0x001F3C, "Member 'AUltra_Dynamic_Sky_C::Cached_Solar_Eclipse_Tint' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Sun_Scale) == 0x001F50, "Member 'AUltra_Dynamic_Sky_C::Cached_Sun_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Moon_Scale) == 0x001F58, "Member 'AUltra_Dynamic_Sky_C::Cached_Moon_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun___Lighting_Channel_0) == 0x001F60, "Member 'AUltra_Dynamic_Sky_C::Sun___Lighting_Channel_0' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun___Lighting_Channel_1) == 0x001F61, "Member 'AUltra_Dynamic_Sky_C::Sun___Lighting_Channel_1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun___Lighting_Channel_2) == 0x001F62, "Member 'AUltra_Dynamic_Sky_C::Sun___Lighting_Channel_2' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon___Lighting_Channel_0) == 0x001F63, "Member 'AUltra_Dynamic_Sky_C::Moon___Lighting_Channel_0' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon___Lighting_Channel_1) == 0x001F64, "Member 'AUltra_Dynamic_Sky_C::Moon___Lighting_Channel_1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon___Lighting_Channel_2) == 0x001F65, "Member 'AUltra_Dynamic_Sky_C::Moon___Lighting_Channel_2' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Space_Roots) == 0x001F68, "Member 'AUltra_Dynamic_Sky_C::Space_Roots' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Water_Level) == 0x001F78, "Member 'AUltra_Dynamic_Sky_C::Water_Level' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Water_Caustics) == 0x001F80, "Member 'AUltra_Dynamic_Sky_C::Render_Water_Caustics' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Caustics_Intensity) == 0x001F88, "Member 'AUltra_Dynamic_Sky_C::Caustics_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Caustics_Falloff) == 0x001F90, "Member 'AUltra_Dynamic_Sky_C::Caustics_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Deep_Water_Falloff) == 0x001F98, "Member 'AUltra_Dynamic_Sky_C::Deep_Water_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Caustics_Texture_Scale) == 0x001FA0, "Member 'AUltra_Dynamic_Sky_C::Caustics_Texture_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moons_Cloud_Mask) == 0x001FA8, "Member 'AUltra_Dynamic_Sky_C::Moons_Cloud_Mask' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Blur_Caustics_With_Depth) == 0x001FB0, "Member 'AUltra_Dynamic_Sky_C::Blur_Caustics_With_Depth' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Blur_Caustics_In_Cloud_Shadow) == 0x001FB8, "Member 'AUltra_Dynamic_Sky_C::Blur_Caustics_In_Cloud_Shadow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Blur_Caustics_With_Camera_Distance) == 0x001FC0, "Member 'AUltra_Dynamic_Sky_C::Blur_Caustics_With_Camera_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Event_Minute) == 0x001FC8, "Member 'AUltra_Dynamic_Sky_C::Event_Minute' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sunset_Event_State) == 0x001FCC, "Member 'AUltra_Dynamic_Sky_C::Sunset_Event_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Cloudiness_Above_Cloud_Layer) == 0x001FD0, "Member 'AUltra_Dynamic_Sky_C::Fog_Cloudiness_Above_Cloud_Layer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Camera_Cloud_Layer_Normalized_Height) == 0x001FD8, "Member 'AUltra_Dynamic_Sky_C::Camera_Cloud_Layer_Normalized_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Camera_Sky_Atmosphere_Normalized_Density) == 0x001FE0, "Member 'AUltra_Dynamic_Sky_C::Camera_Sky_Atmosphere_Normalized_Density' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Fog_Base_Color) == 0x001FE8, "Member 'AUltra_Dynamic_Sky_C::Moon_Fog_Base_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Fog_Base_Color) == 0x001FF8, "Member 'AUltra_Dynamic_Sky_C::Sun_Fog_Base_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Last_High_Frequency_Update_Location) == 0x002008, "Member 'AUltra_Dynamic_Sky_C::Last_High_Frequency_Update_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Old_Cloud_Layer_Camera_Level) == 0x002020, "Member 'AUltra_Dynamic_Sky_C::Old_Cloud_Layer_Camera_Level' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Camera_Relative_Change_Speed) == 0x002028, "Member 'AUltra_Dynamic_Sky_C::Cloud_Camera_Relative_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Sky_Atmosphere_Absorption_Color) == 0x002030, "Member 'AUltra_Dynamic_Sky_C::Cached_Sky_Atmosphere_Absorption_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Coverage_Target_Resolution) == 0x002040, "Member 'AUltra_Dynamic_Sky_C::Cloud_Coverage_Target_Resolution' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Coverage_Render_Target) == 0x002048, "Member 'AUltra_Dynamic_Sky_C::Cloud_Coverage_Render_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Coverage_Target_Size) == 0x002050, "Member 'AUltra_Dynamic_Sky_C::Cloud_Coverage_Target_Size' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Painted_Cloud_Coverage_Opacity) == 0x002058, "Member 'AUltra_Dynamic_Sky_C::Painted_Cloud_Coverage_Opacity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Layer_2_Painted_Cloud_Coverage_Opacity) == 0x002060, "Member 'AUltra_Dynamic_Sky_C::Layer_2_Painted_Cloud_Coverage_Opacity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Painted_Coverage_Affects_Global_Values) == 0x002068, "Member 'AUltra_Dynamic_Sky_C::Painted_Coverage_Affects_Global_Values' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Local_Painting_Cell_Data) == 0x002070, "Member 'AUltra_Dynamic_Sky_C::Local_Painting_Cell_Data' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Local_Painted_Cell_Size) == 0x0020C0, "Member 'AUltra_Dynamic_Sky_C::Local_Painted_Cell_Size' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Painting_Active) == 0x0020C4, "Member 'AUltra_Dynamic_Sky_C::Cloud_Painting_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Coverage_Target_Location) == 0x0020C8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Coverage_Target_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Current_Cloud_Coverage_Render_Target_Mapping) == 0x0020D8, "Member 'AUltra_Dynamic_Sky_C::Current_Cloud_Coverage_Render_Target_Mapping' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Coverage_Target_in_Use) == 0x0020E8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Coverage_Target_in_Use' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Async_Loading_Queue) == 0x0020F0, "Member 'AUltra_Dynamic_Sky_C::Async_Loading_Queue' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Async_Loaded_Objects) == 0x002100, "Member 'AUltra_Dynamic_Sky_C::Async_Loaded_Objects' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Async_Loading_Active) == 0x002110, "Member 'AUltra_Dynamic_Sky_C::Async_Loading_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Coverage_After_Painting) == 0x002118, "Member 'AUltra_Dynamic_Sky_C::Cloud_Coverage_After_Painting' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Paint_Can_Add_Coverage) == 0x002120, "Member 'AUltra_Dynamic_Sky_C::Cloud_Paint_Can_Add_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Paint_Can_Subtract_Coverage) == 0x002121, "Member 'AUltra_Dynamic_Sky_C::Cloud_Paint_Can_Subtract_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Enable_Volumetric_Cloud_Light_Rays) == 0x002122, "Member 'AUltra_Dynamic_Sky_C::Enable_Volumetric_Cloud_Light_Rays' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Ray_Intensity) == 0x002128, "Member 'AUltra_Dynamic_Sky_C::Light_Ray_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Ray_Tint_Color) == 0x002130, "Member 'AUltra_Dynamic_Sky_C::Light_Ray_Tint_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Rays_Point_Spacing) == 0x002140, "Member 'AUltra_Dynamic_Sky_C::Light_Rays_Point_Spacing' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Ray_Length) == 0x002148, "Member 'AUltra_Dynamic_Sky_C::Light_Ray_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Individual_Clouds_Light_Rays) == 0x002150, "Member 'AUltra_Dynamic_Sky_C::Individual_Clouds_Light_Rays' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Scale_Light_Ray_Width) == 0x002158, "Member 'AUltra_Dynamic_Sky_C::Scale_Light_Ray_Width' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Rays_Max_Distance__Km_) == 0x002160, "Member 'AUltra_Dynamic_Sky_C::Light_Rays_Max_Distance__Km_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Rays_Depth_Fade_Distance) == 0x002168, "Member 'AUltra_Dynamic_Sky_C::Light_Rays_Depth_Fade_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Rays_3D_Noise_Scale) == 0x002170, "Member 'AUltra_Dynamic_Sky_C::Light_Rays_3D_Noise_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Light_Rays_Niagara_System) == 0x002178, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Light_Rays_Niagara_System' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Max_Light_Ray_Length) == 0x0021A0, "Member 'AUltra_Dynamic_Sky_C::Max_Light_Ray_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Top_World_Height) == 0x0021A8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Top_World_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Light_Ray_Vector) == 0x0021B0, "Member 'AUltra_Dynamic_Sky_C::Cached_Light_Ray_Vector' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Light_Rays_Using_Sun) == 0x0021C8, "Member 'AUltra_Dynamic_Sky_C::Light_Rays_Using_Sun' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Light_Ray_Strength) == 0x0021D0, "Member 'AUltra_Dynamic_Sky_C::Cached_Light_Ray_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Paint_Draw_MID) == 0x0021D8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Paint_Draw_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Fog_Albedo__Day_) == 0x0021E0, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Fog_Albedo__Day_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Dusk__6) == 0x0021F0, "Member 'AUltra_Dynamic_Sky_C::Dusk__6' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Fog_Albedo__Night_) == 0x002200, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Fog_Albedo__Night_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Global_Volumetric_Material) == 0x002210, "Member 'AUltra_Dynamic_Sky_C::Render_Global_Volumetric_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Noise_Intensity) == 0x002218, "Member 'AUltra_Dynamic_Sky_C::Fog_Noise_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Noise_Scale) == 0x002220, "Member 'AUltra_Dynamic_Sky_C::Fog_Noise_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Noise_Max_Samples) == 0x002228, "Member 'AUltra_Dynamic_Sky_C::Fog_Noise_Max_Samples' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Noise_Speed_Scale) == 0x002230, "Member 'AUltra_Dynamic_Sky_C::Fog_Noise_Speed_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Mask_Fog_with_Weather_Masks) == 0x002238, "Member 'AUltra_Dynamic_Sky_C::Mask_Fog_with_Weather_Masks' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Ground_Fog__Fog_Above_Distance_Fields_) == 0x002240, "Member 'AUltra_Dynamic_Sky_C::Render_Ground_Fog__Fog_Above_Distance_Fields_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ground_Fog_Extinction__Foggy_) == 0x002248, "Member 'AUltra_Dynamic_Sky_C::Ground_Fog_Extinction__Foggy_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ground_Fog_Extinction__Dusty_) == 0x002250, "Member 'AUltra_Dynamic_Sky_C::Ground_Fog_Extinction__Dusty_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ground_Fog_Extinction__Rainy_) == 0x002258, "Member 'AUltra_Dynamic_Sky_C::Ground_Fog_Extinction__Rainy_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ground_Fog_Extinction__Snowy_) == 0x002260, "Member 'AUltra_Dynamic_Sky_C::Ground_Fog_Extinction__Snowy_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ground_Fog_Extinction__Manual_) == 0x002268, "Member 'AUltra_Dynamic_Sky_C::Ground_Fog_Extinction__Manual_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ground_Fog_Height) == 0x002270, "Member 'AUltra_Dynamic_Sky_C::Ground_Fog_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ground_Fog_Height_Noise) == 0x002278, "Member 'AUltra_Dynamic_Sky_C::Ground_Fog_Height_Noise' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ground_Fog_Angle_Threshold) == 0x002280, "Member 'AUltra_Dynamic_Sky_C::Ground_Fog_Angle_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Ground_Fog_Hardness) == 0x002288, "Member 'AUltra_Dynamic_Sky_C::Ground_Fog_Hardness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Apply_Water_Fog_Values) == 0x002290, "Member 'AUltra_Dynamic_Sky_C::Apply_Water_Fog_Values' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Water_Extinction) == 0x002298, "Member 'AUltra_Dynamic_Sky_C::Water_Extinction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Water_Albedo) == 0x0022A0, "Member 'AUltra_Dynamic_Sky_C::Water_Albedo' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Global_Volumetric_Fog_Parent_Material) == 0x0022B0, "Member 'AUltra_Dynamic_Sky_C::Global_Volumetric_Fog_Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Global_Volumetric_Fog_MID) == 0x0022D8, "Member 'AUltra_Dynamic_Sky_C::Global_Volumetric_Fog_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Global_Volumetric_Fog__Ground_Fog___Parent_Material) == 0x0022E0, "Member 'AUltra_Dynamic_Sky_C::Global_Volumetric_Fog__Ground_Fog___Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Position) == 0x002308, "Member 'AUltra_Dynamic_Sky_C::Fog_Position' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Disk_Color_Curve) == 0x002320, "Member 'AUltra_Dynamic_Sky_C::Sun_Disk_Color_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Target) == 0x002328, "Member 'AUltra_Dynamic_Sky_C::Sun_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Target) == 0x002340, "Member 'AUltra_Dynamic_Sky_C::Moon_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Static_Clouds_Texture) == 0x002358, "Member 'AUltra_Dynamic_Sky_C::Static_Clouds_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Static_Clouds_Rotation) == 0x002380, "Member 'AUltra_Dynamic_Sky_C::Static_Clouds_Rotation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Static_Clouds_Rotation_Speed) == 0x002388, "Member 'AUltra_Dynamic_Sky_C::Static_Clouds_Rotation_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Static_Clouds_Color_Intensity) == 0x002390, "Member 'AUltra_Dynamic_Sky_C::Static_Clouds_Color_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Movement_Timer_Length) == 0x002398, "Member 'AUltra_Dynamic_Sky_C::Cloud_Movement_Timer_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Movement_Cache_Timer) == 0x0023A0, "Member 'AUltra_Dynamic_Sky_C::Cloud_Movement_Cache_Timer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Clouds_B_Time_Old) == 0x0023A8, "Member 'AUltra_Dynamic_Sky_C::Clouds_B_Time_Old' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Clouds_Position_Color_A) == 0x0023B0, "Member 'AUltra_Dynamic_Sky_C::Clouds_Position_Color_A' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Clouds_Position_Color_B) == 0x0023C0, "Member 'AUltra_Dynamic_Sky_C::Clouds_Position_Color_B' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Position_Color_A) == 0x0023D0, "Member 'AUltra_Dynamic_Sky_C::Fog_Position_Color_A' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Position_Color_B) == 0x0023E0, "Member 'AUltra_Dynamic_Sky_C::Fog_Position_Color_B' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Clouds_Time_Elapsed_Last_Update) == 0x0023F0, "Member 'AUltra_Dynamic_Sky_C::Clouds_Time_Elapsed_Last_Update' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Next_Cache_Step) == 0x0023F8, "Member 'AUltra_Dynamic_Sky_C::Next_Cache_Step' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Booleans) == 0x002400, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Booleans' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Current_Cache_Timer_Speed) == 0x002410, "Member 'AUltra_Dynamic_Sky_C::Current_Cache_Timer_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Low_Priority_Update_Step) == 0x002418, "Member 'AUltra_Dynamic_Sky_C::Low_Priority_Update_Step' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Active_Update_Speed) == 0x00241C, "Member 'AUltra_Dynamic_Sky_C::Active_Update_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, High_Priority_Update_Step) == 0x002420, "Member 'AUltra_Dynamic_Sky_C::High_Priority_Update_Step' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Non_Cached_Update_Step) == 0x002424, "Member 'AUltra_Dynamic_Sky_C::Non_Cached_Update_Step' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Max_Property_Cache_Period___Time_Of_Day_Transition) == 0x002428, "Member 'AUltra_Dynamic_Sky_C::Max_Property_Cache_Period___Time_Of_Day_Transition' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Minimum_Active_Update_Speed) == 0x002430, "Member 'AUltra_Dynamic_Sky_C::Minimum_Active_Update_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Moon) == 0x002438, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Moon' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Fog) == 0x002448, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Sky_Material) == 0x002458, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Sky_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Sky_Atmosphere) == 0x002468, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Sky_Atmosphere' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Simplified_Color) == 0x002478, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Simplified_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Sky_Light) == 0x002488, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Sky_Light' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Sun) == 0x002498, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Sun' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Volumetric_Clouds) == 0x0024A8, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Volumetric_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_2D_Clouds) == 0x0024B8, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_2D_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Wind_Speed_Multiplier) == 0x0024C8, "Member 'AUltra_Dynamic_Sky_C::Wind_Speed_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fog_Vertical_Velocity) == 0x0024D0, "Member 'AUltra_Dynamic_Sky_C::Fog_Vertical_Velocity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Aurora_Fade_Distance__km_) == 0x0024D8, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Aurora_Fade_Distance__km_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cinematic_Clouds_View_Sample_Scale) == 0x0024E0, "Member 'AUltra_Dynamic_Sky_C::Cinematic_Clouds_View_Sample_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cinematic_Clouds_Shadow_Sample_Scale) == 0x0024E8, "Member 'AUltra_Dynamic_Sky_C::Cinematic_Clouds_Shadow_Sample_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cinematic_Clouds_Tracing_Max_Distance) == 0x0024F0, "Member 'AUltra_Dynamic_Sky_C::Cinematic_Clouds_Tracing_Max_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cinematic_Clouds_View_Ray_Sample_Max_Count) == 0x0024F8, "Member 'AUltra_Dynamic_Sky_C::Cinematic_Clouds_View_Ray_Sample_Max_Count' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Current_Modifiers) == 0x002500, "Member 'AUltra_Dynamic_Sky_C::Current_Modifiers' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Modifier_Alphas) == 0x002510, "Member 'AUltra_Dynamic_Sky_C::Modifier_Alphas' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Modifier_Targets) == 0x002520, "Member 'AUltra_Dynamic_Sky_C::Modifier_Targets' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Modifier_Speeds) == 0x002530, "Member 'AUltra_Dynamic_Sky_C::Modifier_Speeds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Starting_Modifiers) == 0x002540, "Member 'AUltra_Dynamic_Sky_C::Starting_Modifiers' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Preview_Starting_Modifiers_in_Editor) == 0x002550, "Member 'AUltra_Dynamic_Sky_C::Preview_Starting_Modifiers_in_Editor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Replicate_Modifiers_To_Clients) == 0x002551, "Member 'AUltra_Dynamic_Sky_C::Replicate_Modifiers_To_Clients' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_Volumetric_Light_Rays) == 0x002552, "Member 'AUltra_Dynamic_Sky_C::Using_Volumetric_Light_Rays' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Last_Material_Quality_Level) == 0x002554, "Member 'AUltra_Dynamic_Sky_C::Last_Material_Quality_Level' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Last_Effects_Quality_Level) == 0x002558, "Member 'AUltra_Dynamic_Sky_C::Last_Effects_Quality_Level' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Instant_Exposure_Adjustment_on_Begin_Play) == 0x00255C, "Member 'AUltra_Dynamic_Sky_C::Instant_Exposure_Adjustment_on_Begin_Play' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Instant_Exposure_Post_Process) == 0x002560, "Member 'AUltra_Dynamic_Sky_C::Instant_Exposure_Post_Process' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Editor_Tick_Handler) == 0x002568, "Member 'AUltra_Dynamic_Sky_C::Editor_Tick_Handler' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Tick_Delta_Seconds) == 0x002570, "Member 'AUltra_Dynamic_Sky_C::Tick_Delta_Seconds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Editor_Camera_Position) == 0x002578, "Member 'AUltra_Dynamic_Sky_C::Editor_Camera_Position' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Editor_Camera_Position_Offset) == 0x002590, "Member 'AUltra_Dynamic_Sky_C::Editor_Camera_Position_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Last_Editor_Tick_Time) == 0x0025A8, "Member 'AUltra_Dynamic_Sky_C::Last_Editor_Tick_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Run_Context) == 0x0025B0, "Member 'AUltra_Dynamic_Sky_C::Run_Context' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Slow_Falling_Combined_Change_Speed) == 0x0025B8, "Member 'AUltra_Dynamic_Sky_C::Slow_Falling_Combined_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Combined_Change_Speed) == 0x0025C0, "Member 'AUltra_Dynamic_Sky_C::Combined_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Custom_Sky_Sphere_Static_Mesh) == 0x0025C8, "Member 'AUltra_Dynamic_Sky_C::Custom_Sky_Sphere_Static_Mesh' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Fallback_Cloud_Layer_Altitude) == 0x0025D0, "Member 'AUltra_Dynamic_Sky_C::Fallback_Cloud_Layer_Altitude' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Level_Editor_Tick) == 0x0025D8, "Member 'AUltra_Dynamic_Sky_C::Level_Editor_Tick' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_of_Last_Cloud_Cache) == 0x0025E0, "Member 'AUltra_Dynamic_Sky_C::Time_of_Last_Cloud_Cache' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Occlusion_State) == 0x0025E8, "Member 'AUltra_Dynamic_Sky_C::Occlusion_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Last_Static_Clouds_Update_Time) == 0x0025F0, "Member 'AUltra_Dynamic_Sky_C::Last_Static_Clouds_Update_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Static_Clouds_Dynamic_Rotation) == 0x0025F8, "Member 'AUltra_Dynamic_Sky_C::Static_Clouds_Dynamic_Rotation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Editor_Camera_Rotation) == 0x002600, "Member 'AUltra_Dynamic_Sky_C::Editor_Camera_Rotation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Cloud_Shadows_Cancel_Value) == 0x002618, "Member 'AUltra_Dynamic_Sky_C::Cached_Cloud_Shadows_Cancel_Value' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Interior_Occlusion_Change_Speed) == 0x002620, "Member 'AUltra_Dynamic_Sky_C::Interior_Occlusion_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Time_of_Last_Cache_Timing_Update) == 0x002628, "Member 'AUltra_Dynamic_Sky_C::Time_of_Last_Cache_Timing_Update' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Old_Interior_Occlusion) == 0x002630, "Member 'AUltra_Dynamic_Sky_C::Old_Interior_Occlusion' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Transitioning_Sky_Light_Intensity) == 0x002638, "Member 'AUltra_Dynamic_Sky_C::Transitioning_Sky_Light_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Sun_Cast_Shadows) == 0x002639, "Member 'AUltra_Dynamic_Sky_C::Cache_Sun_Cast_Shadows' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Eclipse_Percent) == 0x002640, "Member 'AUltra_Dynamic_Sky_C::Eclipse_Percent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Shadows_Cutoff_Z) == 0x002648, "Member 'AUltra_Dynamic_Sky_C::Sun_Shadows_Cutoff_Z' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Current_Hour_Changed) == 0x002650, "Member 'AUltra_Dynamic_Sky_C::Current_Hour_Changed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Every_Minute) == 0x002660, "Member 'AUltra_Dynamic_Sky_C::Every_Minute' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Called_Starting_Event_Dispatchers) == 0x002670, "Member 'AUltra_Dynamic_Sky_C::Called_Starting_Event_Dispatchers' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, World_Origin_Location) == 0x002678, "Member 'AUltra_Dynamic_Sky_C::World_Origin_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Editor_Tick_Handler_Class) == 0x002690, "Member 'AUltra_Dynamic_Sky_C::Editor_Tick_Handler_Class' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Editor_Utility_Opener_Class) == 0x0026B8, "Member 'AUltra_Dynamic_Sky_C::Editor_Utility_Opener_Class' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Event_Date) == 0x0026E0, "Member 'AUltra_Dynamic_Sky_C::Event_Date' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Date_Changed) == 0x0026E8, "Member 'AUltra_Dynamic_Sky_C::Date_Changed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Initial_Replication) == 0x0026F8, "Member 'AUltra_Dynamic_Sky_C::Initial_Replication' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Starting_Date) == 0x002700, "Member 'AUltra_Dynamic_Sky_C::Starting_Date' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Clouds_Time_Based_Movement_Offset) == 0x002708, "Member 'AUltra_Dynamic_Sky_C::Clouds_Time_Based_Movement_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Unmodified_Floats) == 0x002710, "Member 'AUltra_Dynamic_Sky_C::Unmodified_Floats' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Unmodified_Colors) == 0x002720, "Member 'AUltra_Dynamic_Sky_C::Unmodified_Colors' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Modifiers_Ticking) == 0x002730, "Member 'AUltra_Dynamic_Sky_C::Modifiers_Ticking' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Modified_Float_Properties) == 0x002738, "Member 'AUltra_Dynamic_Sky_C::Modified_Float_Properties' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Modified_Color_Properties) == 0x002748, "Member 'AUltra_Dynamic_Sky_C::Modified_Color_Properties' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Move_Sky_Light_Location) == 0x002758, "Member 'AUltra_Dynamic_Sky_C::Move_Sky_Light_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Light_Movement_uses_Z_Axis) == 0x002759, "Member 'AUltra_Dynamic_Sky_C::Sky_Light_Movement_uses_Z_Axis' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Weather_Cloud_Coverage) == 0x002760, "Member 'AUltra_Dynamic_Sky_C::Weather_Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Weather_Fog) == 0x002768, "Member 'AUltra_Dynamic_Sky_C::Weather_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Weather_Dust_Amount) == 0x002770, "Member 'AUltra_Dynamic_Sky_C::Weather_Dust_Amount' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Glow_Fog_Color) == 0x002778, "Member 'AUltra_Dynamic_Sky_C::Sky_Glow_Fog_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Night_Sky_Glow) == 0x002788, "Member 'AUltra_Dynamic_Sky_C::Cached_Night_Sky_Glow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Light_Pollution) == 0x002798, "Member 'AUltra_Dynamic_Sky_C::Cached_Light_Pollution' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Local_Cloud_Coverage) == 0x0027A8, "Member 'AUltra_Dynamic_Sky_C::Local_Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Storm_Clouds_Draw_MID) == 0x0027B0, "Member 'AUltra_Dynamic_Sky_C::Storm_Clouds_Draw_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Painting_Present) == 0x0027B8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Painting_Present' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Radial_Storm_Clouds_Present) == 0x0027B9, "Member 'AUltra_Dynamic_Sky_C::Radial_Storm_Clouds_Present' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Radial_Storm_Cloud_Coverage_Cache) == 0x0027C0, "Member 'AUltra_Dynamic_Sky_C::Radial_Storm_Cloud_Coverage_Cache' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cvar_Cache) == 0x0027D0, "Member 'AUltra_Dynamic_Sky_C::Cvar_Cache' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Paint_Container) == 0x002820, "Member 'AUltra_Dynamic_Sky_C::Cloud_Paint_Container' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Radial_Storms) == 0x002828, "Member 'AUltra_Dynamic_Sky_C::Radial_Storms' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_World_Rotation) == 0x002838, "Member 'AUltra_Dynamic_Sky_C::Sun_World_Rotation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_World_Rotation) == 0x002850, "Member 'AUltra_Dynamic_Sky_C::Moon_World_Rotation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Path_Tracer_Sky_Light) == 0x002868, "Member 'AUltra_Dynamic_Sky_C::Path_Tracer_Sky_Light' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Compass_Mesh) == 0x002870, "Member 'AUltra_Dynamic_Sky_C::Compass_Mesh' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Global_Volumetric_Fog_Mesh) == 0x002878, "Member 'AUltra_Dynamic_Sky_C::Global_Volumetric_Fog_Mesh' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Player_Occlusion) == 0x002880, "Member 'AUltra_Dynamic_Sky_C::Player_Occlusion' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, TwoD_Turbulence_Disk) == 0x002888, "Member 'AUltra_Dynamic_Sky_C::TwoD_Turbulence_Disk' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Light_Rays) == 0x002890, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Light_Rays' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Inside_Cloud_Fog_Mesh) == 0x002898, "Member 'AUltra_Dynamic_Sky_C::Inside_Cloud_Fog_Mesh' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Volumetric_Cloud_Shadow_Altitude) == 0x0028A0, "Member 'AUltra_Dynamic_Sky_C::Volumetric_Cloud_Shadow_Altitude' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Post_Process_Mats_Toggle_State) == 0x0028A8, "Member 'AUltra_Dynamic_Sky_C::Post_Process_Mats_Toggle_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Lens_Flare_WB) == 0x0028B8, "Member 'AUltra_Dynamic_Sky_C::Lens_Flare_WB' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Fog_PP_WB) == 0x0028C8, "Member 'AUltra_Dynamic_Sky_C::Cloud_Fog_PP_WB' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Static_PPM_Component) == 0x0028D8, "Member 'AUltra_Dynamic_Sky_C::Static_PPM_Component' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Path_Tracer_Fog_Parent_Mat) == 0x0028E0, "Member 'AUltra_Dynamic_Sky_C::Path_Tracer_Fog_Parent_Mat' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Path_Tracer_Fog_MID) == 0x002908, "Member 'AUltra_Dynamic_Sky_C::Path_Tracer_Fog_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Path_Tracer_Fog_WB) == 0x002910, "Member 'AUltra_Dynamic_Sky_C::Path_Tracer_Fog_WB' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Render_Height_Fog_in_Path_Tracer_using_Post_Process) == 0x002920, "Member 'AUltra_Dynamic_Sky_C::Render_Height_Fog_in_Path_Tracer_using_Post_Process' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cloud_Profile_LUT_Preview) == 0x002928, "Member 'AUltra_Dynamic_Sky_C::Cloud_Profile_LUT_Preview' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Previewing_Cloud_Profile_Editor) == 0x002930, "Member 'AUltra_Dynamic_Sky_C::Previewing_Cloud_Profile_Editor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Alternate_Tick) == 0x002931, "Member 'AUltra_Dynamic_Sky_C::Alternate_Tick' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sky_Atmosphere_Density_Curve) == 0x002938, "Member 'AUltra_Dynamic_Sky_C::Sky_Atmosphere_Density_Curve' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Allow_Disabling_Directional_Shadows_with_Coverage) == 0x002940, "Member 'AUltra_Dynamic_Sky_C::Allow_Disabling_Directional_Shadows_with_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Apply_Flat_Cloudiness) == 0x002941, "Member 'AUltra_Dynamic_Sky_C::Apply_Flat_Cloudiness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_Sky_Atmosphere) == 0x002942, "Member 'AUltra_Dynamic_Sky_C::Using_Sky_Atmosphere' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Moon_Effective_Illumination_0_1) == 0x002948, "Member 'AUltra_Dynamic_Sky_C::Cached_Moon_Effective_Illumination_0_1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Absent_Directional_Lights_Brightness) == 0x002950, "Member 'AUltra_Dynamic_Sky_C::Cached_Absent_Directional_Lights_Brightness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Current_Moon_Lit_Percent) == 0x002958, "Member 'AUltra_Dynamic_Sky_C::Cached_Current_Moon_Lit_Percent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_Space_Mode) == 0x002960, "Member 'AUltra_Dynamic_Sky_C::Using_Space_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_Volumetric_Clouds) == 0x002961, "Member 'AUltra_Dynamic_Sky_C::Using_Volumetric_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_Static_Clouds) == 0x002962, "Member 'AUltra_Dynamic_Sky_C::Using_Static_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_2D_Dynamic_Clouds) == 0x002963, "Member 'AUltra_Dynamic_Sky_C::Using_2D_Dynamic_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Using_Volumetric_Aurora) == 0x002964, "Member 'AUltra_Dynamic_Sky_C::Using_Volumetric_Aurora' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Lit_Intensity) == 0x002968, "Member 'AUltra_Dynamic_Sky_C::Cached_Lit_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Directional_Light_Dimming) == 0x002970, "Member 'AUltra_Dynamic_Sky_C::Cached_Directional_Light_Dimming' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Modifiers_Animating) == 0x002978, "Member 'AUltra_Dynamic_Sky_C::Modifiers_Animating' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Directional_Inscattering_Multiplier) == 0x002980, "Member 'AUltra_Dynamic_Sky_C::Cached_Directional_Inscattering_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Moon_Renders_Behind_Space_Layer) == 0x002988, "Member 'AUltra_Dynamic_Sky_C::Moon_Renders_Behind_Space_Layer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cached_Fog_Directional_Inscattering) == 0x00298C, "Member 'AUltra_Dynamic_Sky_C::Cached_Fog_Directional_Inscattering' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Sun_Moon_Delta) == 0x0029A0, "Member 'AUltra_Dynamic_Sky_C::Sun_Moon_Delta' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Weather_Particle_Collision_Channel) == 0x0029A8, "Member 'AUltra_Dynamic_Sky_C::Weather_Particle_Collision_Channel' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Use_Legacy_Cloud_Coverage_Range) == 0x0029A9, "Member 'AUltra_Dynamic_Sky_C::Use_Legacy_Cloud_Coverage_Range' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Sky_C, Cache_Group_Countdowns) == 0x0029B0, "Member 'AUltra_Dynamic_Sky_C::Cache_Group_Countdowns' has a wrong offset!");

}

