﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Weather_Override_Volume

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "Engine_classes.hpp"
#include "CoreUObject_structs.hpp"
#include "UDS_RandomWeatherTiming_structs.hpp"
#include "UDS_TemperatureType_structs.hpp"


namespace SDK
{

// BlueprintGeneratedClass Weather_Override_Volume.Weather_Override_Volume_C
// 0x0310 (0x05A8 - 0x0298)
class AWeather_Override_Volume_C final : public AActor
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0298(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UUDW_Material_State_Manager_C*          Material_State_Manager;                            // 0x02A0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UUDW_Temperature_Manager_C*             Temperature_Manager;                               // 0x02A8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class URandom_Weather_Variation_C*            Random_Weather_Manager;                            // 0x02B0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UBillboardComponent*                    Billboard;                                         // 0x02B8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class USplineComponent*                       Spline;                                            // 0x02C0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Weather;                                           // 0x02C8(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	double                                        Transition_Width;                                  // 0x02D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	int32                                         Priority;                                          // 0x02D8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	bool                                          Override_Material_Effects;                         // 0x02DC(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_2DD[0x3];                                      // 0x02DD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Volume_Alpha;                                      // 0x02E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Scaled_Transition_Width;                           // 0x02E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               UDW;                                               // 0x02F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, Transient, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	EUDS_RandomWeatherTiming                      Random_Weather_Variation;                          // 0x02F8(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_2F9[0x7];                                      // 0x02F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Climate_Preset_C*                  Apply_Climate_Preset;                              // 0x0300(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TMap<class UUDS_Weather_Settings_C*, double>  Weather_Type_Probabilities__Spring_;               // 0x0308(0x0050)(Edit, BlueprintVisible, ExposeOnSpawn)
	TMap<class UUDS_Weather_Settings_C*, double>  Weather_Type_Probabilities__Summer_;               // 0x0358(0x0050)(Edit, BlueprintVisible, ExposeOnSpawn)
	TMap<class UUDS_Weather_Settings_C*, double>  Weather_Type_Probabilities__Autumn_;               // 0x03A8(0x0050)(Edit, BlueprintVisible, ExposeOnSpawn)
	TMap<class UUDS_Weather_Settings_C*, double>  Weather_Type_Probabilities__Winter_;               // 0x03F8(0x0050)(Edit, BlueprintVisible, ExposeOnSpawn)
	double                                        Total_Sphere_Bounds;                               // 0x0448(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Spline_Bounds_Center;                              // 0x0450(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Show_Weather_Label_in_Editor;                      // 0x0468(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_469[0x7];                                      // 0x0469(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                Weather_State;                                     // 0x0470(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TArray<struct FVector>                        World_Space_Triangles;                             // 0x0478(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FVector                                Canvas_Scale_Factor;                               // 0x0488(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Canvas_Offset;                                     // 0x04A0(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Volume_Color;                                      // 0x04B8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FCanvasUVTri>                   Canvas_Space_Triangles;                            // 0x04C8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          Started;                                           // 0x04D8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4D9[0x7];                                      // 0x04D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Material_State_Buffer;                             // 0x04E0(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Transitioning_Weather;                             // 0x04F8(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4F9[0x7];                                      // 0x04F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                Transition_Weather_A;                              // 0x0500(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Transition_Weather_B;                              // 0x0508(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Transition_Time_Remaining;                         // 0x0510(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Changing_to_Random_Weather;                        // 0x0518(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Changing_from_Random_Weather;                      // 0x0519(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51A[0x6];                                      // 0x051A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Transition_Timer_Length;                           // 0x0520(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 Random_Weather_Label_Text;                         // 0x0528(0x0010)(Edit, BlueprintVisible, ZeroConstructor, HasGetValueTypeHash)
	TArray<struct FVector>                        Runtime_Spline_Points;                             // 0x0538(0x0010)(Edit, BlueprintVisible, Net, ExposeOnSpawn)
	bool                                          Apply_Wind_Direction;                              // 0x0548(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_549[0x7];                                      // 0x0549(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Wind_Direction;                                    // 0x0550(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	bool                                          Erase_Other_WOVs;                                  // 0x0558(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Apply_Temperature_Ranges;                          // 0x0559(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_55A[0x6];                                      // 0x055A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              Summer_Temperature_Min_and_Max;                    // 0x0560(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	struct FVector2D                              Autumn_Temperature_Min_and_Max;                    // 0x0570(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	struct FVector2D                              Winter_Temperature_Min_and_Max;                    // 0x0580(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	struct FVector2D                              Spring_Temperature_Min_and_Max;                    // 0x0590(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	EUDS_TemperatureType                          Temperature_Scale;                                 // 0x05A0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Material_State_Needs_Check;                        // 0x05A1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Add_Quad(const struct FVector& Vert_1, const struct FVector& Vert_2, const struct FVector& Vert_3, const struct FVector& Vert_4);
	void Add_Triangle(const struct FVector& Vert_1, const struct FVector& Vert_2, const struct FVector& Vert_3);
	void Apply_Climate_Preset_Object(class UUDS_Climate_Preset_C* Climate_Preset);
	void Apply_Current_Single_Weather();
	void Apply_Saved_WOV_State(const struct FUDW_WOV_State& State);
	void Calculate_Spline_Bounds();
	void Change_to_Random_Weather_Variation(double Time_to_Transition_to_Random_Weather__Seconds_, EUDS_RandomWeatherTiming Random_Weather_Mode);
	void Change_Weather(class UUDS_Weather_Settings_C* New_Weather_Type, double Time_To_Transition_To_New_Weather__Seconds_);
	void Check_for_Changing_Material_State_to_Request_Target_Redraw();
	void Check_to_Update_Temperature_Scale();
	void Construct_Editor_Only_Weather_Labels();
	void Construct_Weather_State_Objects();
	void Create_Canvas_Space_Triangles(const struct FVector2D& Corner_Position, double Width, int32 Resolution);
	void Create_World_Space_Drawing_Geometry();
	void Custom_Volume_Behavior(double Alpha, class AUltra_Dynamic_Sky_C* UDS, class AUltra_Dynamic_Weather_C* UDW_0);
	void Disable_Volume();
	void Enable_Volume();
	void ExecuteUbergraph_Weather_Override_Volume(int32 EntryPoint);
	void Force_Startup();
	void Force_Tick();
	void Force_Update_Current_Weather();
	void Get_State_for_Saving(struct FUDW_WOV_State* State);
	void Increment_Material_State();
	void Initialize_Spline_Data();
	void Is_Point_In_Triangle(const struct FVector2D& Point, const struct FVector2D& v1, const struct FVector2D& v2, const struct FVector2D& V3, bool* Yes);
	void ReceiveBeginPlay();
	void ReceiveEndPlay(EEndPlayReason EndPlayReason);
	void ReceiveTick(float DeltaSeconds);
	void Sample_Point_for_Current_Alpha(const struct FVector& Location, double* Alpha);
	void Scale_And_Place_Vertex_in_Canvas_Space(const struct FVector& In, struct FVector2D* Pos, struct FLinearColor* Color);
	void Shut_Down_WOV();
	void Start_Up_Random_Weather_Component();
	void Start_Up_WOV();
	void Triangulate_Polygon(TArray<struct FVector2D>& Vertices__Clockwise_);
	void UDW_End_Play(class AActor* Actor, EEndPlayReason EndPlayReason);
	void Update_Material_State_Buffer();
	void Update_Volume_Color(class AUltra_Dynamic_Weather_C* UDW_0);
	void UserConstructionScript();
	void __Weather_Override_Volumes();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Weather_Override_Volume_C">();
	}
	static class AWeather_Override_Volume_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<AWeather_Override_Volume_C>();
	}
};
static_assert(alignof(AWeather_Override_Volume_C) == 0x000008, "Wrong alignment on AWeather_Override_Volume_C");
static_assert(sizeof(AWeather_Override_Volume_C) == 0x0005A8, "Wrong size on AWeather_Override_Volume_C");
static_assert(offsetof(AWeather_Override_Volume_C, UberGraphFrame) == 0x000298, "Member 'AWeather_Override_Volume_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Material_State_Manager) == 0x0002A0, "Member 'AWeather_Override_Volume_C::Material_State_Manager' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Temperature_Manager) == 0x0002A8, "Member 'AWeather_Override_Volume_C::Temperature_Manager' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Random_Weather_Manager) == 0x0002B0, "Member 'AWeather_Override_Volume_C::Random_Weather_Manager' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Billboard) == 0x0002B8, "Member 'AWeather_Override_Volume_C::Billboard' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Spline) == 0x0002C0, "Member 'AWeather_Override_Volume_C::Spline' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Weather) == 0x0002C8, "Member 'AWeather_Override_Volume_C::Weather' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Transition_Width) == 0x0002D0, "Member 'AWeather_Override_Volume_C::Transition_Width' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Priority) == 0x0002D8, "Member 'AWeather_Override_Volume_C::Priority' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Override_Material_Effects) == 0x0002DC, "Member 'AWeather_Override_Volume_C::Override_Material_Effects' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Volume_Alpha) == 0x0002E0, "Member 'AWeather_Override_Volume_C::Volume_Alpha' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Scaled_Transition_Width) == 0x0002E8, "Member 'AWeather_Override_Volume_C::Scaled_Transition_Width' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, UDW) == 0x0002F0, "Member 'AWeather_Override_Volume_C::UDW' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Random_Weather_Variation) == 0x0002F8, "Member 'AWeather_Override_Volume_C::Random_Weather_Variation' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Apply_Climate_Preset) == 0x000300, "Member 'AWeather_Override_Volume_C::Apply_Climate_Preset' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Weather_Type_Probabilities__Spring_) == 0x000308, "Member 'AWeather_Override_Volume_C::Weather_Type_Probabilities__Spring_' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Weather_Type_Probabilities__Summer_) == 0x000358, "Member 'AWeather_Override_Volume_C::Weather_Type_Probabilities__Summer_' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Weather_Type_Probabilities__Autumn_) == 0x0003A8, "Member 'AWeather_Override_Volume_C::Weather_Type_Probabilities__Autumn_' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Weather_Type_Probabilities__Winter_) == 0x0003F8, "Member 'AWeather_Override_Volume_C::Weather_Type_Probabilities__Winter_' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Total_Sphere_Bounds) == 0x000448, "Member 'AWeather_Override_Volume_C::Total_Sphere_Bounds' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Spline_Bounds_Center) == 0x000450, "Member 'AWeather_Override_Volume_C::Spline_Bounds_Center' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Show_Weather_Label_in_Editor) == 0x000468, "Member 'AWeather_Override_Volume_C::Show_Weather_Label_in_Editor' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Weather_State) == 0x000470, "Member 'AWeather_Override_Volume_C::Weather_State' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, World_Space_Triangles) == 0x000478, "Member 'AWeather_Override_Volume_C::World_Space_Triangles' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Canvas_Scale_Factor) == 0x000488, "Member 'AWeather_Override_Volume_C::Canvas_Scale_Factor' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Canvas_Offset) == 0x0004A0, "Member 'AWeather_Override_Volume_C::Canvas_Offset' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Volume_Color) == 0x0004B8, "Member 'AWeather_Override_Volume_C::Volume_Color' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Canvas_Space_Triangles) == 0x0004C8, "Member 'AWeather_Override_Volume_C::Canvas_Space_Triangles' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Started) == 0x0004D8, "Member 'AWeather_Override_Volume_C::Started' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Material_State_Buffer) == 0x0004E0, "Member 'AWeather_Override_Volume_C::Material_State_Buffer' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Transitioning_Weather) == 0x0004F8, "Member 'AWeather_Override_Volume_C::Transitioning_Weather' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Transition_Weather_A) == 0x000500, "Member 'AWeather_Override_Volume_C::Transition_Weather_A' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Transition_Weather_B) == 0x000508, "Member 'AWeather_Override_Volume_C::Transition_Weather_B' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Transition_Time_Remaining) == 0x000510, "Member 'AWeather_Override_Volume_C::Transition_Time_Remaining' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Changing_to_Random_Weather) == 0x000518, "Member 'AWeather_Override_Volume_C::Changing_to_Random_Weather' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Changing_from_Random_Weather) == 0x000519, "Member 'AWeather_Override_Volume_C::Changing_from_Random_Weather' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Transition_Timer_Length) == 0x000520, "Member 'AWeather_Override_Volume_C::Transition_Timer_Length' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Random_Weather_Label_Text) == 0x000528, "Member 'AWeather_Override_Volume_C::Random_Weather_Label_Text' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Runtime_Spline_Points) == 0x000538, "Member 'AWeather_Override_Volume_C::Runtime_Spline_Points' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Apply_Wind_Direction) == 0x000548, "Member 'AWeather_Override_Volume_C::Apply_Wind_Direction' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Wind_Direction) == 0x000550, "Member 'AWeather_Override_Volume_C::Wind_Direction' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Erase_Other_WOVs) == 0x000558, "Member 'AWeather_Override_Volume_C::Erase_Other_WOVs' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Apply_Temperature_Ranges) == 0x000559, "Member 'AWeather_Override_Volume_C::Apply_Temperature_Ranges' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Summer_Temperature_Min_and_Max) == 0x000560, "Member 'AWeather_Override_Volume_C::Summer_Temperature_Min_and_Max' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Autumn_Temperature_Min_and_Max) == 0x000570, "Member 'AWeather_Override_Volume_C::Autumn_Temperature_Min_and_Max' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Winter_Temperature_Min_and_Max) == 0x000580, "Member 'AWeather_Override_Volume_C::Winter_Temperature_Min_and_Max' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Spring_Temperature_Min_and_Max) == 0x000590, "Member 'AWeather_Override_Volume_C::Spring_Temperature_Min_and_Max' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Temperature_Scale) == 0x0005A0, "Member 'AWeather_Override_Volume_C::Temperature_Scale' has a wrong offset!");
static_assert(offsetof(AWeather_Override_Volume_C, Material_State_Needs_Check) == 0x0005A1, "Member 'AWeather_Override_Volume_C::Material_State_Needs_Check' has a wrong offset!");

}

