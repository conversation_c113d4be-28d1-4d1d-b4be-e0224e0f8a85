﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_NarrativeMenu_QuestJournal

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "NarrativeCommonUI_structs.hpp"


namespace SDK::Params
{

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.BndEvt__BP_QuestJournal_UseShared?_K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct W_NarrativeMenu_QuestJournal_C_BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature final
{
public:
	bool                                          bIsChecked;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintR<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ZeroConstructor, <PERSON><PERSON><PERSON><PERSON>ldD<PERSON>, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_NarrativeMenu_QuestJournal_C_BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature) == 0x000001, "Wrong alignment on W_NarrativeMenu_QuestJournal_C_BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature");
static_assert(sizeof(W_NarrativeMenu_QuestJournal_C_BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature) == 0x000001, "Wrong size on W_NarrativeMenu_QuestJournal_C_BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature, bIsChecked) == 0x000000, "Member 'W_NarrativeMenu_QuestJournal_C_BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature::bIsChecked' has a wrong offset!");

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.CreateQuestWidgetButton
// 0x0028 (0x0028 - 0x0000)
struct W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton final
{
public:
	class UQuest*                                 Quest;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_QuestJournalQuest_C*                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UBP_QuestJournalQuest_C*                Button;                                            // 0x0010(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UBP_QuestJournalQuest_C*                CallFunc_Create_ReturnValue;                       // 0x0018(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton) == 0x000008, "Wrong alignment on W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton");
static_assert(sizeof(W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton) == 0x000028, "Wrong size on W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton, Quest) == 0x000000, "Member 'W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton::Quest' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton, ReturnValue) == 0x000008, "Member 'W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton, Button) == 0x000010, "Member 'W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton::Button' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton, CallFunc_Create_ReturnValue) == 0x000018, "Member 'W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton, CallFunc_Array_Add_ReturnValue) == 0x000020, "Member 'W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton::CallFunc_Array_Add_ReturnValue' has a wrong offset!");

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.ExecuteUbergraph_W_NarrativeMenu_QuestJournal
// 0x0498 (0x0498 - 0x0000)
struct W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x3];                                       // 0x0011(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   Temp_text_Variable;                                // 0x0018(0x0018)()
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_2;                  // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_1;            // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue_1;        // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3D[0x3];                                       // 0x003D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_2;                   // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_49[0x3];                                       // 0x0049(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable_3;                   // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   Temp_text_Variable_1;                              // 0x0050(0x0018)()
	int32                                         Temp_int_Loop_Counter_Variable_3;                  // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_2;            // 0x006C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue_2;        // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_71[0x3];                                       // 0x0071(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable_4;                   // 0x0074(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UNarrativeComponent*                    K2Node_CustomEvent_Narrative;                      // 0x0078(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0080(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_81[0x7];                                       // 0x0081(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UQuest*>                         CallFunc_GetInProgressQuests_ReturnValue;          // 0x0088(0x0010)(ReferenceParm)
	class UQuest*                                 CallFunc_Array_Get_Item;                           // 0x0098(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_QuestJournalQuest_C*                CallFunc_CreateQuestWidgetButton_ReturnValue;      // 0x00A0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AC[0x4];                                       // 0x00AC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x00B0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_3;            // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Max_ReturnValue;                          // 0x00BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class UQuest*>                         CallFunc_GetSucceededQuests_ReturnValue;           // 0x00C0(0x0010)(ReferenceParm)
	class UQuest*                                 CallFunc_Array_Get_Item_1;                         // 0x00D0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_DC[0x4];                                       // 0x00DC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UBP_QuestJournalQuest_C*                CallFunc_CreateQuestWidgetButton_ReturnValue_1;    // 0x00E0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_4;            // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Max_ReturnValue_1;                        // 0x00EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_5;                   // 0x00F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F4[0x4];                                       // 0x00F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_1;                   // 0x00F8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UQuest*                                 K2Node_CustomEvent_Quest;                          // 0x0100(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_QuestJournalQuest_C*                K2Node_CustomEvent_JournalButton;                  // 0x0108(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TArray<class UQuest*>                         CallFunc_GetFailedQuests_ReturnValue;              // 0x0110(0x0010)(ReferenceParm)
	class UQuest*                                 CallFunc_Array_Get_Item_2;                         // 0x0120(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x0128(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12C[0x4];                                      // 0x012C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UBP_QuestJournalQuest_C*                CallFunc_CreateQuestWidgetButton_ReturnValue_2;    // 0x0130(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_5;            // 0x0138(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13C[0x4];                                      // 0x013C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_2;                   // 0x0140(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Max_ReturnValue_2;                        // 0x0148(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14C[0x4];                                      // 0x014C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetQuestName_ReturnValue;                 // 0x0150(0x0018)()
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0168(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_QuestBranch_C*                      CallFunc_Create_ReturnValue;                       // 0x0170(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_3;                   // 0x0178(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_4;                  // 0x0180(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0184(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0188(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_189[0x7];                                      // 0x0189(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_1;            // 0x0190(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UQuestState*                            CallFunc_Array_Get_Item_3;                         // 0x0198(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x01A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A1[0x7];                                      // 0x01A1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UQuestBranch*                           CallFunc_Array_Get_Item_4;                         // 0x01A8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_3;               // 0x01B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x01B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1B5[0x3];                                      // 0x01B5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_4;               // 0x01B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_bIsChecked;             // 0x01BC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1BD[0x3];                                      // 0x01BD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_2;            // 0x01C0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UNarrativeComponent*                    CallFunc_GetNarrativeComponentFromTarget_ReturnValue; // 0x01C8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x01D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue_1;           // 0x01D4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int64                                         CallFunc_Conv_IntToInt64_ReturnValue;              // 0x01D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int64                                         CallFunc_Conv_IntToInt64_ReturnValue_1;            // 0x01E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x01E8(0x0050)(HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x0238(0x0050)(HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0288(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x0289(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_28A[0x6];                                      // 0x028A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_Select_Default;                             // 0x0290(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_2;            // 0x02A8(0x0050)(HasGetValueTypeHash)
	class FText                                   CallFunc_GetEmptyText_ReturnValue;                 // 0x02F8(0x0018)()
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0310(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0320(0x0018)()
	class FText                                   CallFunc_GetEmptyText_ReturnValue_1;               // 0x0338(0x0018)()
	TArray<class UWidget*>                        CallFunc_GetAllChildren_ReturnValue;               // 0x0350(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UWidget*                                CallFunc_Array_Get_Item_5;                         // 0x0360(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UBP_QuestJournalQuest_C*                CallFunc_Array_Get_Item_6;                         // 0x0368(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UBP_QuestJournalQuest_C*                K2Node_DynamicCast_AsBP_Quest_Journal_Quest;       // 0x0370(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0378(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_379[0x3];                                      // 0x0379(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_5;               // 0x037C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0380(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsNotEmpty_ReturnValue;             // 0x0381(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_382[0x6];                                      // 0x0382(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UBP_QuestJournalQuest_C*>        K2Node_MakeArray_Array_1;                          // 0x0388(0x0010)(ReferenceParm, ContainsInstancedReference)
	bool                                          K2Node_CustomEvent_PartyQuests;                    // 0x0398(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_399[0x7];                                      // 0x0399(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_3;            // 0x03A0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_5;                  // 0x03A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3AC[0x4];                                      // 0x03AC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UNarrativeComponent*                    CallFunc_GetNarrativeComponentFromTarget_ReturnValue_1; // 0x03B0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_2;                // 0x03B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3B9[0x3];                                      // 0x03B9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x03BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   Temp_text_Variable_2;                              // 0x03C0(0x0018)()
	class FText                                   Temp_text_Variable_3;                              // 0x03D8(0x0018)()
	bool                                          Temp_bool_Variable_1;                              // 0x03F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_2;                              // 0x03F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3F2[0x6];                                      // 0x03F2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_Select_Default_1;                           // 0x03F8(0x0018)()
	class UNarrativeComponent*                    K2Node_Select_Default_2;                           // 0x0410(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_3;            // 0x0418(0x0050)(HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array_2;                          // 0x0468(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue_1;                     // 0x0478(0x0018)()
	bool                                          CallFunc_NotEqual_ObjectObject_ReturnValue;        // 0x0490(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal) == 0x000008, "Wrong alignment on W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal");
static_assert(sizeof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal) == 0x000498, "Wrong size on W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, EntryPoint) == 0x000000, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Array_Index_Variable) == 0x000004, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Loop_Counter_Variable) == 0x000008, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Subtract_IntInt_ReturnValue) == 0x00000C, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x000010, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Array_Index_Variable_1) == 0x000014, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_text_Variable) == 0x000018, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_text_Variable' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Loop_Counter_Variable_1) == 0x000030, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Loop_Counter_Variable_2) == 0x000034, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Loop_Counter_Variable_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Subtract_IntInt_ReturnValue_1) == 0x000038, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Subtract_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GreaterEqual_IntInt_ReturnValue_1) == 0x00003C, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GreaterEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Add_IntInt_ReturnValue) == 0x000040, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Array_Index_Variable_2) == 0x000044, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Array_Index_Variable_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_IsValid_ReturnValue) == 0x000048, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Array_Index_Variable_3) == 0x00004C, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Array_Index_Variable_3' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_text_Variable_1) == 0x000050, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_text_Variable_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Loop_Counter_Variable_3) == 0x000068, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Loop_Counter_Variable_3' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Subtract_IntInt_ReturnValue_2) == 0x00006C, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Subtract_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GreaterEqual_IntInt_ReturnValue_2) == 0x000070, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GreaterEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Array_Index_Variable_4) == 0x000074, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Array_Index_Variable_4' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_CustomEvent_Narrative) == 0x000078, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_CustomEvent_Narrative' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_IsValid_ReturnValue_1) == 0x000080, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetInProgressQuests_ReturnValue) == 0x000088, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetInProgressQuests_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Get_Item) == 0x000098, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_CreateQuestWidgetButton_ReturnValue) == 0x0000A0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_CreateQuestWidgetButton_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Length_ReturnValue) == 0x0000A8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_AddChild_ReturnValue) == 0x0000B0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Subtract_IntInt_ReturnValue_3) == 0x0000B8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Subtract_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Max_ReturnValue) == 0x0000BC, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Max_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetSucceededQuests_ReturnValue) == 0x0000C0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetSucceededQuests_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Get_Item_1) == 0x0000D0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Length_ReturnValue_1) == 0x0000D8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_CreateQuestWidgetButton_ReturnValue_1) == 0x0000E0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_CreateQuestWidgetButton_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Subtract_IntInt_ReturnValue_4) == 0x0000E8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Subtract_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Max_ReturnValue_1) == 0x0000EC, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Max_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Array_Index_Variable_5) == 0x0000F0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Array_Index_Variable_5' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_AddChild_ReturnValue_1) == 0x0000F8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_AddChild_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_CustomEvent_Quest) == 0x000100, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_CustomEvent_Quest' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_CustomEvent_JournalButton) == 0x000108, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_CustomEvent_JournalButton' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetFailedQuests_ReturnValue) == 0x000110, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetFailedQuests_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Get_Item_2) == 0x000120, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Length_ReturnValue_2) == 0x000128, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_CreateQuestWidgetButton_ReturnValue_2) == 0x000130, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_CreateQuestWidgetButton_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Subtract_IntInt_ReturnValue_5) == 0x000138, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Subtract_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_AddChild_ReturnValue_2) == 0x000140, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_AddChild_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Max_ReturnValue_2) == 0x000148, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Max_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetQuestName_ReturnValue) == 0x000150, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetQuestName_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetOwningPlayer_ReturnValue) == 0x000168, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Create_ReturnValue) == 0x000170, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_AddChild_ReturnValue_3) == 0x000178, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_AddChild_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Loop_Counter_Variable_4) == 0x000180, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Loop_Counter_Variable_4' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Add_IntInt_ReturnValue_1) == 0x000184, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_bool_Variable) == 0x000188, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetOwningPlayer_ReturnValue_1) == 0x000190, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetOwningPlayer_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Get_Item_3) == 0x000198, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_IsValid_ReturnValue_2) == 0x0001A0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Get_Item_4) == 0x0001A8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Get_Item_4' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Length_ReturnValue_3) == 0x0001B0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Length_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Less_IntInt_ReturnValue) == 0x0001B4, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Length_ReturnValue_4) == 0x0001B8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Length_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_ComponentBoundEvent_bIsChecked) == 0x0001BC, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_ComponentBoundEvent_bIsChecked' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetOwningPlayer_ReturnValue_2) == 0x0001C0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetOwningPlayer_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetNarrativeComponentFromTarget_ReturnValue) == 0x0001C8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetNarrativeComponentFromTarget_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetChildrenCount_ReturnValue) == 0x0001D0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetChildrenCount_ReturnValue_1) == 0x0001D4, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetChildrenCount_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Conv_IntToInt64_ReturnValue) == 0x0001D8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Conv_IntToInt64_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Conv_IntToInt64_ReturnValue_1) == 0x0001E0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Conv_IntToInt64_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_MakeStruct_FormatArgumentData) == 0x0001E8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_MakeStruct_FormatArgumentData_1) == 0x000238, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000288, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x000289, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_Select_Default) == 0x000290, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_MakeStruct_FormatArgumentData_2) == 0x0002A8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_MakeStruct_FormatArgumentData_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetEmptyText_ReturnValue) == 0x0002F8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetEmptyText_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_MakeArray_Array) == 0x000310, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Format_ReturnValue) == 0x000320, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetEmptyText_ReturnValue_1) == 0x000338, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetEmptyText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetAllChildren_ReturnValue) == 0x000350, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetAllChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Get_Item_5) == 0x000360, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Get_Item_5' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Get_Item_6) == 0x000368, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Get_Item_6' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_DynamicCast_AsBP_Quest_Journal_Quest) == 0x000370, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_DynamicCast_AsBP_Quest_Journal_Quest' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_DynamicCast_bSuccess) == 0x000378, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_Length_ReturnValue_5) == 0x00037C, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_Length_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Less_IntInt_ReturnValue_1) == 0x000380, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Array_IsNotEmpty_ReturnValue) == 0x000381, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Array_IsNotEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_MakeArray_Array_1) == 0x000388, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_MakeArray_Array_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_CustomEvent_PartyQuests) == 0x000398, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_CustomEvent_PartyQuests' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetOwningPlayer_ReturnValue_3) == 0x0003A0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetOwningPlayer_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_int_Loop_Counter_Variable_5) == 0x0003A8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_int_Loop_Counter_Variable_5' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_GetNarrativeComponentFromTarget_ReturnValue_1) == 0x0003B0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_GetNarrativeComponentFromTarget_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Less_IntInt_ReturnValue_2) == 0x0003B8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Less_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Add_IntInt_ReturnValue_2) == 0x0003BC, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_text_Variable_2) == 0x0003C0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_text_Variable_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_text_Variable_3) == 0x0003D8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_text_Variable_3' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_bool_Variable_1) == 0x0003F0, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, Temp_bool_Variable_2) == 0x0003F1, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_Select_Default_1) == 0x0003F8, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_Select_Default_2) == 0x000410, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_Select_Default_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_MakeStruct_FormatArgumentData_3) == 0x000418, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_MakeStruct_FormatArgumentData_3' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, K2Node_MakeArray_Array_2) == 0x000468, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::K2Node_MakeArray_Array_2' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_Format_ReturnValue_1) == 0x000478, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_Format_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal, CallFunc_NotEqual_ObjectObject_ReturnValue) == 0x000490, "Member 'W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal::CallFunc_NotEqual_ObjectObject_ReturnValue' has a wrong offset!");

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.Initialize
// 0x0008 (0x0008 - 0x0000)
struct W_NarrativeMenu_QuestJournal_C_Initialize final
{
public:
	class UNarrativeComponent*                    Narrative;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_NarrativeMenu_QuestJournal_C_Initialize) == 0x000008, "Wrong alignment on W_NarrativeMenu_QuestJournal_C_Initialize");
static_assert(sizeof(W_NarrativeMenu_QuestJournal_C_Initialize) == 0x000008, "Wrong size on W_NarrativeMenu_QuestJournal_C_Initialize");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_Initialize, Narrative) == 0x000000, "Member 'W_NarrativeMenu_QuestJournal_C_Initialize::Narrative' has a wrong offset!");

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.OnTogglePartyQuestsAction
// 0x000C (0x000C - 0x0000)
struct W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction final
{
public:
	class FName                                   ActionName;                                        // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Checked;                                           // 0x0008(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsChecked_ReturnValue;                    // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction) == 0x000004, "Wrong alignment on W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction");
static_assert(sizeof(W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction) == 0x00000C, "Wrong size on W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction, ActionName) == 0x000000, "Member 'W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction::ActionName' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction, Checked) == 0x000008, "Member 'W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction::Checked' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction, CallFunc_IsChecked_ReturnValue) == 0x000009, "Member 'W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction::CallFunc_IsChecked_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction, CallFunc_Not_PreBool_ReturnValue) == 0x00000A, "Member 'W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.Party Quests Toggled
// 0x0001 (0x0001 - 0x0000)
struct W_NarrativeMenu_QuestJournal_C_Party_Quests_Toggled final
{
public:
	bool                                          PartyQuests;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_NarrativeMenu_QuestJournal_C_Party_Quests_Toggled) == 0x000001, "Wrong alignment on W_NarrativeMenu_QuestJournal_C_Party_Quests_Toggled");
static_assert(sizeof(W_NarrativeMenu_QuestJournal_C_Party_Quests_Toggled) == 0x000001, "Wrong size on W_NarrativeMenu_QuestJournal_C_Party_Quests_Toggled");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_Party_Quests_Toggled, PartyQuests) == 0x000000, "Member 'W_NarrativeMenu_QuestJournal_C_Party_Quests_Toggled::PartyQuests' has a wrong offset!");

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.RegisterActions
// 0x0014 (0x0014 - 0x0000)
struct W_NarrativeMenu_QuestJournal_C_RegisterActions final
{
public:
	TDelegate<void(class FName ActionName)>       K2Node_CreateDelegate_OutputDelegate;              // 0x0000(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FInputActionBindingHandle              CallFunc_RegisterBinding_BindingHandle;            // 0x0010(0x0004)(NoDestructor)
};
static_assert(alignof(W_NarrativeMenu_QuestJournal_C_RegisterActions) == 0x000004, "Wrong alignment on W_NarrativeMenu_QuestJournal_C_RegisterActions");
static_assert(sizeof(W_NarrativeMenu_QuestJournal_C_RegisterActions) == 0x000014, "Wrong size on W_NarrativeMenu_QuestJournal_C_RegisterActions");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_RegisterActions, K2Node_CreateDelegate_OutputDelegate) == 0x000000, "Member 'W_NarrativeMenu_QuestJournal_C_RegisterActions::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_RegisterActions, CallFunc_RegisterBinding_BindingHandle) == 0x000010, "Member 'W_NarrativeMenu_QuestJournal_C_RegisterActions::CallFunc_RegisterBinding_BindingHandle' has a wrong offset!");

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.Show Quest
// 0x0010 (0x0010 - 0x0000)
struct W_NarrativeMenu_QuestJournal_C_Show_Quest final
{
public:
	class UQuest*                                 Quest;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_QuestJournalQuest_C*                JournalButton;                                     // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_NarrativeMenu_QuestJournal_C_Show_Quest) == 0x000008, "Wrong alignment on W_NarrativeMenu_QuestJournal_C_Show_Quest");
static_assert(sizeof(W_NarrativeMenu_QuestJournal_C_Show_Quest) == 0x000010, "Wrong size on W_NarrativeMenu_QuestJournal_C_Show_Quest");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_Show_Quest, Quest) == 0x000000, "Member 'W_NarrativeMenu_QuestJournal_C_Show_Quest::Quest' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_Show_Quest, JournalButton) == 0x000008, "Member 'W_NarrativeMenu_QuestJournal_C_Show_Quest::JournalButton' has a wrong offset!");

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.BP_GetDesiredFocusTarget
// 0x0020 (0x0020 - 0x0000)
struct W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget final
{
public:
	class UWidget*                                ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0008(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_1;                 // 0x0010(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget) == 0x000008, "Wrong alignment on W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget");
static_assert(sizeof(W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget) == 0x000020, "Wrong size on W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget, ReturnValue) == 0x000000, "Member 'W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget, CallFunc_GetChildAt_ReturnValue) == 0x000008, "Member 'W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget, CallFunc_GetChildAt_ReturnValue_1) == 0x000010, "Member 'W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget::CallFunc_GetChildAt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget, CallFunc_IsValid_ReturnValue) == 0x000018, "Member 'W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget::CallFunc_IsValid_ReturnValue' has a wrong offset!");

}

