﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_Van

#include "Basic.hpp"

#include "BP_VehicleMaster_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass Vehicle_Van.Vehicle_Van_C
// 0x0000 (0x04C0 - 0x04C0)
class AVehicle_Van_C final : public ABP_VehicleMaster_C
{
public:
	void UserConstructionScript();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Vehicle_Van_C">();
	}
	static class AVehicle_Van_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<AVehicle_Van_C>();
	}
};
static_assert(alignof(AVehicle_Van_C) == 0x000008, "Wrong alignment on AVehicle_Van_C");
static_assert(sizeof(AVehicle_Van_C) == 0x0004C0, "Wrong size on AVehicle_Van_C");

}

