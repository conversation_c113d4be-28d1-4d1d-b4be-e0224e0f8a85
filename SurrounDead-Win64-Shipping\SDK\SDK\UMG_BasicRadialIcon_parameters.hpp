﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UMG_BasicRadialIcon

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "EIconAnimationStyles_structs.hpp"


namespace SDK::Params
{

// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.ExecuteUbergraph_UMG_BasicRadialIcon
// 0x00C0 (0x00C0 - 0x0000)
struct UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidgetAnimation*                       Temp_object_Variable;                              // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EIconAnimationStyles                          Temp_byte_Variable;                                // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12[0x6];                                       // 0x0012(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidgetAnimation*                       K2Node_Select_Default;                             // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0030(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_LinearColorLerp_ReturnValue;              // 0x0088(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetHeightOverride_InHeightOverride_ImplicitCast; // 0x00A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetWidthOverride_InWidthOverride_ImplicitCast; // 0x00A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_A_ImplicitCast;       // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_B_ImplicitCast;       // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_LinearColorLerp_Alpha_ImplicitCast;       // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon) == 0x000008, "Wrong alignment on UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon");
static_assert(sizeof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon) == 0x0000C0, "Wrong size on UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, EntryPoint) == 0x000000, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::EntryPoint' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, Temp_object_Variable) == 0x000008, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::Temp_object_Variable' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, K2Node_Event_IsDesignTime) == 0x000010, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, Temp_byte_Variable) == 0x000011, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, K2Node_Select_Default) == 0x000018, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_IsValid_ReturnValue) == 0x000020, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_PlayAnimation_ReturnValue) == 0x000028, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, K2Node_Event_MyGeometry) == 0x000030, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, K2Node_Event_InDeltaTime) == 0x000068, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_FClamp_ReturnValue) == 0x000070, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000078, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_Add_DoubleDouble_ReturnValue) == 0x000080, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_LinearColorLerp_ReturnValue) == 0x000088, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_LinearColorLerp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000098, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_SetHeightOverride_InHeightOverride_ImplicitCast) == 0x0000A0, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_SetHeightOverride_InHeightOverride_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_SetWidthOverride_InWidthOverride_ImplicitCast) == 0x0000A4, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_SetWidthOverride_InWidthOverride_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_Divide_DoubleDouble_A_ImplicitCast) == 0x0000A8, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_Divide_DoubleDouble_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_Divide_DoubleDouble_B_ImplicitCast) == 0x0000B0, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_Divide_DoubleDouble_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon, CallFunc_LinearColorLerp_Alpha_ImplicitCast) == 0x0000B8, "Member 'UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon::CallFunc_LinearColorLerp_Alpha_ImplicitCast' has a wrong offset!");

// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.Get_Icon_ColorAndOpacity_0
// 0x0010 (0x0010 - 0x0000)
struct UMG_BasicRadialIcon_C_Get_Icon_ColorAndOpacity_0 final
{
public:
	struct FLinearColor                           ReturnValue;                                       // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UMG_BasicRadialIcon_C_Get_Icon_ColorAndOpacity_0) == 0x000004, "Wrong alignment on UMG_BasicRadialIcon_C_Get_Icon_ColorAndOpacity_0");
static_assert(sizeof(UMG_BasicRadialIcon_C_Get_Icon_ColorAndOpacity_0) == 0x000010, "Wrong size on UMG_BasicRadialIcon_C_Get_Icon_ColorAndOpacity_0");
static_assert(offsetof(UMG_BasicRadialIcon_C_Get_Icon_ColorAndOpacity_0, ReturnValue) == 0x000000, "Member 'UMG_BasicRadialIcon_C_Get_Icon_ColorAndOpacity_0::ReturnValue' has a wrong offset!");

// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct UMG_BasicRadialIcon_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UMG_BasicRadialIcon_C_PreConstruct) == 0x000001, "Wrong alignment on UMG_BasicRadialIcon_C_PreConstruct");
static_assert(sizeof(UMG_BasicRadialIcon_C_PreConstruct) == 0x000001, "Wrong size on UMG_BasicRadialIcon_C_PreConstruct");
static_assert(offsetof(UMG_BasicRadialIcon_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'UMG_BasicRadialIcon_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.Tick
// 0x003C (0x003C - 0x0000)
struct UMG_BasicRadialIcon_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UMG_BasicRadialIcon_C_Tick) == 0x000004, "Wrong alignment on UMG_BasicRadialIcon_C_Tick");
static_assert(sizeof(UMG_BasicRadialIcon_C_Tick) == 0x00003C, "Wrong size on UMG_BasicRadialIcon_C_Tick");
static_assert(offsetof(UMG_BasicRadialIcon_C_Tick, MyGeometry) == 0x000000, "Member 'UMG_BasicRadialIcon_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(UMG_BasicRadialIcon_C_Tick, InDeltaTime) == 0x000038, "Member 'UMG_BasicRadialIcon_C_Tick::InDeltaTime' has a wrong offset!");

}

