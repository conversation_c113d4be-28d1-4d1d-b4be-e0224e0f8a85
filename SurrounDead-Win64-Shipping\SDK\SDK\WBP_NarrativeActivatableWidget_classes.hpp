﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeActivatableWidget

#include "Basic.hpp"

#include "NarrativeCommonUI_classes.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C
// 0x0010 (0x0430 - 0x0420)
class UWBP_NarrativeActivatableWidget_C : public UNarrativeActivatableWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0420(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	bool                                          DeactivateOnBack;                                  // 0x0428(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          AutoFocusDesiredTargetOnActivate;                  // 0x0429(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void BP_OnActivated();
	void BP_OnDeactivated();
	bool BP_OnHandleBackAction();
	void Construct();
	void ExecuteUbergraph_WBP_NarrativeActivatableWidget(int32 EntryPoint);
	void HandleFocus();
	void RegisterActions();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WBP_NarrativeActivatableWidget_C">();
	}
	static class UWBP_NarrativeActivatableWidget_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWBP_NarrativeActivatableWidget_C>();
	}
};
static_assert(alignof(UWBP_NarrativeActivatableWidget_C) == 0x000008, "Wrong alignment on UWBP_NarrativeActivatableWidget_C");
static_assert(sizeof(UWBP_NarrativeActivatableWidget_C) == 0x000430, "Wrong size on UWBP_NarrativeActivatableWidget_C");
static_assert(offsetof(UWBP_NarrativeActivatableWidget_C, UberGraphFrame) == 0x000420, "Member 'UWBP_NarrativeActivatableWidget_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeActivatableWidget_C, DeactivateOnBack) == 0x000428, "Member 'UWBP_NarrativeActivatableWidget_C::DeactivateOnBack' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeActivatableWidget_C, AutoFocusDesiredTargetOnActivate) == 0x000429, "Member 'UWBP_NarrativeActivatableWidget_C::AutoFocusDesiredTargetOnActivate' has a wrong offset!");

}

