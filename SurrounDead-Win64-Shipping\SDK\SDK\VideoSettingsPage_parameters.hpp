﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: VideoSettingsPage

#include "Basic.hpp"


namespace SDK::Params
{

// Function VideoSettingsPage.VideoSettingsPage_C.BndEvt__AutoApplyCheckbox_K2Node_ComponentBoundEvent_336_OnCheckBoxComponentStateChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct VideoSettingsPage_C_BndEvt__AutoApplyCheckbox_K2Node_ComponentBoundEvent_336_OnCheckBoxComponentStateChanged__DelegateSignature final
{
public:
	bool                                          bIsChecked;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(VideoSettingsPage_C_BndEvt__AutoApplyCheckbox_K2Node_ComponentBoundEvent_336_OnCheckBoxComponentStateChanged__DelegateSignature) == 0x000001, "Wrong alignment on VideoSettingsPage_C_BndEvt__AutoApplyCheckbox_K2Node_ComponentBoundEvent_336_OnCheckBoxComponentStateChanged__DelegateSignature");
static_assert(sizeof(VideoSettingsPage_C_BndEvt__AutoApplyCheckbox_K2Node_ComponentBoundEvent_336_OnCheckBoxComponentStateChanged__DelegateSignature) == 0x000001, "Wrong size on VideoSettingsPage_C_BndEvt__AutoApplyCheckbox_K2Node_ComponentBoundEvent_336_OnCheckBoxComponentStateChanged__DelegateSignature");
static_assert(offsetof(VideoSettingsPage_C_BndEvt__AutoApplyCheckbox_K2Node_ComponentBoundEvent_336_OnCheckBoxComponentStateChanged__DelegateSignature, bIsChecked) == 0x000000, "Member 'VideoSettingsPage_C_BndEvt__AutoApplyCheckbox_K2Node_ComponentBoundEvent_336_OnCheckBoxComponentStateChanged__DelegateSignature::bIsChecked' has a wrong offset!");

// Function VideoSettingsPage.VideoSettingsPage_C.BndEvt__AutoSaveCheckbox_K2Node_ComponentBoundEvent_40_OnCheckBoxComponentStateChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct VideoSettingsPage_C_BndEvt__AutoSaveCheckbox_K2Node_ComponentBoundEvent_40_OnCheckBoxComponentStateChanged__DelegateSignature final
{
public:
	bool                                          bIsChecked;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(VideoSettingsPage_C_BndEvt__AutoSaveCheckbox_K2Node_ComponentBoundEvent_40_OnCheckBoxComponentStateChanged__DelegateSignature) == 0x000001, "Wrong alignment on VideoSettingsPage_C_BndEvt__AutoSaveCheckbox_K2Node_ComponentBoundEvent_40_OnCheckBoxComponentStateChanged__DelegateSignature");
static_assert(sizeof(VideoSettingsPage_C_BndEvt__AutoSaveCheckbox_K2Node_ComponentBoundEvent_40_OnCheckBoxComponentStateChanged__DelegateSignature) == 0x000001, "Wrong size on VideoSettingsPage_C_BndEvt__AutoSaveCheckbox_K2Node_ComponentBoundEvent_40_OnCheckBoxComponentStateChanged__DelegateSignature");
static_assert(offsetof(VideoSettingsPage_C_BndEvt__AutoSaveCheckbox_K2Node_ComponentBoundEvent_40_OnCheckBoxComponentStateChanged__DelegateSignature, bIsChecked) == 0x000000, "Member 'VideoSettingsPage_C_BndEvt__AutoSaveCheckbox_K2Node_ComponentBoundEvent_40_OnCheckBoxComponentStateChanged__DelegateSignature::bIsChecked' has a wrong offset!");

// Function VideoSettingsPage.VideoSettingsPage_C.CallbackUpscalingMethod
// 0x0004 (0x0004 - 0x0000)
struct VideoSettingsPage_C_CallbackUpscalingMethod final
{
public:
	int32                                         NewValue;                                          // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(VideoSettingsPage_C_CallbackUpscalingMethod) == 0x000004, "Wrong alignment on VideoSettingsPage_C_CallbackUpscalingMethod");
static_assert(sizeof(VideoSettingsPage_C_CallbackUpscalingMethod) == 0x000004, "Wrong size on VideoSettingsPage_C_CallbackUpscalingMethod");
static_assert(offsetof(VideoSettingsPage_C_CallbackUpscalingMethod, NewValue) == 0x000000, "Member 'VideoSettingsPage_C_CallbackUpscalingMethod::NewValue' has a wrong offset!");

// Function VideoSettingsPage.VideoSettingsPage_C.ExecuteUbergraph_VideoSettingsPage
// 0x0078 (0x0078 - 0x0000)
struct VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_bIsChecked_1;           // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UAutoSettingWidget*>             CallFunc_GetChildSettings_ReturnValue;             // 0x0018(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UAutoSettingWidget*                     CallFunc_Array_Get_Item;                           // 0x0028(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_bIsChecked;             // 0x0034(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_35[0x3];                                       // 0x0035(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UAutoSettingWidget*>             CallFunc_GetChildSettings_ReturnValue_1;           // 0x0038(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UAutoSettingWidget*                     CallFunc_Array_Get_Item_1;                         // 0x0048(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0054(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_55[0x3];                                       // 0x0055(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(int32 NewValue)>               K2Node_CreateDelegate_OutputDelegate;              // 0x0058(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_NewValue;                       // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x006C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0071(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0072(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_73[0x1];                                       // 0x0073(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0074(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage) == 0x000008, "Wrong alignment on VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage");
static_assert(sizeof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage) == 0x000078, "Wrong size on VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, EntryPoint) == 0x000000, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, Temp_int_Array_Index_Variable) == 0x000004, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, Temp_int_Loop_Counter_Variable) == 0x000008, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_Add_IntInt_ReturnValue) == 0x00000C, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, Temp_int_Array_Index_Variable_1) == 0x000010, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, K2Node_ComponentBoundEvent_bIsChecked_1) == 0x000014, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::K2Node_ComponentBoundEvent_bIsChecked_1' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_GetChildSettings_ReturnValue) == 0x000018, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_GetChildSettings_ReturnValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_Array_Get_Item) == 0x000028, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_Array_Length_ReturnValue) == 0x000030, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, K2Node_ComponentBoundEvent_bIsChecked) == 0x000034, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::K2Node_ComponentBoundEvent_bIsChecked' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_GetChildSettings_ReturnValue_1) == 0x000038, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_GetChildSettings_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_Array_Get_Item_1) == 0x000048, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_Array_Length_ReturnValue_1) == 0x000050, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_Less_IntInt_ReturnValue) == 0x000054, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, K2Node_CreateDelegate_OutputDelegate) == 0x000058, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, K2Node_CustomEvent_NewValue) == 0x000068, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::K2Node_CustomEvent_NewValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, Temp_int_Loop_Counter_Variable_1) == 0x00006C, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000070, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_Not_PreBool_ReturnValue) == 0x000071, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_Less_IntInt_ReturnValue_1) == 0x000072, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage, CallFunc_Add_IntInt_ReturnValue_1) == 0x000074, "Member 'VideoSettingsPage_C_ExecuteUbergraph_VideoSettingsPage::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");

// Function VideoSettingsPage.VideoSettingsPage_C.Get_ApplyButton_bIsEnabled_0
// 0x0002 (0x0002 - 0x0000)
struct VideoSettingsPage_C_Get_ApplyButton_bIsEnabled_0 final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_DoesAnyChildSettingHaveUnsavedChange_ReturnValue; // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(VideoSettingsPage_C_Get_ApplyButton_bIsEnabled_0) == 0x000001, "Wrong alignment on VideoSettingsPage_C_Get_ApplyButton_bIsEnabled_0");
static_assert(sizeof(VideoSettingsPage_C_Get_ApplyButton_bIsEnabled_0) == 0x000002, "Wrong size on VideoSettingsPage_C_Get_ApplyButton_bIsEnabled_0");
static_assert(offsetof(VideoSettingsPage_C_Get_ApplyButton_bIsEnabled_0, ReturnValue) == 0x000000, "Member 'VideoSettingsPage_C_Get_ApplyButton_bIsEnabled_0::ReturnValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_Get_ApplyButton_bIsEnabled_0, CallFunc_DoesAnyChildSettingHaveUnsavedChange_ReturnValue) == 0x000001, "Member 'VideoSettingsPage_C_Get_ApplyButton_bIsEnabled_0::CallFunc_DoesAnyChildSettingHaveUnsavedChange_ReturnValue' has a wrong offset!");

// Function VideoSettingsPage.VideoSettingsPage_C.Get_AutoApplyCheckbox_bIsEnabled_0
// 0x0002 (0x0002 - 0x0000)
struct VideoSettingsPage_C_Get_AutoApplyCheckbox_bIsEnabled_0 final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(VideoSettingsPage_C_Get_AutoApplyCheckbox_bIsEnabled_0) == 0x000001, "Wrong alignment on VideoSettingsPage_C_Get_AutoApplyCheckbox_bIsEnabled_0");
static_assert(sizeof(VideoSettingsPage_C_Get_AutoApplyCheckbox_bIsEnabled_0) == 0x000002, "Wrong size on VideoSettingsPage_C_Get_AutoApplyCheckbox_bIsEnabled_0");
static_assert(offsetof(VideoSettingsPage_C_Get_AutoApplyCheckbox_bIsEnabled_0, ReturnValue) == 0x000000, "Member 'VideoSettingsPage_C_Get_AutoApplyCheckbox_bIsEnabled_0::ReturnValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_Get_AutoApplyCheckbox_bIsEnabled_0, CallFunc_Not_PreBool_ReturnValue) == 0x000001, "Member 'VideoSettingsPage_C_Get_AutoApplyCheckbox_bIsEnabled_0::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");

// Function VideoSettingsPage.VideoSettingsPage_C.Get_SaveButton_bIsEnabled_0
// 0x0002 (0x0002 - 0x0000)
struct VideoSettingsPage_C_Get_SaveButton_bIsEnabled_0 final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_DoesAnyChildSettingHaveUnsavedChange_ReturnValue; // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(VideoSettingsPage_C_Get_SaveButton_bIsEnabled_0) == 0x000001, "Wrong alignment on VideoSettingsPage_C_Get_SaveButton_bIsEnabled_0");
static_assert(sizeof(VideoSettingsPage_C_Get_SaveButton_bIsEnabled_0) == 0x000002, "Wrong size on VideoSettingsPage_C_Get_SaveButton_bIsEnabled_0");
static_assert(offsetof(VideoSettingsPage_C_Get_SaveButton_bIsEnabled_0, ReturnValue) == 0x000000, "Member 'VideoSettingsPage_C_Get_SaveButton_bIsEnabled_0::ReturnValue' has a wrong offset!");
static_assert(offsetof(VideoSettingsPage_C_Get_SaveButton_bIsEnabled_0, CallFunc_DoesAnyChildSettingHaveUnsavedChange_ReturnValue) == 0x000001, "Member 'VideoSettingsPage_C_Get_SaveButton_bIsEnabled_0::CallFunc_DoesAnyChildSettingHaveUnsavedChange_ReturnValue' has a wrong offset!");

}

