﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Effect

#include "Basic.hpp"

#include "S_Effects_structs.hpp"
#include "EEffectType_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "ETriggerMethod_structs.hpp"
#include "UMG_structs.hpp"
#include "Slate_structs.hpp"


namespace SDK::Params
{

// Function WB_Effect.WB_Effect_C.AddAttributes
// 0x0060 (0x0060 - 0x0000)
struct WB_Effect_C_AddAttributes final
{
public:
	class UImage*                                 Image;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectShear_Effect_Shear;              // 0x0008(0x0010)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetEffectAngle_Effect_Angle;              // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UObject*                                CallFunc_GetEffectTexture_Custom_Effect_Texture;   // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_GetEffectColor_Effect_Color;              // 0x0028(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectTranslation_Transition;          // 0x0038(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectScale_EffectScale;               // 0x0048(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetRenderTransformAngle_Angle_ImplicitCast; // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_AddAttributes) == 0x000008, "Wrong alignment on WB_Effect_C_AddAttributes");
static_assert(sizeof(WB_Effect_C_AddAttributes) == 0x000060, "Wrong size on WB_Effect_C_AddAttributes");
static_assert(offsetof(WB_Effect_C_AddAttributes, Image) == 0x000000, "Member 'WB_Effect_C_AddAttributes::Image' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_AddAttributes, CallFunc_GetEffectShear_Effect_Shear) == 0x000008, "Member 'WB_Effect_C_AddAttributes::CallFunc_GetEffectShear_Effect_Shear' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_AddAttributes, CallFunc_GetEffectAngle_Effect_Angle) == 0x000018, "Member 'WB_Effect_C_AddAttributes::CallFunc_GetEffectAngle_Effect_Angle' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_AddAttributes, CallFunc_GetEffectTexture_Custom_Effect_Texture) == 0x000020, "Member 'WB_Effect_C_AddAttributes::CallFunc_GetEffectTexture_Custom_Effect_Texture' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_AddAttributes, CallFunc_GetEffectColor_Effect_Color) == 0x000028, "Member 'WB_Effect_C_AddAttributes::CallFunc_GetEffectColor_Effect_Color' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_AddAttributes, CallFunc_GetEffectTranslation_Transition) == 0x000038, "Member 'WB_Effect_C_AddAttributes::CallFunc_GetEffectTranslation_Transition' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_AddAttributes, CallFunc_GetEffectScale_EffectScale) == 0x000048, "Member 'WB_Effect_C_AddAttributes::CallFunc_GetEffectScale_EffectScale' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_AddAttributes, CallFunc_SetRenderTransformAngle_Angle_ImplicitCast) == 0x000058, "Member 'WB_Effect_C_AddAttributes::CallFunc_SetRenderTransformAngle_Angle_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.AddEffect
// 0x0088 (0x0088 - 0x0000)
struct WB_Effect_C_AddEffect final
{
public:
	struct FS_Effects                             Effect_0;                                          // 0x0000(0x0088)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_AddEffect) == 0x000008, "Wrong alignment on WB_Effect_C_AddEffect");
static_assert(sizeof(WB_Effect_C_AddEffect) == 0x000088, "Wrong size on WB_Effect_C_AddEffect");
static_assert(offsetof(WB_Effect_C_AddEffect, Effect_0) == 0x000000, "Member 'WB_Effect_C_AddEffect::Effect_0' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.Anim_Fade
// 0x0038 (0x0038 - 0x0000)
struct WB_Effect_C_Anim_Fade final
{
public:
	bool                                          FadeIn_0;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        PlaybackSpeed;                                     // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable_1;                              // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13[0x1];                                       // 0x0013(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_Select_Default_1;                           // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_25[0x3];                                       // 0x0025(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast; // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_Anim_Fade) == 0x000008, "Wrong alignment on WB_Effect_C_Anim_Fade");
static_assert(sizeof(WB_Effect_C_Anim_Fade) == 0x000038, "Wrong size on WB_Effect_C_Anim_Fade");
static_assert(offsetof(WB_Effect_C_Anim_Fade, FadeIn_0) == 0x000000, "Member 'WB_Effect_C_Anim_Fade::FadeIn_0' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, PlaybackSpeed) == 0x000008, "Member 'WB_Effect_C_Anim_Fade::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, Temp_byte_Variable) == 0x000010, "Member 'WB_Effect_C_Anim_Fade::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, Temp_byte_Variable_1) == 0x000011, "Member 'WB_Effect_C_Anim_Fade::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, Temp_bool_Variable) == 0x000012, "Member 'WB_Effect_C_Anim_Fade::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, Temp_int_Variable) == 0x000014, "Member 'WB_Effect_C_Anim_Fade::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, Temp_int_Variable_1) == 0x000018, "Member 'WB_Effect_C_Anim_Fade::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, Temp_bool_Variable_1) == 0x00001C, "Member 'WB_Effect_C_Anim_Fade::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, K2Node_Select_Default) == 0x000020, "Member 'WB_Effect_C_Anim_Fade::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, K2Node_Select_Default_1) == 0x000024, "Member 'WB_Effect_C_Anim_Fade::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, CallFunc_PlayAnimation_ReturnValue) == 0x000028, "Member 'WB_Effect_C_Anim_Fade::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Fade, CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast) == 0x000030, "Member 'WB_Effect_C_Anim_Fade::CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.Anim_Highlight1
// 0x0030 (0x0030 - 0x0000)
struct WB_Effect_C_Anim_Highlight1 final
{
public:
	double                                        PlaybackSpeed;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable_1;                              // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B[0x1];                                        // 0x000B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_Select_Default_1;                           // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast; // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_Anim_Highlight1) == 0x000008, "Wrong alignment on WB_Effect_C_Anim_Highlight1");
static_assert(sizeof(WB_Effect_C_Anim_Highlight1) == 0x000030, "Wrong size on WB_Effect_C_Anim_Highlight1");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, PlaybackSpeed) == 0x000000, "Member 'WB_Effect_C_Anim_Highlight1::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, Temp_byte_Variable) == 0x000008, "Member 'WB_Effect_C_Anim_Highlight1::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, Temp_byte_Variable_1) == 0x000009, "Member 'WB_Effect_C_Anim_Highlight1::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, Temp_bool_Variable) == 0x00000A, "Member 'WB_Effect_C_Anim_Highlight1::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, Temp_int_Variable) == 0x00000C, "Member 'WB_Effect_C_Anim_Highlight1::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, Temp_int_Variable_1) == 0x000010, "Member 'WB_Effect_C_Anim_Highlight1::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, Temp_bool_Variable_1) == 0x000014, "Member 'WB_Effect_C_Anim_Highlight1::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, K2Node_Select_Default) == 0x000018, "Member 'WB_Effect_C_Anim_Highlight1::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, K2Node_Select_Default_1) == 0x00001C, "Member 'WB_Effect_C_Anim_Highlight1::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, CallFunc_PlayAnimation_ReturnValue) == 0x000020, "Member 'WB_Effect_C_Anim_Highlight1::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight1, CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast) == 0x000028, "Member 'WB_Effect_C_Anim_Highlight1::CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.Anim_Highlight2
// 0x0030 (0x0030 - 0x0000)
struct WB_Effect_C_Anim_Highlight2 final
{
public:
	double                                        PlaybackSpeed;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable_1;                              // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B[0x1];                                        // 0x000B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_Select_Default_1;                           // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast; // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_Anim_Highlight2) == 0x000008, "Wrong alignment on WB_Effect_C_Anim_Highlight2");
static_assert(sizeof(WB_Effect_C_Anim_Highlight2) == 0x000030, "Wrong size on WB_Effect_C_Anim_Highlight2");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, PlaybackSpeed) == 0x000000, "Member 'WB_Effect_C_Anim_Highlight2::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, Temp_byte_Variable) == 0x000008, "Member 'WB_Effect_C_Anim_Highlight2::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, Temp_byte_Variable_1) == 0x000009, "Member 'WB_Effect_C_Anim_Highlight2::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, Temp_bool_Variable) == 0x00000A, "Member 'WB_Effect_C_Anim_Highlight2::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, Temp_int_Variable) == 0x00000C, "Member 'WB_Effect_C_Anim_Highlight2::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, Temp_int_Variable_1) == 0x000010, "Member 'WB_Effect_C_Anim_Highlight2::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, Temp_bool_Variable_1) == 0x000014, "Member 'WB_Effect_C_Anim_Highlight2::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, K2Node_Select_Default) == 0x000018, "Member 'WB_Effect_C_Anim_Highlight2::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, K2Node_Select_Default_1) == 0x00001C, "Member 'WB_Effect_C_Anim_Highlight2::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, CallFunc_PlayAnimation_ReturnValue) == 0x000020, "Member 'WB_Effect_C_Anim_Highlight2::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_Highlight2, CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast) == 0x000028, "Member 'WB_Effect_C_Anim_Highlight2::CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.Anim_HighlightLoop
// 0x0038 (0x0038 - 0x0000)
struct WB_Effect_C_Anim_HighlightLoop final
{
public:
	bool                                          StartStop;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        PlaybackSpeed;                                     // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable_1;                              // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13[0x1];                                       // 0x0013(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_Select_Default_1;                           // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_25[0x3];                                       // 0x0025(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast; // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_Anim_HighlightLoop) == 0x000008, "Wrong alignment on WB_Effect_C_Anim_HighlightLoop");
static_assert(sizeof(WB_Effect_C_Anim_HighlightLoop) == 0x000038, "Wrong size on WB_Effect_C_Anim_HighlightLoop");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, StartStop) == 0x000000, "Member 'WB_Effect_C_Anim_HighlightLoop::StartStop' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, PlaybackSpeed) == 0x000008, "Member 'WB_Effect_C_Anim_HighlightLoop::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, Temp_byte_Variable) == 0x000010, "Member 'WB_Effect_C_Anim_HighlightLoop::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, Temp_byte_Variable_1) == 0x000011, "Member 'WB_Effect_C_Anim_HighlightLoop::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, Temp_bool_Variable) == 0x000012, "Member 'WB_Effect_C_Anim_HighlightLoop::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, Temp_int_Variable) == 0x000014, "Member 'WB_Effect_C_Anim_HighlightLoop::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, Temp_int_Variable_1) == 0x000018, "Member 'WB_Effect_C_Anim_HighlightLoop::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, Temp_bool_Variable_1) == 0x00001C, "Member 'WB_Effect_C_Anim_HighlightLoop::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, K2Node_Select_Default) == 0x000020, "Member 'WB_Effect_C_Anim_HighlightLoop::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, K2Node_Select_Default_1) == 0x000024, "Member 'WB_Effect_C_Anim_HighlightLoop::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, CallFunc_PlayAnimation_ReturnValue) == 0x000028, "Member 'WB_Effect_C_Anim_HighlightLoop::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Anim_HighlightLoop, CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast) == 0x000030, "Member 'WB_Effect_C_Anim_HighlightLoop::CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.CreateParticle
// 0x0004 (0x0004 - 0x0000)
struct WB_Effect_C_CreateParticle final
{
public:
	int32                                         NumParticles;                                      // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_CreateParticle) == 0x000004, "Wrong alignment on WB_Effect_C_CreateParticle");
static_assert(sizeof(WB_Effect_C_CreateParticle) == 0x000004, "Wrong size on WB_Effect_C_CreateParticle");
static_assert(offsetof(WB_Effect_C_CreateParticle, NumParticles) == 0x000000, "Member 'WB_Effect_C_CreateParticle::NumParticles' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.EventPreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_C_EventPreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_EventPreConstruct) == 0x000001, "Wrong alignment on WB_Effect_C_EventPreConstruct");
static_assert(sizeof(WB_Effect_C_EventPreConstruct) == 0x000001, "Wrong size on WB_Effect_C_EventPreConstruct");
static_assert(offsetof(WB_Effect_C_EventPreConstruct, IsDesignTime) == 0x000000, "Member 'WB_Effect_C_EventPreConstruct::IsDesignTime' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.ExecuteUbergraph_WB_Effect
// 0x03E0 (0x03E0 - 0x0000)
struct WB_Effect_C_ExecuteUbergraph_WB_Effect final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_1;               // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_1;                     // 0x001A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_2;               // 0x001B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_3;               // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_2;                     // 0x001D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_3;                     // 0x001E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_4;               // 0x001F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_5;               // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x3];                                       // 0x0021(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_2;                               // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0030(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3C[0x4];                                       // 0x003C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Particle_C*                         K2Node_DynamicCast_AsWB_Particle;                  // 0x0040(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0049(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x004A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_4;                     // 0x004B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_6;               // 0x004C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4D[0x3];                                       // 0x004D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_1;                 // 0x0050(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UWB_Particle_C*                         K2Node_DynamicCast_AsWB_Particle_1;                // 0x0058(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0061(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_62[0x2];                                       // 0x0062(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetChildrenCount_ReturnValue_1;           // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_7;               // 0x0069(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_5;                     // 0x006A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_8;               // 0x006B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_9;               // 0x006C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_6;                     // 0x006D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_10;              // 0x006E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6F[0x1];                                       // 0x006F(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value; // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_7;                     // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0079(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7A[0x6];                                       // 0x007A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_1; // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x0088(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0089(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue;       // 0x008A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_8;                     // 0x008B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue_1;     // 0x008C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8D[0x3];                                       // 0x008D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_2; // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0098(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_11;              // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue_2;     // 0x00A9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AA[0x6];                                       // 0x00AA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_3; // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue_3;     // 0x00B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_1;          // 0x00B9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_BA[0x6];                                       // 0x00BA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_4; // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue_4;     // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_2;          // 0x00C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_CA[0x2];                                       // 0x00CA(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(double Percent)>               K2Node_CreateDelegate_OutputDelegate_1;            // 0x00CC(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_9;                     // 0x00DC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x00DD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_DoubleDouble_ReturnValue;        // 0x00DE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_3;          // 0x00DF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_4;          // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E1[0x3];                                       // 0x00E1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_2;            // 0x00E4(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_3;                               // 0x00F4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_3;                 // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_FC[0x4];                                       // 0x00FC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Particle_C*                         CallFunc_Create_ReturnValue;                       // 0x0100(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable;                                // 0x0108(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_12;              // 0x0110(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_111[0x7];                                      // 0x0111(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              K2Node_CustomEvent_Size;                           // 0x0118(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FS_Effects                             K2Node_CustomEvent_Effect;                         // 0x0128(0x0088)(NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          K2Node_CustomEvent_FillType;                       // 0x01B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_2;                              // 0x01B1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1B2[0x2];                                      // 0x01B2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x01B4(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x01EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x01F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x01F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_IsDesignTime;                   // 0x01F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_10;                    // 0x01F3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_13;              // 0x01F4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x01F5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1F6[0x2];                                      // 0x01F6(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_CustomEvent_NumParticles;                   // 0x01F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_2;           // 0x01FC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_11;                    // 0x01FD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1FE[0x2];                                      // 0x01FE(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                CallFunc_GetEffectTexture_Custom_Effect_Texture;   // 0x0200(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetPlaybackSpeed_Playback_Speed;          // 0x0208(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_GetEffectColor_Effect_Color;              // 0x0210(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectScale_EffectScale;               // 0x0220(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectTranslation_Transition;          // 0x0230(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetEffectAngle_Effect_Angle;              // 0x0240(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectShear_Effect_Shear;              // 0x0248(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_12;                    // 0x0258(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_13;                    // 0x0259(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger;                 // 0x025A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_3;                              // 0x025B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_25C[0x4];                                      // 0x025C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetParticleSpread_Particle_Spread;        // 0x0260(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetAddRotationToParticles_Add_Rotation_to_Particles; // 0x0268(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_269[0x7];                                      // 0x0269(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class USoundBase*                             CallFunc_GetSoundEffect_Sound_Effect;              // 0x0270(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffect_ReturnValue;      // 0x0278(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_279[0x7];                                      // 0x0279(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSoundVolume_Sound_Volume_Multiplier;   // 0x0280(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAudioComponent*                        CallFunc_CreateSound2D_ReturnValue;                // 0x0288(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue; // 0x0290(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0291(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffect_ReturnValue_1;    // 0x0292(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_1; // 0x0293(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_14;              // 0x0294(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x0295(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_296[0x2];                                      // 0x0296(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetDecayTime_Decay_Time;                  // 0x0298(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_GetEffectColor_Effect_Color_1;            // 0x02A0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x02B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeProgressChangeColor_ReturnValue; // 0x02B1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2B2[0x6];                                      // 0x02B2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_5; // 0x02B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsTriggerSpecificPercent_ReturnValue;     // 0x02C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_14;                    // 0x02C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C2[0x6];                                      // 0x02C2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x02C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x02D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x02D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x02E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_15;              // 0x02E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2E9[0x7];                                      // 0x02E9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue;         // 0x02F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_4;                              // 0x02F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_2; // 0x02F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2FA[0x6];                                      // 0x02FA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0300(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_1;                           // 0x0308(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0310(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0318(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_15;                    // 0x0328(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_16;                    // 0x0329(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_16;              // 0x032A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_17;                    // 0x032B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_17;              // 0x032C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_18;                    // 0x032D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger_1;               // 0x032E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x032F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FindMinSize_ReturnValue;                  // 0x0330(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UOverlaySlot*                           CallFunc_AddChildToOverlay_ReturnValue;            // 0x0338(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x0340(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_1;                        // 0x0350(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_1;                        // 0x0358(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_2;                    // 0x0360(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsAnimationPlayingForward_ReturnValue;    // 0x0361(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x0362(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_363[0x1];                                      // 0x0363(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetNumParticles_NumParticles;             // 0x0364(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeParticle_ReturnValue;         // 0x0368(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_5;                              // 0x0369(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_18;              // 0x036A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_36B[0x5];                                      // 0x036B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_2;                 // 0x0370(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UWB_Particle_C*                         K2Node_DynamicCast_AsWB_Particle_2;                // 0x0378(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x0380(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_3;                    // 0x0381(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_382[0x2];                                      // 0x0382(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetChildrenCount_ReturnValue_2;           // 0x0384(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_19;                    // 0x0388(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_3;           // 0x0389(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_19;              // 0x038A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_38B[0x5];                                      // 0x038B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBarLinear_C*                   K2Node_CustomEvent_ProgressBar;                    // 0x0390(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Percent;                        // 0x0398(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_2;                           // 0x03A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_20;              // 0x03A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3A9[0x7];                                      // 0x03A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FInterpTo_Constant_ReturnValue;           // 0x03B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_3;                           // 0x03B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_6;                              // 0x03C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Select_Default_4;                           // 0x03C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_21;              // 0x03C2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_20;                    // 0x03C3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_3;                // 0x03C4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_4;                    // 0x03C5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsPlaying_ReturnValue;                    // 0x03C6(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffect_ReturnValue_2;    // 0x03C7(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_7;                              // 0x03C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeParticle_ReturnValue_1;       // 0x03C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_3; // 0x03CA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Select_Default_5;                           // 0x03CB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffect_ReturnValue_3;    // 0x03CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeProgressChangeColor_ReturnValue_1; // 0x03CD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsTriggeredAlwaysOnSpecificPercentValue_ReturnValue; // 0x03CE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_2;                  // 0x03CF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsTriggeredAlways_ReturnValue;            // 0x03D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_3;                  // 0x03D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_4;                  // 0x03D2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_5;                  // 0x03D3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_4;                // 0x03D4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x03D5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_21;                    // 0x03D6(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3D7[0x1];                                      // 0x03D7(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_CreateSound2D_VolumeMultiplier_ImplicitCast; // 0x03D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Delay_Duration_ImplicitCast;              // 0x03DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_ExecuteUbergraph_WB_Effect) == 0x000008, "Wrong alignment on WB_Effect_C_ExecuteUbergraph_WB_Effect");
static_assert(sizeof(WB_Effect_C_ExecuteUbergraph_WB_Effect) == 0x0003E0, "Wrong size on WB_Effect_C_ExecuteUbergraph_WB_Effect");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, EntryPoint) == 0x000000, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Variable) == 0x000004, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable) == 0x000005, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_int_Variable) == 0x000008, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Add_IntInt_ReturnValue) == 0x00000C, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_int_Variable_1) == 0x000010, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Add_IntInt_ReturnValue_1) == 0x000014, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable) == 0x000018, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_1) == 0x000019, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_1) == 0x00001A, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_2) == 0x00001B, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_3) == 0x00001C, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_2) == 0x00001D, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_3) == 0x00001E, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_4) == 0x00001F, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_5) == 0x000020, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_int_Variable_2) == 0x000024, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_int_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Add_IntInt_ReturnValue_2) == 0x000028, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetChildAt_ReturnValue) == 0x000030, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetChildrenCount_ReturnValue) == 0x000038, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_DynamicCast_AsWB_Particle) == 0x000040, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_DynamicCast_AsWB_Particle' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_DynamicCast_bSuccess) == 0x000048, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000049, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsValid_ReturnValue) == 0x00004A, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_4) == 0x00004B, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_6) == 0x00004C, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetChildAt_ReturnValue_1) == 0x000050, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetChildAt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_DynamicCast_AsWB_Particle_1) == 0x000058, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_DynamicCast_AsWB_Particle_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_DynamicCast_bSuccess_1) == 0x000060, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsValid_ReturnValue_1) == 0x000061, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetChildrenCount_ReturnValue_1) == 0x000064, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetChildrenCount_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x000068, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_7) == 0x000069, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_5) == 0x00006A, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_8) == 0x00006B, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_9) == 0x00006C, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_9' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_6) == 0x00006D, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_10) == 0x00006E, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_10' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetSpecificPercentValue_Specific_Percent_Value) == 0x000070, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetSpecificPercentValue_Specific_Percent_Value' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_7) == 0x000078, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000079, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_1) == 0x000080, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsValid_ReturnValue_2) == 0x000088, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000089, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_NearlyEqual_FloatFloat_ReturnValue) == 0x00008A, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_NearlyEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_8) == 0x00008B, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_NearlyEqual_FloatFloat_ReturnValue_1) == 0x00008C, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_NearlyEqual_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_2) == 0x000090, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CreateDelegate_OutputDelegate) == 0x000098, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_11) == 0x0000A8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_11' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_NearlyEqual_FloatFloat_ReturnValue_2) == 0x0000A9, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_NearlyEqual_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_3) == 0x0000B0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_NearlyEqual_FloatFloat_ReturnValue_3) == 0x0000B8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_NearlyEqual_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Less_DoubleDouble_ReturnValue_1) == 0x0000B9, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Less_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_4) == 0x0000C0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_NearlyEqual_FloatFloat_ReturnValue_4) == 0x0000C8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_NearlyEqual_FloatFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Less_DoubleDouble_ReturnValue_2) == 0x0000C9, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Less_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CreateDelegate_OutputDelegate_1) == 0x0000CC, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_9) == 0x0000DC, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_9' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Variable_1) == 0x0000DD, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_NotEqual_DoubleDouble_ReturnValue) == 0x0000DE, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_NotEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Less_DoubleDouble_ReturnValue_3) == 0x0000DF, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Less_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Less_DoubleDouble_ReturnValue_4) == 0x0000E0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Less_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CreateDelegate_OutputDelegate_2) == 0x0000E4, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CreateDelegate_OutputDelegate_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_int_Variable_3) == 0x0000F4, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_int_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Add_IntInt_ReturnValue_3) == 0x0000F8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Add_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Create_ReturnValue) == 0x000100, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_real_Variable) == 0x000108, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_12) == 0x000110, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_12' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CustomEvent_Size) == 0x000118, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CustomEvent_Size' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CustomEvent_Effect) == 0x000128, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CustomEvent_Effect' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CustomEvent_FillType) == 0x0001B0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CustomEvent_FillType' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Variable_2) == 0x0001B1, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_Event_MyGeometry) == 0x0001B4, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_Event_InDeltaTime) == 0x0001EC, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetEffectType_Effect_Texture_Type) == 0x0001F0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_SwitchEnum_CmpSuccess) == 0x0001F1, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CustomEvent_IsDesignTime) == 0x0001F2, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CustomEvent_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_10) == 0x0001F3, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_10' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_13) == 0x0001F4, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_13' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Not_PreBool_ReturnValue) == 0x0001F5, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CustomEvent_NumParticles) == 0x0001F8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CustomEvent_NumParticles' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_LessEqual_IntInt_ReturnValue_2) == 0x0001FC, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_LessEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_11) == 0x0001FD, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_11' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetEffectTexture_Custom_Effect_Texture) == 0x000200, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetEffectTexture_Custom_Effect_Texture' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetPlaybackSpeed_Playback_Speed) == 0x000208, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetPlaybackSpeed_Playback_Speed' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetEffectColor_Effect_Color) == 0x000210, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetEffectColor_Effect_Color' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetEffectScale_EffectScale) == 0x000220, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetEffectScale_EffectScale' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetEffectTranslation_Transition) == 0x000230, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetEffectTranslation_Transition' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetEffectAngle_Effect_Angle) == 0x000240, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetEffectAngle_Effect_Angle' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetEffectShear_Effect_Shear) == 0x000248, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetEffectShear_Effect_Shear' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_12) == 0x000258, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_12' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_13) == 0x000259, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_13' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetTriggerMethod_Trigger) == 0x00025A, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetTriggerMethod_Trigger' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Variable_3) == 0x00025B, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetParticleSpread_Particle_Spread) == 0x000260, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetParticleSpread_Particle_Spread' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetAddRotationToParticles_Add_Rotation_to_Particles) == 0x000268, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetAddRotationToParticles_Add_Rotation_to_Particles' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetSoundEffect_Sound_Effect) == 0x000270, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetSoundEffect_Sound_Effect' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeSoundEffect_ReturnValue) == 0x000278, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeSoundEffect_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetSoundVolume_Sound_Volume_Multiplier) == 0x000280, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetSoundVolume_Sound_Volume_Multiplier' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_CreateSound2D_ReturnValue) == 0x000288, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_CreateSound2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue) == 0x000290, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BooleanOR_ReturnValue) == 0x000291, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeSoundEffect_ReturnValue_1) == 0x000292, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeSoundEffect_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_1) == 0x000293, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_14) == 0x000294, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_14' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BooleanOR_ReturnValue_1) == 0x000295, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetDecayTime_Decay_Time) == 0x000298, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetDecayTime_Decay_Time' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetEffectColor_Effect_Color_1) == 0x0002A0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetEffectColor_Effect_Color_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Not_PreBool_ReturnValue_1) == 0x0002B0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeProgressChangeColor_ReturnValue) == 0x0002B1, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeProgressChangeColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_5) == 0x0002B8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsTriggerSpecificPercent_ReturnValue) == 0x0002C0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsTriggerSpecificPercent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_14) == 0x0002C1, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_14' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BreakVector2D_X) == 0x0002C8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BreakVector2D_Y) == 0x0002D0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x0002D8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0002E0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_15) == 0x0002E8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_15' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetWorldDeltaSeconds_ReturnValue) == 0x0002F0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetWorldDeltaSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Variable_4) == 0x0002F8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_2) == 0x0002F9, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_Select_Default) == 0x000300, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_Select_Default_1) == 0x000308, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_MapRangeClamped_ReturnValue) == 0x000310, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_MakeVector2D_ReturnValue) == 0x000318, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_15) == 0x000328, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_15' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_16) == 0x000329, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_16' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_16) == 0x00032A, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_16' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_17) == 0x00032B, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_17' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_17) == 0x00032C, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_17' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_18) == 0x00032D, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_18' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetTriggerMethod_Trigger_1) == 0x00032E, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetTriggerMethod_Trigger_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_SwitchEnum_CmpSuccess_1) == 0x00032F, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_FindMinSize_ReturnValue) == 0x000330, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_FindMinSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_AddChildToOverlay_ReturnValue) == 0x000338, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_AddChildToOverlay_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_MakeVector2D_ReturnValue_1) == 0x000340, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BreakVector2D_X_1) == 0x000350, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BreakVector2D_Y_1) == 0x000358, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_SwitchEnum_CmpSuccess_2) == 0x000360, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_SwitchEnum_CmpSuccess_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsAnimationPlayingForward_ReturnValue) == 0x000361, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsAnimationPlayingForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Not_PreBool_ReturnValue_2) == 0x000362, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetNumParticles_NumParticles) == 0x000364, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetNumParticles_NumParticles' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeParticle_ReturnValue) == 0x000368, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeParticle_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Variable_5) == 0x000369, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_18) == 0x00036A, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_18' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetChildAt_ReturnValue_2) == 0x000370, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetChildAt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_DynamicCast_AsWB_Particle_2) == 0x000378, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_DynamicCast_AsWB_Particle_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_DynamicCast_bSuccess_2) == 0x000380, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsValid_ReturnValue_3) == 0x000381, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsValid_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_GetChildrenCount_ReturnValue_2) == 0x000384, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_GetChildrenCount_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_19) == 0x000388, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_19' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_LessEqual_IntInt_ReturnValue_3) == 0x000389, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_LessEqual_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_19) == 0x00038A, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_19' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CustomEvent_ProgressBar) == 0x000390, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CustomEvent_ProgressBar' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_CustomEvent_Percent) == 0x000398, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_CustomEvent_Percent' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_Select_Default_2) == 0x0003A0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_Select_Default_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_20) == 0x0003A8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_20' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_FInterpTo_Constant_ReturnValue) == 0x0003B0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_FInterpTo_Constant_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_Select_Default_3) == 0x0003B8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_Select_Default_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Variable_6) == 0x0003C0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_Select_Default_4) == 0x0003C1, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_Select_Default_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Has_Been_Initd_Variable_21) == 0x0003C2, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Has_Been_Initd_Variable_21' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_20) == 0x0003C3, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_20' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Not_PreBool_ReturnValue_3) == 0x0003C4, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Not_PreBool_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsValid_ReturnValue_4) == 0x0003C5, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsValid_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsPlaying_ReturnValue) == 0x0003C6, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeSoundEffect_ReturnValue_2) == 0x0003C7, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeSoundEffect_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_Variable_7) == 0x0003C8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeParticle_ReturnValue_1) == 0x0003C9, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeParticle_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_3) == 0x0003CA, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, K2Node_Select_Default_5) == 0x0003CB, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::K2Node_Select_Default_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeSoundEffect_ReturnValue_3) == 0x0003CC, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeSoundEffect_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsEffectTypeProgressChangeColor_ReturnValue_1) == 0x0003CD, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsEffectTypeProgressChangeColor_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsTriggeredAlwaysOnSpecificPercentValue_ReturnValue) == 0x0003CE, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsTriggeredAlwaysOnSpecificPercentValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BooleanOR_ReturnValue_2) == 0x0003CF, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BooleanOR_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_IsTriggeredAlways_ReturnValue) == 0x0003D0, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_IsTriggeredAlways_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BooleanOR_ReturnValue_3) == 0x0003D1, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BooleanOR_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BooleanOR_ReturnValue_4) == 0x0003D2, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BooleanOR_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BooleanOR_ReturnValue_5) == 0x0003D3, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BooleanOR_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Not_PreBool_ReturnValue_4) == 0x0003D4, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Not_PreBool_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_BooleanAND_ReturnValue) == 0x0003D5, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, Temp_bool_IsClosed_Variable_21) == 0x0003D6, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::Temp_bool_IsClosed_Variable_21' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_CreateSound2D_VolumeMultiplier_ImplicitCast) == 0x0003D8, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_CreateSound2D_VolumeMultiplier_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_ExecuteUbergraph_WB_Effect, CallFunc_Delay_Duration_ImplicitCast) == 0x0003DC, "Member 'WB_Effect_C_ExecuteUbergraph_WB_Effect::CallFunc_Delay_Duration_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.FindMinSize
// 0x0040 (0x0040 - 0x0000)
struct WB_Effect_C_FindMinSize final
{
public:
	struct FVector2D                              Size_0;                                            // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        ReturnValue;                                       // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue;       // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_FindMinSize) == 0x000008, "Wrong alignment on WB_Effect_C_FindMinSize");
static_assert(sizeof(WB_Effect_C_FindMinSize) == 0x000040, "Wrong size on WB_Effect_C_FindMinSize");
static_assert(offsetof(WB_Effect_C_FindMinSize, Size_0) == 0x000000, "Member 'WB_Effect_C_FindMinSize::Size_0' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_FindMinSize, ReturnValue) == 0x000010, "Member 'WB_Effect_C_FindMinSize::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_FindMinSize, Temp_bool_Variable) == 0x000018, "Member 'WB_Effect_C_FindMinSize::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_FindMinSize, CallFunc_BreakVector2D_X) == 0x000020, "Member 'WB_Effect_C_FindMinSize::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_FindMinSize, CallFunc_BreakVector2D_Y) == 0x000028, "Member 'WB_Effect_C_FindMinSize::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_FindMinSize, CallFunc_LessEqual_DoubleDouble_ReturnValue) == 0x000030, "Member 'WB_Effect_C_FindMinSize::CallFunc_LessEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_FindMinSize, K2Node_Select_Default) == 0x000038, "Member 'WB_Effect_C_FindMinSize::K2Node_Select_Default' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetAddRotationToParticles
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_C_GetAddRotationToParticles final
{
public:
	bool                                          Add_Rotation_to_Particles;                         // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetAddRotationToParticles) == 0x000001, "Wrong alignment on WB_Effect_C_GetAddRotationToParticles");
static_assert(sizeof(WB_Effect_C_GetAddRotationToParticles) == 0x000001, "Wrong size on WB_Effect_C_GetAddRotationToParticles");
static_assert(offsetof(WB_Effect_C_GetAddRotationToParticles, Add_Rotation_to_Particles) == 0x000000, "Member 'WB_Effect_C_GetAddRotationToParticles::Add_Rotation_to_Particles' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetDecayTime
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_C_GetDecayTime final
{
public:
	double                                        Decay_Time;                                        // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_FunctionResult_Decay_Time_ImplicitCast;     // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetDecayTime) == 0x000008, "Wrong alignment on WB_Effect_C_GetDecayTime");
static_assert(sizeof(WB_Effect_C_GetDecayTime) == 0x000010, "Wrong size on WB_Effect_C_GetDecayTime");
static_assert(offsetof(WB_Effect_C_GetDecayTime, Decay_Time) == 0x000000, "Member 'WB_Effect_C_GetDecayTime::Decay_Time' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetDecayTime, K2Node_FunctionResult_Decay_Time_ImplicitCast) == 0x000008, "Member 'WB_Effect_C_GetDecayTime::K2Node_FunctionResult_Decay_Time_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetEffectAngle
// 0x0018 (0x0018 - 0x0000)
struct WB_Effect_C_GetEffectAngle final
{
public:
	double                                        Effect_Angle;                                      // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_Value_ImplicitCast;                // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetEffectAngle) == 0x000008, "Wrong alignment on WB_Effect_C_GetEffectAngle");
static_assert(sizeof(WB_Effect_C_GetEffectAngle) == 0x000018, "Wrong size on WB_Effect_C_GetEffectAngle");
static_assert(offsetof(WB_Effect_C_GetEffectAngle, Effect_Angle) == 0x000000, "Member 'WB_Effect_C_GetEffectAngle::Effect_Angle' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectAngle, CallFunc_FClamp_ReturnValue) == 0x000008, "Member 'WB_Effect_C_GetEffectAngle::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectAngle, CallFunc_FClamp_Value_ImplicitCast) == 0x000010, "Member 'WB_Effect_C_GetEffectAngle::CallFunc_FClamp_Value_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetEffectColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_C_GetEffectColor final
{
public:
	struct FLinearColor                           Effect_Color;                                      // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetEffectColor) == 0x000004, "Wrong alignment on WB_Effect_C_GetEffectColor");
static_assert(sizeof(WB_Effect_C_GetEffectColor) == 0x000010, "Wrong size on WB_Effect_C_GetEffectColor");
static_assert(offsetof(WB_Effect_C_GetEffectColor, Effect_Color) == 0x000000, "Member 'WB_Effect_C_GetEffectColor::Effect_Color' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetEffectScale
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_C_GetEffectScale final
{
public:
	struct FVector2D                              EffectScale;                                       // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetEffectScale) == 0x000008, "Wrong alignment on WB_Effect_C_GetEffectScale");
static_assert(sizeof(WB_Effect_C_GetEffectScale) == 0x000010, "Wrong size on WB_Effect_C_GetEffectScale");
static_assert(offsetof(WB_Effect_C_GetEffectScale, EffectScale) == 0x000000, "Member 'WB_Effect_C_GetEffectScale::EffectScale' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetEffectShear
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_C_GetEffectShear final
{
public:
	struct FVector2D                              Effect_Shear;                                      // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetEffectShear) == 0x000008, "Wrong alignment on WB_Effect_C_GetEffectShear");
static_assert(sizeof(WB_Effect_C_GetEffectShear) == 0x000010, "Wrong size on WB_Effect_C_GetEffectShear");
static_assert(offsetof(WB_Effect_C_GetEffectShear, Effect_Shear) == 0x000000, "Member 'WB_Effect_C_GetEffectShear::Effect_Shear' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetEffectTexture
// 0x0060 (0x0060 - 0x0000)
struct WB_Effect_C_GetEffectTexture final
{
public:
	class UObject*                                Custom_Effect_Texture;                             // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable;                              // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable_1;                            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable_2;                            // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable_3;                            // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable_4;                            // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                Temp_object_Variable_5;                            // 0x0038(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   Temp_byte_Variable;                                // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_42[0x6];                                       // 0x0042(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                K2Node_Select_Default;                             // 0x0048(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51[0x7];                                       // 0x0051(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                K2Node_Select_Default_1;                           // 0x0058(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetEffectTexture) == 0x000008, "Wrong alignment on WB_Effect_C_GetEffectTexture");
static_assert(sizeof(WB_Effect_C_GetEffectTexture) == 0x000060, "Wrong size on WB_Effect_C_GetEffectTexture");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, Custom_Effect_Texture) == 0x000000, "Member 'WB_Effect_C_GetEffectTexture::Custom_Effect_Texture' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, Temp_object_Variable) == 0x000008, "Member 'WB_Effect_C_GetEffectTexture::Temp_object_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, Temp_object_Variable_1) == 0x000010, "Member 'WB_Effect_C_GetEffectTexture::Temp_object_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, Temp_object_Variable_2) == 0x000018, "Member 'WB_Effect_C_GetEffectTexture::Temp_object_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, Temp_object_Variable_3) == 0x000020, "Member 'WB_Effect_C_GetEffectTexture::Temp_object_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, Temp_object_Variable_4) == 0x000028, "Member 'WB_Effect_C_GetEffectTexture::Temp_object_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, Temp_bool_Variable) == 0x000030, "Member 'WB_Effect_C_GetEffectTexture::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, Temp_object_Variable_5) == 0x000038, "Member 'WB_Effect_C_GetEffectTexture::Temp_object_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, Temp_byte_Variable) == 0x000040, "Member 'WB_Effect_C_GetEffectTexture::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000041, "Member 'WB_Effect_C_GetEffectTexture::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, K2Node_Select_Default) == 0x000048, "Member 'WB_Effect_C_GetEffectTexture::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, CallFunc_IsValid_ReturnValue) == 0x000050, "Member 'WB_Effect_C_GetEffectTexture::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetEffectTexture, K2Node_Select_Default_1) == 0x000058, "Member 'WB_Effect_C_GetEffectTexture::K2Node_Select_Default_1' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetEffectTranslation
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_C_GetEffectTranslation final
{
public:
	struct FVector2D                              Transition;                                        // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetEffectTranslation) == 0x000008, "Wrong alignment on WB_Effect_C_GetEffectTranslation");
static_assert(sizeof(WB_Effect_C_GetEffectTranslation) == 0x000010, "Wrong size on WB_Effect_C_GetEffectTranslation");
static_assert(offsetof(WB_Effect_C_GetEffectTranslation, Transition) == 0x000000, "Member 'WB_Effect_C_GetEffectTranslation::Transition' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetEffectType
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_C_GetEffectType final
{
public:
	EEffectType                                   Effect_Texture_Type;                               // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetEffectType) == 0x000001, "Wrong alignment on WB_Effect_C_GetEffectType");
static_assert(sizeof(WB_Effect_C_GetEffectType) == 0x000001, "Wrong size on WB_Effect_C_GetEffectType");
static_assert(offsetof(WB_Effect_C_GetEffectType, Effect_Texture_Type) == 0x000000, "Member 'WB_Effect_C_GetEffectType::Effect_Texture_Type' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetNumParticles
// 0x000C (0x000C - 0x0000)
struct WB_Effect_C_GetNumParticles final
{
public:
	int32                                         NumParticles;                                      // 0x0000(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Clamp_ReturnValue;                        // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetNumParticles) == 0x000004, "Wrong alignment on WB_Effect_C_GetNumParticles");
static_assert(sizeof(WB_Effect_C_GetNumParticles) == 0x00000C, "Wrong size on WB_Effect_C_GetNumParticles");
static_assert(offsetof(WB_Effect_C_GetNumParticles, NumParticles) == 0x000000, "Member 'WB_Effect_C_GetNumParticles::NumParticles' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetNumParticles, CallFunc_Subtract_IntInt_ReturnValue) == 0x000004, "Member 'WB_Effect_C_GetNumParticles::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetNumParticles, CallFunc_Clamp_ReturnValue) == 0x000008, "Member 'WB_Effect_C_GetNumParticles::CallFunc_Clamp_ReturnValue' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetParticleSpread
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_C_GetParticleSpread final
{
public:
	double                                        Particle_Spread;                                   // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_FunctionResult_Particle_Spread_ImplicitCast; // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetParticleSpread) == 0x000008, "Wrong alignment on WB_Effect_C_GetParticleSpread");
static_assert(sizeof(WB_Effect_C_GetParticleSpread) == 0x000010, "Wrong size on WB_Effect_C_GetParticleSpread");
static_assert(offsetof(WB_Effect_C_GetParticleSpread, Particle_Spread) == 0x000000, "Member 'WB_Effect_C_GetParticleSpread::Particle_Spread' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetParticleSpread, K2Node_FunctionResult_Particle_Spread_ImplicitCast) == 0x000008, "Member 'WB_Effect_C_GetParticleSpread::K2Node_FunctionResult_Particle_Spread_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetPlaybackSpeed
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_C_GetPlaybackSpeed final
{
public:
	double                                        Playback_Speed;                                    // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_FunctionResult_Playback_Speed_ImplicitCast; // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetPlaybackSpeed) == 0x000008, "Wrong alignment on WB_Effect_C_GetPlaybackSpeed");
static_assert(sizeof(WB_Effect_C_GetPlaybackSpeed) == 0x000010, "Wrong size on WB_Effect_C_GetPlaybackSpeed");
static_assert(offsetof(WB_Effect_C_GetPlaybackSpeed, Playback_Speed) == 0x000000, "Member 'WB_Effect_C_GetPlaybackSpeed::Playback_Speed' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetPlaybackSpeed, K2Node_FunctionResult_Playback_Speed_ImplicitCast) == 0x000008, "Member 'WB_Effect_C_GetPlaybackSpeed::K2Node_FunctionResult_Playback_Speed_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetProgressBar
// 0x0008 (0x0008 - 0x0000)
struct WB_Effect_C_GetProgressBar final
{
public:
	class UProgressBarLinear_C*                   ProgressBar;                                       // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetProgressBar) == 0x000008, "Wrong alignment on WB_Effect_C_GetProgressBar");
static_assert(sizeof(WB_Effect_C_GetProgressBar) == 0x000008, "Wrong size on WB_Effect_C_GetProgressBar");
static_assert(offsetof(WB_Effect_C_GetProgressBar, ProgressBar) == 0x000000, "Member 'WB_Effect_C_GetProgressBar::ProgressBar' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetSoundEffect
// 0x0008 (0x0008 - 0x0000)
struct WB_Effect_C_GetSoundEffect final
{
public:
	class USoundBase*                             Sound_Effect;                                      // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetSoundEffect) == 0x000008, "Wrong alignment on WB_Effect_C_GetSoundEffect");
static_assert(sizeof(WB_Effect_C_GetSoundEffect) == 0x000008, "Wrong size on WB_Effect_C_GetSoundEffect");
static_assert(offsetof(WB_Effect_C_GetSoundEffect, Sound_Effect) == 0x000000, "Member 'WB_Effect_C_GetSoundEffect::Sound_Effect' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetSoundVolume
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_C_GetSoundVolume final
{
public:
	double                                        Sound_Volume_Multiplier;                           // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_FunctionResult_Sound_Volume_Multiplier_ImplicitCast; // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetSoundVolume) == 0x000008, "Wrong alignment on WB_Effect_C_GetSoundVolume");
static_assert(sizeof(WB_Effect_C_GetSoundVolume) == 0x000010, "Wrong size on WB_Effect_C_GetSoundVolume");
static_assert(offsetof(WB_Effect_C_GetSoundVolume, Sound_Volume_Multiplier) == 0x000000, "Member 'WB_Effect_C_GetSoundVolume::Sound_Volume_Multiplier' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetSoundVolume, K2Node_FunctionResult_Sound_Volume_Multiplier_ImplicitCast) == 0x000008, "Member 'WB_Effect_C_GetSoundVolume::K2Node_FunctionResult_Sound_Volume_Multiplier_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetSpecificPercentValue
// 0x0018 (0x0018 - 0x0000)
struct WB_Effect_C_GetSpecificPercentValue final
{
public:
	double                                        Specific_Percent_Value;                            // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_Value_ImplicitCast;                // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetSpecificPercentValue) == 0x000008, "Wrong alignment on WB_Effect_C_GetSpecificPercentValue");
static_assert(sizeof(WB_Effect_C_GetSpecificPercentValue) == 0x000018, "Wrong size on WB_Effect_C_GetSpecificPercentValue");
static_assert(offsetof(WB_Effect_C_GetSpecificPercentValue, Specific_Percent_Value) == 0x000000, "Member 'WB_Effect_C_GetSpecificPercentValue::Specific_Percent_Value' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetSpecificPercentValue, CallFunc_FClamp_ReturnValue) == 0x000008, "Member 'WB_Effect_C_GetSpecificPercentValue::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_GetSpecificPercentValue, CallFunc_FClamp_Value_ImplicitCast) == 0x000010, "Member 'WB_Effect_C_GetSpecificPercentValue::CallFunc_FClamp_Value_ImplicitCast' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.GetTriggerMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_C_GetTriggerMethod final
{
public:
	ETriggerMethod                                Trigger;                                           // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_GetTriggerMethod) == 0x000001, "Wrong alignment on WB_Effect_C_GetTriggerMethod");
static_assert(sizeof(WB_Effect_C_GetTriggerMethod) == 0x000001, "Wrong size on WB_Effect_C_GetTriggerMethod");
static_assert(offsetof(WB_Effect_C_GetTriggerMethod, Trigger) == 0x000000, "Member 'WB_Effect_C_GetTriggerMethod::Trigger' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.IsEffectTypeParticle
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_C_IsEffectTypeParticle final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_IsEffectTypeParticle) == 0x000001, "Wrong alignment on WB_Effect_C_IsEffectTypeParticle");
static_assert(sizeof(WB_Effect_C_IsEffectTypeParticle) == 0x000003, "Wrong size on WB_Effect_C_IsEffectTypeParticle");
static_assert(offsetof(WB_Effect_C_IsEffectTypeParticle, ReturnValue) == 0x000000, "Member 'WB_Effect_C_IsEffectTypeParticle::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsEffectTypeParticle, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000001, "Member 'WB_Effect_C_IsEffectTypeParticle::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsEffectTypeParticle, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_C_IsEffectTypeParticle::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.IsEffectTypeProgressChangeColor
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_C_IsEffectTypeProgressChangeColor final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_IsEffectTypeProgressChangeColor) == 0x000001, "Wrong alignment on WB_Effect_C_IsEffectTypeProgressChangeColor");
static_assert(sizeof(WB_Effect_C_IsEffectTypeProgressChangeColor) == 0x000003, "Wrong size on WB_Effect_C_IsEffectTypeProgressChangeColor");
static_assert(offsetof(WB_Effect_C_IsEffectTypeProgressChangeColor, ReturnValue) == 0x000000, "Member 'WB_Effect_C_IsEffectTypeProgressChangeColor::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsEffectTypeProgressChangeColor, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000001, "Member 'WB_Effect_C_IsEffectTypeProgressChangeColor::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsEffectTypeProgressChangeColor, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_C_IsEffectTypeProgressChangeColor::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.IsEffectTypeSoundEffect
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_C_IsEffectTypeSoundEffect final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_IsEffectTypeSoundEffect) == 0x000001, "Wrong alignment on WB_Effect_C_IsEffectTypeSoundEffect");
static_assert(sizeof(WB_Effect_C_IsEffectTypeSoundEffect) == 0x000003, "Wrong size on WB_Effect_C_IsEffectTypeSoundEffect");
static_assert(offsetof(WB_Effect_C_IsEffectTypeSoundEffect, ReturnValue) == 0x000000, "Member 'WB_Effect_C_IsEffectTypeSoundEffect::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsEffectTypeSoundEffect, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000001, "Member 'WB_Effect_C_IsEffectTypeSoundEffect::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsEffectTypeSoundEffect, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_C_IsEffectTypeSoundEffect::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.IsEffectTypeSoundEffectLooped
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_C_IsEffectTypeSoundEffectLooped final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_IsEffectTypeSoundEffectLooped) == 0x000001, "Wrong alignment on WB_Effect_C_IsEffectTypeSoundEffectLooped");
static_assert(sizeof(WB_Effect_C_IsEffectTypeSoundEffectLooped) == 0x000003, "Wrong size on WB_Effect_C_IsEffectTypeSoundEffectLooped");
static_assert(offsetof(WB_Effect_C_IsEffectTypeSoundEffectLooped, ReturnValue) == 0x000000, "Member 'WB_Effect_C_IsEffectTypeSoundEffectLooped::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsEffectTypeSoundEffectLooped, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000001, "Member 'WB_Effect_C_IsEffectTypeSoundEffectLooped::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsEffectTypeSoundEffectLooped, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_C_IsEffectTypeSoundEffectLooped::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.IsTriggeredAlways
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_C_IsTriggeredAlways final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger;                 // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_IsTriggeredAlways) == 0x000001, "Wrong alignment on WB_Effect_C_IsTriggeredAlways");
static_assert(sizeof(WB_Effect_C_IsTriggeredAlways) == 0x000003, "Wrong size on WB_Effect_C_IsTriggeredAlways");
static_assert(offsetof(WB_Effect_C_IsTriggeredAlways, ReturnValue) == 0x000000, "Member 'WB_Effect_C_IsTriggeredAlways::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggeredAlways, CallFunc_GetTriggerMethod_Trigger) == 0x000001, "Member 'WB_Effect_C_IsTriggeredAlways::CallFunc_GetTriggerMethod_Trigger' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggeredAlways, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_C_IsTriggeredAlways::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.IsTriggeredAlwaysOnSpecificPercentValue
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger;                 // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue) == 0x000001, "Wrong alignment on WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue");
static_assert(sizeof(WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue) == 0x000003, "Wrong size on WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue");
static_assert(offsetof(WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue, ReturnValue) == 0x000000, "Member 'WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue, CallFunc_GetTriggerMethod_Trigger) == 0x000001, "Member 'WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue::CallFunc_GetTriggerMethod_Trigger' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.IsTriggerSpecificPercent
// 0x0009 (0x0009 - 0x0000)
struct WB_Effect_C_IsTriggerSpecificPercent final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger;                 // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_2;        // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_3;        // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_2;                  // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_IsTriggerSpecificPercent) == 0x000001, "Wrong alignment on WB_Effect_C_IsTriggerSpecificPercent");
static_assert(sizeof(WB_Effect_C_IsTriggerSpecificPercent) == 0x000009, "Wrong size on WB_Effect_C_IsTriggerSpecificPercent");
static_assert(offsetof(WB_Effect_C_IsTriggerSpecificPercent, ReturnValue) == 0x000000, "Member 'WB_Effect_C_IsTriggerSpecificPercent::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggerSpecificPercent, CallFunc_GetTriggerMethod_Trigger) == 0x000001, "Member 'WB_Effect_C_IsTriggerSpecificPercent::CallFunc_GetTriggerMethod_Trigger' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggerSpecificPercent, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_C_IsTriggerSpecificPercent::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggerSpecificPercent, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000003, "Member 'WB_Effect_C_IsTriggerSpecificPercent::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggerSpecificPercent, CallFunc_EqualEqual_ByteByte_ReturnValue_2) == 0x000004, "Member 'WB_Effect_C_IsTriggerSpecificPercent::CallFunc_EqualEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggerSpecificPercent, CallFunc_EqualEqual_ByteByte_ReturnValue_3) == 0x000005, "Member 'WB_Effect_C_IsTriggerSpecificPercent::CallFunc_EqualEqual_ByteByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggerSpecificPercent, CallFunc_BooleanOR_ReturnValue) == 0x000006, "Member 'WB_Effect_C_IsTriggerSpecificPercent::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggerSpecificPercent, CallFunc_BooleanOR_ReturnValue_1) == 0x000007, "Member 'WB_Effect_C_IsTriggerSpecificPercent::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_IsTriggerSpecificPercent, CallFunc_BooleanOR_ReturnValue_2) == 0x000008, "Member 'WB_Effect_C_IsTriggerSpecificPercent::CallFunc_BooleanOR_ReturnValue_2' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.SetFillType
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_C_SetFillType final
{
public:
	EProgressBarFillType                          FillType_0;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_SetFillType) == 0x000001, "Wrong alignment on WB_Effect_C_SetFillType");
static_assert(sizeof(WB_Effect_C_SetFillType) == 0x000001, "Wrong size on WB_Effect_C_SetFillType");
static_assert(offsetof(WB_Effect_C_SetFillType, FillType_0) == 0x000000, "Member 'WB_Effect_C_SetFillType::FillType_0' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.SetSize
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_C_SetSize final
{
public:
	struct FVector2D                              Size_0;                                            // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_SetSize) == 0x000008, "Wrong alignment on WB_Effect_C_SetSize");
static_assert(sizeof(WB_Effect_C_SetSize) == 0x000010, "Wrong size on WB_Effect_C_SetSize");
static_assert(offsetof(WB_Effect_C_SetSize, Size_0) == 0x000000, "Member 'WB_Effect_C_SetSize::Size_0' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.SwitchEffectType
// 0x0004 (0x0004 - 0x0000)
struct WB_Effect_C_SwitchEffectType final
{
public:
	int32                                         Index_0;                                           // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_SwitchEffectType) == 0x000004, "Wrong alignment on WB_Effect_C_SwitchEffectType");
static_assert(sizeof(WB_Effect_C_SwitchEffectType) == 0x000004, "Wrong size on WB_Effect_C_SwitchEffectType");
static_assert(offsetof(WB_Effect_C_SwitchEffectType, Index_0) == 0x000000, "Member 'WB_Effect_C_SwitchEffectType::Index_0' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.Tick
// 0x003C (0x003C - 0x0000)
struct WB_Effect_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_Tick) == 0x000004, "Wrong alignment on WB_Effect_C_Tick");
static_assert(sizeof(WB_Effect_C_Tick) == 0x00003C, "Wrong size on WB_Effect_C_Tick");
static_assert(offsetof(WB_Effect_C_Tick, MyGeometry) == 0x000000, "Member 'WB_Effect_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Effect_C_Tick, InDeltaTime) == 0x000038, "Member 'WB_Effect_C_Tick::InDeltaTime' has a wrong offset!");

// Function WB_Effect.WB_Effect_C.UpdatePercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Effect_C_UpdatePercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_C_UpdatePercent) == 0x000008, "Wrong alignment on WB_Effect_C_UpdatePercent");
static_assert(sizeof(WB_Effect_C_UpdatePercent) == 0x000008, "Wrong size on WB_Effect_C_UpdatePercent");
static_assert(offsetof(WB_Effect_C_UpdatePercent, Percent) == 0x000000, "Member 'WB_Effect_C_UpdatePercent::Percent' has a wrong offset!");

}

