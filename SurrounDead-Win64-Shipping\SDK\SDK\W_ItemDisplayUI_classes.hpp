﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_ItemDisplayUI

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_ItemDisplayUI.W_ItemDisplayUI_C
// 0x0020 (0x02E0 - 0x02C0)
class UW_ItemDisplayUI_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UJSIContainer_C*                        MainContainer;                                     // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TArray<class UJSI_Slot_C*>                    Array_Of_Items;                                    // 0x02D0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)

public:
	void ExecuteUbergraph_W_ItemDisplayUI(int32 EntryPoint);
	void ForceInitSpecialcontainer();
	void GetAllAttachments(TArray<class FName>* Attachments);
	void GetContainerByAttachmentType(const struct FGameplayTag& Type, class UJSIContainer_C** JigContainer, int32* ContainerIndex);
	void GetDropWidget(class UDropItemBackGwidget_C** DropWRef);
	void GetJSIContainerByPlayerSlots(const struct FGameplayTag& Slot_0, class UJSIContainer_C** Container, class UJSI_Slot_C** EquippedItem, bool* IsPending_);
	void GetListOfNonAddContainers(TArray<class UJSIContainer_C*>* Containers);
	void GetLootContent(class UUserWidget** Widget);
	void GetValidReloadContainers(TArray<class UJSIContainer_C*>* Containers);
	void JigSetLootContent(class UUserWidget* Widget, const class FText& Name_0);
	void JSICheckStatus();
	void JSIOnWeightUpdated(double NewWeight);
	void OnCreatedFromUtility();
	void SetActionbarFollower(class UJSI_Slot_C* JigRef, bool* Return);
	void SetActorOwner(class AActor* ActorRef);
	void SetInspectorRef(class UBP_InspectorWindowWidget_C* Inspector);
	void SetItemReference(class UJSI_Slot_C* ItemRef);

	void GetListOfContainers(TArray<class UJSIContainer_C*>* Containers) const;

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_ItemDisplayUI_C">();
	}
	static class UW_ItemDisplayUI_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_ItemDisplayUI_C>();
	}
};
static_assert(alignof(UW_ItemDisplayUI_C) == 0x000008, "Wrong alignment on UW_ItemDisplayUI_C");
static_assert(sizeof(UW_ItemDisplayUI_C) == 0x0002E0, "Wrong size on UW_ItemDisplayUI_C");
static_assert(offsetof(UW_ItemDisplayUI_C, UberGraphFrame) == 0x0002C0, "Member 'UW_ItemDisplayUI_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_ItemDisplayUI_C, MainContainer) == 0x0002C8, "Member 'UW_ItemDisplayUI_C::MainContainer' has a wrong offset!");
static_assert(offsetof(UW_ItemDisplayUI_C, Array_Of_Items) == 0x0002D0, "Member 'UW_ItemDisplayUI_C::Array_Of_Items' has a wrong offset!");

}

