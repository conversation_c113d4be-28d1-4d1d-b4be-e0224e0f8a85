﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_WorldMarker

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "S_MarkerData_structs.hpp"
#include "UMG_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_WorldMarker.W_WorldMarker_C
// 0x00D0 (0x0390 - 0x02C0)
class UW_WorldMarker_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Anim;                                              // 0x02C8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 Fade;                                              // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Icon;                                              // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               Marker_Box;                                        // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class AActor*                                 Target_Actor;                                      // 0x02E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	struct FS_MarkerData                          Marker_Data;                                       // 0x02F0(0x0070)(Edit, BlueprintVisible, ExposeOnSpawn, HasGetValueTypeHash)
	class UW_WorldMarkerTooltip_C*                MarkerTooltip;                                     // 0x0360(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   Description;                                       // 0x0368(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	TMulticastInlineDelegate<void()>              POIExplored;                                       // 0x0380(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	void Construct();
	void Construct_Marker();
	void ExecuteUbergraph_W_WorldMarker(int32 EntryPoint);
	class UWidget* Get_ToolTipWidget();
	struct FEventReply OnMouseButtonDoubleClick(const struct FGeometry& InMyGeometry, const struct FPointerEvent& InMouseEvent);
	void OnMouseEnter(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	void OnMouseLeave(const struct FPointerEvent& MouseEvent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_WorldMarker_C">();
	}
	static class UW_WorldMarker_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_WorldMarker_C>();
	}
};
static_assert(alignof(UW_WorldMarker_C) == 0x000008, "Wrong alignment on UW_WorldMarker_C");
static_assert(sizeof(UW_WorldMarker_C) == 0x000390, "Wrong size on UW_WorldMarker_C");
static_assert(offsetof(UW_WorldMarker_C, UberGraphFrame) == 0x0002C0, "Member 'UW_WorldMarker_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_WorldMarker_C, Anim) == 0x0002C8, "Member 'UW_WorldMarker_C::Anim' has a wrong offset!");
static_assert(offsetof(UW_WorldMarker_C, Fade) == 0x0002D0, "Member 'UW_WorldMarker_C::Fade' has a wrong offset!");
static_assert(offsetof(UW_WorldMarker_C, Icon) == 0x0002D8, "Member 'UW_WorldMarker_C::Icon' has a wrong offset!");
static_assert(offsetof(UW_WorldMarker_C, Marker_Box) == 0x0002E0, "Member 'UW_WorldMarker_C::Marker_Box' has a wrong offset!");
static_assert(offsetof(UW_WorldMarker_C, Target_Actor) == 0x0002E8, "Member 'UW_WorldMarker_C::Target_Actor' has a wrong offset!");
static_assert(offsetof(UW_WorldMarker_C, Marker_Data) == 0x0002F0, "Member 'UW_WorldMarker_C::Marker_Data' has a wrong offset!");
static_assert(offsetof(UW_WorldMarker_C, MarkerTooltip) == 0x000360, "Member 'UW_WorldMarker_C::MarkerTooltip' has a wrong offset!");
static_assert(offsetof(UW_WorldMarker_C, Description) == 0x000368, "Member 'UW_WorldMarker_C::Description' has a wrong offset!");
static_assert(offsetof(UW_WorldMarker_C, POIExplored) == 0x000380, "Member 'UW_WorldMarker_C::POIExplored' has a wrong offset!");

}

