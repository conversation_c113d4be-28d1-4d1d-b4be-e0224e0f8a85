﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_LinearProgress_Separated

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "Slate_structs.hpp"
#include "UMG_structs.hpp"


namespace SDK::Params
{

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.AddSegmentsBackground
// 0x0020 (0x0020 - 0x0000)
struct WB_LinearProgress_Separated_C_AddSegmentsBackground final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseBackgroundBlur_0;                              // 0x0010(0x0001)(BlueprintVisible, BlueprintReadOn<PERSON>, <PERSON><PERSON>, ZeroConstructor, Is<PERSON>lainOldData, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        BlurStrength_0;                                    // 0x0018(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_AddSegmentsBackground) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_AddSegmentsBackground");
static_assert(sizeof(WB_LinearProgress_Separated_C_AddSegmentsBackground) == 0x000020, "Wrong size on WB_LinearProgress_Separated_C_AddSegmentsBackground");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsBackground, Color) == 0x000000, "Member 'WB_LinearProgress_Separated_C_AddSegmentsBackground::Color' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsBackground, bUseBackgroundBlur_0) == 0x000010, "Member 'WB_LinearProgress_Separated_C_AddSegmentsBackground::bUseBackgroundBlur_0' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsBackground, BlurStrength_0) == 0x000018, "Member 'WB_LinearProgress_Separated_C_AddSegmentsBackground::BlurStrength_0' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.AddSegmentsProgressBar
// 0x0038 (0x0038 - 0x0000)
struct WB_LinearProgress_Separated_C_AddSegmentsProgressBar final
{
public:
	int32                                         NumSegments_0;                                     // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Spacing_0;                                         // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Size_0;                                            // 0x0010(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           FillColor_0;                                       // 0x0020(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          FillType_0;                                        // 0x0030(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsFillFromCenter_0;                               // 0x0031(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseShader_0;                                      // 0x0032(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_AddSegmentsProgressBar) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_AddSegmentsProgressBar");
static_assert(sizeof(WB_LinearProgress_Separated_C_AddSegmentsProgressBar) == 0x000038, "Wrong size on WB_LinearProgress_Separated_C_AddSegmentsProgressBar");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsProgressBar, NumSegments_0) == 0x000000, "Member 'WB_LinearProgress_Separated_C_AddSegmentsProgressBar::NumSegments_0' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsProgressBar, Spacing_0) == 0x000008, "Member 'WB_LinearProgress_Separated_C_AddSegmentsProgressBar::Spacing_0' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsProgressBar, Size_0) == 0x000010, "Member 'WB_LinearProgress_Separated_C_AddSegmentsProgressBar::Size_0' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsProgressBar, FillColor_0) == 0x000020, "Member 'WB_LinearProgress_Separated_C_AddSegmentsProgressBar::FillColor_0' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsProgressBar, FillType_0) == 0x000030, "Member 'WB_LinearProgress_Separated_C_AddSegmentsProgressBar::FillType_0' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsProgressBar, bIsFillFromCenter_0) == 0x000031, "Member 'WB_LinearProgress_Separated_C_AddSegmentsProgressBar::bIsFillFromCenter_0' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_AddSegmentsProgressBar, bUseShader_0) == 0x000032, "Member 'WB_LinearProgress_Separated_C_AddSegmentsProgressBar::bUseShader_0' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.ExecuteUbergraph_WB_LinearProgress_Separated
// 0x05B8 (0x05B8 - 0x0000)
struct WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FindMaxPaddingValue_Horizontal_ReturnValue; // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x3];                                       // 0x0029(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_1;            // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateChildSize                        K2Node_MakeStruct_SlateChildSize;                  // 0x0034(0x0008)(NoDestructor)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41[0x3];                                       // 0x0041(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_2;            // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0054(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_55[0x3];                                       // 0x0055(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_3;            // 0x005C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_True_if_break_was_hit_Variable;          // 0x0061(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_62[0x2];                                       // 0x0062(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0069(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_2;                              // 0x006A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6B[0x1];                                       // 0x006B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_4;            // 0x006C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_5;            // 0x0070(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_74[0x4];                                       // 0x0074(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_2;                              // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x0080(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_3;                              // 0x0081(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_82[0x2];                                       // 0x0082(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable_2;                   // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateChildSize                        K2Node_MakeStruct_SlateChildSize_1;                // 0x0088(0x0008)(NoDestructor)
	int32                                         Temp_int_Loop_Counter_Variable_2;                  // 0x0090(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0094(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_True_if_break_was_hit_Variable_1;        // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_99[0x3];                                       // 0x0099(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable_3;                   // 0x009C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A1[0x3];                                       // 0x00A1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_1;                               // 0x00A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_3;                              // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x00B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B1[0x3];                                       // 0x00B1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x00B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_4;                              // 0x00B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B9[0x7];                                       // 0x00B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_4;                              // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C9[0x7];                                       // 0x00C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FindMaxPaddingValue_Horizontal_ReturnValue_1; // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_6;            // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_DC[0x4];                                       // 0x00DC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_MapRangeClamped_ReturnValue_1;            // 0x00E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x00E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_5;                              // 0x00E9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_EA[0x2];                                       // 0x00EA(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable_4;                   // 0x00EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_5;                              // 0x00F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_2;                               // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x00FC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_FD[0x3];                                       // 0x00FD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_3;                 // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_7;            // 0x0104(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0108(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_10C[0x4];                                      // 0x010C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FindMaxPaddingValue_Horizontal_ReturnValue_2; // 0x0110(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateChildSize                        K2Node_MakeStruct_SlateChildSize_2;                // 0x0118(0x0008)(NoDestructor)
	double                                        CallFunc_MapRangeClamped_ReturnValue_2;            // 0x0120(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_3;                  // 0x0128(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_4;                 // 0x012C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateChildSize                        K2Node_MakeStruct_SlateChildSize_3;                // 0x0130(0x0008)(NoDestructor)
	double                                        Temp_real_Variable_6;                              // 0x0138(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_4;                  // 0x0140(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_3;                               // 0x0144(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_5;                 // 0x0148(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_2;           // 0x014C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14D[0x3];                                      // 0x014D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_6;                 // 0x0150(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_154[0x4];                                      // 0x0154(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FindMaxPaddingValue_Vertical_ReturnValue; // 0x0158(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_8;            // 0x0160(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_164[0x4];                                      // 0x0164(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_MapRangeClamped_ReturnValue_3;            // 0x0168(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_6;                              // 0x0170(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_171[0x7];                                      // 0x0171(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_7;                              // 0x0178(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_7;                              // 0x0180(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_181[0x3];                                      // 0x0181(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_9;            // 0x0184(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_8;                              // 0x0188(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_189[0x7];                                      // 0x0189(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item;                           // 0x0190(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0198(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x019C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19D[0x3];                                      // 0x019D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FindMaxPaddingValue_Vertical_ReturnValue_1; // 0x01A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_4;            // 0x01A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x01B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x01B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1B5[0x3];                                      // 0x01B5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_CustomEvent_NumSegments;                    // 0x01B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1BC[0x4];                                      // 0x01BC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Spacing;                        // 0x01C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              K2Node_CustomEvent_Size;                           // 0x01C8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_FillColor;                      // 0x01D8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          K2Node_CustomEvent_FillType;                       // 0x01E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bIsFillFromCenter;              // 0x01E9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseShader;                     // 0x01EA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1EB[0x1];                                      // 0x01EB(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_CustomEvent_Color;                          // 0x01EC(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseBackgroundBlur;             // 0x01FC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1FD[0x3];                                      // 0x01FD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_BlurStrength;                   // 0x0200(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_4;                               // 0x0208(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_3;           // 0x020C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_20D[0x3];                                      // 0x020D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_7;                 // 0x0210(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_5;                   // 0x0214(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Percent;                        // 0x0218(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_8;                              // 0x0220(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_6;                   // 0x0228(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_22C[0x4];                                      // 0x022C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item_1;                         // 0x0230(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0238(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bAbsoluteFill;                  // 0x0239(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bSetUseMarquee;                 // 0x023A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_23B[0x5];                                      // 0x023B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item_2;                         // 0x0240(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x0248(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_249[0x3];                                      // 0x0249(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_3;               // 0x024C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0250(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable;                                // 0x0251(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_252[0x6];                                      // 0x0252(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                K2Node_CustomEvent_Image;                          // 0x0258(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              K2Node_CustomEvent_ImageSize;                      // 0x0260(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_3;                    // 0x0270(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_271[0x3];                                      // 0x0271(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_CustomEvent_Tint;                           // 0x0274(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_7;                   // 0x0284(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushDrawType                           K2Node_CustomEvent_DrawAs;                         // 0x0288(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_289[0x7];                                      // 0x0289(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item_3;                         // 0x0290(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_4;                    // 0x0298(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_CustomEvent_Tiling;                         // 0x0299(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29A[0x2];                                      // 0x029A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable_5;                  // 0x029C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_8;                 // 0x02A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_9;                              // 0x02A4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2A5[0x3];                                      // 0x02A5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item_4;                         // 0x02A8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_4;               // 0x02B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_5;                    // 0x02B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_2;                // 0x02B5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2B6[0x2];                                      // 0x02B6(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_9;                              // 0x02B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_NewColor;                       // 0x02C0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_InterpSpeed;                    // 0x02D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue;         // 0x02D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_CInterpTo_ReturnValue;                    // 0x02E0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item_5;                         // 0x02F0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_6;                    // 0x02F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2F9[0x3];                                      // 0x02F9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_5;               // 0x02FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateChildSize                        K2Node_MakeStruct_SlateChildSize_4;                // 0x0300(0x0008)(NoDestructor)
	bool                                          CallFunc_Less_IntInt_ReturnValue_3;                // 0x0308(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable_1;                              // 0x0309(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_30A[0x2];                                      // 0x030A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable_6;                  // 0x030C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_8;                   // 0x0310(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_4;                // 0x0314(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_315[0x3];                                      // 0x0315(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_9;                 // 0x0318(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31C[0x4];                                      // 0x031C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item_6;                         // 0x0320(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_6;               // 0x0328(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_7;                    // 0x032C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_10;                             // 0x032D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_32E[0x2];                                      // 0x032E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable_7;                  // 0x0330(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              K2Node_Select_Default;                             // 0x0334(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_5;                // 0x0335(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_336[0x2];                                      // 0x0336(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_10;                // 0x0338(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_10;           // 0x033C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_LastIndex_ReturnValue;              // 0x0340(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_344[0x4];                                      // 0x0344(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Create_ReturnValue;                       // 0x0348(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue;                   // 0x0350(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue;       // 0x0354(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0355(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x0356(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_357[0x1];                                      // 0x0357(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default_1;                           // 0x0358(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_1;                 // 0x0360(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_1;          // 0x0364(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_365[0x3];                                      // 0x0365(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UHorizontalBoxSlot*                     CallFunc_AddChildToHorizontalBox_ReturnValue;      // 0x0368(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_2;                           // 0x0370(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                K2Node_MakeStruct_Margin;                          // 0x0378(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0388(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FindPercentCurrentSegment_ReturnValue;    // 0x038C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_11;           // 0x0390(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_394[0x4];                                      // 0x0394(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item_7;                         // 0x0398(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_2;          // 0x03A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_8;                    // 0x03A1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_3;                // 0x03A2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3A3[0x1];                                      // 0x03A3(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_7;               // 0x03A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_12;           // 0x03A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_2;                    // 0x03AC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3AD[0x3];                                      // 0x03AD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Max_ReturnValue;                          // 0x03B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue_1;     // 0x03B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_4;                // 0x03B5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3B6[0x2];                                      // 0x03B6(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item_8;                         // 0x03B8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_9;                    // 0x03C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3C1[0x3];                                      // 0x03C1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_8;               // 0x03C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FindPercentCurrentSegment_ReturnValue_1;  // 0x03C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_6;                // 0x03CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_3;          // 0x03CD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x03CE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_5;                // 0x03CF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_8;                  // 0x03D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_7;                // 0x03D4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3D5[0x3];                                      // 0x03D5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_11;                // 0x03D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FindPercentCurrentSegment_ReturnValue_2;  // 0x03DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_LastIndex_ReturnValue_1;            // 0x03E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_13;           // 0x03E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue_2;     // 0x03E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          Temp_byte_Variable_2;                              // 0x03E9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_6;                // 0x03EA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3EB[0x5];                                      // 0x03EB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FindCurrentSegmentPercentValue_ReturnValue; // 0x03F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable_3;                              // 0x03F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3F9[0x3];                                      // 0x03F9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_FindPercentCurrentSegment_ReturnValue_3;  // 0x03FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable_4;                              // 0x0400(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_401[0x7];                                      // 0x0401(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Image_Raw_C*                        CallFunc_Create_ReturnValue_1;                     // 0x0408(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_2;                 // 0x0410(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_3;                 // 0x0414(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_4;          // 0x0418(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_5;          // 0x0419(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41A[0x6];                                      // 0x041A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default_3;                           // 0x0420(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_4;                           // 0x0428(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_11;                             // 0x0430(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_431[0x7];                                      // 0x0431(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_PB_Raw_C*                           CallFunc_Create_ReturnValue_2;                     // 0x0438(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_4;                 // 0x0440(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              K2Node_Select_Default_5;                           // 0x0444(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_6;          // 0x0445(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_446[0x2];                                      // 0x0446(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Find_ReturnValue_5;                 // 0x0448(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_44C[0x4];                                      // 0x044C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default_6;                           // 0x0450(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_7;          // 0x0458(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_459[0x7];                                      // 0x0459(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default_7;                           // 0x0460(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWB_Image_Raw_C*                        CallFunc_Create_ReturnValue_3;                     // 0x0468(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_6;                 // 0x0470(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_1;                  // 0x0474(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_8;          // 0x0478(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_479[0x3];                                      // 0x0479(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Find_ReturnValue_7;                 // 0x047C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_9;          // 0x0480(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_481[0x7];                                      // 0x0481(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default_8;                           // 0x0488(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_9;                           // 0x0490(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWB_PB_Raw_C*                           CallFunc_Create_ReturnValue_4;                     // 0x0498(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_10;                             // 0x04A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_8;                 // 0x04A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_10;         // 0x04AC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4AD[0x3];                                      // 0x04AD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Find_ReturnValue_9;                 // 0x04B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_11;         // 0x04B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4B5[0x3];                                      // 0x04B5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default_10;                          // 0x04B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_11;                          // 0x04C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UHorizontalBoxSlot*                     CallFunc_AddChildToHorizontalBox_ReturnValue_1;    // 0x04C8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_2;                  // 0x04D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                K2Node_MakeStruct_Margin_1;                        // 0x04D4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4E4[0x4];                                      // 0x04E4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UHorizontalBoxSlot*                     CallFunc_AddChildToHorizontalBox_ReturnValue_2;    // 0x04E8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UVerticalBoxSlot*                       CallFunc_AddChildToVerticalBox_ReturnValue;        // 0x04F0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                K2Node_MakeStruct_Margin_2;                        // 0x04F8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FMargin                                K2Node_MakeStruct_Margin_3;                        // 0x0508(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_Variable_12;                             // 0x0518(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_519[0x7];                                      // 0x0519(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_11;                             // 0x0520(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_12;                          // 0x0528(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_3;                  // 0x0530(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_534[0x4];                                      // 0x0534(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UVerticalBoxSlot*                       CallFunc_AddChildToVerticalBox_ReturnValue_1;      // 0x0538(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_5;                               // 0x0540(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_4;           // 0x0544(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_545[0x3];                                      // 0x0545(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FMargin                                K2Node_MakeStruct_Margin_4;                        // 0x0548(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	int32                                         CallFunc_Add_IntInt_ReturnValue_12;                // 0x0558(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_13;                             // 0x055C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_55D[0x3];                                      // 0x055D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue_4;                  // 0x0560(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_564[0x4];                                      // 0x0564(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default_13;                          // 0x0568(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_6;                               // 0x0570(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Select_Default_14;                          // 0x0574(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWB_PB_Raw_C*                           CallFunc_Array_Get_Item_9;                         // 0x0578(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_10;                   // 0x0580(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_581[0x3];                                      // 0x0581(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_CInterpTo_InterpSpeed_ImplicitCast;       // 0x0584(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_CInterpTo_DeltaTime_ImplicitCast;         // 0x0588(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Left_ImplicitCast;               // 0x058C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Right_ImplicitCast;              // 0x0590(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Bottom_ImplicitCast;             // 0x0594(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Top_ImplicitCast;                // 0x0598(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Top_ImplicitCast_1;              // 0x059C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Bottom_ImplicitCast_1;           // 0x05A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Right_ImplicitCast_1;            // 0x05A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Left_ImplicitCast_1;             // 0x05A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Left_ImplicitCast_2;             // 0x05AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_Right_ImplicitCast_2;            // 0x05B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated");
static_assert(sizeof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated) == 0x0005B8, "Wrong size on WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, EntryPoint) == 0x000000, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Variable) == 0x000004, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable) == 0x000008, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindMaxPaddingValue_Horizontal_ReturnValue) == 0x000010, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindMaxPaddingValue_Horizontal_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue) == 0x000018, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_MapRangeClamped_ReturnValue) == 0x000020, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable) == 0x000028, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_1) == 0x00002C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Array_Index_Variable) == 0x000030, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_SlateChildSize) == 0x000034, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_SlateChildSize' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Loop_Counter_Variable) == 0x00003C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_SwitchEnum_CmpSuccess) == 0x000040, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue) == 0x000044, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_1) == 0x000048, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_2) == 0x000050, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_1) == 0x000054, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Loop_Counter_Variable_1) == 0x000058, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_3) == 0x00005C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x000060, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_True_if_break_was_hit_Variable) == 0x000061, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_True_if_break_was_hit_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Array_Index_Variable_1) == 0x000064, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Not_PreBool_ReturnValue) == 0x000068, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_BooleanAND_ReturnValue) == 0x000069, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_2) == 0x00006A, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_4) == 0x00006C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_5) == 0x000070, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_2) == 0x000078, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_SwitchEnum_CmpSuccess_1) == 0x000080, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_3) == 0x000081, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Array_Index_Variable_2) == 0x000084, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Array_Index_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_SlateChildSize_1) == 0x000088, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_SlateChildSize_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Loop_Counter_Variable_2) == 0x000090, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Loop_Counter_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_1) == 0x000094, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_True_if_break_was_hit_Variable_1) == 0x000098, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_True_if_break_was_hit_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Array_Index_Variable_3) == 0x00009C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Array_Index_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Not_PreBool_ReturnValue_1) == 0x0000A0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Variable_1) == 0x0000A4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_3) == 0x0000A8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_LessEqual_IntInt_ReturnValue) == 0x0000B0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_2) == 0x0000B4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_4) == 0x0000B8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_4) == 0x0000C0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Has_Been_Initd_Variable) == 0x0000C8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindMaxPaddingValue_Horizontal_ReturnValue_1) == 0x0000D0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindMaxPaddingValue_Horizontal_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_6) == 0x0000D8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_MapRangeClamped_ReturnValue_1) == 0x0000E0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_MapRangeClamped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_IsClosed_Variable) == 0x0000E8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_5) == 0x0000E9, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Array_Index_Variable_4) == 0x0000EC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Array_Index_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_5) == 0x0000F0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Variable_2) == 0x0000F8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x0000FC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_3) == 0x000100, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_7) == 0x000104, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Length_ReturnValue) == 0x000108, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindMaxPaddingValue_Horizontal_ReturnValue_2) == 0x000110, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindMaxPaddingValue_Horizontal_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_SlateChildSize_2) == 0x000118, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_SlateChildSize_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_MapRangeClamped_ReturnValue_2) == 0x000120, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_MapRangeClamped_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Loop_Counter_Variable_3) == 0x000128, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Loop_Counter_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_4) == 0x00012C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_SlateChildSize_3) == 0x000130, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_SlateChildSize_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_6) == 0x000138, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Loop_Counter_Variable_4) == 0x000140, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Loop_Counter_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Variable_3) == 0x000144, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_5) == 0x000148, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_LessEqual_IntInt_ReturnValue_2) == 0x00014C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_LessEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_6) == 0x000150, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindMaxPaddingValue_Vertical_ReturnValue) == 0x000158, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindMaxPaddingValue_Vertical_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_8) == 0x000160, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_MapRangeClamped_ReturnValue_3) == 0x000168, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_MapRangeClamped_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_6) == 0x000170, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_7) == 0x000178, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_7) == 0x000180, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_9) == 0x000184, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_8) == 0x000188, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item) == 0x000190, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Length_ReturnValue_1) == 0x000198, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue) == 0x00019C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindMaxPaddingValue_Vertical_ReturnValue_1) == 0x0001A0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindMaxPaddingValue_Vertical_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_MapRangeClamped_ReturnValue_4) == 0x0001A8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_MapRangeClamped_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Length_ReturnValue_2) == 0x0001B0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Less_IntInt_ReturnValue) == 0x0001B4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_NumSegments) == 0x0001B8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_NumSegments' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_Spacing) == 0x0001C0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_Spacing' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_Size) == 0x0001C8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_Size' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_FillColor) == 0x0001D8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_FillColor' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_FillType) == 0x0001E8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_FillType' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_bIsFillFromCenter) == 0x0001E9, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_bIsFillFromCenter' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_bUseShader) == 0x0001EA, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_bUseShader' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_Color) == 0x0001EC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_Color' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_bUseBackgroundBlur) == 0x0001FC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_bUseBackgroundBlur' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_BlurStrength) == 0x000200, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_BlurStrength' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Variable_4) == 0x000208, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_LessEqual_IntInt_ReturnValue_3) == 0x00020C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_LessEqual_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_7) == 0x000210, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Array_Index_Variable_5) == 0x000214, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Array_Index_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_Percent) == 0x000218, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_Percent' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_8) == 0x000220, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Array_Index_Variable_6) == 0x000228, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Array_Index_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item_1) == 0x000230, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_1) == 0x000238, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_bAbsoluteFill) == 0x000239, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_bAbsoluteFill' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_bSetUseMarquee) == 0x00023A, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_bSetUseMarquee' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item_2) == 0x000240, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_2) == 0x000248, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Length_ReturnValue_3) == 0x00024C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Length_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Less_IntInt_ReturnValue_1) == 0x000250, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_byte_Variable) == 0x000251, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_Image) == 0x000258, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_Image' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_ImageSize) == 0x000260, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_ImageSize' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_3) == 0x000270, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_Tint) == 0x000274, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_Tint' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Array_Index_Variable_7) == 0x000284, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Array_Index_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_DrawAs) == 0x000288, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_DrawAs' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item_3) == 0x000290, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_4) == 0x000298, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_Tiling) == 0x000299, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_Tiling' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Loop_Counter_Variable_5) == 0x00029C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Loop_Counter_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_8) == 0x0002A0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_9) == 0x0002A4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_9' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item_4) == 0x0002A8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Length_ReturnValue_4) == 0x0002B0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Length_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_5) == 0x0002B4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Less_IntInt_ReturnValue_2) == 0x0002B5, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Less_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_9) == 0x0002B8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_9' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_NewColor) == 0x0002C0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_NewColor' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_CustomEvent_InterpSpeed) == 0x0002D0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_CustomEvent_InterpSpeed' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_GetWorldDeltaSeconds_ReturnValue) == 0x0002D8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_GetWorldDeltaSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_CInterpTo_ReturnValue) == 0x0002E0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_CInterpTo_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item_5) == 0x0002F0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_6) == 0x0002F8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Length_ReturnValue_5) == 0x0002FC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Length_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_SlateChildSize_4) == 0x000300, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_SlateChildSize_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Less_IntInt_ReturnValue_3) == 0x000308, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Less_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_byte_Variable_1) == 0x000309, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Loop_Counter_Variable_6) == 0x00030C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Loop_Counter_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Array_Index_Variable_8) == 0x000310, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Array_Index_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Less_IntInt_ReturnValue_4) == 0x000314, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Less_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_9) == 0x000318, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item_6) == 0x000320, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Length_ReturnValue_6) == 0x000328, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Length_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_7) == 0x00032C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_10) == 0x00032D, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_10' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Loop_Counter_Variable_7) == 0x000330, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Loop_Counter_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default) == 0x000334, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Less_IntInt_ReturnValue_5) == 0x000335, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Less_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_10) == 0x000338, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_10) == 0x00033C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_LastIndex_ReturnValue) == 0x000340, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_LastIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Create_ReturnValue) == 0x000348, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue) == 0x000350, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_LessEqual_DoubleDouble_ReturnValue) == 0x000354, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_LessEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000355, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Not_PreBool_ReturnValue_2) == 0x000356, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_1) == 0x000358, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue_1) == 0x000360, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_1) == 0x000364, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_AddChildToHorizontalBox_ReturnValue) == 0x000368, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_AddChildToHorizontalBox_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_2) == 0x000370, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Margin) == 0x000378, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Margin' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Add_ReturnValue) == 0x000388, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindPercentCurrentSegment_ReturnValue) == 0x00038C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindPercentCurrentSegment_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_11) == 0x000390, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item_7) == 0x000398, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_2) == 0x0003A0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_8) == 0x0003A1, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Not_PreBool_ReturnValue_3) == 0x0003A2, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Not_PreBool_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Length_ReturnValue_7) == 0x0003A4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Length_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_12) == 0x0003A8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_SwitchEnum_CmpSuccess_2) == 0x0003AC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_SwitchEnum_CmpSuccess_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Max_ReturnValue) == 0x0003B0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Max_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_LessEqual_DoubleDouble_ReturnValue_1) == 0x0003B4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_LessEqual_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Not_PreBool_ReturnValue_4) == 0x0003B5, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Not_PreBool_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item_8) == 0x0003B8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_9) == 0x0003C0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Length_ReturnValue_8) == 0x0003C4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Length_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindPercentCurrentSegment_ReturnValue_1) == 0x0003C8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindPercentCurrentSegment_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Less_IntInt_ReturnValue_6) == 0x0003CC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Less_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_3) == 0x0003CD, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_BooleanAND_ReturnValue_1) == 0x0003CE, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Not_PreBool_ReturnValue_5) == 0x0003CF, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Not_PreBool_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Loop_Counter_Variable_8) == 0x0003D0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Loop_Counter_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Less_IntInt_ReturnValue_7) == 0x0003D4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Less_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_11) == 0x0003D8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindPercentCurrentSegment_ReturnValue_2) == 0x0003DC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindPercentCurrentSegment_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_LastIndex_ReturnValue_1) == 0x0003E0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_LastIndex_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Subtract_IntInt_ReturnValue_13) == 0x0003E4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Subtract_IntInt_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_LessEqual_DoubleDouble_ReturnValue_2) == 0x0003E8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_LessEqual_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_byte_Variable_2) == 0x0003E9, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_byte_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Not_PreBool_ReturnValue_6) == 0x0003EA, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Not_PreBool_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindCurrentSegmentPercentValue_ReturnValue) == 0x0003F0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindCurrentSegmentPercentValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_byte_Variable_3) == 0x0003F8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_byte_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_FindPercentCurrentSegment_ReturnValue_3) == 0x0003FC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_FindPercentCurrentSegment_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_byte_Variable_4) == 0x000400, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_byte_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Create_ReturnValue_1) == 0x000408, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Create_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue_2) == 0x000410, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue_3) == 0x000414, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_4) == 0x000418, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_5) == 0x000419, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_3) == 0x000420, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_4) == 0x000428, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_11) == 0x000430, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_11' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Create_ReturnValue_2) == 0x000438, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Create_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue_4) == 0x000440, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_5) == 0x000444, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_6) == 0x000445, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue_5) == 0x000448, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_6) == 0x000450, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_7) == 0x000458, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_7) == 0x000460, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Create_ReturnValue_3) == 0x000468, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Create_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue_6) == 0x000470, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Add_ReturnValue_1) == 0x000474, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Add_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_8) == 0x000478, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue_7) == 0x00047C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_9) == 0x000480, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_8) == 0x000488, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_9) == 0x000490, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_9' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Create_ReturnValue_4) == 0x000498, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Create_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_10) == 0x0004A0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_10' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue_8) == 0x0004A8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_10) == 0x0004AC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Find_ReturnValue_9) == 0x0004B0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Find_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_EqualEqual_IntInt_ReturnValue_11) == 0x0004B4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_EqualEqual_IntInt_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_10) == 0x0004B8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_10' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_11) == 0x0004C0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_11' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_AddChildToHorizontalBox_ReturnValue_1) == 0x0004C8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_AddChildToHorizontalBox_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Add_ReturnValue_2) == 0x0004D0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Add_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Margin_1) == 0x0004D4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Margin_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_AddChildToHorizontalBox_ReturnValue_2) == 0x0004E8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_AddChildToHorizontalBox_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_AddChildToVerticalBox_ReturnValue) == 0x0004F0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_AddChildToVerticalBox_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Margin_2) == 0x0004F8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Margin_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Margin_3) == 0x000508, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Margin_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_12) == 0x000518, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_12' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_real_Variable_11) == 0x000520, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_real_Variable_11' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_12) == 0x000528, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_12' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Add_ReturnValue_3) == 0x000530, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Add_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_AddChildToVerticalBox_ReturnValue_1) == 0x000538, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_AddChildToVerticalBox_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Variable_5) == 0x000540, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_LessEqual_IntInt_ReturnValue_4) == 0x000544, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_LessEqual_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Margin_4) == 0x000548, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Margin_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Add_IntInt_ReturnValue_12) == 0x000558, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Add_IntInt_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_bool_Variable_13) == 0x00055C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_bool_Variable_13' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Add_ReturnValue_4) == 0x000560, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Add_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_13) == 0x000568, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_13' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, Temp_int_Variable_6) == 0x000570, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::Temp_int_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_Select_Default_14) == 0x000574, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_Select_Default_14' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_Array_Get_Item_9) == 0x000578, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_Array_Get_Item_9' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_IsValid_ReturnValue_10) == 0x000580, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_IsValid_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_CInterpTo_InterpSpeed_ImplicitCast) == 0x000584, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_CInterpTo_InterpSpeed_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, CallFunc_CInterpTo_DeltaTime_ImplicitCast) == 0x000588, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::CallFunc_CInterpTo_DeltaTime_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Left_ImplicitCast) == 0x00058C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Left_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Right_ImplicitCast) == 0x000590, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Right_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Bottom_ImplicitCast) == 0x000594, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Bottom_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Top_ImplicitCast) == 0x000598, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Top_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Top_ImplicitCast_1) == 0x00059C, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Top_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Bottom_ImplicitCast_1) == 0x0005A0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Bottom_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Right_ImplicitCast_1) == 0x0005A4, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Right_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Left_ImplicitCast_1) == 0x0005A8, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Left_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Left_ImplicitCast_2) == 0x0005AC, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Left_ImplicitCast_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated, K2Node_MakeStruct_Right_ImplicitCast_2) == 0x0005B0, "Member 'WB_LinearProgress_Separated_C_ExecuteUbergraph_WB_LinearProgress_Separated::K2Node_MakeStruct_Right_ImplicitCast_2' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.FindCurrentSegmentPercentValue
// 0x0038 (0x0038 - 0x0000)
struct WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue final
{
public:
	double                                        ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FindPercentCurrentSegment_ReturnValue;    // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C[0x4];                                        // 0x000C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSegmentSteps_ReturnValue;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_IntFloat_ReturnValue;            // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_IntFloat_ReturnValue_1;          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue");
static_assert(sizeof(WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue) == 0x000038, "Wrong size on WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue, ReturnValue) == 0x000000, "Member 'WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue, CallFunc_FindPercentCurrentSegment_ReturnValue) == 0x000008, "Member 'WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue::CallFunc_FindPercentCurrentSegment_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue, CallFunc_GetSegmentSteps_ReturnValue) == 0x000010, "Member 'WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue::CallFunc_GetSegmentSteps_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue, CallFunc_Add_IntInt_ReturnValue) == 0x000018, "Member 'WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue, CallFunc_Multiply_IntFloat_ReturnValue) == 0x000020, "Member 'WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue, CallFunc_Multiply_IntFloat_ReturnValue_1) == 0x000028, "Member 'WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue::CallFunc_Multiply_IntFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue, CallFunc_MapRangeClamped_ReturnValue) == 0x000030, "Member 'WB_LinearProgress_Separated_C_FindCurrentSegmentPercentValue::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.FindMaxPaddingValue_Horizontal
// 0x0058 (0x0058 - 0x0000)
struct WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal final
{
public:
	double                                        Tolerance;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_2;        // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal");
static_assert(sizeof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal) == 0x000058, "Wrong size on WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, Tolerance) == 0x000000, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::Tolerance' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, ReturnValue) == 0x000008, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, Temp_bool_Variable) == 0x000010, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000018, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, CallFunc_BreakVector2D_X) == 0x000020, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, CallFunc_BreakVector2D_Y) == 0x000028, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000030, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000038, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000040, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, CallFunc_Divide_DoubleDouble_ReturnValue_2) == 0x000048, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::CallFunc_Divide_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal, K2Node_Select_Default) == 0x000050, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Horizontal::K2Node_Select_Default' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.FindMaxPaddingValue_Vertical
// 0x0040 (0x0040 - 0x0000)
struct WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical final
{
public:
	double                                        Tolerance;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical");
static_assert(sizeof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical) == 0x000040, "Wrong size on WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical, Tolerance) == 0x000000, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical::Tolerance' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical, ReturnValue) == 0x000008, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000010, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical, CallFunc_BreakVector2D_X) == 0x000018, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical, CallFunc_BreakVector2D_Y) == 0x000020, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000028, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000030, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000038, "Member 'WB_LinearProgress_Separated_C_FindMaxPaddingValue_Vertical::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.FindPercentCurrentSegment
// 0x0050 (0x0050 - 0x0000)
struct WB_LinearProgress_Separated_C_FindPercentCurrentSegment final
{
public:
	int32                                         ReturnValue;                                       // 0x0000(0x0004)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSegmentSteps_ReturnValue;              // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_1;                           // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue;                       // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_FindPercentCurrentSegment");
static_assert(sizeof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment) == 0x000050, "Wrong size on WB_LinearProgress_Separated_C_FindPercentCurrentSegment");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, ReturnValue) == 0x000000, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, Temp_bool_Variable) == 0x000004, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, Temp_real_Variable) == 0x000008, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, Temp_bool_Variable_1) == 0x000010, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, CallFunc_GetSegmentSteps_ReturnValue) == 0x000018, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::CallFunc_GetSegmentSteps_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000020, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000028, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, K2Node_Select_Default) == 0x000030, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, K2Node_Select_Default_1) == 0x000038, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000040, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_FindPercentCurrentSegment, CallFunc_FTrunc_ReturnValue) == 0x000048, "Member 'WB_LinearProgress_Separated_C_FindPercentCurrentSegment::CallFunc_FTrunc_ReturnValue' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.GetSegmentSteps
// 0x0018 (0x0018 - 0x0000)
struct WB_LinearProgress_Separated_C_GetSegmentSteps final
{
public:
	double                                        ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_GetSegmentSteps) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_GetSegmentSteps");
static_assert(sizeof(WB_LinearProgress_Separated_C_GetSegmentSteps) == 0x000018, "Wrong size on WB_LinearProgress_Separated_C_GetSegmentSteps");
static_assert(offsetof(WB_LinearProgress_Separated_C_GetSegmentSteps, ReturnValue) == 0x000000, "Member 'WB_LinearProgress_Separated_C_GetSegmentSteps::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_GetSegmentSteps, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000008, "Member 'WB_LinearProgress_Separated_C_GetSegmentSteps::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_GetSegmentSteps, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000010, "Member 'WB_LinearProgress_Separated_C_GetSegmentSteps::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetAbsoluteFillMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_LinearProgress_Separated_C_SetAbsoluteFillMethod final
{
public:
	bool                                          bAbsoluteFill_0;                                   // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_SetAbsoluteFillMethod) == 0x000001, "Wrong alignment on WB_LinearProgress_Separated_C_SetAbsoluteFillMethod");
static_assert(sizeof(WB_LinearProgress_Separated_C_SetAbsoluteFillMethod) == 0x000001, "Wrong size on WB_LinearProgress_Separated_C_SetAbsoluteFillMethod");
static_assert(offsetof(WB_LinearProgress_Separated_C_SetAbsoluteFillMethod, bAbsoluteFill_0) == 0x000000, "Member 'WB_LinearProgress_Separated_C_SetAbsoluteFillMethod::bAbsoluteFill_0' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeDrawAs
// 0x0001 (0x0001 - 0x0000)
struct WB_LinearProgress_Separated_C_SetMarqueeDrawAs final
{
public:
	ESlateBrushDrawType                           DrawAs;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_SetMarqueeDrawAs) == 0x000001, "Wrong alignment on WB_LinearProgress_Separated_C_SetMarqueeDrawAs");
static_assert(sizeof(WB_LinearProgress_Separated_C_SetMarqueeDrawAs) == 0x000001, "Wrong size on WB_LinearProgress_Separated_C_SetMarqueeDrawAs");
static_assert(offsetof(WB_LinearProgress_Separated_C_SetMarqueeDrawAs, DrawAs) == 0x000000, "Member 'WB_LinearProgress_Separated_C_SetMarqueeDrawAs::DrawAs' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeImage
// 0x0008 (0x0008 - 0x0000)
struct WB_LinearProgress_Separated_C_SetMarqueeImage final
{
public:
	class UObject*                                Image;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_SetMarqueeImage) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_SetMarqueeImage");
static_assert(sizeof(WB_LinearProgress_Separated_C_SetMarqueeImage) == 0x000008, "Wrong size on WB_LinearProgress_Separated_C_SetMarqueeImage");
static_assert(offsetof(WB_LinearProgress_Separated_C_SetMarqueeImage, Image) == 0x000000, "Member 'WB_LinearProgress_Separated_C_SetMarqueeImage::Image' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeImageSize
// 0x0010 (0x0010 - 0x0000)
struct WB_LinearProgress_Separated_C_SetMarqueeImageSize final
{
public:
	struct FVector2D                              ImageSize;                                         // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_SetMarqueeImageSize) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_SetMarqueeImageSize");
static_assert(sizeof(WB_LinearProgress_Separated_C_SetMarqueeImageSize) == 0x000010, "Wrong size on WB_LinearProgress_Separated_C_SetMarqueeImageSize");
static_assert(offsetof(WB_LinearProgress_Separated_C_SetMarqueeImageSize, ImageSize) == 0x000000, "Member 'WB_LinearProgress_Separated_C_SetMarqueeImageSize::ImageSize' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeTiling
// 0x0001 (0x0001 - 0x0000)
struct WB_LinearProgress_Separated_C_SetMarqueeTiling final
{
public:
	ESlateBrushTileType                           Tiling;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_SetMarqueeTiling) == 0x000001, "Wrong alignment on WB_LinearProgress_Separated_C_SetMarqueeTiling");
static_assert(sizeof(WB_LinearProgress_Separated_C_SetMarqueeTiling) == 0x000001, "Wrong size on WB_LinearProgress_Separated_C_SetMarqueeTiling");
static_assert(offsetof(WB_LinearProgress_Separated_C_SetMarqueeTiling, Tiling) == 0x000000, "Member 'WB_LinearProgress_Separated_C_SetMarqueeTiling::Tiling' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetMarqueeTint
// 0x0010 (0x0010 - 0x0000)
struct WB_LinearProgress_Separated_C_SetMarqueeTint final
{
public:
	struct FLinearColor                           Tint;                                              // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_SetMarqueeTint) == 0x000004, "Wrong alignment on WB_LinearProgress_Separated_C_SetMarqueeTint");
static_assert(sizeof(WB_LinearProgress_Separated_C_SetMarqueeTint) == 0x000010, "Wrong size on WB_LinearProgress_Separated_C_SetMarqueeTint");
static_assert(offsetof(WB_LinearProgress_Separated_C_SetMarqueeTint, Tint) == 0x000000, "Member 'WB_LinearProgress_Separated_C_SetMarqueeTint::Tint' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_LinearProgress_Separated_C_SetPercent final
{
public:
	double                                        Percent_0;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_SetPercent) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_SetPercent");
static_assert(sizeof(WB_LinearProgress_Separated_C_SetPercent) == 0x000008, "Wrong size on WB_LinearProgress_Separated_C_SetPercent");
static_assert(offsetof(WB_LinearProgress_Separated_C_SetPercent, Percent_0) == 0x000000, "Member 'WB_LinearProgress_Separated_C_SetPercent::Percent_0' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.SetUseMarquee
// 0x0001 (0x0001 - 0x0000)
struct WB_LinearProgress_Separated_C_SetUseMarquee final
{
public:
	bool                                          bSetUseMarquee;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_SetUseMarquee) == 0x000001, "Wrong alignment on WB_LinearProgress_Separated_C_SetUseMarquee");
static_assert(sizeof(WB_LinearProgress_Separated_C_SetUseMarquee) == 0x000001, "Wrong size on WB_LinearProgress_Separated_C_SetUseMarquee");
static_assert(offsetof(WB_LinearProgress_Separated_C_SetUseMarquee, bSetUseMarquee) == 0x000000, "Member 'WB_LinearProgress_Separated_C_SetUseMarquee::bSetUseMarquee' has a wrong offset!");

// Function WB_LinearProgress_Separated.WB_LinearProgress_Separated_C.UpdateProgressChangeColor
// 0x0018 (0x0018 - 0x0000)
struct WB_LinearProgress_Separated_C_UpdateProgressChangeColor final
{
public:
	struct FLinearColor                           NewColor;                                          // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        InterpSpeed;                                       // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_LinearProgress_Separated_C_UpdateProgressChangeColor) == 0x000008, "Wrong alignment on WB_LinearProgress_Separated_C_UpdateProgressChangeColor");
static_assert(sizeof(WB_LinearProgress_Separated_C_UpdateProgressChangeColor) == 0x000018, "Wrong size on WB_LinearProgress_Separated_C_UpdateProgressChangeColor");
static_assert(offsetof(WB_LinearProgress_Separated_C_UpdateProgressChangeColor, NewColor) == 0x000000, "Member 'WB_LinearProgress_Separated_C_UpdateProgressChangeColor::NewColor' has a wrong offset!");
static_assert(offsetof(WB_LinearProgress_Separated_C_UpdateProgressChangeColor, InterpSpeed) == 0x000010, "Member 'WB_LinearProgress_Separated_C_UpdateProgressChangeColor::InterpSpeed' has a wrong offset!");

}

