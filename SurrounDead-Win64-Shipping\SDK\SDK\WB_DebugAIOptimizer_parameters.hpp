﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_DebugAIOptimizer

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AIOptimizer_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WB_DebugAIOptimizer.WB_DebugAIOptimizer_C.DrawDebug
// 0x0260 (0x0260 - 0x0000)
struct WB_DebugAIOptimizer_C_DrawDebug final
{
public:
	struct FLinearColor                           L_DebugColor;                                      // 0x0000(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        L_DistanceAlpha;                                   // 0x0010(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAIOptimizerSubsystem*                  CallFunc_GetWorldSubsystem_ReturnValue;            // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TArray<struct FAIODebugSubjectData>           CallFunc_GetDebugSubjects_ReturnValue;             // 0x0020(0x0010)(ReferenceParm)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TMap<EAIODebugGroup, int32>                   CallFunc_GetCategorizedDebugSubjects_ReturnValue;  // 0x0038(0x0050)()
	TArray<int32>                                 CallFunc_Map_Values_Values;                        // 0x0088(0x0010)(ReferenceParm)
	TArray<EAIODebugGroup>                        CallFunc_Map_Keys_Keys;                            // 0x0098(0x0010)(ReferenceParm)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x00A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EAIODebugGroup                                CallFunc_Array_Get_Item;                           // 0x00B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B1[0x7];                                       // 0x00B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x00B8(0x0018)()
	EAIODebugGroup                                Temp_byte_Variable;                                // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D1[0x3];                                       // 0x00D1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x00D4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D9[0x3];                                       // 0x00D9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x00DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x00E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x00E4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E5[0x3];                                       // 0x00E5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x00EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FAIODebugSubjectData                   CallFunc_Array_Get_Item_1;                         // 0x00F0(0x0048)(NoDestructor)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0138(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_139[0x3];                                      // 0x0139(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Round_ReturnValue;                        // 0x013C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0140(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0150(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Round_ReturnValue_1;                      // 0x0158(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15C[0x4];                                      // 0x015C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_IntToString_ReturnValue_1;           // 0x0160(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_BoolToString_ReturnValue;            // 0x0170(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0180(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x0190(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_2;              // 0x01A0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue;             // 0x01B0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue_2;           // 0x01C8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_3;              // 0x01D8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_4;              // 0x01E8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_5;              // 0x01F8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0208(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_1;       // 0x0209(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_1;          // 0x020A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_2;       // 0x020B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_20C[0x4];                                      // 0x020C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UTextBlock*                             Temp_object_Variable;                              // 0x0210(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UTextBlock*                             K2Node_Select_Default;                             // 0x0218(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_DrawDebugPoint_Duration_ImplicitCast;     // 0x0220(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_224[0x4];                                      // 0x0224(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_A_ImplicitCast;       // 0x0228(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Round_A_ImplicitCast;                     // 0x0230(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_B_ImplicitCast;       // 0x0238(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Round_A_ImplicitCast_1;                   // 0x0240(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Greater_DoubleDouble_A_ImplicitCast;      // 0x0248(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_DrawDebugString_Duration_ImplicitCast;    // 0x0250(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_DrawDebugString_Duration_ImplicitCast_1;  // 0x0254(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_DrawDebugLine_Duration_ImplicitCast;      // 0x0258(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_DebugAIOptimizer_C_DrawDebug) == 0x000008, "Wrong alignment on WB_DebugAIOptimizer_C_DrawDebug");
static_assert(sizeof(WB_DebugAIOptimizer_C_DrawDebug) == 0x000260, "Wrong size on WB_DebugAIOptimizer_C_DrawDebug");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, L_DebugColor) == 0x000000, "Member 'WB_DebugAIOptimizer_C_DrawDebug::L_DebugColor' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, L_DistanceAlpha) == 0x000010, "Member 'WB_DebugAIOptimizer_C_DrawDebug::L_DistanceAlpha' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_GetWorldSubsystem_ReturnValue) == 0x000018, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_GetWorldSubsystem_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_GetDebugSubjects_ReturnValue) == 0x000020, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_GetDebugSubjects_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Array_Length_ReturnValue) == 0x000030, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_GetCategorizedDebugSubjects_ReturnValue) == 0x000038, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_GetCategorizedDebugSubjects_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Map_Values_Values) == 0x000088, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Map_Values_Values' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Map_Keys_Keys) == 0x000098, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Map_Keys_Keys' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, Temp_int_Array_Index_Variable) == 0x0000A8, "Member 'WB_DebugAIOptimizer_C_DrawDebug::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Array_Length_ReturnValue_1) == 0x0000AC, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Array_Get_Item) == 0x0000B0, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Conv_IntToText_ReturnValue) == 0x0000B8, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, Temp_byte_Variable) == 0x0000D0, "Member 'WB_DebugAIOptimizer_C_DrawDebug::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, Temp_int_Loop_Counter_Variable) == 0x0000D4, "Member 'WB_DebugAIOptimizer_C_DrawDebug::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Less_IntInt_ReturnValue) == 0x0000D8, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Add_IntInt_ReturnValue) == 0x0000DC, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, Temp_int_Loop_Counter_Variable_1) == 0x0000E0, "Member 'WB_DebugAIOptimizer_C_DrawDebug::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Less_IntInt_ReturnValue_1) == 0x0000E4, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Add_IntInt_ReturnValue_1) == 0x0000E8, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, Temp_int_Array_Index_Variable_1) == 0x0000EC, "Member 'WB_DebugAIOptimizer_C_DrawDebug::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Array_Get_Item_1) == 0x0000F0, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000138, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Round_ReturnValue) == 0x00013C, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Round_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Conv_IntToString_ReturnValue) == 0x000140, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000150, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Round_ReturnValue_1) == 0x000158, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Round_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Conv_IntToString_ReturnValue_1) == 0x000160, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Conv_IntToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Conv_BoolToString_ReturnValue) == 0x000170, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Conv_BoolToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Concat_StrStr_ReturnValue) == 0x000180, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Concat_StrStr_ReturnValue_1) == 0x000190, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Concat_StrStr_ReturnValue_2) == 0x0001A0, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Concat_StrStr_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Add_VectorVector_ReturnValue) == 0x0001B0, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Add_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Conv_IntToString_ReturnValue_2) == 0x0001C8, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Conv_IntToString_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Concat_StrStr_ReturnValue_3) == 0x0001D8, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Concat_StrStr_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Concat_StrStr_ReturnValue_4) == 0x0001E8, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Concat_StrStr_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Concat_StrStr_ReturnValue_5) == 0x0001F8, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Concat_StrStr_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000208, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Greater_DoubleDouble_ReturnValue_1) == 0x000209, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Greater_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Less_DoubleDouble_ReturnValue_1) == 0x00020A, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Less_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Greater_DoubleDouble_ReturnValue_2) == 0x00020B, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Greater_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, Temp_object_Variable) == 0x000210, "Member 'WB_DebugAIOptimizer_C_DrawDebug::Temp_object_Variable' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, K2Node_Select_Default) == 0x000218, "Member 'WB_DebugAIOptimizer_C_DrawDebug::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_DrawDebugPoint_Duration_ImplicitCast) == 0x000220, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_DrawDebugPoint_Duration_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Divide_DoubleDouble_A_ImplicitCast) == 0x000228, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Divide_DoubleDouble_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Round_A_ImplicitCast) == 0x000230, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Round_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Divide_DoubleDouble_B_ImplicitCast) == 0x000238, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Divide_DoubleDouble_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Round_A_ImplicitCast_1) == 0x000240, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Round_A_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_Greater_DoubleDouble_A_ImplicitCast) == 0x000248, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_Greater_DoubleDouble_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_DrawDebugString_Duration_ImplicitCast) == 0x000250, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_DrawDebugString_Duration_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_DrawDebugString_Duration_ImplicitCast_1) == 0x000254, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_DrawDebugString_Duration_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_DrawDebug, CallFunc_DrawDebugLine_Duration_ImplicitCast) == 0x000258, "Member 'WB_DebugAIOptimizer_C_DrawDebug::CallFunc_DrawDebugLine_Duration_ImplicitCast' has a wrong offset!");

// Function WB_DebugAIOptimizer.WB_DebugAIOptimizer_C.ExecuteUbergraph_WB_DebugAIOptimizer
// 0x0040 (0x0040 - 0x0000)
struct WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bIsEnabled;                     // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(bool bIsEnabled)>              K2Node_CreateDelegate_OutputDelegate;              // 0x0008(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_1;            // 0x0018(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           CallFunc_K2_SetTimerDelegate_ReturnValue;          // 0x0028(0x0008)(NoDestructor, HasGetValueTypeHash)
	class UAIOptimizerSubsystem*                  CallFunc_GetWorldSubsystem_ReturnValue;            // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_K2_SetTimerDelegate_Time_ImplicitCast;    // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer) == 0x000008, "Wrong alignment on WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer");
static_assert(sizeof(WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer) == 0x000040, "Wrong size on WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer");
static_assert(offsetof(WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer, EntryPoint) == 0x000000, "Member 'WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer, K2Node_CustomEvent_bIsEnabled) == 0x000004, "Member 'WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer::K2Node_CustomEvent_bIsEnabled' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer, K2Node_CreateDelegate_OutputDelegate) == 0x000008, "Member 'WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer, K2Node_CreateDelegate_OutputDelegate_1) == 0x000018, "Member 'WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer, CallFunc_K2_SetTimerDelegate_ReturnValue) == 0x000028, "Member 'WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer::CallFunc_K2_SetTimerDelegate_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer, CallFunc_GetWorldSubsystem_ReturnValue) == 0x000030, "Member 'WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer::CallFunc_GetWorldSubsystem_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer, CallFunc_K2_SetTimerDelegate_Time_ImplicitCast) == 0x000038, "Member 'WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer::CallFunc_K2_SetTimerDelegate_Time_ImplicitCast' has a wrong offset!");

// Function WB_DebugAIOptimizer.WB_DebugAIOptimizer_C.OnSubsystemEnabledChanged_Event_0
// 0x0001 (0x0001 - 0x0000)
struct WB_DebugAIOptimizer_C_OnSubsystemEnabledChanged_Event_0 final
{
public:
	bool                                          bIsEnabled_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_DebugAIOptimizer_C_OnSubsystemEnabledChanged_Event_0) == 0x000001, "Wrong alignment on WB_DebugAIOptimizer_C_OnSubsystemEnabledChanged_Event_0");
static_assert(sizeof(WB_DebugAIOptimizer_C_OnSubsystemEnabledChanged_Event_0) == 0x000001, "Wrong size on WB_DebugAIOptimizer_C_OnSubsystemEnabledChanged_Event_0");
static_assert(offsetof(WB_DebugAIOptimizer_C_OnSubsystemEnabledChanged_Event_0, bIsEnabled_0) == 0x000000, "Member 'WB_DebugAIOptimizer_C_OnSubsystemEnabledChanged_Event_0::bIsEnabled_0' has a wrong offset!");

}

