﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_PB_Raw

#include "Basic.hpp"

#include "WB_PB_Raw_classes.hpp"
#include "WB_PB_Raw_parameters.hpp"


namespace SDK
{

// Function WB_PB_Raw.WB_PB_Raw_C.ExecuteUbergraph_WB_PB_Raw
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::ExecuteUbergraph_WB_PB_Raw(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "ExecuteUbergraph_WB_PB_Raw");

	Params::WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetBackgroundTint
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColor                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetBackgroundTint(const struct FLinearColor& InColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetBackgroundTint");

	Params::WB_PB_Raw_C_SetBackgroundTint Parms{};

	Parms.InColor = std::move(InColor);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetBarFillType
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressBarFillType                    BarFillType                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bUseShader                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetBarFillType(EProgressBarFillType BarFillType, bool bUseShader)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetBarFillType");

	Params::WB_PB_Raw_C_SetBarFillType Parms{};

	Parms.BarFillType = BarFillType;
	Parms.bUseShader = bUseShader;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColor                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetColor(const struct FLinearColor& InColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetColor");

	Params::WB_PB_Raw_C_SetColor Parms{};

	Parms.InColor = std::move(InColor);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetFillColorAndOpacity
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColor                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetFillColorAndOpacity(const struct FLinearColor& InColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetFillColorAndOpacity");

	Params::WB_PB_Raw_C_SetFillColorAndOpacity Parms{};

	Parms.InColor = std::move(InColor);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImage
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          FillImage                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetFillImage(class UObject* FillImage)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetFillImage");

	Params::WB_PB_Raw_C_SetFillImage Parms{};

	Parms.FillImage = FillImage;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImageDrawAs
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushDrawType                     Draw_As                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetFillImageDrawAs(ESlateBrushDrawType Draw_As)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetFillImageDrawAs");

	Params::WB_PB_Raw_C_SetFillImageDrawAs Parms{};

	Parms.Draw_As = Draw_As;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImageMargin
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Margin                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetFillImageMargin(double Margin)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetFillImageMargin");

	Params::WB_PB_Raw_C_SetFillImageMargin Parms{};

	Parms.Margin = Margin;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImageSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 FillImageSize                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetFillImageSize(const struct FVector2D& FillImageSize)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetFillImageSize");

	Params::WB_PB_Raw_C_SetFillImageSize Parms{};

	Parms.FillImageSize = std::move(FillImageSize);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImageTiling
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetFillImageTiling(ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetFillImageTiling");

	Params::WB_PB_Raw_C_SetFillImageTiling Parms{};

	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetMarquee
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsMarquee                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetMarquee(bool IsMarquee)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetMarquee");

	Params::WB_PB_Raw_C_SetMarquee Parms{};

	Parms.IsMarquee = IsMarquee;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeDrawAs
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushDrawType                     DrawAs                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetMarqueeDrawAs(ESlateBrushDrawType DrawAs)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetMarqueeDrawAs");

	Params::WB_PB_Raw_C_SetMarqueeDrawAs Parms{};

	Parms.DrawAs = DrawAs;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeImage
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          Image                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetMarqueeImage(class UObject* Image)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetMarqueeImage");

	Params::WB_PB_Raw_C_SetMarqueeImage Parms{};

	Parms.Image = Image;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeImageSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 ImageSize                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetMarqueeImageSize(const struct FVector2D& ImageSize)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetMarqueeImageSize");

	Params::WB_PB_Raw_C_SetMarqueeImageSize Parms{};

	Parms.ImageSize = std::move(ImageSize);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeTiling
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetMarqueeTiling(ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetMarqueeTiling");

	Params::WB_PB_Raw_C_SetMarqueeTiling Parms{};

	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeTint
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Tint                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetMarqueeTint(const struct FLinearColor& Tint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetMarqueeTint");

	Params::WB_PB_Raw_C_SetMarqueeTint Parms{};

	Parms.Tint = std::move(Tint);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Raw.WB_PB_Raw_C.SetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  InPercent                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Raw_C::SetPercent(double InPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Raw_C", "SetPercent");

	Params::WB_PB_Raw_C_SetPercent Parms{};

	Parms.InPercent = InPercent;

	UObject::ProcessEvent(Func, &Parms);
}

}

