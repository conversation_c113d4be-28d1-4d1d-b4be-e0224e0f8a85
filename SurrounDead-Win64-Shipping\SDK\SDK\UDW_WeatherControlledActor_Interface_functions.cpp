﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDW_WeatherControlledActor_Interface

#include "Basic.hpp"

#include "UDW_WeatherControlledActor_Interface_classes.hpp"


namespace SDK
{

// Function UDW_WeatherControlledActor_Interface.UDW_WeatherControlledActor_Interface_C.UDW Editor Update
// (Public, BlueprintCallable, BlueprintEvent)

void IUDW_WeatherControlledActor_Interface_C::UDW_Editor_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = AsUObject()->Class->GetFunction("UDW_WeatherControlledActor_Interface_C", "UDW Editor Update");

	AsUObject()->ProcessEvent(Func, nullptr);
}


// Function UDW_WeatherControlledActor_Interface.UDW_WeatherControlledActor_Interface_C.UDW Instant Update
// (Public, BlueprintCallable, BlueprintEvent)

void IUDW_WeatherControlledActor_Interface_C::UDW_Instant_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = AsUObject()->Class->GetFunction("UDW_WeatherControlledActor_Interface_C", "UDW Instant Update");

	AsUObject()->ProcessEvent(Func, nullptr);
}

}

