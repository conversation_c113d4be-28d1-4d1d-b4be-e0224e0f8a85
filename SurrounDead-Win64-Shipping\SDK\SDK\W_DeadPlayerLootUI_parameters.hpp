﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_DeadPlayerLootUI

#include "Basic.hpp"

#include "GameplayTags_structs.hpp"


namespace SDK::Params
{

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.AddContentToPanel
// 0x0060 (0x0060 - 0x0000)
struct W_DeadPlayerLootUI_C_AddContentToPanel final
{
public:
	class UUserWidget*                            Content;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        ToContainer;                                       // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0018(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue_1;    // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_1;                   // 0x0028(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_2;                   // 0x0030(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue_2;    // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue_3;    // 0x0039(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3A[0x6];                                       // 0x003A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_3;                   // 0x0040(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_4;                   // 0x0048(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_5;                   // 0x0050(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue_4;    // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_AddContentToPanel) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_AddContentToPanel");
static_assert(sizeof(W_DeadPlayerLootUI_C_AddContentToPanel) == 0x000060, "Wrong size on W_DeadPlayerLootUI_C_AddContentToPanel");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, Content) == 0x000000, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::Content' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, ToContainer) == 0x000008, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::ToContainer' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x000010, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_AddChild_ReturnValue) == 0x000018, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_EqualEqual_ObjectObject_ReturnValue_1) == 0x000020, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_EqualEqual_ObjectObject_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_AddChild_ReturnValue_1) == 0x000028, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_AddChild_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_AddChild_ReturnValue_2) == 0x000030, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_AddChild_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_EqualEqual_ObjectObject_ReturnValue_2) == 0x000038, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_EqualEqual_ObjectObject_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_EqualEqual_ObjectObject_ReturnValue_3) == 0x000039, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_EqualEqual_ObjectObject_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_AddChild_ReturnValue_3) == 0x000040, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_AddChild_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_AddChild_ReturnValue_4) == 0x000048, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_AddChild_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_AddChild_ReturnValue_5) == 0x000050, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_AddChild_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_AddContentToPanel, CallFunc_EqualEqual_ObjectObject_ReturnValue_4) == 0x000058, "Member 'W_DeadPlayerLootUI_C_AddContentToPanel::CallFunc_EqualEqual_ObjectObject_ReturnValue_4' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.DropInfo_OnItemEquippedChange_Event
// 0x0020 (0x0020 - 0x0000)
struct W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event final
{
public:
	class UJSIContainer_C*                        FromContainer;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        ToContainer;                                       // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            SlotRef;                                           // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Equipped_;                                         // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event");
static_assert(sizeof(W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event) == 0x000020, "Wrong size on W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event");
static_assert(offsetof(W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event, FromContainer) == 0x000000, "Member 'W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event::FromContainer' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event, ToContainer) == 0x000008, "Member 'W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event::ToContainer' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event, SlotRef) == 0x000010, "Member 'W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event::SlotRef' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event, Equipped_) == 0x000018, "Member 'W_DeadPlayerLootUI_C_DropInfo_OnItemEquippedChange_Event::Equipped_' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.ExecuteUbergraph_W_DeadPlayerLootUI
// 0x00B0 (0x00B0 - 0x0000)
struct W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UJSIContainer_C* FromContainer, class UJSIContainer_C* ToContainer, class UJSI_Slot_C* SlotRef, bool Equipped_)> K2Node_CreateDelegate_OutputDelegate; // 0x0010(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        CallFunc_Array_Get_Item;                           // 0x0020(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x002C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2D[0x3];                                       // 0x002D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            K2Node_Event_ItemRef;                              // 0x0030(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 K2Node_Event_ActorRef;                             // 0x0038(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_NewWeight;                            // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUserWidget*                            K2Node_Event_Widget;                               // 0x0048(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   K2Node_Event_Name;                                 // 0x0050(0x0018)()
	class UBP_InspectorWindowWidget_C*            K2Node_Event_Inspector;                            // 0x0068(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TArray<class UJSIContainer_C*>                CallFunc_GetListOfContainers_Containers;           // 0x0070(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UJSIContainer_C*                        K2Node_CustomEvent_FromContainer;                  // 0x0080(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        K2Node_CustomEvent_ToContainer;                    // 0x0088(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            K2Node_CustomEvent_SlotRef;                        // 0x0090(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_Equipped_;                      // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_99[0x7];                                       // 0x0099(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUserWidget*                            CallFunc_GetContainerOneRef_ContainerRef;          // 0x00A0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x00A9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI");
static_assert(sizeof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI) == 0x0000B0, "Wrong size on W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, EntryPoint) == 0x000000, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, Temp_int_Loop_Counter_Variable) == 0x000004, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, Temp_int_Array_Index_Variable) == 0x000008, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, CallFunc_Add_IntInt_ReturnValue) == 0x00000C, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_CreateDelegate_OutputDelegate) == 0x000010, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, CallFunc_Array_Get_Item) == 0x000020, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, CallFunc_Array_Length_ReturnValue) == 0x000028, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, CallFunc_Less_IntInt_ReturnValue) == 0x00002C, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_Event_ItemRef) == 0x000030, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_Event_ItemRef' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_Event_ActorRef) == 0x000038, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_Event_ActorRef' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_Event_NewWeight) == 0x000040, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_Event_NewWeight' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_Event_Widget) == 0x000048, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_Event_Widget' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_Event_Name) == 0x000050, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_Event_Name' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_Event_Inspector) == 0x000068, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_Event_Inspector' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, CallFunc_GetListOfContainers_Containers) == 0x000070, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::CallFunc_GetListOfContainers_Containers' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_CustomEvent_FromContainer) == 0x000080, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_CustomEvent_FromContainer' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_CustomEvent_ToContainer) == 0x000088, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_CustomEvent_ToContainer' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_CustomEvent_SlotRef) == 0x000090, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_CustomEvent_SlotRef' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, K2Node_CustomEvent_Equipped_) == 0x000098, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::K2Node_CustomEvent_Equipped_' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, CallFunc_GetContainerOneRef_ContainerRef) == 0x0000A0, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::CallFunc_GetContainerOneRef_ContainerRef' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, CallFunc_IsValid_ReturnValue) == 0x0000A8, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI, CallFunc_IsValid_ReturnValue_1) == 0x0000A9, "Member 'W_DeadPlayerLootUI_C_ExecuteUbergraph_W_DeadPlayerLootUI::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.GetAllAttachments
// 0x0010 (0x0010 - 0x0000)
struct W_DeadPlayerLootUI_C_GetAllAttachments final
{
public:
	TArray<class FName>                           Attachments;                                       // 0x0000(0x0010)(Parm, OutParm)
};
static_assert(alignof(W_DeadPlayerLootUI_C_GetAllAttachments) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_GetAllAttachments");
static_assert(sizeof(W_DeadPlayerLootUI_C_GetAllAttachments) == 0x000010, "Wrong size on W_DeadPlayerLootUI_C_GetAllAttachments");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetAllAttachments, Attachments) == 0x000000, "Member 'W_DeadPlayerLootUI_C_GetAllAttachments::Attachments' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.GetContainerByAttachmentType
// 0x0018 (0x0018 - 0x0000)
struct W_DeadPlayerLootUI_C_GetContainerByAttachmentType final
{
public:
	struct FGameplayTag                           Type;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        JigContainer;                                      // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         ContainerIndex;                                    // 0x0010(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_GetContainerByAttachmentType) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_GetContainerByAttachmentType");
static_assert(sizeof(W_DeadPlayerLootUI_C_GetContainerByAttachmentType) == 0x000018, "Wrong size on W_DeadPlayerLootUI_C_GetContainerByAttachmentType");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetContainerByAttachmentType, Type) == 0x000000, "Member 'W_DeadPlayerLootUI_C_GetContainerByAttachmentType::Type' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetContainerByAttachmentType, JigContainer) == 0x000008, "Member 'W_DeadPlayerLootUI_C_GetContainerByAttachmentType::JigContainer' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetContainerByAttachmentType, ContainerIndex) == 0x000010, "Member 'W_DeadPlayerLootUI_C_GetContainerByAttachmentType::ContainerIndex' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.GetDropWidget
// 0x0008 (0x0008 - 0x0000)
struct W_DeadPlayerLootUI_C_GetDropWidget final
{
public:
	class UDropItemBackGwidget_C*                 DropWRef;                                          // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_GetDropWidget) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_GetDropWidget");
static_assert(sizeof(W_DeadPlayerLootUI_C_GetDropWidget) == 0x000008, "Wrong size on W_DeadPlayerLootUI_C_GetDropWidget");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetDropWidget, DropWRef) == 0x000000, "Member 'W_DeadPlayerLootUI_C_GetDropWidget::DropWRef' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.GetJSIContainerByPlayerSlots
// 0x0020 (0x0020 - 0x0000)
struct W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots final
{
public:
	struct FGameplayTag                           Slot_0;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        Container;                                         // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            EquippedItem;                                      // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          IsPending_;                                        // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots");
static_assert(sizeof(W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots) == 0x000020, "Wrong size on W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots, Slot_0) == 0x000000, "Member 'W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots::Slot_0' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots, Container) == 0x000008, "Member 'W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots::Container' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots, EquippedItem) == 0x000010, "Member 'W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots::EquippedItem' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots, IsPending_) == 0x000018, "Member 'W_DeadPlayerLootUI_C_GetJSIContainerByPlayerSlots::IsPending_' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.GetListOfNonAddContainers
// 0x0010 (0x0010 - 0x0000)
struct W_DeadPlayerLootUI_C_GetListOfNonAddContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_DeadPlayerLootUI_C_GetListOfNonAddContainers) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_GetListOfNonAddContainers");
static_assert(sizeof(W_DeadPlayerLootUI_C_GetListOfNonAddContainers) == 0x000010, "Wrong size on W_DeadPlayerLootUI_C_GetListOfNonAddContainers");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetListOfNonAddContainers, Containers) == 0x000000, "Member 'W_DeadPlayerLootUI_C_GetListOfNonAddContainers::Containers' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.GetLootContent
// 0x0008 (0x0008 - 0x0000)
struct W_DeadPlayerLootUI_C_GetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_GetLootContent) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_GetLootContent");
static_assert(sizeof(W_DeadPlayerLootUI_C_GetLootContent) == 0x000008, "Wrong size on W_DeadPlayerLootUI_C_GetLootContent");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetLootContent, Widget) == 0x000000, "Member 'W_DeadPlayerLootUI_C_GetLootContent::Widget' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.GetValidReloadContainers
// 0x0010 (0x0010 - 0x0000)
struct W_DeadPlayerLootUI_C_GetValidReloadContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_DeadPlayerLootUI_C_GetValidReloadContainers) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_GetValidReloadContainers");
static_assert(sizeof(W_DeadPlayerLootUI_C_GetValidReloadContainers) == 0x000010, "Wrong size on W_DeadPlayerLootUI_C_GetValidReloadContainers");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetValidReloadContainers, Containers) == 0x000000, "Member 'W_DeadPlayerLootUI_C_GetValidReloadContainers::Containers' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.JigSetLootContent
// 0x0020 (0x0020 - 0x0000)
struct W_DeadPlayerLootUI_C_JigSetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   Name_0;                                            // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(W_DeadPlayerLootUI_C_JigSetLootContent) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_JigSetLootContent");
static_assert(sizeof(W_DeadPlayerLootUI_C_JigSetLootContent) == 0x000020, "Wrong size on W_DeadPlayerLootUI_C_JigSetLootContent");
static_assert(offsetof(W_DeadPlayerLootUI_C_JigSetLootContent, Widget) == 0x000000, "Member 'W_DeadPlayerLootUI_C_JigSetLootContent::Widget' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_JigSetLootContent, Name_0) == 0x000008, "Member 'W_DeadPlayerLootUI_C_JigSetLootContent::Name_0' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.JSIOnWeightUpdated
// 0x0008 (0x0008 - 0x0000)
struct W_DeadPlayerLootUI_C_JSIOnWeightUpdated final
{
public:
	double                                        NewWeight;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_JSIOnWeightUpdated");
static_assert(sizeof(W_DeadPlayerLootUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong size on W_DeadPlayerLootUI_C_JSIOnWeightUpdated");
static_assert(offsetof(W_DeadPlayerLootUI_C_JSIOnWeightUpdated, NewWeight) == 0x000000, "Member 'W_DeadPlayerLootUI_C_JSIOnWeightUpdated::NewWeight' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.SetActionbarFollower
// 0x0010 (0x0010 - 0x0000)
struct W_DeadPlayerLootUI_C_SetActionbarFollower final
{
public:
	class UJSI_Slot_C*                            JigRef;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Return;                                            // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_SetActionbarFollower) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_SetActionbarFollower");
static_assert(sizeof(W_DeadPlayerLootUI_C_SetActionbarFollower) == 0x000010, "Wrong size on W_DeadPlayerLootUI_C_SetActionbarFollower");
static_assert(offsetof(W_DeadPlayerLootUI_C_SetActionbarFollower, JigRef) == 0x000000, "Member 'W_DeadPlayerLootUI_C_SetActionbarFollower::JigRef' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_SetActionbarFollower, Return) == 0x000008, "Member 'W_DeadPlayerLootUI_C_SetActionbarFollower::Return' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.SetActorOwner
// 0x0008 (0x0008 - 0x0000)
struct W_DeadPlayerLootUI_C_SetActorOwner final
{
public:
	class AActor*                                 ActorRef;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_SetActorOwner) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_SetActorOwner");
static_assert(sizeof(W_DeadPlayerLootUI_C_SetActorOwner) == 0x000008, "Wrong size on W_DeadPlayerLootUI_C_SetActorOwner");
static_assert(offsetof(W_DeadPlayerLootUI_C_SetActorOwner, ActorRef) == 0x000000, "Member 'W_DeadPlayerLootUI_C_SetActorOwner::ActorRef' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.SetInspectorRef
// 0x0008 (0x0008 - 0x0000)
struct W_DeadPlayerLootUI_C_SetInspectorRef final
{
public:
	class UBP_InspectorWindowWidget_C*            Inspector;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_SetInspectorRef) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_SetInspectorRef");
static_assert(sizeof(W_DeadPlayerLootUI_C_SetInspectorRef) == 0x000008, "Wrong size on W_DeadPlayerLootUI_C_SetInspectorRef");
static_assert(offsetof(W_DeadPlayerLootUI_C_SetInspectorRef, Inspector) == 0x000000, "Member 'W_DeadPlayerLootUI_C_SetInspectorRef::Inspector' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.SetItemReference
// 0x0008 (0x0008 - 0x0000)
struct W_DeadPlayerLootUI_C_SetItemReference final
{
public:
	class UJSI_Slot_C*                            ItemRef;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadPlayerLootUI_C_SetItemReference) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_SetItemReference");
static_assert(sizeof(W_DeadPlayerLootUI_C_SetItemReference) == 0x000008, "Wrong size on W_DeadPlayerLootUI_C_SetItemReference");
static_assert(offsetof(W_DeadPlayerLootUI_C_SetItemReference, ItemRef) == 0x000000, "Member 'W_DeadPlayerLootUI_C_SetItemReference::ItemRef' has a wrong offset!");

// Function W_DeadPlayerLootUI.W_DeadPlayerLootUI_C.GetListOfContainers
// 0x0020 (0x0020 - 0x0000)
struct W_DeadPlayerLootUI_C_GetListOfContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
	TArray<class UJSIContainer_C*>                K2Node_MakeArray_Array;                            // 0x0010(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(W_DeadPlayerLootUI_C_GetListOfContainers) == 0x000008, "Wrong alignment on W_DeadPlayerLootUI_C_GetListOfContainers");
static_assert(sizeof(W_DeadPlayerLootUI_C_GetListOfContainers) == 0x000020, "Wrong size on W_DeadPlayerLootUI_C_GetListOfContainers");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetListOfContainers, Containers) == 0x000000, "Member 'W_DeadPlayerLootUI_C_GetListOfContainers::Containers' has a wrong offset!");
static_assert(offsetof(W_DeadPlayerLootUI_C_GetListOfContainers, K2Node_MakeArray_Array) == 0x000010, "Member 'W_DeadPlayerLootUI_C_GetListOfContainers::K2Node_MakeArray_Array' has a wrong offset!");

}

