﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_Startup_Interface

#include "Basic.hpp"


namespace SDK::Params
{

// Function UDS_Startup_Interface.UDS_Startup_Interface_C.UDS Starting Up
// 0x0008 (0x0008 - 0x0000)
struct UDS_Startup_Interface_C_UDS_Starting_Up final
{
public:
	class AUltra_Dynamic_Sky_C*                   UDS;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_Startup_Interface_C_UDS_Starting_Up) == 0x000008, "Wrong alignment on UDS_Startup_Interface_C_UDS_Starting_Up");
static_assert(sizeof(UDS_Startup_Interface_C_UDS_Starting_Up) == 0x000008, "Wrong size on UDS_Startup_Interface_C_UDS_Starting_Up");
static_assert(offsetof(UDS_Startup_Interface_C_UDS_Starting_Up, UDS) == 0x000000, "Member 'UDS_Startup_Interface_C_UDS_Starting_Up::UDS' has a wrong offset!");

}

