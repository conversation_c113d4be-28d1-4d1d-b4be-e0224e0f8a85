﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Particle

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "UMG_structs.hpp"
#include "ETriggerMethod_structs.hpp"


namespace SDK::Params
{

// Function WB_Particle.WB_Particle_C.Anim_Fade
// 0x0030 (0x0030 - 0x0000)
struct WB_Particle_C_Anim_Fade final
{
public:
	double                                        PlaybackSpeed_0;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable_1;                              // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B[0x1];                                        // 0x000B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_Select_Default_1;                           // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast; // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_Anim_Fade) == 0x000008, "Wrong alignment on WB_Particle_C_Anim_Fade");
static_assert(sizeof(WB_Particle_C_Anim_Fade) == 0x000030, "Wrong size on WB_Particle_C_Anim_Fade");
static_assert(offsetof(WB_Particle_C_Anim_Fade, PlaybackSpeed_0) == 0x000000, "Member 'WB_Particle_C_Anim_Fade::PlaybackSpeed_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, Temp_byte_Variable) == 0x000008, "Member 'WB_Particle_C_Anim_Fade::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, Temp_byte_Variable_1) == 0x000009, "Member 'WB_Particle_C_Anim_Fade::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, Temp_bool_Variable) == 0x00000A, "Member 'WB_Particle_C_Anim_Fade::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, Temp_int_Variable) == 0x00000C, "Member 'WB_Particle_C_Anim_Fade::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, Temp_int_Variable_1) == 0x000010, "Member 'WB_Particle_C_Anim_Fade::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, Temp_bool_Variable_1) == 0x000014, "Member 'WB_Particle_C_Anim_Fade::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, K2Node_Select_Default) == 0x000018, "Member 'WB_Particle_C_Anim_Fade::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, K2Node_Select_Default_1) == 0x00001C, "Member 'WB_Particle_C_Anim_Fade::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, CallFunc_PlayAnimation_ReturnValue) == 0x000020, "Member 'WB_Particle_C_Anim_Fade::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Anim_Fade, CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast) == 0x000028, "Member 'WB_Particle_C_Anim_Fade::CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.CountValue
// 0x0050 (0x0050 - 0x0000)
struct WB_Particle_C_CountValue final
{
public:
	double                                        Time;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable;                                // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue;         // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_CountValue) == 0x000008, "Wrong alignment on WB_Particle_C_CountValue");
static_assert(sizeof(WB_Particle_C_CountValue) == 0x000050, "Wrong size on WB_Particle_C_CountValue");
static_assert(offsetof(WB_Particle_C_CountValue, Time) == 0x000000, "Member 'WB_Particle_C_CountValue::Time' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CountValue, ReturnValue) == 0x000008, "Member 'WB_Particle_C_CountValue::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CountValue, Temp_bool_Variable) == 0x000010, "Member 'WB_Particle_C_CountValue::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CountValue, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000018, "Member 'WB_Particle_C_CountValue::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CountValue, Temp_real_Variable) == 0x000020, "Member 'WB_Particle_C_CountValue::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CountValue, CallFunc_GetWorldDeltaSeconds_ReturnValue) == 0x000028, "Member 'WB_Particle_C_CountValue::CallFunc_GetWorldDeltaSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CountValue, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000030, "Member 'WB_Particle_C_CountValue::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CountValue, CallFunc_Add_DoubleDouble_ReturnValue) == 0x000038, "Member 'WB_Particle_C_CountValue::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CountValue, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000040, "Member 'WB_Particle_C_CountValue::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CountValue, K2Node_Select_Default) == 0x000048, "Member 'WB_Particle_C_CountValue::K2Node_Select_Default' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.CreateParticle
// 0x0080 (0x0080 - 0x0000)
struct WB_Particle_C_CreateParticle final
{
public:
	class UObject*                                Image;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        PlaybackSpeed_0;                                   // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Color;                                             // 0x0010(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Scale_0;                                           // 0x0020(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Translation_0;                                     // 0x0030(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Shear_0;                                           // 0x0040(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Angle_0;                                           // 0x0050(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Size_0;                                            // 0x0058(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                TriggerMethod_0;                                   // 0x0068(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsDesignTime;                                     // 0x0069(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6A[0x6];                                       // 0x006A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        ParticleSpread_0;                                  // 0x0070(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          UseParticleRotation_0;                             // 0x0078(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_CreateParticle) == 0x000008, "Wrong alignment on WB_Particle_C_CreateParticle");
static_assert(sizeof(WB_Particle_C_CreateParticle) == 0x000080, "Wrong size on WB_Particle_C_CreateParticle");
static_assert(offsetof(WB_Particle_C_CreateParticle, Image) == 0x000000, "Member 'WB_Particle_C_CreateParticle::Image' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, PlaybackSpeed_0) == 0x000008, "Member 'WB_Particle_C_CreateParticle::PlaybackSpeed_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, Color) == 0x000010, "Member 'WB_Particle_C_CreateParticle::Color' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, Scale_0) == 0x000020, "Member 'WB_Particle_C_CreateParticle::Scale_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, Translation_0) == 0x000030, "Member 'WB_Particle_C_CreateParticle::Translation_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, Shear_0) == 0x000040, "Member 'WB_Particle_C_CreateParticle::Shear_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, Angle_0) == 0x000050, "Member 'WB_Particle_C_CreateParticle::Angle_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, Size_0) == 0x000058, "Member 'WB_Particle_C_CreateParticle::Size_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, TriggerMethod_0) == 0x000068, "Member 'WB_Particle_C_CreateParticle::TriggerMethod_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, bIsDesignTime) == 0x000069, "Member 'WB_Particle_C_CreateParticle::bIsDesignTime' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, ParticleSpread_0) == 0x000070, "Member 'WB_Particle_C_CreateParticle::ParticleSpread_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_CreateParticle, UseParticleRotation_0) == 0x000078, "Member 'WB_Particle_C_CreateParticle::UseParticleRotation_0' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.ExecuteUbergraph_WB_Particle
// 0x0210 (0x0210 - 0x0000)
struct WB_Particle_C_ExecuteUbergraph_WB_Particle final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0004(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UObject*                                K2Node_CustomEvent_Image;                          // 0x0040(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_PlaybackSpeed;                  // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_Color;                          // 0x0050(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              K2Node_CustomEvent_Scale;                          // 0x0060(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              K2Node_CustomEvent_Translation;                    // 0x0070(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              K2Node_CustomEvent_Shear;                          // 0x0080(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Angle;                          // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              K2Node_CustomEvent_Size;                           // 0x0098(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                K2Node_CustomEvent_TriggerMethod;                  // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bIsDesignTime;                  // 0x00A9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AA[0x6];                                       // 0x00AA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_ParticleSpread;                 // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_UseParticleRotation;            // 0x00B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x00B9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_BA[0x6];                                       // 0x00BA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_RandomFloatInRange_ReturnValue;           // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_RandomFloatInRange_ReturnValue_1;         // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x00D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D2[0x2];                                       // 0x00D2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_GetEndTime_ReturnValue;                   // 0x00D4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_CountValue_ReturnValue;                   // 0x00E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F1[0x7];                                       // 0x00F1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_RandomFloatInRange_ReturnValue_2;         // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0100(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Whether_the_gate_is_currently_open_or_close_Variable; // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_109[0x3];                                      // 0x0109(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x010C(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_1;               // 0x011C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11D[0x3];                                      // 0x011D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                Temp_object_Variable;                              // 0x0120(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetClampedMinSize_ReturnValue;            // 0x0128(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0130(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetClampedMinSize_ReturnValue_1;          // 0x0138(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_RandomFloatInRange_ReturnValue_3;         // 0x0140(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_2;        // 0x0148(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_RandomizePositiveNegativeFloat_ReturnValue; // 0x0150(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_RandomFloatInRange_ReturnValue_4;         // 0x0158(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_RandomizePositiveNegativeFloat_ReturnValue_1; // 0x0160(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0168(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_3;        // 0x0178(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_DoubleDouble_ReturnValue;    // 0x0180(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_181[0x7];                                      // 0x0181(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0190(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0198(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x01A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_1;            // 0x01A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_1;                        // 0x01B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_1;                        // 0x01B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_1;                     // 0x01C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C1[0x7];                                      // 0x01C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x01C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_2;            // 0x01D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x01D8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x01E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1E9[0x7];                                      // 0x01E9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                K2Node_Select_Default_1;                           // 0x01F0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_3;            // 0x01F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_A_ImplicitCast;       // 0x0200(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetRenderTransformAngle_Angle_ImplicitCast; // 0x0208(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_ExecuteUbergraph_WB_Particle) == 0x000008, "Wrong alignment on WB_Particle_C_ExecuteUbergraph_WB_Particle");
static_assert(sizeof(WB_Particle_C_ExecuteUbergraph_WB_Particle) == 0x000210, "Wrong size on WB_Particle_C_ExecuteUbergraph_WB_Particle");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, EntryPoint) == 0x000000, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_Event_MyGeometry) == 0x000004, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_Event_InDeltaTime) == 0x00003C, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_Image) == 0x000040, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_Image' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_PlaybackSpeed) == 0x000048, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_Color) == 0x000050, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_Color' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_Scale) == 0x000060, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_Scale' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_Translation) == 0x000070, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_Translation' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_Shear) == 0x000080, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_Shear' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_Angle) == 0x000090, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_Angle' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_Size) == 0x000098, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_Size' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_TriggerMethod) == 0x0000A8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_TriggerMethod' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_bIsDesignTime) == 0x0000A9, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_bIsDesignTime' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_ParticleSpread) == 0x0000B0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_ParticleSpread' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CustomEvent_UseParticleRotation) == 0x0000B8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CustomEvent_UseParticleRotation' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_IsValid_ReturnValue) == 0x0000B9, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_RandomFloatInRange_ReturnValue) == 0x0000C0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_RandomFloatInRange_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_RandomFloatInRange_ReturnValue_1) == 0x0000C8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_RandomFloatInRange_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, Temp_bool_Has_Been_Initd_Variable) == 0x0000D0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, Temp_bool_Variable) == 0x0000D1, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_GetEndTime_ReturnValue) == 0x0000D4, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_GetEndTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x0000D8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_CountValue_ReturnValue) == 0x0000E0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_CountValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0000E8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, Temp_bool_IsClosed_Variable) == 0x0000F0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_RandomFloatInRange_ReturnValue_2) == 0x0000F8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_RandomFloatInRange_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_MapRangeClamped_ReturnValue) == 0x000100, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, Temp_bool_Whether_the_gate_is_currently_open_or_close_Variable) == 0x000108, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::Temp_bool_Whether_the_gate_is_currently_open_or_close_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_CreateDelegate_OutputDelegate) == 0x00010C, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, Temp_bool_Has_Been_Initd_Variable_1) == 0x00011C, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::Temp_bool_Has_Been_Initd_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, Temp_object_Variable) == 0x000120, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::Temp_object_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_GetClampedMinSize_ReturnValue) == 0x000128, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_GetClampedMinSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000130, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_GetClampedMinSize_ReturnValue_1) == 0x000138, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_GetClampedMinSize_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_RandomFloatInRange_ReturnValue_3) == 0x000140, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_RandomFloatInRange_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_Divide_DoubleDouble_ReturnValue_2) == 0x000148, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_Divide_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_RandomizePositiveNegativeFloat_ReturnValue) == 0x000150, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_RandomizePositiveNegativeFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_RandomFloatInRange_ReturnValue_4) == 0x000158, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_RandomFloatInRange_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_RandomizePositiveNegativeFloat_ReturnValue_1) == 0x000160, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_RandomizePositiveNegativeFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_MakeVector2D_ReturnValue) == 0x000168, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_Divide_DoubleDouble_ReturnValue_3) == 0x000178, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_Divide_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_GreaterEqual_DoubleDouble_ReturnValue) == 0x000180, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_GreaterEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_Select_Default) == 0x000188, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_BreakVector2D_X) == 0x000190, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_BreakVector2D_Y) == 0x000198, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x0001A0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_MapRangeClamped_ReturnValue_1) == 0x0001A8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_MapRangeClamped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_BreakVector2D_X_1) == 0x0001B0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_BreakVector2D_Y_1) == 0x0001B8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, Temp_bool_IsClosed_Variable_1) == 0x0001C0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::Temp_bool_IsClosed_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x0001C8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_MapRangeClamped_ReturnValue_2) == 0x0001D0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_MapRangeClamped_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_MakeVector2D_ReturnValue_1) == 0x0001D8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, Temp_bool_Variable_1) == 0x0001E8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, K2Node_Select_Default_1) == 0x0001F0, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_MapRangeClamped_ReturnValue_3) == 0x0001F8, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_MapRangeClamped_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_Divide_DoubleDouble_A_ImplicitCast) == 0x000200, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_Divide_DoubleDouble_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_ExecuteUbergraph_WB_Particle, CallFunc_SetRenderTransformAngle_Angle_ImplicitCast) == 0x000208, "Member 'WB_Particle_C_ExecuteUbergraph_WB_Particle::CallFunc_SetRenderTransformAngle_Angle_ImplicitCast' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.GetClampedMinSize
// 0x0030 (0x0030 - 0x0000)
struct WB_Particle_C_GetClampedMinSize final
{
public:
	double                                        Multiplicator;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMaxSize_ReturnValue;                   // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMinSize_ReturnValue;                   // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_GetClampedMinSize) == 0x000008, "Wrong alignment on WB_Particle_C_GetClampedMinSize");
static_assert(sizeof(WB_Particle_C_GetClampedMinSize) == 0x000030, "Wrong size on WB_Particle_C_GetClampedMinSize");
static_assert(offsetof(WB_Particle_C_GetClampedMinSize, Multiplicator) == 0x000000, "Member 'WB_Particle_C_GetClampedMinSize::Multiplicator' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetClampedMinSize, ReturnValue) == 0x000008, "Member 'WB_Particle_C_GetClampedMinSize::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetClampedMinSize, CallFunc_GetMaxSize_ReturnValue) == 0x000010, "Member 'WB_Particle_C_GetClampedMinSize::CallFunc_GetMaxSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetClampedMinSize, CallFunc_GetMinSize_ReturnValue) == 0x000018, "Member 'WB_Particle_C_GetClampedMinSize::CallFunc_GetMinSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetClampedMinSize, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000020, "Member 'WB_Particle_C_GetClampedMinSize::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetClampedMinSize, CallFunc_FClamp_ReturnValue) == 0x000028, "Member 'WB_Particle_C_GetClampedMinSize::CallFunc_FClamp_ReturnValue' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.GetMaxSize
// 0x0030 (0x0030 - 0x0000)
struct WB_Particle_C_GetMaxSize final
{
public:
	double                                        ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue;       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_GetMaxSize) == 0x000008, "Wrong alignment on WB_Particle_C_GetMaxSize");
static_assert(sizeof(WB_Particle_C_GetMaxSize) == 0x000030, "Wrong size on WB_Particle_C_GetMaxSize");
static_assert(offsetof(WB_Particle_C_GetMaxSize, ReturnValue) == 0x000000, "Member 'WB_Particle_C_GetMaxSize::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMaxSize, Temp_bool_Variable) == 0x000008, "Member 'WB_Particle_C_GetMaxSize::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMaxSize, CallFunc_BreakVector2D_X) == 0x000010, "Member 'WB_Particle_C_GetMaxSize::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMaxSize, CallFunc_BreakVector2D_Y) == 0x000018, "Member 'WB_Particle_C_GetMaxSize::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMaxSize, CallFunc_LessEqual_DoubleDouble_ReturnValue) == 0x000020, "Member 'WB_Particle_C_GetMaxSize::CallFunc_LessEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMaxSize, K2Node_Select_Default) == 0x000028, "Member 'WB_Particle_C_GetMaxSize::K2Node_Select_Default' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.GetMinSize
// 0x0030 (0x0030 - 0x0000)
struct WB_Particle_C_GetMinSize final
{
public:
	double                                        ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue;       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_GetMinSize) == 0x000008, "Wrong alignment on WB_Particle_C_GetMinSize");
static_assert(sizeof(WB_Particle_C_GetMinSize) == 0x000030, "Wrong size on WB_Particle_C_GetMinSize");
static_assert(offsetof(WB_Particle_C_GetMinSize, ReturnValue) == 0x000000, "Member 'WB_Particle_C_GetMinSize::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMinSize, Temp_bool_Variable) == 0x000008, "Member 'WB_Particle_C_GetMinSize::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMinSize, CallFunc_BreakVector2D_X) == 0x000010, "Member 'WB_Particle_C_GetMinSize::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMinSize, CallFunc_BreakVector2D_Y) == 0x000018, "Member 'WB_Particle_C_GetMinSize::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMinSize, CallFunc_LessEqual_DoubleDouble_ReturnValue) == 0x000020, "Member 'WB_Particle_C_GetMinSize::CallFunc_LessEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_GetMinSize, K2Node_Select_Default) == 0x000028, "Member 'WB_Particle_C_GetMinSize::K2Node_Select_Default' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.GetTranslation
// 0x0010 (0x0010 - 0x0000)
struct WB_Particle_C_GetTranslation final
{
public:
	struct FVector2D                              Translation_0;                                     // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_GetTranslation) == 0x000008, "Wrong alignment on WB_Particle_C_GetTranslation");
static_assert(sizeof(WB_Particle_C_GetTranslation) == 0x000010, "Wrong size on WB_Particle_C_GetTranslation");
static_assert(offsetof(WB_Particle_C_GetTranslation, Translation_0) == 0x000000, "Member 'WB_Particle_C_GetTranslation::Translation_0' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.IsTriggerMethodAlways
// 0x0004 (0x0004 - 0x0000)
struct WB_Particle_C_IsTriggerMethodAlways final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_IsTriggerMethodAlways) == 0x000001, "Wrong alignment on WB_Particle_C_IsTriggerMethodAlways");
static_assert(sizeof(WB_Particle_C_IsTriggerMethodAlways) == 0x000004, "Wrong size on WB_Particle_C_IsTriggerMethodAlways");
static_assert(offsetof(WB_Particle_C_IsTriggerMethodAlways, ReturnValue) == 0x000000, "Member 'WB_Particle_C_IsTriggerMethodAlways::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_IsTriggerMethodAlways, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000001, "Member 'WB_Particle_C_IsTriggerMethodAlways::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_IsTriggerMethodAlways, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000002, "Member 'WB_Particle_C_IsTriggerMethodAlways::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_IsTriggerMethodAlways, CallFunc_BooleanOR_ReturnValue) == 0x000003, "Member 'WB_Particle_C_IsTriggerMethodAlways::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.SetAngle
// 0x0010 (0x0010 - 0x0000)
struct WB_Particle_C_SetAngle final
{
public:
	double                                        Angle_0;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetRenderTransformAngle_Angle_ImplicitCast; // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_SetAngle) == 0x000008, "Wrong alignment on WB_Particle_C_SetAngle");
static_assert(sizeof(WB_Particle_C_SetAngle) == 0x000010, "Wrong size on WB_Particle_C_SetAngle");
static_assert(offsetof(WB_Particle_C_SetAngle, Angle_0) == 0x000000, "Member 'WB_Particle_C_SetAngle::Angle_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_SetAngle, CallFunc_SetRenderTransformAngle_Angle_ImplicitCast) == 0x000008, "Member 'WB_Particle_C_SetAngle::CallFunc_SetRenderTransformAngle_Angle_ImplicitCast' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.SetScale
// 0x0018 (0x0018 - 0x0000)
struct WB_Particle_C_SetScale final
{
public:
	double                                        Scale_0;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0008(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_SetScale) == 0x000008, "Wrong alignment on WB_Particle_C_SetScale");
static_assert(sizeof(WB_Particle_C_SetScale) == 0x000018, "Wrong size on WB_Particle_C_SetScale");
static_assert(offsetof(WB_Particle_C_SetScale, Scale_0) == 0x000000, "Member 'WB_Particle_C_SetScale::Scale_0' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_SetScale, CallFunc_MakeVector2D_ReturnValue) == 0x000008, "Member 'WB_Particle_C_SetScale::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.SetScaleV2D
// 0x0010 (0x0010 - 0x0000)
struct WB_Particle_C_SetScaleV2D final
{
public:
	struct FVector2D                              Scale_0;                                           // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_SetScaleV2D) == 0x000008, "Wrong alignment on WB_Particle_C_SetScaleV2D");
static_assert(sizeof(WB_Particle_C_SetScaleV2D) == 0x000010, "Wrong size on WB_Particle_C_SetScaleV2D");
static_assert(offsetof(WB_Particle_C_SetScaleV2D, Scale_0) == 0x000000, "Member 'WB_Particle_C_SetScaleV2D::Scale_0' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.SetTranslation
// 0x0010 (0x0010 - 0x0000)
struct WB_Particle_C_SetTranslation final
{
public:
	struct FVector2D                              Translation_0;                                     // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_SetTranslation) == 0x000008, "Wrong alignment on WB_Particle_C_SetTranslation");
static_assert(sizeof(WB_Particle_C_SetTranslation) == 0x000010, "Wrong size on WB_Particle_C_SetTranslation");
static_assert(offsetof(WB_Particle_C_SetTranslation, Translation_0) == 0x000000, "Member 'WB_Particle_C_SetTranslation::Translation_0' has a wrong offset!");

// Function WB_Particle.WB_Particle_C.Tick
// 0x003C (0x003C - 0x0000)
struct WB_Particle_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Particle_C_Tick) == 0x000004, "Wrong alignment on WB_Particle_C_Tick");
static_assert(sizeof(WB_Particle_C_Tick) == 0x00003C, "Wrong size on WB_Particle_C_Tick");
static_assert(offsetof(WB_Particle_C_Tick, MyGeometry) == 0x000000, "Member 'WB_Particle_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Particle_C_Tick, InDeltaTime) == 0x000038, "Member 'WB_Particle_C_Tick::InDeltaTime' has a wrong offset!");

}

