﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_MovementSpeed

#include "Basic.hpp"

#include "Zombie_MovementSpeed_classes.hpp"
#include "Zombie_MovementSpeed_parameters.hpp"


namespace SDK
{

// Function Zombie_MovementSpeed.Zombie_MovementSpeed_C.ExecuteUbergraph_Zombie_MovementSpeed
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UZombie_MovementSpeed_C::ExecuteUbergraph_Zombie_MovementSpeed(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Zombie_MovementSpeed_C", "ExecuteUbergraph_Zombie_MovementSpeed");

	Params::Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Zombie_MovementSpeed.Zombie_MovementSpeed_C.ReceiveActivationAI
// (Event, Protected, BlueprintEvent)
// Parameters:
// class AAIController*                    OwnerController                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class APawn*                            ControlledPawn                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UZombie_MovementSpeed_C::ReceiveActivationAI(class AAIController* OwnerController, class APawn* ControlledPawn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Zombie_MovementSpeed_C", "ReceiveActivationAI");

	Params::Zombie_MovementSpeed_C_ReceiveActivationAI Parms{};

	Parms.OwnerController = OwnerController;
	Parms.ControlledPawn = ControlledPawn;

	UObject::ProcessEvent(Func, &Parms);
}

}

