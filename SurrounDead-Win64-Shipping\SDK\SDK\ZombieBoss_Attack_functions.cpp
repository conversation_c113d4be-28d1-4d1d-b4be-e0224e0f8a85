﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: ZombieBoss_Attack

#include "Basic.hpp"

#include "ZombieBoss_Attack_classes.hpp"
#include "ZombieBoss_Attack_parameters.hpp"


namespace SDK
{

// Function ZombieBoss_Attack.ZombieBoss_Attack_C.ExecuteUbergraph_ZombieBoss_Attack
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UZombieBoss_Attack_C::ExecuteUbergraph_ZombieBoss_Attack(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZombieBoss_Attack_C", "ExecuteUbergraph_ZombieBoss_Attack");

	Params::ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function ZombieBoss_Attack.ZombieBoss_Attack_C.ReceiveExecuteAI
// (Event, Protected, BlueprintEvent)
// Parameters:
// class AAIController*                    OwnerController                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class APawn*                            ControlledPawn                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UZombieBoss_Attack_C::ReceiveExecuteAI(class AAIController* OwnerController, class APawn* ControlledPawn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZombieBoss_Attack_C", "ReceiveExecuteAI");

	Params::ZombieBoss_Attack_C_ReceiveExecuteAI Parms{};

	Parms.OwnerController = OwnerController;
	Parms.ControlledPawn = ControlledPawn;

	UObject::ProcessEvent(Func, &Parms);
}

}

