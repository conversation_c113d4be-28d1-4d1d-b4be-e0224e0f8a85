﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Particle

#include "Basic.hpp"

#include "WB_Particle_classes.hpp"
#include "WB_Particle_parameters.hpp"


namespace SDK
{

// Function WB_Particle.WB_Particle_C.Anim_Fade
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  PlaybackSpeed_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Particle_C::Anim_Fade(double PlaybackSpeed_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "Anim_Fade");

	Params::WB_Particle_C_Anim_Fade Parms{};

	Parms.PlaybackSpeed_0 = PlaybackSpeed_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Particle.WB_Particle_C.CountValue
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Time                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Particle_C::CountValue(double Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "CountValue");

	Params::WB_Particle_C_CountValue Parms{};

	Parms.Time = Time;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Particle.WB_Particle_C.CreateParticle
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          Image                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  PlaybackSpeed_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Scale_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Translation_0                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Shear_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Angle_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Size_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// ETriggerMethod                          TriggerMethod_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bIsDesignTime                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  ParticleSpread_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    UseParticleRotation_0                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Particle_C::CreateParticle(class UObject* Image, double PlaybackSpeed_0, const struct FLinearColor& Color, const struct FVector2D& Scale_0, const struct FVector2D& Translation_0, const struct FVector2D& Shear_0, double Angle_0, const struct FVector2D& Size_0, ETriggerMethod TriggerMethod_0, bool bIsDesignTime, double ParticleSpread_0, bool UseParticleRotation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "CreateParticle");

	Params::WB_Particle_C_CreateParticle Parms{};

	Parms.Image = Image;
	Parms.PlaybackSpeed_0 = PlaybackSpeed_0;
	Parms.Color = std::move(Color);
	Parms.Scale_0 = std::move(Scale_0);
	Parms.Translation_0 = std::move(Translation_0);
	Parms.Shear_0 = std::move(Shear_0);
	Parms.Angle_0 = Angle_0;
	Parms.Size_0 = std::move(Size_0);
	Parms.TriggerMethod_0 = TriggerMethod_0;
	Parms.bIsDesignTime = bIsDesignTime;
	Parms.ParticleSpread_0 = ParticleSpread_0;
	Parms.UseParticleRotation_0 = UseParticleRotation_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Particle.WB_Particle_C.ExecuteUbergraph_WB_Particle
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Particle_C::ExecuteUbergraph_WB_Particle(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "ExecuteUbergraph_WB_Particle");

	Params::WB_Particle_C_ExecuteUbergraph_WB_Particle Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Particle.WB_Particle_C.GetClampedMinSize
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Multiplicator                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Particle_C::GetClampedMinSize(double Multiplicator)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "GetClampedMinSize");

	Params::WB_Particle_C_GetClampedMinSize Parms{};

	Parms.Multiplicator = Multiplicator;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Particle.WB_Particle_C.GetMaxSize
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Particle_C::GetMaxSize()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "GetMaxSize");

	Params::WB_Particle_C_GetMaxSize Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Particle.WB_Particle_C.GetMinSize
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Particle_C::GetMinSize()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "GetMinSize");

	Params::WB_Particle_C_GetMinSize Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Particle.WB_Particle_C.GetTranslation
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector2D*                       Translation_0                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Particle_C::GetTranslation(struct FVector2D* Translation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "GetTranslation");

	Params::WB_Particle_C_GetTranslation Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Translation_0 != nullptr)
		*Translation_0 = std::move(Parms.Translation_0);
}


// Function WB_Particle.WB_Particle_C.IsTriggerMethodAlways
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Particle_C::IsTriggerMethodAlways()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "IsTriggerMethodAlways");

	Params::WB_Particle_C_IsTriggerMethodAlways Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Particle.WB_Particle_C.OnAnimationFinished_Event_0
// (BlueprintCallable, BlueprintEvent)

void UWB_Particle_C::OnAnimationFinished_Event_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "OnAnimationFinished_Event_0");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Particle.WB_Particle_C.SetAngle
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Angle_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Particle_C::SetAngle(double Angle_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "SetAngle");

	Params::WB_Particle_C_SetAngle Parms{};

	Parms.Angle_0 = Angle_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Particle.WB_Particle_C.SetParticleInfo
// (BlueprintCallable, BlueprintEvent)

void UWB_Particle_C::SetParticleInfo()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "SetParticleInfo");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Particle.WB_Particle_C.SetScale
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Scale_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Particle_C::SetScale(double Scale_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "SetScale");

	Params::WB_Particle_C_SetScale Parms{};

	Parms.Scale_0 = Scale_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Particle.WB_Particle_C.SetScaleV2D
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 Scale_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Particle_C::SetScaleV2D(const struct FVector2D& Scale_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "SetScaleV2D");

	Params::WB_Particle_C_SetScaleV2D Parms{};

	Parms.Scale_0 = std::move(Scale_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Particle.WB_Particle_C.SetTranslation
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 Translation_0                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Particle_C::SetTranslation(const struct FVector2D& Translation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "SetTranslation");

	Params::WB_Particle_C_SetTranslation Parms{};

	Parms.Translation_0 = std::move(Translation_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Particle.WB_Particle_C.StartTrigger
// (BlueprintCallable, BlueprintEvent)

void UWB_Particle_C::StartTrigger()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "StartTrigger");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Particle.WB_Particle_C.StopTrigger
// (BlueprintCallable, BlueprintEvent)

void UWB_Particle_C::StopTrigger()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "StopTrigger");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Particle.WB_Particle_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Particle_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "Tick");

	Params::WB_Particle_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Particle.WB_Particle_C.TriggerParticle
// (BlueprintCallable, BlueprintEvent)

void UWB_Particle_C::TriggerParticle()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Particle_C", "TriggerParticle");

	UObject::ProcessEvent(Func, nullptr);
}

}

