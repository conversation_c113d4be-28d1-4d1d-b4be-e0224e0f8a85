﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UltraDynamicWeather_Functions

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "UDS_Season_structs.hpp"
#include "UDS_Weather_Display_Names_structs.hpp"
#include "UDS_Temperature_Sample_Location_structs.hpp"
#include "UDS_TemperatureType_structs.hpp"


namespace SDK::Params
{

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Change to Random Weather Variation · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶 final
{
public:
	double                                        Time_to_Transition_to_Random_Weather__Seconds_;    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	class UObject*                                __WorldContext;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶, Time_to_Transition_to_Random_Weather__Seconds_) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶::Time_to_Transition_to_Random_Weather__Seconds_' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶, __WorldContext) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Change Weather · 𝖴𝖣𝖶
// 0x0028 (0x0028 - 0x0000)
struct UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶 final
{
public:
	class UUDS_Weather_Settings_C*                New_Weather_Type;                                  // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Time_To_Transition_To_New_Weather__Seconds_;       // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UObject*                                __WorldContext;                                    // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶) == 0x000028, "Wrong size on UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶, New_Weather_Type) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶::New_Weather_Type' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶, Time_To_Transition_To_New_Weather__Seconds_) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶::Time_To_Transition_To_New_Weather__Seconds_' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶, __WorldContext) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000020, "Member 'UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Change Wind Direction · 𝖴𝖣𝖶
// 0x0028 (0x0028 - 0x0000)
struct UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶 final
{
public:
	double                                        New_Wind_Direction;                                // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Change_Duration;                                   // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UObject*                                __WorldContext;                                    // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶) == 0x000028, "Wrong size on UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶, New_Wind_Direction) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶::New_Wind_Direction' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶, Change_Duration) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶::Change_Duration' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶, __WorldContext) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000020, "Member 'UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Flash Lightning · 𝖴𝖣𝖶
// 0x0058 (0x0058 - 0x0000)
struct UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶 final
{
public:
	bool                                          Use_Custom_Lightning_Location;                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Custom_Lightning_Location;                         // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Custom_Target_Location;                            // 0x0020(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Lightning_Bolt_Seed;                               // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3C[0x4];                                       // 0x003C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                __WorldContext;                                    // 0x0040(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0048(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶) == 0x000058, "Wrong size on UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶, Use_Custom_Lightning_Location) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶::Use_Custom_Lightning_Location' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶, Custom_Lightning_Location) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶::Custom_Lightning_Location' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶, Custom_Target_Location) == 0x000020, "Member 'UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶::Custom_Target_Location' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶, Lightning_Bolt_Seed) == 0x000038, "Member 'UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶::Lightning_Bolt_Seed' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶, __WorldContext) == 0x000040, "Member 'UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000048, "Member 'UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000050, "Member 'UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Cloud Coverage · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Coverage;                                    // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶, Cloud_Coverage) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶::Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Current Temperature · 𝖴𝖣𝖶
// 0x0050 (0x0050 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶 final
{
public:
	EUDS_Temperature_Sample_Location              Sample_Location;                                   // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Custom_Sample_Location;                            // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_TemperatureType                          Scale;                                             // 0x0020(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                __WorldContext;                                    // 0x0028(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Temperature;                                       // 0x0030(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0038(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Get_Current_Temperature_Output;           // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶) == 0x000050, "Wrong size on UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶, Sample_Location) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶::Sample_Location' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶, Custom_Sample_Location) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶::Custom_Sample_Location' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶, Scale) == 0x000020, "Member 'UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶::Scale' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶, __WorldContext) == 0x000028, "Member 'UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶, Temperature) == 0x000030, "Member 'UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶::Temperature' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000038, "Member 'UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000040, "Member 'UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶, CallFunc_Get_Current_Temperature_Output) == 0x000048, "Member 'UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶::CallFunc_Get_Current_Temperature_Output' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Display Name for Current Weather · 𝖴𝖣𝖶
// 0x0048 (0x0048 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class FString                                 As_String;                                         // 0x0008(0x0010)(Parm, OutParm, ZeroConstructor, HasGetValueTypeHash)
	EUDS_Weather_Display_Names                    As_Enumerator;                                     // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Get_Display_Name_for_Current_Weather_As_String; // 0x0030(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	EUDS_Weather_Display_Names                    CallFunc_Get_Display_Name_for_Current_Weather_As_Enumerator; // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶) == 0x000048, "Wrong size on UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶, As_String) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶::As_String' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶, As_Enumerator) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶::As_Enumerator' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000020, "Member 'UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000028, "Member 'UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶, CallFunc_Get_Display_Name_for_Current_Weather_As_String) == 0x000030, "Member 'UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶::CallFunc_Get_Display_Name_for_Current_Weather_As_String' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶, CallFunc_Get_Display_Name_for_Current_Weather_As_Enumerator) == 0x000040, "Member 'UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶::CallFunc_Get_Display_Name_for_Current_Weather_As_Enumerator' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Sand Amount · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Dust;                                              // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶, Dust) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶::Dust' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Fog · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Fog;                                               // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶, Fog) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶::Fog' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Local Weather State · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Local_Weather_State;                               // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶, Local_Weather_State) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶::Local_Weather_State' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Material Dust Coverage · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Material_Dust_Coverage;                            // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶, Material_Dust_Coverage) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶::Material_Dust_Coverage' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Material Snow Coverage · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Material_Snow_Coverage;                            // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶, Material_Snow_Coverage) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶::Material_Snow_Coverage' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Material Wetness · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Material_Wetness;                                  // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶, Material_Wetness) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶::Material_Wetness' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Rain Amount · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Rain;                                              // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶, Rain) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶::Rain' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Season · 𝖴𝖣𝖶
// 0x0038 (0x0038 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Season;                                            // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_Season                                   Season_Enum;                                       // 0x0010(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Get_Season_Season;                        // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_Season                                   CallFunc_Get_Season_Season_Enum;                   // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶) == 0x000038, "Wrong size on UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶, Season) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶::Season' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶, Season_Enum) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶::Season_Enum' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000020, "Member 'UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶, CallFunc_Get_Season_Season) == 0x000028, "Member 'UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶::CallFunc_Get_Season_Season' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶, CallFunc_Get_Season_Season_Enum) == 0x000030, "Member 'UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶::CallFunc_Get_Season_Season_Enum' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Snow Amount · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Snow;                                              // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶, Snow) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶::Snow' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Lightning · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Lightning;                                         // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶, Lightning) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶::Lightning' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Ultra Dynamic Weather
// 0x0028 (0x0028 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               UDW;                                               // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Valid;                                             // 0x0010(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Weather_C*               CallFunc_GetActorOfClass_ReturnValue;              // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather) == 0x000028, "Wrong size on UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather, UDW) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather::UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather, Valid) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather::Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather, CallFunc_GetActorOfClass_ReturnValue) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather::CallFunc_GetActorOfClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather, CallFunc_IsValid_ReturnValue) == 0x000020, "Member 'UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Wind Direction Vector · 𝖴𝖣𝖶
// 0x0048 (0x0048 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Wind_Direction;                                    // 0x0008(0x0018)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Get_Normalized_Wind_Direction_Wind_Vector; // 0x0030(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶) == 0x000048, "Wrong size on UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶, Wind_Direction) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶::Wind_Direction' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000020, "Member 'UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000028, "Member 'UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶, CallFunc_Get_Normalized_Wind_Direction_Wind_Vector) == 0x000030, "Member 'UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶::CallFunc_Get_Normalized_Wind_Direction_Wind_Vector' has a wrong offset!");

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Wind Intensity · 𝖴𝖣𝖶
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Wind_Intensity;                                    // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶) == 0x000008, "Wrong alignment on UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶");
static_assert(sizeof(UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶) == 0x000020, "Wrong size on UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶, __WorldContext) == 0x000000, "Member 'UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶, Wind_Intensity) == 0x000008, "Member 'UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶::Wind_Intensity' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000010, "Member 'UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000018, "Member 'UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

}

