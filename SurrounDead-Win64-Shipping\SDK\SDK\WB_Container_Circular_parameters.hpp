﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Container_Circular

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "EProgressMethod_structs.hpp"
#include "EGradientTypes_structs.hpp"
#include "EMarqueeMask_structs.hpp"


namespace SDK::Params
{

// Function WB_Container_Circular.WB_Container_Circular_C.ExecuteUbergraph_WB_Container_Circular
// 0x02B0 (0x02B0 - 0x0000)
struct WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoD<PERSON>ructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             Temp_object_Variable;                              // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_2;                              // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x3];                                       // 0x0021(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_3;                              // 0x002C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2D[0x3];                                       // 0x002D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_1;                              // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Select_Default;                             // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3C[0x4];                                       // 0x003C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Thickness;                      // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_TargetPercent;                  // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_Steps;                          // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_54[0x4];                                       // 0x0054(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Percent;                        // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Spacing;                        // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Size;                           // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Density;                        // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_HardStepBorder;                 // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bIsTargetPercent;               // 0x0080(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_81[0x7];                                       // 0x0081(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_CustomEvent_BackgroundMask;                 // 0x0088(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_Color;                          // 0x0090(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_FillColor_2;                    // 0x00A0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_GradientPower;                  // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bAbsoluteFillMethod;            // 0x00B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B9[0x7];                                       // 0x00B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_1;                           // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressMethod                               K2Node_CustomEvent_ProgressMethod;                 // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x00D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_1;               // 0x00D2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x00D3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsNegativeFillValue_ReturnValue;          // 0x00D4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D5[0x3];                                       // 0x00D5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_FindTargetFillColor_ReturnValue;          // 0x00D8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetAbsoluteTargetPercent_ReturnValue;     // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_FillColor_1;                    // 0x00F0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_FillColor;                      // 0x0100(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTexture*                               Temp_object_Variable_1;                            // 0x0110(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_GradientOpacity;                // 0x0118(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Temp_object_Variable_2;                            // 0x0120(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_1;                     // 0x0128(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EGradientTypes                                K2Node_CustomEvent_GradientType;                   // 0x0129(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseGradient;                   // 0x012A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12B[0x5];                                      // 0x012B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_CustomEvent_Mask;                           // 0x0130(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	EGradientTypes                                Temp_byte_Variable;                                // 0x0138(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseMarquee;                    // 0x0139(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13A[0x2];                                      // 0x013A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_CustomEvent_MarqueeColor;                   // 0x013C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_MarqueeBackgroundColor;         // 0x014C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15C[0x4];                                      // 0x015C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Value;                          // 0x0160(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMarqueeMask                                  K2Node_CustomEvent_MarqueeMask;                    // 0x0168(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_169[0x7];                                      // 0x0169(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_CustomEvent_CustomMask;                     // 0x0170(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Temp_object_Variable_3;                            // 0x0178(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_ProgressChangeColor;            // 0x0180(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_NewColor;                       // 0x0190(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_InterpSpeed;                    // 0x01A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_IsChanging;                     // 0x01A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A9[0x7];                                      // 0x01A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue;         // 0x01B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Temp_object_Variable_4;                            // 0x01B8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Temp_object_Variable_5;                            // 0x01C0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Temp_object_Variable_6;                            // 0x01C8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x01D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D1[0x3];                                      // 0x01D1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_CInterpTo_ReturnValue;                    // 0x01D4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x01E4(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x021C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMarqueeMask                                  Temp_byte_Variable_1;                              // 0x0220(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_221[0x7];                                      // 0x0221(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_Select_Default_2;                           // 0x0228(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0230(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0231(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsProgressMethodInterpolate_ReturnValue;  // 0x0232(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0233(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_2;               // 0x0234(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_235[0x3];                                      // 0x0235(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_SubtractBrightnessValue_ReturnValue;      // 0x0238(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0248(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_249[0x7];                                      // 0x0249(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_Select_Default_3;                           // 0x0250(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue_1;       // 0x0258(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetGradientOpacity_ReturnValue;           // 0x0260(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_2;                     // 0x0268(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_269[0x7];                                      // 0x0269(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default_4;                           // 0x0270(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FInterpTo_ReturnValue;                    // 0x0278(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTexture*                               Temp_object_Variable_7;                            // 0x0280(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture*                               Temp_object_Variable_8;                            // 0x0288(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture*                               Temp_object_Variable_9;                            // 0x0290(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture*                               K2Node_Select_Default_5;                           // 0x0298(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x02A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x02A1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2A2[0x2];                                      // 0x02A2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_CInterpTo_InterpSpeed_ImplicitCast;       // 0x02A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_CInterpTo_DeltaTime_ImplicitCast;         // 0x02A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular) == 0x000008, "Wrong alignment on WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular");
static_assert(sizeof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular) == 0x0002B0, "Wrong size on WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, EntryPoint) == 0x000000, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_Variable) == 0x000004, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_real_Variable) == 0x000008, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_Variable_1) == 0x000010, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable) == 0x000018, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_Variable_2) == 0x000020, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_int_Variable) == 0x000024, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_int_Variable_1) == 0x000028, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_Variable_3) == 0x00002C, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_real_Variable_1) == 0x000030, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_Select_Default) == 0x000038, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_Thickness) == 0x000040, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_Thickness' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_TargetPercent) == 0x000048, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_TargetPercent' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_Steps) == 0x000050, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_Steps' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_Percent) == 0x000058, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_Percent' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_Spacing) == 0x000060, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_Spacing' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_Size) == 0x000068, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_Size' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_Density) == 0x000070, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_Density' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_HardStepBorder) == 0x000078, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_HardStepBorder' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_bIsTargetPercent) == 0x000080, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_bIsTargetPercent' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_BackgroundMask) == 0x000088, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_BackgroundMask' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_Color) == 0x000090, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_Color' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_FillColor_2) == 0x0000A0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_FillColor_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_GradientPower) == 0x0000B0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_GradientPower' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_bAbsoluteFillMethod) == 0x0000B8, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_bAbsoluteFillMethod' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_MapRangeClamped_ReturnValue) == 0x0000C0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_Select_Default_1) == 0x0000C8, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_ProgressMethod) == 0x0000D0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_ProgressMethod' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_Has_Been_Initd_Variable) == 0x0000D1, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_Has_Been_Initd_Variable_1) == 0x0000D2, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_Has_Been_Initd_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_IsClosed_Variable) == 0x0000D3, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_IsNegativeFillValue_ReturnValue) == 0x0000D4, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_IsNegativeFillValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_FindTargetFillColor_ReturnValue) == 0x0000D8, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_FindTargetFillColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_GetAbsoluteTargetPercent_ReturnValue) == 0x0000E8, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_GetAbsoluteTargetPercent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_FillColor_1) == 0x0000F0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_FillColor_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_FillColor) == 0x000100, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_FillColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable_1) == 0x000110, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_GradientOpacity) == 0x000118, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_GradientOpacity' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable_2) == 0x000120, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_IsClosed_Variable_1) == 0x000128, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_IsClosed_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_GradientType) == 0x000129, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_GradientType' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_bUseGradient) == 0x00012A, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_bUseGradient' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_Mask) == 0x000130, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_Mask' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_byte_Variable) == 0x000138, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_bUseMarquee) == 0x000139, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_bUseMarquee' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_MarqueeColor) == 0x00013C, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_MarqueeColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_MarqueeBackgroundColor) == 0x00014C, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_MarqueeBackgroundColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_Value) == 0x000160, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_MarqueeMask) == 0x000168, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_MarqueeMask' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_CustomMask) == 0x000170, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_CustomMask' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable_3) == 0x000178, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_ProgressChangeColor) == 0x000180, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_ProgressChangeColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_NewColor) == 0x000190, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_NewColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_InterpSpeed) == 0x0001A0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_InterpSpeed' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_CustomEvent_IsChanging) == 0x0001A8, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_CustomEvent_IsChanging' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_GetWorldDeltaSeconds_ReturnValue) == 0x0001B0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_GetWorldDeltaSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable_4) == 0x0001B8, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable_5) == 0x0001C0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable_6) == 0x0001C8, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_Event_IsDesignTime) == 0x0001D0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_CInterpTo_ReturnValue) == 0x0001D4, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_CInterpTo_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_Event_MyGeometry) == 0x0001E4, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_Event_InDeltaTime) == 0x00021C, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_byte_Variable_1) == 0x000220, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_Select_Default_2) == 0x000228, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_Select_Default_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_Not_PreBool_ReturnValue) == 0x000230, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_IsValid_ReturnValue) == 0x000231, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_IsProgressMethodInterpolate_ReturnValue) == 0x000232, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_IsProgressMethodInterpolate_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_BooleanAND_ReturnValue) == 0x000233, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_Has_Been_Initd_Variable_2) == 0x000234, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_Has_Been_Initd_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_SubtractBrightnessValue_ReturnValue) == 0x000238, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_SubtractBrightnessValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_IsValid_ReturnValue_1) == 0x000248, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_Select_Default_3) == 0x000250, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_Select_Default_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_GetWorldDeltaSeconds_ReturnValue_1) == 0x000258, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_GetWorldDeltaSeconds_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_GetGradientOpacity_ReturnValue) == 0x000260, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_GetGradientOpacity_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_bool_IsClosed_Variable_2) == 0x000268, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_bool_IsClosed_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_Select_Default_4) == 0x000270, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_Select_Default_4' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_FInterpTo_ReturnValue) == 0x000278, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_FInterpTo_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable_7) == 0x000280, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable_8) == 0x000288, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, Temp_object_Variable_9) == 0x000290, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::Temp_object_Variable_9' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, K2Node_Select_Default_5) == 0x000298, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::K2Node_Select_Default_5' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_Not_PreBool_ReturnValue_1) == 0x0002A0, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_BooleanAND_ReturnValue_1) == 0x0002A1, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_CInterpTo_InterpSpeed_ImplicitCast) == 0x0002A4, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_CInterpTo_InterpSpeed_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular, CallFunc_CInterpTo_DeltaTime_ImplicitCast) == 0x0002A8, "Member 'WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular::CallFunc_CInterpTo_DeltaTime_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.FindTargetFillColor
// 0x0024 (0x0024 - 0x0000)
struct WB_Container_Circular_C_FindTargetFillColor final
{
public:
	struct FLinearColor                           ReturnValue;                                       // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x3];                                       // 0x0011(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_Select_Default;                             // 0x0014(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_FindTargetFillColor) == 0x000004, "Wrong alignment on WB_Container_Circular_C_FindTargetFillColor");
static_assert(sizeof(WB_Container_Circular_C_FindTargetFillColor) == 0x000024, "Wrong size on WB_Container_Circular_C_FindTargetFillColor");
static_assert(offsetof(WB_Container_Circular_C_FindTargetFillColor, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_FindTargetFillColor::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_FindTargetFillColor, Temp_bool_Variable) == 0x000010, "Member 'WB_Container_Circular_C_FindTargetFillColor::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_FindTargetFillColor, K2Node_Select_Default) == 0x000014, "Member 'WB_Container_Circular_C_FindTargetFillColor::K2Node_Select_Default' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.FindTargetProgressBarPosition
// 0x0038 (0x0038 - 0x0000)
struct WB_Container_Circular_C_FindTargetProgressBarPosition final
{
public:
	double                                        Progress;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0010(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetRenderTransformAngle_Angle_ImplicitCast; // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_FindTargetProgressBarPosition) == 0x000008, "Wrong alignment on WB_Container_Circular_C_FindTargetProgressBarPosition");
static_assert(sizeof(WB_Container_Circular_C_FindTargetProgressBarPosition) == 0x000038, "Wrong size on WB_Container_Circular_C_FindTargetProgressBarPosition");
static_assert(offsetof(WB_Container_Circular_C_FindTargetProgressBarPosition, Progress) == 0x000000, "Member 'WB_Container_Circular_C_FindTargetProgressBarPosition::Progress' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_FindTargetProgressBarPosition, CallFunc_MapRangeClamped_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_FindTargetProgressBarPosition::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_FindTargetProgressBarPosition, CallFunc_MakeVector2D_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_FindTargetProgressBarPosition::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_FindTargetProgressBarPosition, CallFunc_MakeVector2D_ReturnValue_1) == 0x000020, "Member 'WB_Container_Circular_C_FindTargetProgressBarPosition::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_FindTargetProgressBarPosition, CallFunc_SetRenderTransformAngle_Angle_ImplicitCast) == 0x000030, "Member 'WB_Container_Circular_C_FindTargetProgressBarPosition::CallFunc_SetRenderTransformAngle_Angle_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.GetAbsoluteTargetPercent
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_GetAbsoluteTargetPercent final
{
public:
	double                                        ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Abs_ReturnValue;                          // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_GetAbsoluteTargetPercent) == 0x000008, "Wrong alignment on WB_Container_Circular_C_GetAbsoluteTargetPercent");
static_assert(sizeof(WB_Container_Circular_C_GetAbsoluteTargetPercent) == 0x000018, "Wrong size on WB_Container_Circular_C_GetAbsoluteTargetPercent");
static_assert(offsetof(WB_Container_Circular_C_GetAbsoluteTargetPercent, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_GetAbsoluteTargetPercent::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetAbsoluteTargetPercent, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_GetAbsoluteTargetPercent::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetAbsoluteTargetPercent, CallFunc_Abs_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_GetAbsoluteTargetPercent::CallFunc_Abs_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.GetCircleMaterial
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_GetCircleMaterial final
{
public:
	class UMaterialInstanceDynamic*               ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetDynamicMaterial_ReturnValue;           // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_GetCircleMaterial) == 0x000008, "Wrong alignment on WB_Container_Circular_C_GetCircleMaterial");
static_assert(sizeof(WB_Container_Circular_C_GetCircleMaterial) == 0x000010, "Wrong size on WB_Container_Circular_C_GetCircleMaterial");
static_assert(offsetof(WB_Container_Circular_C_GetCircleMaterial, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_GetCircleMaterial::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetCircleMaterial, CallFunc_GetDynamicMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_GetCircleMaterial::CallFunc_GetDynamicMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.GetCircleMaterialMarquee
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_GetCircleMaterialMarquee final
{
public:
	class UMaterialInstanceDynamic*               ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetDynamicMaterial_ReturnValue;           // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_GetCircleMaterialMarquee) == 0x000008, "Wrong alignment on WB_Container_Circular_C_GetCircleMaterialMarquee");
static_assert(sizeof(WB_Container_Circular_C_GetCircleMaterialMarquee) == 0x000010, "Wrong size on WB_Container_Circular_C_GetCircleMaterialMarquee");
static_assert(offsetof(WB_Container_Circular_C_GetCircleMaterialMarquee, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_GetCircleMaterialMarquee::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetCircleMaterialMarquee, CallFunc_GetDynamicMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_GetCircleMaterialMarquee::CallFunc_GetDynamicMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.GetCircleMaterialMarqueeBG
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_GetCircleMaterialMarqueeBG final
{
public:
	class UMaterialInstanceDynamic*               ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetDynamicMaterial_ReturnValue;           // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_GetCircleMaterialMarqueeBG) == 0x000008, "Wrong alignment on WB_Container_Circular_C_GetCircleMaterialMarqueeBG");
static_assert(sizeof(WB_Container_Circular_C_GetCircleMaterialMarqueeBG) == 0x000010, "Wrong size on WB_Container_Circular_C_GetCircleMaterialMarqueeBG");
static_assert(offsetof(WB_Container_Circular_C_GetCircleMaterialMarqueeBG, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_GetCircleMaterialMarqueeBG::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetCircleMaterialMarqueeBG, CallFunc_GetDynamicMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_GetCircleMaterialMarqueeBG::CallFunc_GetDynamicMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.GetCircleMaterialTarget
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_GetCircleMaterialTarget final
{
public:
	class UMaterialInstanceDynamic*               ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetDynamicMaterial_ReturnValue;           // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_GetCircleMaterialTarget) == 0x000008, "Wrong alignment on WB_Container_Circular_C_GetCircleMaterialTarget");
static_assert(sizeof(WB_Container_Circular_C_GetCircleMaterialTarget) == 0x000010, "Wrong size on WB_Container_Circular_C_GetCircleMaterialTarget");
static_assert(offsetof(WB_Container_Circular_C_GetCircleMaterialTarget, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_GetCircleMaterialTarget::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetCircleMaterialTarget, CallFunc_GetDynamicMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_GetCircleMaterialTarget::CallFunc_GetDynamicMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.GetFillColor
// 0x0044 (0x0044 - 0x0000)
struct WB_Container_Circular_C_GetFillColor final
{
public:
	struct FLinearColor                           ReturnValue;                                       // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Temp_struct_Variable;                              // 0x0010(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0022(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_23[0x1];                                       // 0x0023(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_K2_GetVectorParameterValue_ReturnValue;   // 0x0024(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_Select_Default;                             // 0x0034(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_GetFillColor) == 0x000004, "Wrong alignment on WB_Container_Circular_C_GetFillColor");
static_assert(sizeof(WB_Container_Circular_C_GetFillColor) == 0x000044, "Wrong size on WB_Container_Circular_C_GetFillColor");
static_assert(offsetof(WB_Container_Circular_C_GetFillColor, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_GetFillColor::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetFillColor, Temp_struct_Variable) == 0x000010, "Member 'WB_Container_Circular_C_GetFillColor::Temp_struct_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetFillColor, Temp_bool_Variable) == 0x000020, "Member 'WB_Container_Circular_C_GetFillColor::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetFillColor, CallFunc_IsValid_ReturnValue) == 0x000021, "Member 'WB_Container_Circular_C_GetFillColor::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetFillColor, CallFunc_IsValid_ReturnValue_1) == 0x000022, "Member 'WB_Container_Circular_C_GetFillColor::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetFillColor, CallFunc_K2_GetVectorParameterValue_ReturnValue) == 0x000024, "Member 'WB_Container_Circular_C_GetFillColor::CallFunc_K2_GetVectorParameterValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetFillColor, K2Node_Select_Default) == 0x000034, "Member 'WB_Container_Circular_C_GetFillColor::K2Node_Select_Default' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.GetGradientOpacity
// 0x0028 (0x0028 - 0x0000)
struct WB_Container_Circular_C_GetGradientOpacity final
{
public:
	double                                        ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13[0x1];                                       // 0x0013(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_K2_GetScalarParameterValue_ReturnValue;   // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Option_1_ImplicitCast;               // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_GetGradientOpacity) == 0x000008, "Wrong alignment on WB_Container_Circular_C_GetGradientOpacity");
static_assert(sizeof(WB_Container_Circular_C_GetGradientOpacity) == 0x000028, "Wrong size on WB_Container_Circular_C_GetGradientOpacity");
static_assert(offsetof(WB_Container_Circular_C_GetGradientOpacity, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_GetGradientOpacity::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetGradientOpacity, Temp_real_Variable) == 0x000008, "Member 'WB_Container_Circular_C_GetGradientOpacity::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetGradientOpacity, Temp_bool_Variable) == 0x000010, "Member 'WB_Container_Circular_C_GetGradientOpacity::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetGradientOpacity, CallFunc_IsValid_ReturnValue) == 0x000011, "Member 'WB_Container_Circular_C_GetGradientOpacity::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetGradientOpacity, CallFunc_IsValid_ReturnValue_1) == 0x000012, "Member 'WB_Container_Circular_C_GetGradientOpacity::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetGradientOpacity, CallFunc_K2_GetScalarParameterValue_ReturnValue) == 0x000014, "Member 'WB_Container_Circular_C_GetGradientOpacity::CallFunc_K2_GetScalarParameterValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetGradientOpacity, K2Node_Select_Default) == 0x000018, "Member 'WB_Container_Circular_C_GetGradientOpacity::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_GetGradientOpacity, K2Node_Select_Option_1_ImplicitCast) == 0x000020, "Member 'WB_Container_Circular_C_GetGradientOpacity::K2Node_Select_Option_1_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.GetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_GetPercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_GetPercent) == 0x000008, "Wrong alignment on WB_Container_Circular_C_GetPercent");
static_assert(sizeof(WB_Container_Circular_C_GetPercent) == 0x000008, "Wrong size on WB_Container_Circular_C_GetPercent");
static_assert(offsetof(WB_Container_Circular_C_GetPercent, Percent) == 0x000000, "Member 'WB_Container_Circular_C_GetPercent::Percent' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.GetTargetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_GetTargetPercent final
{
public:
	double                                        TargetPercent_0;                                   // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_GetTargetPercent) == 0x000008, "Wrong alignment on WB_Container_Circular_C_GetTargetPercent");
static_assert(sizeof(WB_Container_Circular_C_GetTargetPercent) == 0x000008, "Wrong size on WB_Container_Circular_C_GetTargetPercent");
static_assert(offsetof(WB_Container_Circular_C_GetTargetPercent, TargetPercent_0) == 0x000000, "Member 'WB_Container_Circular_C_GetTargetPercent::TargetPercent_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.IsNegativeFillValue
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_IsNegativeFillValue final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_IsNegativeFillValue) == 0x000008, "Wrong alignment on WB_Container_Circular_C_IsNegativeFillValue");
static_assert(sizeof(WB_Container_Circular_C_IsNegativeFillValue) == 0x000018, "Wrong size on WB_Container_Circular_C_IsNegativeFillValue");
static_assert(offsetof(WB_Container_Circular_C_IsNegativeFillValue, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_IsNegativeFillValue::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_IsNegativeFillValue, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_IsNegativeFillValue::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_IsNegativeFillValue, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_IsNegativeFillValue::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.IsProgressMethodInterpolate
// 0x0002 (0x0002 - 0x0000)
struct WB_Container_Circular_C_IsProgressMethodInterpolate final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_IsProgressMethodInterpolate) == 0x000001, "Wrong alignment on WB_Container_Circular_C_IsProgressMethodInterpolate");
static_assert(sizeof(WB_Container_Circular_C_IsProgressMethodInterpolate) == 0x000002, "Wrong size on WB_Container_Circular_C_IsProgressMethodInterpolate");
static_assert(offsetof(WB_Container_Circular_C_IsProgressMethodInterpolate, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_IsProgressMethodInterpolate::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_IsProgressMethodInterpolate, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000001, "Member 'WB_Container_Circular_C_IsProgressMethodInterpolate::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.IsProgressMethodStatic
// 0x0002 (0x0002 - 0x0000)
struct WB_Container_Circular_C_IsProgressMethodStatic final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_IsProgressMethodStatic) == 0x000001, "Wrong alignment on WB_Container_Circular_C_IsProgressMethodStatic");
static_assert(sizeof(WB_Container_Circular_C_IsProgressMethodStatic) == 0x000002, "Wrong size on WB_Container_Circular_C_IsProgressMethodStatic");
static_assert(offsetof(WB_Container_Circular_C_IsProgressMethodStatic, ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_IsProgressMethodStatic::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_IsProgressMethodStatic, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000001, "Member 'WB_Container_Circular_C_IsProgressMethodStatic::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Circular_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_PreConstruct) == 0x000001, "Wrong alignment on WB_Container_Circular_C_PreConstruct");
static_assert(sizeof(WB_Container_Circular_C_PreConstruct) == 0x000001, "Wrong size on WB_Container_Circular_C_PreConstruct");
static_assert(offsetof(WB_Container_Circular_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WB_Container_Circular_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetAbsoluteFillMethod
// 0x0038 (0x0038 - 0x0000)
struct WB_Container_Circular_C_SetAbsoluteFillMethod final
{
public:
	bool                                          bAbsoluteFillMethod_0;                             // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetAbsoluteFillMethod) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetAbsoluteFillMethod");
static_assert(sizeof(WB_Container_Circular_C_SetAbsoluteFillMethod) == 0x000038, "Wrong size on WB_Container_Circular_C_SetAbsoluteFillMethod");
static_assert(offsetof(WB_Container_Circular_C_SetAbsoluteFillMethod, bAbsoluteFillMethod_0) == 0x000000, "Member 'WB_Container_Circular_C_SetAbsoluteFillMethod::bAbsoluteFillMethod_0' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetAbsoluteFillMethod, Temp_real_Variable) == 0x000008, "Member 'WB_Container_Circular_C_SetAbsoluteFillMethod::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetAbsoluteFillMethod, Temp_real_Variable_1) == 0x000010, "Member 'WB_Container_Circular_C_SetAbsoluteFillMethod::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetAbsoluteFillMethod, Temp_bool_Variable) == 0x000018, "Member 'WB_Container_Circular_C_SetAbsoluteFillMethod::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetAbsoluteFillMethod, CallFunc_GetCircleMaterial_ReturnValue) == 0x000020, "Member 'WB_Container_Circular_C_SetAbsoluteFillMethod::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetAbsoluteFillMethod, K2Node_Select_Default) == 0x000028, "Member 'WB_Container_Circular_C_SetAbsoluteFillMethod::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetAbsoluteFillMethod, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000030, "Member 'WB_Container_Circular_C_SetAbsoluteFillMethod::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetBackgroundColor
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetBackgroundColor final
{
public:
	class UTexture2D*                             BackgroundMask_0;                                  // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Color;                                             // 0x0008(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetBackgroundColor) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetBackgroundColor");
static_assert(sizeof(WB_Container_Circular_C_SetBackgroundColor) == 0x000018, "Wrong size on WB_Container_Circular_C_SetBackgroundColor");
static_assert(offsetof(WB_Container_Circular_C_SetBackgroundColor, BackgroundMask_0) == 0x000000, "Member 'WB_Container_Circular_C_SetBackgroundColor::BackgroundMask_0' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetBackgroundColor, Color) == 0x000008, "Member 'WB_Container_Circular_C_SetBackgroundColor::Color' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetBackgroundColorMaskParam
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_SetBackgroundColorMaskParam final
{
public:
	class UTexture*                               Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetBackgroundColorMaskParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetBackgroundColorMaskParam");
static_assert(sizeof(WB_Container_Circular_C_SetBackgroundColorMaskParam) == 0x000010, "Wrong size on WB_Container_Circular_C_SetBackgroundColorMaskParam");
static_assert(offsetof(WB_Container_Circular_C_SetBackgroundColorMaskParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetBackgroundColorMaskParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetBackgroundColorMaskParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetBackgroundColorMaskParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetDensity
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetDensity final
{
public:
	double                                        Density_0;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetDensity) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetDensity");
static_assert(sizeof(WB_Container_Circular_C_SetDensity) == 0x000008, "Wrong size on WB_Container_Circular_C_SetDensity");
static_assert(offsetof(WB_Container_Circular_C_SetDensity, Density_0) == 0x000000, "Member 'WB_Container_Circular_C_SetDensity::Density_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetDensityParam
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetDensityParam final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetDensityParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetDensityParam");
static_assert(sizeof(WB_Container_Circular_C_SetDensityParam) == 0x000018, "Wrong size on WB_Container_Circular_C_SetDensityParam");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetDensityParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetDensityParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParam, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000010, "Member 'WB_Container_Circular_C_SetDensityParam::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetDensityParamMarquee
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Circular_C_SetDensityParamMarquee final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarqueeBG_ReturnValue;   // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarquee_ReturnValue;     // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_1; // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetDensityParamMarquee) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetDensityParamMarquee");
static_assert(sizeof(WB_Container_Circular_C_SetDensityParamMarquee) == 0x000020, "Wrong size on WB_Container_Circular_C_SetDensityParamMarquee");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParamMarquee, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetDensityParamMarquee::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParamMarquee, CallFunc_GetCircleMaterialMarqueeBG_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetDensityParamMarquee::CallFunc_GetCircleMaterialMarqueeBG_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParamMarquee, CallFunc_GetCircleMaterialMarquee_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetDensityParamMarquee::CallFunc_GetCircleMaterialMarquee_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000018, "Member 'WB_Container_Circular_C_SetDensityParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast_1) == 0x00001C, "Member 'WB_Container_Circular_C_SetDensityParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast_1' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetDensityParamTarget
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetDensityParamTarget final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialTarget_ReturnValue;      // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetDensityParamTarget) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetDensityParamTarget");
static_assert(sizeof(WB_Container_Circular_C_SetDensityParamTarget) == 0x000018, "Wrong size on WB_Container_Circular_C_SetDensityParamTarget");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParamTarget, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetDensityParamTarget::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParamTarget, CallFunc_GetCircleMaterialTarget_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetDensityParamTarget::CallFunc_GetCircleMaterialTarget_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetDensityParamTarget, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000010, "Member 'WB_Container_Circular_C_SetDensityParamTarget::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetEmptyColorParam
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetEmptyColorParam final
{
public:
	struct FLinearColor                           Value;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetEmptyColorParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetEmptyColorParam");
static_assert(sizeof(WB_Container_Circular_C_SetEmptyColorParam) == 0x000018, "Wrong size on WB_Container_Circular_C_SetEmptyColorParam");
static_assert(offsetof(WB_Container_Circular_C_SetEmptyColorParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetEmptyColorParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetEmptyColorParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetEmptyColorParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColor
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetFillColor final
{
public:
	struct FLinearColor                           FillColor_0;                                       // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        GradientPower;                                     // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetFillColor) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetFillColor");
static_assert(sizeof(WB_Container_Circular_C_SetFillColor) == 0x000018, "Wrong size on WB_Container_Circular_C_SetFillColor");
static_assert(offsetof(WB_Container_Circular_C_SetFillColor, FillColor_0) == 0x000000, "Member 'WB_Container_Circular_C_SetFillColor::FillColor_0' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetFillColor, GradientPower) == 0x000010, "Member 'WB_Container_Circular_C_SetFillColor::GradientPower' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColorMask
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetFillColorMask final
{
public:
	class UTexture2D*                             Mask;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetFillColorMask) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetFillColorMask");
static_assert(sizeof(WB_Container_Circular_C_SetFillColorMask) == 0x000008, "Wrong size on WB_Container_Circular_C_SetFillColorMask");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorMask, Mask) == 0x000000, "Member 'WB_Container_Circular_C_SetFillColorMask::Mask' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColorMaskParam
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_SetFillColorMaskParam final
{
public:
	class UTexture*                               Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetFillColorMaskParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetFillColorMaskParam");
static_assert(sizeof(WB_Container_Circular_C_SetFillColorMaskParam) == 0x000010, "Wrong size on WB_Container_Circular_C_SetFillColorMaskParam");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorMaskParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetFillColorMaskParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorMaskParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetFillColorMaskParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColorParam
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetFillColorParam final
{
public:
	struct FLinearColor                           Value;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetFillColorParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetFillColorParam");
static_assert(sizeof(WB_Container_Circular_C_SetFillColorParam) == 0x000018, "Wrong size on WB_Container_Circular_C_SetFillColorParam");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetFillColorParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetFillColorParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColorParamTarget
// 0x0028 (0x0028 - 0x0000)
struct WB_Container_Circular_C_SetFillColorParamTarget final
{
public:
	struct FLinearColor                           Value;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialTarget_ReturnValue;      // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_R;                             // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_G;                             // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_B;                             // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_A;                             // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetFillColorParamTarget) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetFillColorParamTarget");
static_assert(sizeof(WB_Container_Circular_C_SetFillColorParamTarget) == 0x000028, "Wrong size on WB_Container_Circular_C_SetFillColorParamTarget");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorParamTarget, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetFillColorParamTarget::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorParamTarget, CallFunc_GetCircleMaterialTarget_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetFillColorParamTarget::CallFunc_GetCircleMaterialTarget_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorParamTarget, CallFunc_BreakColor_R) == 0x000018, "Member 'WB_Container_Circular_C_SetFillColorParamTarget::CallFunc_BreakColor_R' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorParamTarget, CallFunc_BreakColor_G) == 0x00001C, "Member 'WB_Container_Circular_C_SetFillColorParamTarget::CallFunc_BreakColor_G' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorParamTarget, CallFunc_BreakColor_B) == 0x000020, "Member 'WB_Container_Circular_C_SetFillColorParamTarget::CallFunc_BreakColor_B' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetFillColorParamTarget, CallFunc_BreakColor_A) == 0x000024, "Member 'WB_Container_Circular_C_SetFillColorParamTarget::CallFunc_BreakColor_A' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientColorParam
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetGradientColorParam final
{
public:
	struct FLinearColor                           Value;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetGradientColorParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetGradientColorParam");
static_assert(sizeof(WB_Container_Circular_C_SetGradientColorParam) == 0x000018, "Wrong size on WB_Container_Circular_C_SetGradientColorParam");
static_assert(offsetof(WB_Container_Circular_C_SetGradientColorParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetGradientColorParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetGradientColorParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetGradientColorParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientMaskParam
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_SetGradientMaskParam final
{
public:
	class UTexture*                               Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetGradientMaskParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetGradientMaskParam");
static_assert(sizeof(WB_Container_Circular_C_SetGradientMaskParam) == 0x000010, "Wrong size on WB_Container_Circular_C_SetGradientMaskParam");
static_assert(offsetof(WB_Container_Circular_C_SetGradientMaskParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetGradientMaskParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetGradientMaskParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetGradientMaskParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientOpacity
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetGradientOpacity final
{
public:
	double                                        GradientOpacity_0;                                 // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetGradientOpacity) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetGradientOpacity");
static_assert(sizeof(WB_Container_Circular_C_SetGradientOpacity) == 0x000008, "Wrong size on WB_Container_Circular_C_SetGradientOpacity");
static_assert(offsetof(WB_Container_Circular_C_SetGradientOpacity, GradientOpacity_0) == 0x000000, "Member 'WB_Container_Circular_C_SetGradientOpacity::GradientOpacity_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientOpacityParam
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetGradientOpacityParam final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetGradientOpacityParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetGradientOpacityParam");
static_assert(sizeof(WB_Container_Circular_C_SetGradientOpacityParam) == 0x000018, "Wrong size on WB_Container_Circular_C_SetGradientOpacityParam");
static_assert(offsetof(WB_Container_Circular_C_SetGradientOpacityParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetGradientOpacityParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetGradientOpacityParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetGradientOpacityParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetGradientOpacityParam, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000010, "Member 'WB_Container_Circular_C_SetGradientOpacityParam::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientType
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Circular_C_SetGradientType final
{
public:
	EGradientTypes                                GradientType;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetGradientType) == 0x000001, "Wrong alignment on WB_Container_Circular_C_SetGradientType");
static_assert(sizeof(WB_Container_Circular_C_SetGradientType) == 0x000001, "Wrong size on WB_Container_Circular_C_SetGradientType");
static_assert(offsetof(WB_Container_Circular_C_SetGradientType, GradientType) == 0x000000, "Member 'WB_Container_Circular_C_SetGradientType::GradientType' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeBGColorParam
// 0x0028 (0x0028 - 0x0000)
struct WB_Container_Circular_C_SetMarqueeBGColorParam final
{
public:
	struct FLinearColor                           Value;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarqueeBG_ReturnValue;   // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_R;                             // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_G;                             // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_B;                             // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_A;                             // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetMarqueeBGColorParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetMarqueeBGColorParam");
static_assert(sizeof(WB_Container_Circular_C_SetMarqueeBGColorParam) == 0x000028, "Wrong size on WB_Container_Circular_C_SetMarqueeBGColorParam");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeBGColorParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetMarqueeBGColorParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeBGColorParam, CallFunc_GetCircleMaterialMarqueeBG_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetMarqueeBGColorParam::CallFunc_GetCircleMaterialMarqueeBG_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeBGColorParam, CallFunc_BreakColor_R) == 0x000018, "Member 'WB_Container_Circular_C_SetMarqueeBGColorParam::CallFunc_BreakColor_R' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeBGColorParam, CallFunc_BreakColor_G) == 0x00001C, "Member 'WB_Container_Circular_C_SetMarqueeBGColorParam::CallFunc_BreakColor_G' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeBGColorParam, CallFunc_BreakColor_B) == 0x000020, "Member 'WB_Container_Circular_C_SetMarqueeBGColorParam::CallFunc_BreakColor_B' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeBGColorParam, CallFunc_BreakColor_A) == 0x000024, "Member 'WB_Container_Circular_C_SetMarqueeBGColorParam::CallFunc_BreakColor_A' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeColorParam
// 0x0028 (0x0028 - 0x0000)
struct WB_Container_Circular_C_SetMarqueeColorParam final
{
public:
	struct FLinearColor                           Value;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarquee_ReturnValue;     // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_R;                             // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_G;                             // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_B;                             // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_A;                             // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetMarqueeColorParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetMarqueeColorParam");
static_assert(sizeof(WB_Container_Circular_C_SetMarqueeColorParam) == 0x000028, "Wrong size on WB_Container_Circular_C_SetMarqueeColorParam");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeColorParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetMarqueeColorParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeColorParam, CallFunc_GetCircleMaterialMarquee_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetMarqueeColorParam::CallFunc_GetCircleMaterialMarquee_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeColorParam, CallFunc_BreakColor_R) == 0x000018, "Member 'WB_Container_Circular_C_SetMarqueeColorParam::CallFunc_BreakColor_R' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeColorParam, CallFunc_BreakColor_G) == 0x00001C, "Member 'WB_Container_Circular_C_SetMarqueeColorParam::CallFunc_BreakColor_G' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeColorParam, CallFunc_BreakColor_B) == 0x000020, "Member 'WB_Container_Circular_C_SetMarqueeColorParam::CallFunc_BreakColor_B' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeColorParam, CallFunc_BreakColor_A) == 0x000024, "Member 'WB_Container_Circular_C_SetMarqueeColorParam::CallFunc_BreakColor_A' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeMask
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_SetMarqueeMask final
{
public:
	EMarqueeMask                                  MarqueeMask;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             CustomMask;                                        // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetMarqueeMask) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetMarqueeMask");
static_assert(sizeof(WB_Container_Circular_C_SetMarqueeMask) == 0x000010, "Wrong size on WB_Container_Circular_C_SetMarqueeMask");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeMask, MarqueeMask) == 0x000000, "Member 'WB_Container_Circular_C_SetMarqueeMask::MarqueeMask' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeMask, CustomMask) == 0x000008, "Member 'WB_Container_Circular_C_SetMarqueeMask::CustomMask' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeMaskParam
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_SetMarqueeMaskParam final
{
public:
	class UTexture*                               Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarquee_ReturnValue;     // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetMarqueeMaskParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetMarqueeMaskParam");
static_assert(sizeof(WB_Container_Circular_C_SetMarqueeMaskParam) == 0x000010, "Wrong size on WB_Container_Circular_C_SetMarqueeMaskParam");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeMaskParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetMarqueeMaskParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeMaskParam, CallFunc_GetCircleMaterialMarquee_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetMarqueeMaskParam::CallFunc_GetCircleMaterialMarquee_ReturnValue' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeTime
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetMarqueeTime final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetMarqueeTime) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetMarqueeTime");
static_assert(sizeof(WB_Container_Circular_C_SetMarqueeTime) == 0x000008, "Wrong size on WB_Container_Circular_C_SetMarqueeTime");
static_assert(offsetof(WB_Container_Circular_C_SetMarqueeTime, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetMarqueeTime::Value' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetPercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetPercent) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetPercent");
static_assert(sizeof(WB_Container_Circular_C_SetPercent) == 0x000008, "Wrong size on WB_Container_Circular_C_SetPercent");
static_assert(offsetof(WB_Container_Circular_C_SetPercent, Percent) == 0x000000, "Member 'WB_Container_Circular_C_SetPercent::Percent' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetPercentParam
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetPercentParam final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetPercentParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetPercentParam");
static_assert(sizeof(WB_Container_Circular_C_SetPercentParam) == 0x000018, "Wrong size on WB_Container_Circular_C_SetPercentParam");
static_assert(offsetof(WB_Container_Circular_C_SetPercentParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetPercentParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetPercentParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetPercentParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetPercentParam, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000010, "Member 'WB_Container_Circular_C_SetPercentParam::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetPercentParamTarget
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetPercentParamTarget final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialTarget_ReturnValue;      // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetPercentParamTarget) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetPercentParamTarget");
static_assert(sizeof(WB_Container_Circular_C_SetPercentParamTarget) == 0x000018, "Wrong size on WB_Container_Circular_C_SetPercentParamTarget");
static_assert(offsetof(WB_Container_Circular_C_SetPercentParamTarget, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetPercentParamTarget::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetPercentParamTarget, CallFunc_GetCircleMaterialTarget_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetPercentParamTarget::CallFunc_GetCircleMaterialTarget_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetPercentParamTarget, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000010, "Member 'WB_Container_Circular_C_SetPercentParamTarget::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetProgressMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Circular_C_SetProgressMethod final
{
public:
	EProgressMethod                               ProgressMethod_0;                                  // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetProgressMethod) == 0x000001, "Wrong alignment on WB_Container_Circular_C_SetProgressMethod");
static_assert(sizeof(WB_Container_Circular_C_SetProgressMethod) == 0x000001, "Wrong size on WB_Container_Circular_C_SetProgressMethod");
static_assert(offsetof(WB_Container_Circular_C_SetProgressMethod, ProgressMethod_0) == 0x000000, "Member 'WB_Container_Circular_C_SetProgressMethod::ProgressMethod_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetSize
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetSize final
{
public:
	double                                        Size;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetSize) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetSize");
static_assert(sizeof(WB_Container_Circular_C_SetSize) == 0x000008, "Wrong size on WB_Container_Circular_C_SetSize");
static_assert(offsetof(WB_Container_Circular_C_SetSize, Size) == 0x000000, "Member 'WB_Container_Circular_C_SetSize::Size' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetSpacing
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetSpacing final
{
public:
	double                                        Spacing_0;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetSpacing) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetSpacing");
static_assert(sizeof(WB_Container_Circular_C_SetSpacing) == 0x000008, "Wrong size on WB_Container_Circular_C_SetSpacing");
static_assert(offsetof(WB_Container_Circular_C_SetSpacing, Spacing_0) == 0x000000, "Member 'WB_Container_Circular_C_SetSpacing::Spacing_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetSpacingParam
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetSpacingParam final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetSpacingParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetSpacingParam");
static_assert(sizeof(WB_Container_Circular_C_SetSpacingParam) == 0x000018, "Wrong size on WB_Container_Circular_C_SetSpacingParam");
static_assert(offsetof(WB_Container_Circular_C_SetSpacingParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetSpacingParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetSpacingParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetSpacingParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetSpacingParam, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000010, "Member 'WB_Container_Circular_C_SetSpacingParam::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetSpacingParamMarquee
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Circular_C_SetSpacingParamMarquee final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarqueeBG_ReturnValue;   // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarquee_ReturnValue;     // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_1; // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetSpacingParamMarquee) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetSpacingParamMarquee");
static_assert(sizeof(WB_Container_Circular_C_SetSpacingParamMarquee) == 0x000020, "Wrong size on WB_Container_Circular_C_SetSpacingParamMarquee");
static_assert(offsetof(WB_Container_Circular_C_SetSpacingParamMarquee, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetSpacingParamMarquee::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetSpacingParamMarquee, CallFunc_GetCircleMaterialMarqueeBG_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetSpacingParamMarquee::CallFunc_GetCircleMaterialMarqueeBG_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetSpacingParamMarquee, CallFunc_GetCircleMaterialMarquee_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetSpacingParamMarquee::CallFunc_GetCircleMaterialMarquee_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetSpacingParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000018, "Member 'WB_Container_Circular_C_SetSpacingParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetSpacingParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast_1) == 0x00001C, "Member 'WB_Container_Circular_C_SetSpacingParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast_1' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetStepDensity
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetStepDensity final
{
public:
	double                                        HardStepBorder;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetStepDensity) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetStepDensity");
static_assert(sizeof(WB_Container_Circular_C_SetStepDensity) == 0x000008, "Wrong size on WB_Container_Circular_C_SetStepDensity");
static_assert(offsetof(WB_Container_Circular_C_SetStepDensity, HardStepBorder) == 0x000000, "Member 'WB_Container_Circular_C_SetStepDensity::HardStepBorder' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetStepDensityParam
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetStepDensityParam final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetStepDensityParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetStepDensityParam");
static_assert(sizeof(WB_Container_Circular_C_SetStepDensityParam) == 0x000018, "Wrong size on WB_Container_Circular_C_SetStepDensityParam");
static_assert(offsetof(WB_Container_Circular_C_SetStepDensityParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetStepDensityParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepDensityParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetStepDensityParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepDensityParam, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000010, "Member 'WB_Container_Circular_C_SetStepDensityParam::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetStepDensityParamMarquee
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Circular_C_SetStepDensityParamMarquee final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarqueeBG_ReturnValue;   // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarquee_ReturnValue;     // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_1; // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetStepDensityParamMarquee) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetStepDensityParamMarquee");
static_assert(sizeof(WB_Container_Circular_C_SetStepDensityParamMarquee) == 0x000020, "Wrong size on WB_Container_Circular_C_SetStepDensityParamMarquee");
static_assert(offsetof(WB_Container_Circular_C_SetStepDensityParamMarquee, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetStepDensityParamMarquee::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepDensityParamMarquee, CallFunc_GetCircleMaterialMarqueeBG_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetStepDensityParamMarquee::CallFunc_GetCircleMaterialMarqueeBG_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepDensityParamMarquee, CallFunc_GetCircleMaterialMarquee_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetStepDensityParamMarquee::CallFunc_GetCircleMaterialMarquee_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepDensityParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000018, "Member 'WB_Container_Circular_C_SetStepDensityParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepDensityParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast_1) == 0x00001C, "Member 'WB_Container_Circular_C_SetStepDensityParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast_1' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetSteps
// 0x0004 (0x0004 - 0x0000)
struct WB_Container_Circular_C_SetSteps final
{
public:
	int32                                         Steps_0;                                           // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetSteps) == 0x000004, "Wrong alignment on WB_Container_Circular_C_SetSteps");
static_assert(sizeof(WB_Container_Circular_C_SetSteps) == 0x000004, "Wrong size on WB_Container_Circular_C_SetSteps");
static_assert(offsetof(WB_Container_Circular_C_SetSteps, Steps_0) == 0x000000, "Member 'WB_Container_Circular_C_SetSteps::Steps_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetStepsParam
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Circular_C_SetStepsParam final
{
public:
	int32                                         Value;                                             // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetStepsParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetStepsParam");
static_assert(sizeof(WB_Container_Circular_C_SetStepsParam) == 0x000020, "Wrong size on WB_Container_Circular_C_SetStepsParam");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetStepsParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetStepsParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParam, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetStepsParam::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParam, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000018, "Member 'WB_Container_Circular_C_SetStepsParam::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetStepsParamMarquee
// 0x0028 (0x0028 - 0x0000)
struct WB_Container_Circular_C_SetStepsParamMarquee final
{
public:
	int32                                         Value;                                             // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarqueeBG_ReturnValue;   // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarquee_ReturnValue;     // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_1; // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetStepsParamMarquee) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetStepsParamMarquee");
static_assert(sizeof(WB_Container_Circular_C_SetStepsParamMarquee) == 0x000028, "Wrong size on WB_Container_Circular_C_SetStepsParamMarquee");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParamMarquee, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetStepsParamMarquee::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParamMarquee, CallFunc_GetCircleMaterialMarqueeBG_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetStepsParamMarquee::CallFunc_GetCircleMaterialMarqueeBG_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParamMarquee, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetStepsParamMarquee::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParamMarquee, CallFunc_GetCircleMaterialMarquee_ReturnValue) == 0x000018, "Member 'WB_Container_Circular_C_SetStepsParamMarquee::CallFunc_GetCircleMaterialMarquee_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000020, "Member 'WB_Container_Circular_C_SetStepsParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetStepsParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast_1) == 0x000024, "Member 'WB_Container_Circular_C_SetStepsParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast_1' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetTargetFillColor_Negative
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_SetTargetFillColor_Negative final
{
public:
	struct FLinearColor                           FillColor_0;                                       // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetTargetFillColor_Negative) == 0x000004, "Wrong alignment on WB_Container_Circular_C_SetTargetFillColor_Negative");
static_assert(sizeof(WB_Container_Circular_C_SetTargetFillColor_Negative) == 0x000010, "Wrong size on WB_Container_Circular_C_SetTargetFillColor_Negative");
static_assert(offsetof(WB_Container_Circular_C_SetTargetFillColor_Negative, FillColor_0) == 0x000000, "Member 'WB_Container_Circular_C_SetTargetFillColor_Negative::FillColor_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetTargetFillColor_Positive
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_SetTargetFillColor_Positive final
{
public:
	struct FLinearColor                           FillColor_0;                                       // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetTargetFillColor_Positive) == 0x000004, "Wrong alignment on WB_Container_Circular_C_SetTargetFillColor_Positive");
static_assert(sizeof(WB_Container_Circular_C_SetTargetFillColor_Positive) == 0x000010, "Wrong size on WB_Container_Circular_C_SetTargetFillColor_Positive");
static_assert(offsetof(WB_Container_Circular_C_SetTargetFillColor_Positive, FillColor_0) == 0x000000, "Member 'WB_Container_Circular_C_SetTargetFillColor_Positive::FillColor_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetTargetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetTargetPercent final
{
public:
	double                                        TargetPercent_0;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetTargetPercent) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetTargetPercent");
static_assert(sizeof(WB_Container_Circular_C_SetTargetPercent) == 0x000008, "Wrong size on WB_Container_Circular_C_SetTargetPercent");
static_assert(offsetof(WB_Container_Circular_C_SetTargetPercent, TargetPercent_0) == 0x000000, "Member 'WB_Container_Circular_C_SetTargetPercent::TargetPercent_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetThickness
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Circular_C_SetThickness final
{
public:
	double                                        Thickness_0;                                       // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetThickness) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetThickness");
static_assert(sizeof(WB_Container_Circular_C_SetThickness) == 0x000008, "Wrong size on WB_Container_Circular_C_SetThickness");
static_assert(offsetof(WB_Container_Circular_C_SetThickness, Thickness_0) == 0x000000, "Member 'WB_Container_Circular_C_SetThickness::Thickness_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetThicknessParam
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetThicknessParam final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetThicknessParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetThicknessParam");
static_assert(sizeof(WB_Container_Circular_C_SetThicknessParam) == 0x000018, "Wrong size on WB_Container_Circular_C_SetThicknessParam");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParam, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetThicknessParam::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetThicknessParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParam, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000010, "Member 'WB_Container_Circular_C_SetThicknessParam::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetThicknessParamMarquee
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Circular_C_SetThicknessParamMarquee final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarqueeBG_ReturnValue;   // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarquee_ReturnValue;     // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_1; // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetThicknessParamMarquee) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetThicknessParamMarquee");
static_assert(sizeof(WB_Container_Circular_C_SetThicknessParamMarquee) == 0x000020, "Wrong size on WB_Container_Circular_C_SetThicknessParamMarquee");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParamMarquee, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetThicknessParamMarquee::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParamMarquee, CallFunc_GetCircleMaterialMarqueeBG_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetThicknessParamMarquee::CallFunc_GetCircleMaterialMarqueeBG_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParamMarquee, CallFunc_GetCircleMaterialMarquee_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetThicknessParamMarquee::CallFunc_GetCircleMaterialMarquee_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000018, "Member 'WB_Container_Circular_C_SetThicknessParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast_1) == 0x00001C, "Member 'WB_Container_Circular_C_SetThicknessParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast_1' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetThicknessParamTarget
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Circular_C_SetThicknessParamTarget final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialTarget_ReturnValue;      // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetThicknessParamTarget) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetThicknessParamTarget");
static_assert(sizeof(WB_Container_Circular_C_SetThicknessParamTarget) == 0x000018, "Wrong size on WB_Container_Circular_C_SetThicknessParamTarget");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParamTarget, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetThicknessParamTarget::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParamTarget, CallFunc_GetCircleMaterialTarget_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetThicknessParamTarget::CallFunc_GetCircleMaterialTarget_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetThicknessParamTarget, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000010, "Member 'WB_Container_Circular_C_SetThicknessParamTarget::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetTimeParamMarquee
// 0x0028 (0x0028 - 0x0000)
struct WB_Container_Circular_C_SetTimeParamMarquee final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarqueeBG_ReturnValue;   // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterialMarquee_ReturnValue;     // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_1; // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetTimeParamMarquee) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetTimeParamMarquee");
static_assert(sizeof(WB_Container_Circular_C_SetTimeParamMarquee) == 0x000028, "Wrong size on WB_Container_Circular_C_SetTimeParamMarquee");
static_assert(offsetof(WB_Container_Circular_C_SetTimeParamMarquee, Value) == 0x000000, "Member 'WB_Container_Circular_C_SetTimeParamMarquee::Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetTimeParamMarquee, CallFunc_GetCircleMaterialMarqueeBG_ReturnValue) == 0x000008, "Member 'WB_Container_Circular_C_SetTimeParamMarquee::CallFunc_GetCircleMaterialMarqueeBG_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetTimeParamMarquee, CallFunc_MapRangeClamped_ReturnValue) == 0x000010, "Member 'WB_Container_Circular_C_SetTimeParamMarquee::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetTimeParamMarquee, CallFunc_GetCircleMaterialMarquee_ReturnValue) == 0x000018, "Member 'WB_Container_Circular_C_SetTimeParamMarquee::CallFunc_GetCircleMaterialMarquee_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetTimeParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000020, "Member 'WB_Container_Circular_C_SetTimeParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetTimeParamMarquee, CallFunc_SetScalarParameterValue_Value_ImplicitCast_1) == 0x000024, "Member 'WB_Container_Circular_C_SetTimeParamMarquee::CallFunc_SetScalarParameterValue_Value_ImplicitCast_1' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetupMarquee
// 0x0024 (0x0024 - 0x0000)
struct WB_Container_Circular_C_SetupMarquee final
{
public:
	bool                                          bUseMarquee_0;                                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           MarqueeColor_0;                                    // 0x0004(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           MarqueeBackgroundColor;                            // 0x0014(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetupMarquee) == 0x000004, "Wrong alignment on WB_Container_Circular_C_SetupMarquee");
static_assert(sizeof(WB_Container_Circular_C_SetupMarquee) == 0x000024, "Wrong size on WB_Container_Circular_C_SetupMarquee");
static_assert(offsetof(WB_Container_Circular_C_SetupMarquee, bUseMarquee_0) == 0x000000, "Member 'WB_Container_Circular_C_SetupMarquee::bUseMarquee_0' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetupMarquee, MarqueeColor_0) == 0x000004, "Member 'WB_Container_Circular_C_SetupMarquee::MarqueeColor_0' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetupMarquee, MarqueeBackgroundColor) == 0x000014, "Member 'WB_Container_Circular_C_SetupMarquee::MarqueeBackgroundColor' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetUseAbsoluteFillMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Circular_C_SetUseAbsoluteFillMethod final
{
public:
	bool                                          bAbsoluteFillMethod_0;                             // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetUseAbsoluteFillMethod) == 0x000001, "Wrong alignment on WB_Container_Circular_C_SetUseAbsoluteFillMethod");
static_assert(sizeof(WB_Container_Circular_C_SetUseAbsoluteFillMethod) == 0x000001, "Wrong size on WB_Container_Circular_C_SetUseAbsoluteFillMethod");
static_assert(offsetof(WB_Container_Circular_C_SetUseAbsoluteFillMethod, bAbsoluteFillMethod_0) == 0x000000, "Member 'WB_Container_Circular_C_SetUseAbsoluteFillMethod::bAbsoluteFillMethod_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetUseGradient
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Circular_C_SetUseGradient final
{
public:
	bool                                          bUseGradient_0;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetUseGradient) == 0x000001, "Wrong alignment on WB_Container_Circular_C_SetUseGradient");
static_assert(sizeof(WB_Container_Circular_C_SetUseGradient) == 0x000001, "Wrong size on WB_Container_Circular_C_SetUseGradient");
static_assert(offsetof(WB_Container_Circular_C_SetUseGradient, bUseGradient_0) == 0x000000, "Member 'WB_Container_Circular_C_SetUseGradient::bUseGradient_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetUseGradientParam
// 0x0038 (0x0038 - 0x0000)
struct WB_Container_Circular_C_SetUseGradientParam final
{
public:
	bool                                          UseGradient;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               CallFunc_GetCircleMaterial_ReturnValue;            // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetUseGradientParam) == 0x000008, "Wrong alignment on WB_Container_Circular_C_SetUseGradientParam");
static_assert(sizeof(WB_Container_Circular_C_SetUseGradientParam) == 0x000038, "Wrong size on WB_Container_Circular_C_SetUseGradientParam");
static_assert(offsetof(WB_Container_Circular_C_SetUseGradientParam, UseGradient) == 0x000000, "Member 'WB_Container_Circular_C_SetUseGradientParam::UseGradient' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetUseGradientParam, Temp_real_Variable) == 0x000008, "Member 'WB_Container_Circular_C_SetUseGradientParam::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetUseGradientParam, Temp_real_Variable_1) == 0x000010, "Member 'WB_Container_Circular_C_SetUseGradientParam::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetUseGradientParam, Temp_bool_Variable) == 0x000018, "Member 'WB_Container_Circular_C_SetUseGradientParam::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetUseGradientParam, CallFunc_GetCircleMaterial_ReturnValue) == 0x000020, "Member 'WB_Container_Circular_C_SetUseGradientParam::CallFunc_GetCircleMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetUseGradientParam, K2Node_Select_Default) == 0x000028, "Member 'WB_Container_Circular_C_SetUseGradientParam::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_SetUseGradientParam, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000030, "Member 'WB_Container_Circular_C_SetUseGradientParam::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.SetUseTargetPercent
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Circular_C_SetUseTargetPercent final
{
public:
	bool                                          bIsTargetPercent;                                  // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_SetUseTargetPercent) == 0x000001, "Wrong alignment on WB_Container_Circular_C_SetUseTargetPercent");
static_assert(sizeof(WB_Container_Circular_C_SetUseTargetPercent) == 0x000001, "Wrong size on WB_Container_Circular_C_SetUseTargetPercent");
static_assert(offsetof(WB_Container_Circular_C_SetUseTargetPercent, bIsTargetPercent) == 0x000000, "Member 'WB_Container_Circular_C_SetUseTargetPercent::bIsTargetPercent' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.StartTriggerProgressChangeColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Circular_C_StartTriggerProgressChangeColor final
{
public:
	struct FLinearColor                           ProgressChangeColor_0;                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_StartTriggerProgressChangeColor) == 0x000004, "Wrong alignment on WB_Container_Circular_C_StartTriggerProgressChangeColor");
static_assert(sizeof(WB_Container_Circular_C_StartTriggerProgressChangeColor) == 0x000010, "Wrong size on WB_Container_Circular_C_StartTriggerProgressChangeColor");
static_assert(offsetof(WB_Container_Circular_C_StartTriggerProgressChangeColor, ProgressChangeColor_0) == 0x000000, "Member 'WB_Container_Circular_C_StartTriggerProgressChangeColor::ProgressChangeColor_0' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.Tick
// 0x003C (0x003C - 0x0000)
struct WB_Container_Circular_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_Tick) == 0x000004, "Wrong alignment on WB_Container_Circular_C_Tick");
static_assert(sizeof(WB_Container_Circular_C_Tick) == 0x00003C, "Wrong size on WB_Container_Circular_C_Tick");
static_assert(offsetof(WB_Container_Circular_C_Tick, MyGeometry) == 0x000000, "Member 'WB_Container_Circular_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_Tick, InDeltaTime) == 0x000038, "Member 'WB_Container_Circular_C_Tick::InDeltaTime' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.UpdateProgressChangeColor
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Circular_C_UpdateProgressChangeColor final
{
public:
	struct FLinearColor                           NewColor;                                          // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        InterpSpeed;                                       // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsChanging;                                        // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_UpdateProgressChangeColor) == 0x000008, "Wrong alignment on WB_Container_Circular_C_UpdateProgressChangeColor");
static_assert(sizeof(WB_Container_Circular_C_UpdateProgressChangeColor) == 0x000020, "Wrong size on WB_Container_Circular_C_UpdateProgressChangeColor");
static_assert(offsetof(WB_Container_Circular_C_UpdateProgressChangeColor, NewColor) == 0x000000, "Member 'WB_Container_Circular_C_UpdateProgressChangeColor::NewColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_UpdateProgressChangeColor, InterpSpeed) == 0x000010, "Member 'WB_Container_Circular_C_UpdateProgressChangeColor::InterpSpeed' has a wrong offset!");
static_assert(offsetof(WB_Container_Circular_C_UpdateProgressChangeColor, IsChanging) == 0x000018, "Member 'WB_Container_Circular_C_UpdateProgressChangeColor::IsChanging' has a wrong offset!");

// Function WB_Container_Circular.WB_Container_Circular_C.UpdateStaticPercent
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Circular_C_UpdateStaticPercent final
{
public:
	bool                                          CallFunc_IsProgressMethodStatic_ReturnValue;       // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Circular_C_UpdateStaticPercent) == 0x000001, "Wrong alignment on WB_Container_Circular_C_UpdateStaticPercent");
static_assert(sizeof(WB_Container_Circular_C_UpdateStaticPercent) == 0x000001, "Wrong size on WB_Container_Circular_C_UpdateStaticPercent");
static_assert(offsetof(WB_Container_Circular_C_UpdateStaticPercent, CallFunc_IsProgressMethodStatic_ReturnValue) == 0x000000, "Member 'WB_Container_Circular_C_UpdateStaticPercent::CallFunc_IsProgressMethodStatic_ReturnValue' has a wrong offset!");

}

