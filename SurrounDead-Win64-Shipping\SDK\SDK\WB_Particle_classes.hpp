﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Particle

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "ETriggerMethod_structs.hpp"
#include "UMG_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_Particle.WB_Particle_C
// 0x00E0 (0x03A0 - 0x02C0)
class UWB_Particle_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Fade;                                              // 0x02C8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UBorder*                                B_Color;                                           // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                B_Particle;                                        // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UOverlay*                               OV_ParticleFX;                                     // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 ParticleImage;                                     // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	double                                        PlaybackSpeed;                                     // 0x02F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bCanTrigger;                                       // 0x02F8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2F9[0x7];                                      // 0x02F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Value;                                             // 0x0300(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        RandomScale;                                       // 0x0308(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWB_Effect_C*                           Parent;                                            // 0x0310(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              RandomTranslation;                                 // 0x0318(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Scale;                                             // 0x0328(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Translation;                                       // 0x0338(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Shear;                                             // 0x0348(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Angle;                                             // 0x0358(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Size;                                              // 0x0360(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                TriggerMethod;                                     // 0x0370(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsDesignTime;                                      // 0x0371(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_372[0x6];                                      // 0x0372(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        RandomPlaybackspeed;                               // 0x0378(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          TriggerActive;                                     // 0x0380(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_381[0x7];                                      // 0x0381(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        ParticleSpread;                                    // 0x0388(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          UseParticleRotation;                               // 0x0390(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_391[0x7];                                      // 0x0391(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        RandomAngle;                                       // 0x0398(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Anim_Fade(double PlaybackSpeed_0);
	double CountValue(double Time);
	void CreateParticle(class UObject* Image, double PlaybackSpeed_0, const struct FLinearColor& Color, const struct FVector2D& Scale_0, const struct FVector2D& Translation_0, const struct FVector2D& Shear_0, double Angle_0, const struct FVector2D& Size_0, ETriggerMethod TriggerMethod_0, bool bIsDesignTime, double ParticleSpread_0, bool UseParticleRotation_0);
	void ExecuteUbergraph_WB_Particle(int32 EntryPoint);
	double GetClampedMinSize(double Multiplicator);
	double GetMaxSize();
	double GetMinSize();
	void GetTranslation(struct FVector2D* Translation_0);
	bool IsTriggerMethodAlways();
	void OnAnimationFinished_Event_0();
	void SetAngle(double Angle_0);
	void SetParticleInfo();
	void SetScale(double Scale_0);
	void SetScaleV2D(const struct FVector2D& Scale_0);
	void SetTranslation(const struct FVector2D& Translation_0);
	void StartTrigger();
	void StopTrigger();
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void TriggerParticle();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_Particle_C">();
	}
	static class UWB_Particle_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_Particle_C>();
	}
};
static_assert(alignof(UWB_Particle_C) == 0x000008, "Wrong alignment on UWB_Particle_C");
static_assert(sizeof(UWB_Particle_C) == 0x0003A0, "Wrong size on UWB_Particle_C");
static_assert(offsetof(UWB_Particle_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_Particle_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, Fade) == 0x0002C8, "Member 'UWB_Particle_C::Fade' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, B_Color) == 0x0002D0, "Member 'UWB_Particle_C::B_Color' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, B_Particle) == 0x0002D8, "Member 'UWB_Particle_C::B_Particle' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, OV_ParticleFX) == 0x0002E0, "Member 'UWB_Particle_C::OV_ParticleFX' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, ParticleImage) == 0x0002E8, "Member 'UWB_Particle_C::ParticleImage' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, PlaybackSpeed) == 0x0002F0, "Member 'UWB_Particle_C::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, bCanTrigger) == 0x0002F8, "Member 'UWB_Particle_C::bCanTrigger' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, Value) == 0x000300, "Member 'UWB_Particle_C::Value' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, RandomScale) == 0x000308, "Member 'UWB_Particle_C::RandomScale' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, Parent) == 0x000310, "Member 'UWB_Particle_C::Parent' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, RandomTranslation) == 0x000318, "Member 'UWB_Particle_C::RandomTranslation' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, Scale) == 0x000328, "Member 'UWB_Particle_C::Scale' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, Translation) == 0x000338, "Member 'UWB_Particle_C::Translation' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, Shear) == 0x000348, "Member 'UWB_Particle_C::Shear' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, Angle) == 0x000358, "Member 'UWB_Particle_C::Angle' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, Size) == 0x000360, "Member 'UWB_Particle_C::Size' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, TriggerMethod) == 0x000370, "Member 'UWB_Particle_C::TriggerMethod' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, IsDesignTime) == 0x000371, "Member 'UWB_Particle_C::IsDesignTime' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, RandomPlaybackspeed) == 0x000378, "Member 'UWB_Particle_C::RandomPlaybackspeed' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, TriggerActive) == 0x000380, "Member 'UWB_Particle_C::TriggerActive' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, ParticleSpread) == 0x000388, "Member 'UWB_Particle_C::ParticleSpread' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, UseParticleRotation) == 0x000390, "Member 'UWB_Particle_C::UseParticleRotation' has a wrong offset!");
static_assert(offsetof(UWB_Particle_C, RandomAngle) == 0x000398, "Member 'UWB_Particle_C::RandomAngle' has a wrong offset!");

}

