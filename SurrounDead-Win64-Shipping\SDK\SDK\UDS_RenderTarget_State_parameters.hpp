﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_RenderTarget_State

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Canvas Brush Location
// 0x0040 (0x0040 - 0x0000)
struct UDS_RenderTarget_State_C_Canvas_Brush_Location final
{
public:
	struct FVector2D                              In;                                                // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Out;                                               // 0x0010(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Subtract_Vector2DVector2D_ReturnValue;    // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Divide_Vector2DFloat_ReturnValue;         // 0x0030(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_RenderTarget_State_C_Canvas_Brush_Location) == 0x000008, "Wrong alignment on UDS_RenderTarget_State_C_Canvas_Brush_Location");
static_assert(sizeof(UDS_RenderTarget_State_C_Canvas_Brush_Location) == 0x000040, "Wrong size on UDS_RenderTarget_State_C_Canvas_Brush_Location");
static_assert(offsetof(UDS_RenderTarget_State_C_Canvas_Brush_Location, In) == 0x000000, "Member 'UDS_RenderTarget_State_C_Canvas_Brush_Location::In' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Canvas_Brush_Location, Out) == 0x000010, "Member 'UDS_RenderTarget_State_C_Canvas_Brush_Location::Out' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Canvas_Brush_Location, CallFunc_Subtract_Vector2DVector2D_ReturnValue) == 0x000020, "Member 'UDS_RenderTarget_State_C_Canvas_Brush_Location::CallFunc_Subtract_Vector2DVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Canvas_Brush_Location, CallFunc_Divide_Vector2DFloat_ReturnValue) == 0x000030, "Member 'UDS_RenderTarget_State_C_Canvas_Brush_Location::CallFunc_Divide_Vector2DFloat_ReturnValue' has a wrong offset!");

// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Canvas Brush Size
// 0x0030 (0x0030 - 0x0000)
struct UDS_RenderTarget_State_C_Canvas_Brush_Size final
{
public:
	struct FVector2D                              In;                                                // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Out;                                               // 0x0010(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Divide_Vector2DFloat_ReturnValue;         // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_RenderTarget_State_C_Canvas_Brush_Size) == 0x000008, "Wrong alignment on UDS_RenderTarget_State_C_Canvas_Brush_Size");
static_assert(sizeof(UDS_RenderTarget_State_C_Canvas_Brush_Size) == 0x000030, "Wrong size on UDS_RenderTarget_State_C_Canvas_Brush_Size");
static_assert(offsetof(UDS_RenderTarget_State_C_Canvas_Brush_Size, In) == 0x000000, "Member 'UDS_RenderTarget_State_C_Canvas_Brush_Size::In' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Canvas_Brush_Size, Out) == 0x000010, "Member 'UDS_RenderTarget_State_C_Canvas_Brush_Size::Out' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Canvas_Brush_Size, CallFunc_Divide_Vector2DFloat_ReturnValue) == 0x000020, "Member 'UDS_RenderTarget_State_C_Canvas_Brush_Size::CallFunc_Divide_Vector2DFloat_ReturnValue' has a wrong offset!");

// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Set Location
// 0x00C0 (0x00C0 - 0x0000)
struct UDS_RenderTarget_State_C_Set_Location final
{
public:
	struct FVector                                Center_Location_0;                                 // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Mapping_Vector4;                                   // 0x0018(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X;                            // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue;        // 0x0050(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x0060(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue_1;      // 0x0070(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_DoubleToVector_ReturnValue;          // 0x0080(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue;        // 0x0098(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_B_ImplicitCast;                  // 0x00B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast;                  // 0x00B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_R_ImplicitCast;                  // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_G_ImplicitCast;                  // 0x00BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_RenderTarget_State_C_Set_Location) == 0x000008, "Wrong alignment on UDS_RenderTarget_State_C_Set_Location");
static_assert(sizeof(UDS_RenderTarget_State_C_Set_Location) == 0x0000C0, "Wrong size on UDS_RenderTarget_State_C_Set_Location");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, Center_Location_0) == 0x000000, "Member 'UDS_RenderTarget_State_C_Set_Location::Center_Location_0' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, Mapping_Vector4) == 0x000018, "Member 'UDS_RenderTarget_State_C_Set_Location::Mapping_Vector4' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, CallFunc_BreakVector_X) == 0x000028, "Member 'UDS_RenderTarget_State_C_Set_Location::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, CallFunc_BreakVector_Y) == 0x000030, "Member 'UDS_RenderTarget_State_C_Set_Location::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, CallFunc_BreakVector_Z) == 0x000038, "Member 'UDS_RenderTarget_State_C_Set_Location::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, CallFunc_BreakVector2D_X) == 0x000040, "Member 'UDS_RenderTarget_State_C_Set_Location::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, CallFunc_BreakVector2D_Y) == 0x000048, "Member 'UDS_RenderTarget_State_C_Set_Location::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, CallFunc_Conv_VectorToVector2D_ReturnValue) == 0x000050, "Member 'UDS_RenderTarget_State_C_Set_Location::CallFunc_Conv_VectorToVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, K2Node_MakeStruct_LinearColor) == 0x000060, "Member 'UDS_RenderTarget_State_C_Set_Location::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, CallFunc_Conv_VectorToVector2D_ReturnValue_1) == 0x000070, "Member 'UDS_RenderTarget_State_C_Set_Location::CallFunc_Conv_VectorToVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, CallFunc_Conv_DoubleToVector_ReturnValue) == 0x000080, "Member 'UDS_RenderTarget_State_C_Set_Location::CallFunc_Conv_DoubleToVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, CallFunc_Subtract_VectorVector_ReturnValue) == 0x000098, "Member 'UDS_RenderTarget_State_C_Set_Location::CallFunc_Subtract_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, K2Node_MakeStruct_B_ImplicitCast) == 0x0000B0, "Member 'UDS_RenderTarget_State_C_Set_Location::K2Node_MakeStruct_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, K2Node_MakeStruct_A_ImplicitCast) == 0x0000B4, "Member 'UDS_RenderTarget_State_C_Set_Location::K2Node_MakeStruct_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, K2Node_MakeStruct_R_ImplicitCast) == 0x0000B8, "Member 'UDS_RenderTarget_State_C_Set_Location::K2Node_MakeStruct_R_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Location, K2Node_MakeStruct_G_ImplicitCast) == 0x0000BC, "Member 'UDS_RenderTarget_State_C_Set_Location::K2Node_MakeStruct_G_ImplicitCast' has a wrong offset!");

// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Set Render Target
// 0x0010 (0x0010 - 0x0000)
struct UDS_RenderTarget_State_C_Set_Render_Target final
{
public:
	class UTextureRenderTarget2D*                 Render_Target_0;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTextureRenderTarget2D*                 Buffer_Target_0;                                   // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_RenderTarget_State_C_Set_Render_Target) == 0x000008, "Wrong alignment on UDS_RenderTarget_State_C_Set_Render_Target");
static_assert(sizeof(UDS_RenderTarget_State_C_Set_Render_Target) == 0x000010, "Wrong size on UDS_RenderTarget_State_C_Set_Render_Target");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Render_Target, Render_Target_0) == 0x000000, "Member 'UDS_RenderTarget_State_C_Set_Render_Target::Render_Target_0' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Render_Target, Buffer_Target_0) == 0x000008, "Member 'UDS_RenderTarget_State_C_Set_Render_Target::Buffer_Target_0' has a wrong offset!");

// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Set Size
// 0x0020 (0x0020 - 0x0000)
struct UDS_RenderTarget_State_C_Set_Size final
{
public:
	double                                        Size_0;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_RenderTarget_State_C_Set_Size) == 0x000008, "Wrong alignment on UDS_RenderTarget_State_C_Set_Size");
static_assert(sizeof(UDS_RenderTarget_State_C_Set_Size) == 0x000020, "Wrong size on UDS_RenderTarget_State_C_Set_Size");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Size, Size_0) == 0x000000, "Member 'UDS_RenderTarget_State_C_Set_Size::Size_0' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Size, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000008, "Member 'UDS_RenderTarget_State_C_Set_Size::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Size, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000010, "Member 'UDS_RenderTarget_State_C_Set_Size::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Set_Size, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000018, "Member 'UDS_RenderTarget_State_C_Set_Size::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");

// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Target Extent 2D
// 0x0028 (0x0028 - 0x0000)
struct UDS_RenderTarget_State_C_Target_Extent_2D final
{
public:
	struct FVector2D                              Extent;                                            // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0018(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_RenderTarget_State_C_Target_Extent_2D) == 0x000008, "Wrong alignment on UDS_RenderTarget_State_C_Target_Extent_2D");
static_assert(sizeof(UDS_RenderTarget_State_C_Target_Extent_2D) == 0x000028, "Wrong size on UDS_RenderTarget_State_C_Target_Extent_2D");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Extent_2D, Extent) == 0x000000, "Member 'UDS_RenderTarget_State_C_Target_Extent_2D::Extent' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Extent_2D, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000010, "Member 'UDS_RenderTarget_State_C_Target_Extent_2D::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Extent_2D, CallFunc_MakeVector2D_ReturnValue) == 0x000018, "Member 'UDS_RenderTarget_State_C_Target_Extent_2D::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");

// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Target Needs Recenter
// 0x0080 (0x0080 - 0x0000)
struct UDS_RenderTarget_State_C_Target_Needs_Recenter final
{
public:
	struct FVector                                Control_Location;                                  // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Axis_Mask;                                         // 0x0018(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Yes;                                               // 0x0030(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue;        // 0x0040(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Multiply_VectorVector_ReturnValue;        // 0x0058(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Vector_GetAbsMax_ReturnValue;             // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_RenderTarget_State_C_Target_Needs_Recenter) == 0x000008, "Wrong alignment on UDS_RenderTarget_State_C_Target_Needs_Recenter");
static_assert(sizeof(UDS_RenderTarget_State_C_Target_Needs_Recenter) == 0x000080, "Wrong size on UDS_RenderTarget_State_C_Target_Needs_Recenter");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Needs_Recenter, Control_Location) == 0x000000, "Member 'UDS_RenderTarget_State_C_Target_Needs_Recenter::Control_Location' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Needs_Recenter, Axis_Mask) == 0x000018, "Member 'UDS_RenderTarget_State_C_Target_Needs_Recenter::Axis_Mask' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Needs_Recenter, Yes) == 0x000030, "Member 'UDS_RenderTarget_State_C_Target_Needs_Recenter::Yes' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Needs_Recenter, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000038, "Member 'UDS_RenderTarget_State_C_Target_Needs_Recenter::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Needs_Recenter, CallFunc_Subtract_VectorVector_ReturnValue) == 0x000040, "Member 'UDS_RenderTarget_State_C_Target_Needs_Recenter::CallFunc_Subtract_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Needs_Recenter, CallFunc_Multiply_VectorVector_ReturnValue) == 0x000058, "Member 'UDS_RenderTarget_State_C_Target_Needs_Recenter::CallFunc_Multiply_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Needs_Recenter, CallFunc_Vector_GetAbsMax_ReturnValue) == 0x000070, "Member 'UDS_RenderTarget_State_C_Target_Needs_Recenter::CallFunc_Vector_GetAbsMax_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_RenderTarget_State_C_Target_Needs_Recenter, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000078, "Member 'UDS_RenderTarget_State_C_Target_Needs_Recenter::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");

}

