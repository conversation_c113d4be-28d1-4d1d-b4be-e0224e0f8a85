﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_DisassemblyWorkbenchUI

#include "Basic.hpp"

#include "ContainerPickupsInfo_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_DisassemblyWorkbenchUI.W_DisassemblyWorkbenchUI_C
// 0x0050 (0x0310 - 0x02C0)
class UW_DisassemblyWorkbenchUI_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UTextBlock*                             BackText;                                          // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UEquipmentSlotTitle_C*                  EquipmentSlotTitle;                                // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UButton*                                ExitButton;                                        // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        JSIContainer;                                      // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        JSIContainer_1;                                    // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TArray<struct FContainerPickupsInfo>          All_Items;                                         // 0x02F0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	TArray<class UJSIContainer_C*>                NonAdd;                                            // 0x0300(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)

public:
	void BndEvt__BP_BinWidget_ExitButton_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature();
	void ExecuteUbergraph_W_DisassemblyWorkbenchUI(int32 EntryPoint);
	void ForceInitSpecialcontainer();
	void GetAllAttachments(TArray<class FName>* Attachments);
	void GetContainerByAttachmentType(const struct FGameplayTag& Type, class UJSIContainer_C** JigContainer, int32* ContainerIndex);
	void GetDropWidget(class UDropItemBackGwidget_C** DropWRef);
	void GetJSIContainerByPlayerSlots(const struct FGameplayTag& Slot_0, class UJSIContainer_C** Container, class UJSI_Slot_C** EquippedItem, bool* IsPending_);
	void GetListOfNonAddContainers(TArray<class UJSIContainer_C*>* Containers);
	void GetLootContent(class UUserWidget** Widget);
	void GetValidReloadContainers(TArray<class UJSIContainer_C*>* Containers);
	void JigSetLootContent(class UUserWidget* Widget, const class FText& Name_0);
	void JSICheckStatus();
	void JSIOnWeightUpdated(double NewWeight);
	void OnCreatedFromUtility();
	void SetActionbarFollower(class UJSI_Slot_C* JigRef, bool* Return);
	void SetActorOwner(class AActor* ActorRef);
	void SetInspectorRef(class UBP_InspectorWindowWidget_C* Inspector);
	void SetItemReference(class UJSI_Slot_C* ItemRef);

	void GetListOfContainers(TArray<class UJSIContainer_C*>* Containers) const;

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_DisassemblyWorkbenchUI_C">();
	}
	static class UW_DisassemblyWorkbenchUI_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_DisassemblyWorkbenchUI_C>();
	}
};
static_assert(alignof(UW_DisassemblyWorkbenchUI_C) == 0x000008, "Wrong alignment on UW_DisassemblyWorkbenchUI_C");
static_assert(sizeof(UW_DisassemblyWorkbenchUI_C) == 0x000310, "Wrong size on UW_DisassemblyWorkbenchUI_C");
static_assert(offsetof(UW_DisassemblyWorkbenchUI_C, UberGraphFrame) == 0x0002C0, "Member 'UW_DisassemblyWorkbenchUI_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_DisassemblyWorkbenchUI_C, BackText) == 0x0002C8, "Member 'UW_DisassemblyWorkbenchUI_C::BackText' has a wrong offset!");
static_assert(offsetof(UW_DisassemblyWorkbenchUI_C, EquipmentSlotTitle) == 0x0002D0, "Member 'UW_DisassemblyWorkbenchUI_C::EquipmentSlotTitle' has a wrong offset!");
static_assert(offsetof(UW_DisassemblyWorkbenchUI_C, ExitButton) == 0x0002D8, "Member 'UW_DisassemblyWorkbenchUI_C::ExitButton' has a wrong offset!");
static_assert(offsetof(UW_DisassemblyWorkbenchUI_C, JSIContainer) == 0x0002E0, "Member 'UW_DisassemblyWorkbenchUI_C::JSIContainer' has a wrong offset!");
static_assert(offsetof(UW_DisassemblyWorkbenchUI_C, JSIContainer_1) == 0x0002E8, "Member 'UW_DisassemblyWorkbenchUI_C::JSIContainer_1' has a wrong offset!");
static_assert(offsetof(UW_DisassemblyWorkbenchUI_C, All_Items) == 0x0002F0, "Member 'UW_DisassemblyWorkbenchUI_C::All_Items' has a wrong offset!");
static_assert(offsetof(UW_DisassemblyWorkbenchUI_C, NonAdd) == 0x000300, "Member 'UW_DisassemblyWorkbenchUI_C::NonAdd' has a wrong offset!");

}

