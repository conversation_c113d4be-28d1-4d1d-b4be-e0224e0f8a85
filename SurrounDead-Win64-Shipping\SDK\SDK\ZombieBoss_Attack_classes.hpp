﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: ZombieBoss_Attack

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AIModule_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass ZombieBoss_Attack.ZombieBoss_Attack_C
// 0x0008 (0x00B0 - 0x00A8)
class UZombieBoss_Attack_C final : public UBTTask_BlueprintBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x00A8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)

public:
	void ExecuteUbergraph_ZombieBoss_Attack(int32 EntryPoint);
	void ReceiveExecuteAI(class AAIController* OwnerController, class APawn* ControlledPawn);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"ZombieBoss_Attack_C">();
	}
	static class UZombieBoss_Attack_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UZombieBoss_Attack_C>();
	}
};
static_assert(alignof(UZombieBoss_Attack_C) == 0x000008, "Wrong alignment on UZombieBoss_Attack_C");
static_assert(sizeof(UZombieBoss_Attack_C) == 0x0000B0, "Wrong size on UZombieBoss_Attack_C");
static_assert(offsetof(UZombieBoss_Attack_C, UberGraphFrame) == 0x0000A8, "Member 'UZombieBoss_Attack_C::UberGraphFrame' has a wrong offset!");

}

