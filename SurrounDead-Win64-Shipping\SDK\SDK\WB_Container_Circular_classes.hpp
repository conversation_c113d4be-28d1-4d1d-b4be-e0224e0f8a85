﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Container_Circular

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"
#include "EProgressMethod_structs.hpp"
#include "EGradientTypes_structs.hpp"
#include "EMarqueeMask_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_Container_Circular.WB_Container_Circular_C
// 0x0160 (0x0420 - 0x02C0)
class UWB_Container_Circular_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 Circle;                                            // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 MarqueeBackground;                                 // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 MarqueeCircle;                                     // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UOverlay*                               OV_TargetCircle;                                   // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               SB_Content;                                        // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 TargetCircle;                                      // 0x02F0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        WS_Marquee;                                        // 0x02F8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CircleMaterial;                                    // 0x0300(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Thickness;                                         // 0x0308(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Density;                                           // 0x0310(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Steps;                                             // 0x0318(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31C[0x4];                                      // 0x031C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Spacing;                                           // 0x0320(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        StepDensity;                                       // 0x0328(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           BackgroundColor;                                   // 0x0330(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           FillColor;                                         // 0x0340(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        TargetPercent;                                     // 0x0350(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CurrentPercent;                                    // 0x0358(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bAbsoluteFillMethod;                               // 0x0360(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsNegativeFillValue;                              // 0x0361(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressMethod                               ProgressMethod;                                    // 0x0362(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsDesignTime;                                     // 0x0363(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_364[0x4];                                      // 0x0364(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        DeltaTime;                                         // 0x0368(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           Timer;                                             // 0x0370(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseTargetPercent;                                 // 0x0378(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_379[0x3];                                      // 0x0379(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           TargetFillColor_Positive;                          // 0x037C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           TargetFillColor_Negative;                          // 0x038C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           GradientColor;                                     // 0x039C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3AC[0x4];                                      // 0x03AC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        GradientOpacity;                                   // 0x03B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseGradient;                                      // 0x03B8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3B9[0x7];                                      // 0x03B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             FillColorMask;                                     // 0x03C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             BackgroundMask;                                    // 0x03C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        BackgroundThickness;                               // 0x03D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseMarquee;                                       // 0x03D8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3D9[0x3];                                      // 0x03D9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           MarqueeColor;                                      // 0x03DC(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           MarqueeColorBG;                                    // 0x03EC(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           ProgressChangeColor;                               // 0x03FC(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsProgressChanging;                               // 0x040C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseProgressChangeColor;                           // 0x040D(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsChanging;                                       // 0x040E(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_40F[0x1];                                      // 0x040F(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           LocalColor;                                        // 0x0410(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WB_Container_Circular(int32 EntryPoint);
	struct FLinearColor FindTargetFillColor();
	void FindTargetProgressBarPosition(double Progress);
	double GetAbsoluteTargetPercent();
	class UMaterialInstanceDynamic* GetCircleMaterial();
	class UMaterialInstanceDynamic* GetCircleMaterialMarquee();
	class UMaterialInstanceDynamic* GetCircleMaterialMarqueeBG();
	class UMaterialInstanceDynamic* GetCircleMaterialTarget();
	struct FLinearColor GetFillColor();
	double GetGradientOpacity();
	void GetPercent(double* Percent);
	void GetTargetPercent(double* TargetPercent_0);
	bool IsNegativeFillValue();
	bool IsProgressMethodInterpolate();
	bool IsProgressMethodStatic();
	void PreConstruct(bool IsDesignTime);
	void SetAbsoluteFillMethod(bool bAbsoluteFillMethod_0);
	void SetBackgroundColor(class UTexture2D* BackgroundMask_0, const struct FLinearColor& Color);
	void SetBackgroundColorMaskParam(class UTexture* Value);
	void SetDensity(double Density_0);
	void SetDensityParam(double Value);
	void SetDensityParamMarquee(double Value);
	void SetDensityParamTarget(double Value);
	void SetEmptyColorParam(const struct FLinearColor& Value);
	void SetFillColor(const struct FLinearColor& FillColor_0, double GradientPower);
	void SetFillColorMask(class UTexture2D* Mask);
	void SetFillColorMaskParam(class UTexture* Value);
	void SetFillColorParam(const struct FLinearColor& Value);
	void SetFillColorParamTarget(const struct FLinearColor& Value);
	void SetGradientColorParam(const struct FLinearColor& Value);
	void SetGradientMaskParam(class UTexture* Value);
	void SetGradientOpacity(double GradientOpacity_0);
	void SetGradientOpacityParam(double Value);
	void SetGradientType(EGradientTypes GradientType);
	void SetMarqueeBGColorParam(const struct FLinearColor& Value);
	void SetMarqueeColorParam(const struct FLinearColor& Value);
	void SetMarqueeMask(EMarqueeMask MarqueeMask, class UTexture2D* CustomMask);
	void SetMarqueeMaskParam(class UTexture* Value);
	void SetMarqueeTime(double Value);
	void SetPercent(double Percent);
	void SetPercentParam(double Value);
	void SetPercentParamTarget(double Value);
	void SetProgressMethod(EProgressMethod ProgressMethod_0);
	void SetSize(double Size);
	void SetSpacing(double Spacing_0);
	void SetSpacingParam(double Value);
	void SetSpacingParamMarquee(double Value);
	void SetStepDensity(double HardStepBorder);
	void SetStepDensityParam(double Value);
	void SetStepDensityParamMarquee(double Value);
	void SetSteps(int32 Steps_0);
	void SetStepsParam(int32 Value);
	void SetStepsParamMarquee(int32 Value);
	void SetTargetFillColor_Negative(const struct FLinearColor& FillColor_0);
	void SetTargetFillColor_Positive(const struct FLinearColor& FillColor_0);
	void SetTargetPercent(double TargetPercent_0);
	void SetThickness(double Thickness_0);
	void SetThicknessParam(double Value);
	void SetThicknessParamMarquee(double Value);
	void SetThicknessParamTarget(double Value);
	void SetTimeParamMarquee(double Value);
	void SetupMarquee(bool bUseMarquee_0, const struct FLinearColor& MarqueeColor_0, const struct FLinearColor& MarqueeBackgroundColor);
	void SetUseAbsoluteFillMethod(bool bAbsoluteFillMethod_0);
	void SetUseGradient(bool bUseGradient_0);
	void SetUseGradientParam(bool UseGradient);
	void SetUseTargetPercent(bool bIsTargetPercent);
	void StartTriggerProgressChangeColor(const struct FLinearColor& ProgressChangeColor_0);
	void StopTriggerProgressChangeColor();
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void UpdatePercent();
	void UpdateProgressChangeColor(const struct FLinearColor& NewColor, double InterpSpeed, bool IsChanging);
	void UpdateStaticPercent();
	void UpdateTargetPercent();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_Container_Circular_C">();
	}
	static class UWB_Container_Circular_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_Container_Circular_C>();
	}
};
static_assert(alignof(UWB_Container_Circular_C) == 0x000008, "Wrong alignment on UWB_Container_Circular_C");
static_assert(sizeof(UWB_Container_Circular_C) == 0x000420, "Wrong size on UWB_Container_Circular_C");
static_assert(offsetof(UWB_Container_Circular_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_Container_Circular_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, Circle) == 0x0002C8, "Member 'UWB_Container_Circular_C::Circle' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, MarqueeBackground) == 0x0002D0, "Member 'UWB_Container_Circular_C::MarqueeBackground' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, MarqueeCircle) == 0x0002D8, "Member 'UWB_Container_Circular_C::MarqueeCircle' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, OV_TargetCircle) == 0x0002E0, "Member 'UWB_Container_Circular_C::OV_TargetCircle' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, SB_Content) == 0x0002E8, "Member 'UWB_Container_Circular_C::SB_Content' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, TargetCircle) == 0x0002F0, "Member 'UWB_Container_Circular_C::TargetCircle' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, WS_Marquee) == 0x0002F8, "Member 'UWB_Container_Circular_C::WS_Marquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, CircleMaterial) == 0x000300, "Member 'UWB_Container_Circular_C::CircleMaterial' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, Thickness) == 0x000308, "Member 'UWB_Container_Circular_C::Thickness' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, Density) == 0x000310, "Member 'UWB_Container_Circular_C::Density' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, Steps) == 0x000318, "Member 'UWB_Container_Circular_C::Steps' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, Spacing) == 0x000320, "Member 'UWB_Container_Circular_C::Spacing' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, StepDensity) == 0x000328, "Member 'UWB_Container_Circular_C::StepDensity' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, BackgroundColor) == 0x000330, "Member 'UWB_Container_Circular_C::BackgroundColor' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, FillColor) == 0x000340, "Member 'UWB_Container_Circular_C::FillColor' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, TargetPercent) == 0x000350, "Member 'UWB_Container_Circular_C::TargetPercent' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, CurrentPercent) == 0x000358, "Member 'UWB_Container_Circular_C::CurrentPercent' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, bAbsoluteFillMethod) == 0x000360, "Member 'UWB_Container_Circular_C::bAbsoluteFillMethod' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, bIsNegativeFillValue) == 0x000361, "Member 'UWB_Container_Circular_C::bIsNegativeFillValue' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, ProgressMethod) == 0x000362, "Member 'UWB_Container_Circular_C::ProgressMethod' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, bIsDesignTime) == 0x000363, "Member 'UWB_Container_Circular_C::bIsDesignTime' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, DeltaTime) == 0x000368, "Member 'UWB_Container_Circular_C::DeltaTime' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, Timer) == 0x000370, "Member 'UWB_Container_Circular_C::Timer' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, bUseTargetPercent) == 0x000378, "Member 'UWB_Container_Circular_C::bUseTargetPercent' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, TargetFillColor_Positive) == 0x00037C, "Member 'UWB_Container_Circular_C::TargetFillColor_Positive' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, TargetFillColor_Negative) == 0x00038C, "Member 'UWB_Container_Circular_C::TargetFillColor_Negative' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, GradientColor) == 0x00039C, "Member 'UWB_Container_Circular_C::GradientColor' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, GradientOpacity) == 0x0003B0, "Member 'UWB_Container_Circular_C::GradientOpacity' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, bUseGradient) == 0x0003B8, "Member 'UWB_Container_Circular_C::bUseGradient' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, FillColorMask) == 0x0003C0, "Member 'UWB_Container_Circular_C::FillColorMask' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, BackgroundMask) == 0x0003C8, "Member 'UWB_Container_Circular_C::BackgroundMask' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, BackgroundThickness) == 0x0003D0, "Member 'UWB_Container_Circular_C::BackgroundThickness' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, bUseMarquee) == 0x0003D8, "Member 'UWB_Container_Circular_C::bUseMarquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, MarqueeColor) == 0x0003DC, "Member 'UWB_Container_Circular_C::MarqueeColor' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, MarqueeColorBG) == 0x0003EC, "Member 'UWB_Container_Circular_C::MarqueeColorBG' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, ProgressChangeColor) == 0x0003FC, "Member 'UWB_Container_Circular_C::ProgressChangeColor' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, bIsProgressChanging) == 0x00040C, "Member 'UWB_Container_Circular_C::bIsProgressChanging' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, bUseProgressChangeColor) == 0x00040D, "Member 'UWB_Container_Circular_C::bUseProgressChangeColor' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, bIsChanging) == 0x00040E, "Member 'UWB_Container_Circular_C::bIsChanging' has a wrong offset!");
static_assert(offsetof(UWB_Container_Circular_C, LocalColor) == 0x000410, "Member 'UWB_Container_Circular_C::LocalColor' has a wrong offset!");

}

