﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_MovementSpeed

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "Enum_ZombieMovementType_structs.hpp"
#include "AIModule_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass Zombie_MovementSpeed.Zombie_MovementSpeed_C
// 0x0018 (0x00B0 - 0x0098)
class UZombie_MovementSpeed_C final : public UBTService_BlueprintBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0098(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	Enum_ZombieMovementType                       MovementType;                                      // 0x00A0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A1[0x7];                                       // 0x00A1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ABP_MasterZombie_C*                     Zombie;                                            // 0x00A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_Zombie_MovementSpeed(int32 EntryPoint);
	void ReceiveActivationAI(class AAIController* OwnerController, class APawn* ControlledPawn);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Zombie_MovementSpeed_C">();
	}
	static class UZombie_MovementSpeed_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UZombie_MovementSpeed_C>();
	}
};
static_assert(alignof(UZombie_MovementSpeed_C) == 0x000008, "Wrong alignment on UZombie_MovementSpeed_C");
static_assert(sizeof(UZombie_MovementSpeed_C) == 0x0000B0, "Wrong size on UZombie_MovementSpeed_C");
static_assert(offsetof(UZombie_MovementSpeed_C, UberGraphFrame) == 0x000098, "Member 'UZombie_MovementSpeed_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UZombie_MovementSpeed_C, MovementType) == 0x0000A0, "Member 'UZombie_MovementSpeed_C::MovementType' has a wrong offset!");
static_assert(offsetof(UZombie_MovementSpeed_C, Zombie) == 0x0000A8, "Member 'UZombie_MovementSpeed_C::Zombie' has a wrong offset!");

}

