﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Container_Circular

#include "Basic.hpp"

#include "WB_Container_Circular_classes.hpp"
#include "WB_Container_Circular_parameters.hpp"


namespace SDK
{

// Function WB_Container_Circular.WB_Container_Circular_C.ExecuteUbergraph_WB_Container_Circular
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::ExecuteUbergraph_WB_Container_Circular(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "ExecuteUbergraph_WB_Container_Circular");

	Params::WB_Container_Circular_C_ExecuteUbergraph_WB_Container_Circular Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.FindTargetFillColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor UWB_Container_Circular_C::FindTargetFillColor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "FindTargetFillColor");

	Params::WB_Container_Circular_C_FindTargetFillColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.FindTargetProgressBarPosition
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Progress                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::FindTargetProgressBarPosition(double Progress)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "FindTargetProgressBarPosition");

	Params::WB_Container_Circular_C_FindTargetProgressBarPosition Parms{};

	Parms.Progress = Progress;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.GetAbsoluteTargetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Container_Circular_C::GetAbsoluteTargetPercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "GetAbsoluteTargetPercent");

	Params::WB_Container_Circular_C_GetAbsoluteTargetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.GetCircleMaterial
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UMaterialInstanceDynamic*         ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)

class UMaterialInstanceDynamic* UWB_Container_Circular_C::GetCircleMaterial()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "GetCircleMaterial");

	Params::WB_Container_Circular_C_GetCircleMaterial Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.GetCircleMaterialMarquee
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UMaterialInstanceDynamic*         ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)

class UMaterialInstanceDynamic* UWB_Container_Circular_C::GetCircleMaterialMarquee()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "GetCircleMaterialMarquee");

	Params::WB_Container_Circular_C_GetCircleMaterialMarquee Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.GetCircleMaterialMarqueeBG
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UMaterialInstanceDynamic*         ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)

class UMaterialInstanceDynamic* UWB_Container_Circular_C::GetCircleMaterialMarqueeBG()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "GetCircleMaterialMarqueeBG");

	Params::WB_Container_Circular_C_GetCircleMaterialMarqueeBG Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.GetCircleMaterialTarget
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UMaterialInstanceDynamic*         ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)

class UMaterialInstanceDynamic* UWB_Container_Circular_C::GetCircleMaterialTarget()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "GetCircleMaterialTarget");

	Params::WB_Container_Circular_C_GetCircleMaterialTarget Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.GetFillColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor UWB_Container_Circular_C::GetFillColor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "GetFillColor");

	Params::WB_Container_Circular_C_GetFillColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.GetGradientOpacity
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Container_Circular_C::GetGradientOpacity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "GetGradientOpacity");

	Params::WB_Container_Circular_C_GetGradientOpacity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.GetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Percent                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::GetPercent(double* Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "GetPercent");

	Params::WB_Container_Circular_C_GetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Percent != nullptr)
		*Percent = Parms.Percent;
}


// Function WB_Container_Circular.WB_Container_Circular_C.GetTargetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 TargetPercent_0                                        (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::GetTargetPercent(double* TargetPercent_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "GetTargetPercent");

	Params::WB_Container_Circular_C_GetTargetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (TargetPercent_0 != nullptr)
		*TargetPercent_0 = Parms.TargetPercent_0;
}


// Function WB_Container_Circular.WB_Container_Circular_C.IsNegativeFillValue
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Container_Circular_C::IsNegativeFillValue()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "IsNegativeFillValue");

	Params::WB_Container_Circular_C_IsNegativeFillValue Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.IsProgressMethodInterpolate
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Container_Circular_C::IsProgressMethodInterpolate()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "IsProgressMethodInterpolate");

	Params::WB_Container_Circular_C_IsProgressMethodInterpolate Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.IsProgressMethodStatic
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Container_Circular_C::IsProgressMethodStatic()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "IsProgressMethodStatic");

	Params::WB_Container_Circular_C_IsProgressMethodStatic Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Container_Circular.WB_Container_Circular_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "PreConstruct");

	Params::WB_Container_Circular_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetAbsoluteFillMethod
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bAbsoluteFillMethod_0                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetAbsoluteFillMethod(bool bAbsoluteFillMethod_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetAbsoluteFillMethod");

	Params::WB_Container_Circular_C_SetAbsoluteFillMethod Parms{};

	Parms.bAbsoluteFillMethod_0 = bAbsoluteFillMethod_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetBackgroundColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture2D*                       BackgroundMask_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetBackgroundColor(class UTexture2D* BackgroundMask_0, const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetBackgroundColor");

	Params::WB_Container_Circular_C_SetBackgroundColor Parms{};

	Parms.BackgroundMask_0 = BackgroundMask_0;
	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetBackgroundColorMaskParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture*                         Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetBackgroundColorMaskParam(class UTexture* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetBackgroundColorMaskParam");

	Params::WB_Container_Circular_C_SetBackgroundColorMaskParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetDensity
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Density_0                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetDensity(double Density_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetDensity");

	Params::WB_Container_Circular_C_SetDensity Parms{};

	Parms.Density_0 = Density_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetDensityParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetDensityParam(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetDensityParam");

	Params::WB_Container_Circular_C_SetDensityParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetDensityParamMarquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetDensityParamMarquee(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetDensityParamMarquee");

	Params::WB_Container_Circular_C_SetDensityParamMarquee Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetDensityParamTarget
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetDensityParamTarget(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetDensityParamTarget");

	Params::WB_Container_Circular_C_SetDensityParamTarget Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetEmptyColorParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetEmptyColorParam(const struct FLinearColor& Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetEmptyColorParam");

	Params::WB_Container_Circular_C_SetEmptyColorParam Parms{};

	Parms.Value = std::move(Value);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              FillColor_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  GradientPower                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetFillColor(const struct FLinearColor& FillColor_0, double GradientPower)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetFillColor");

	Params::WB_Container_Circular_C_SetFillColor Parms{};

	Parms.FillColor_0 = std::move(FillColor_0);
	Parms.GradientPower = GradientPower;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColorMask
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture2D*                       Mask                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetFillColorMask(class UTexture2D* Mask)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetFillColorMask");

	Params::WB_Container_Circular_C_SetFillColorMask Parms{};

	Parms.Mask = Mask;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColorMaskParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture*                         Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetFillColorMaskParam(class UTexture* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetFillColorMaskParam");

	Params::WB_Container_Circular_C_SetFillColorMaskParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColorParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetFillColorParam(const struct FLinearColor& Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetFillColorParam");

	Params::WB_Container_Circular_C_SetFillColorParam Parms{};

	Parms.Value = std::move(Value);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetFillColorParamTarget
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetFillColorParamTarget(const struct FLinearColor& Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetFillColorParamTarget");

	Params::WB_Container_Circular_C_SetFillColorParamTarget Parms{};

	Parms.Value = std::move(Value);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientColorParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetGradientColorParam(const struct FLinearColor& Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetGradientColorParam");

	Params::WB_Container_Circular_C_SetGradientColorParam Parms{};

	Parms.Value = std::move(Value);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientMaskParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture*                         Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetGradientMaskParam(class UTexture* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetGradientMaskParam");

	Params::WB_Container_Circular_C_SetGradientMaskParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientOpacity
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  GradientOpacity_0                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetGradientOpacity(double GradientOpacity_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetGradientOpacity");

	Params::WB_Container_Circular_C_SetGradientOpacity Parms{};

	Parms.GradientOpacity_0 = GradientOpacity_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientOpacityParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetGradientOpacityParam(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetGradientOpacityParam");

	Params::WB_Container_Circular_C_SetGradientOpacityParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetGradientType
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EGradientTypes                          GradientType                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetGradientType(EGradientTypes GradientType)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetGradientType");

	Params::WB_Container_Circular_C_SetGradientType Parms{};

	Parms.GradientType = GradientType;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeBGColorParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetMarqueeBGColorParam(const struct FLinearColor& Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetMarqueeBGColorParam");

	Params::WB_Container_Circular_C_SetMarqueeBGColorParam Parms{};

	Parms.Value = std::move(Value);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeColorParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetMarqueeColorParam(const struct FLinearColor& Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetMarqueeColorParam");

	Params::WB_Container_Circular_C_SetMarqueeColorParam Parms{};

	Parms.Value = std::move(Value);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeMask
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EMarqueeMask                            MarqueeMask                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UTexture2D*                       CustomMask                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetMarqueeMask(EMarqueeMask MarqueeMask, class UTexture2D* CustomMask)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetMarqueeMask");

	Params::WB_Container_Circular_C_SetMarqueeMask Parms{};

	Parms.MarqueeMask = MarqueeMask;
	Parms.CustomMask = CustomMask;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeMaskParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture*                         Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetMarqueeMaskParam(class UTexture* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetMarqueeMaskParam");

	Params::WB_Container_Circular_C_SetMarqueeMaskParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetMarqueeTime
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetMarqueeTime(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetMarqueeTime");

	Params::WB_Container_Circular_C_SetMarqueeTime Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Percent                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetPercent(double Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetPercent");

	Params::WB_Container_Circular_C_SetPercent Parms{};

	Parms.Percent = Percent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetPercentParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetPercentParam(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetPercentParam");

	Params::WB_Container_Circular_C_SetPercentParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetPercentParamTarget
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetPercentParamTarget(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetPercentParamTarget");

	Params::WB_Container_Circular_C_SetPercentParamTarget Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetProgressMethod
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressMethod                         ProgressMethod_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetProgressMethod(EProgressMethod ProgressMethod_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetProgressMethod");

	Params::WB_Container_Circular_C_SetProgressMethod Parms{};

	Parms.ProgressMethod_0 = ProgressMethod_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Size                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetSize(double Size)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetSize");

	Params::WB_Container_Circular_C_SetSize Parms{};

	Parms.Size = Size;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetSpacing
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Spacing_0                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetSpacing(double Spacing_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetSpacing");

	Params::WB_Container_Circular_C_SetSpacing Parms{};

	Parms.Spacing_0 = Spacing_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetSpacingParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetSpacingParam(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetSpacingParam");

	Params::WB_Container_Circular_C_SetSpacingParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetSpacingParamMarquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetSpacingParamMarquee(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetSpacingParamMarquee");

	Params::WB_Container_Circular_C_SetSpacingParamMarquee Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetStepDensity
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  HardStepBorder                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetStepDensity(double HardStepBorder)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetStepDensity");

	Params::WB_Container_Circular_C_SetStepDensity Parms{};

	Parms.HardStepBorder = HardStepBorder;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetStepDensityParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetStepDensityParam(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetStepDensityParam");

	Params::WB_Container_Circular_C_SetStepDensityParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetStepDensityParamMarquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetStepDensityParamMarquee(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetStepDensityParamMarquee");

	Params::WB_Container_Circular_C_SetStepDensityParamMarquee Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetSteps
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Steps_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetSteps(int32 Steps_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetSteps");

	Params::WB_Container_Circular_C_SetSteps Parms{};

	Parms.Steps_0 = Steps_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetStepsParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetStepsParam(int32 Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetStepsParam");

	Params::WB_Container_Circular_C_SetStepsParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetStepsParamMarquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetStepsParamMarquee(int32 Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetStepsParamMarquee");

	Params::WB_Container_Circular_C_SetStepsParamMarquee Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetTargetFillColor_Negative
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              FillColor_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetTargetFillColor_Negative(const struct FLinearColor& FillColor_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetTargetFillColor_Negative");

	Params::WB_Container_Circular_C_SetTargetFillColor_Negative Parms{};

	Parms.FillColor_0 = std::move(FillColor_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetTargetFillColor_Positive
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              FillColor_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetTargetFillColor_Positive(const struct FLinearColor& FillColor_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetTargetFillColor_Positive");

	Params::WB_Container_Circular_C_SetTargetFillColor_Positive Parms{};

	Parms.FillColor_0 = std::move(FillColor_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetTargetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  TargetPercent_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetTargetPercent(double TargetPercent_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetTargetPercent");

	Params::WB_Container_Circular_C_SetTargetPercent Parms{};

	Parms.TargetPercent_0 = TargetPercent_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetThickness
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Thickness_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetThickness(double Thickness_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetThickness");

	Params::WB_Container_Circular_C_SetThickness Parms{};

	Parms.Thickness_0 = Thickness_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetThicknessParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetThicknessParam(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetThicknessParam");

	Params::WB_Container_Circular_C_SetThicknessParam Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetThicknessParamMarquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetThicknessParamMarquee(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetThicknessParamMarquee");

	Params::WB_Container_Circular_C_SetThicknessParamMarquee Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetThicknessParamTarget
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetThicknessParamTarget(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetThicknessParamTarget");

	Params::WB_Container_Circular_C_SetThicknessParamTarget Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetTimeParamMarquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetTimeParamMarquee(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetTimeParamMarquee");

	Params::WB_Container_Circular_C_SetTimeParamMarquee Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetupMarquee
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bUseMarquee_0                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              MarqueeColor_0                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              MarqueeBackgroundColor                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetupMarquee(bool bUseMarquee_0, const struct FLinearColor& MarqueeColor_0, const struct FLinearColor& MarqueeBackgroundColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetupMarquee");

	Params::WB_Container_Circular_C_SetupMarquee Parms{};

	Parms.bUseMarquee_0 = bUseMarquee_0;
	Parms.MarqueeColor_0 = std::move(MarqueeColor_0);
	Parms.MarqueeBackgroundColor = std::move(MarqueeBackgroundColor);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetUseAbsoluteFillMethod
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bAbsoluteFillMethod_0                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetUseAbsoluteFillMethod(bool bAbsoluteFillMethod_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetUseAbsoluteFillMethod");

	Params::WB_Container_Circular_C_SetUseAbsoluteFillMethod Parms{};

	Parms.bAbsoluteFillMethod_0 = bAbsoluteFillMethod_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetUseGradient
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bUseGradient_0                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetUseGradient(bool bUseGradient_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetUseGradient");

	Params::WB_Container_Circular_C_SetUseGradient Parms{};

	Parms.bUseGradient_0 = bUseGradient_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetUseGradientParam
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    UseGradient                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetUseGradientParam(bool UseGradient)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetUseGradientParam");

	Params::WB_Container_Circular_C_SetUseGradientParam Parms{};

	Parms.UseGradient = UseGradient;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.SetUseTargetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bIsTargetPercent                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::SetUseTargetPercent(bool bIsTargetPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "SetUseTargetPercent");

	Params::WB_Container_Circular_C_SetUseTargetPercent Parms{};

	Parms.bIsTargetPercent = bIsTargetPercent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.StartTriggerProgressChangeColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              ProgressChangeColor_0                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::StartTriggerProgressChangeColor(const struct FLinearColor& ProgressChangeColor_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "StartTriggerProgressChangeColor");

	Params::WB_Container_Circular_C_StartTriggerProgressChangeColor Parms{};

	Parms.ProgressChangeColor_0 = std::move(ProgressChangeColor_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.StopTriggerProgressChangeColor
// (BlueprintCallable, BlueprintEvent)

void UWB_Container_Circular_C::StopTriggerProgressChangeColor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "StopTriggerProgressChangeColor");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Circular.WB_Container_Circular_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "Tick");

	Params::WB_Container_Circular_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.UpdatePercent
// (BlueprintCallable, BlueprintEvent)

void UWB_Container_Circular_C::UpdatePercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "UpdatePercent");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Circular.WB_Container_Circular_C.UpdateProgressChangeColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              NewColor                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  InterpSpeed                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    IsChanging                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Container_Circular_C::UpdateProgressChangeColor(const struct FLinearColor& NewColor, double InterpSpeed, bool IsChanging)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "UpdateProgressChangeColor");

	Params::WB_Container_Circular_C_UpdateProgressChangeColor Parms{};

	Parms.NewColor = std::move(NewColor);
	Parms.InterpSpeed = InterpSpeed;
	Parms.IsChanging = IsChanging;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Container_Circular.WB_Container_Circular_C.UpdateStaticPercent
// (Public, BlueprintCallable, BlueprintEvent)

void UWB_Container_Circular_C::UpdateStaticPercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "UpdateStaticPercent");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Container_Circular.WB_Container_Circular_C.UpdateTargetPercent
// (BlueprintCallable, BlueprintEvent)

void UWB_Container_Circular_C::UpdateTargetPercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Container_Circular_C", "UpdateTargetPercent");

	UObject::ProcessEvent(Func, nullptr);
}

}

