﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: ZombieBoss_EnemyInRange

#include "Basic.hpp"

#include "ZombieBoss_EnemyInRange_classes.hpp"
#include "ZombieBoss_EnemyInRange_parameters.hpp"


namespace SDK
{

// Function ZombieBoss_EnemyInRange.ZombieBoss_EnemyInRange_C.ExecuteUbergraph_ZombieBoss_EnemyInRange
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UZombieBoss_EnemyInRange_C::ExecuteUbergraph_ZombieBoss_EnemyInRange(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZombieBoss_EnemyInRange_C", "ExecuteUbergraph_ZombieBoss_EnemyInRange");

	Params::ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function ZombieBoss_EnemyInRange.ZombieBoss_EnemyInRange_C.ReceiveActivationAI
// (Event, Protected, BlueprintEvent)
// Parameters:
// class AAIController*                    OwnerController                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class APawn*                            ControlledPawn                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UZombieBoss_EnemyInRange_C::ReceiveActivationAI(class AAIController* OwnerController, class APawn* ControlledPawn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZombieBoss_EnemyInRange_C", "ReceiveActivationAI");

	Params::ZombieBoss_EnemyInRange_C_ReceiveActivationAI Parms{};

	Parms.OwnerController = OwnerController;
	Parms.ControlledPawn = ControlledPawn;

	UObject::ProcessEvent(Func, &Parms);
}

}

