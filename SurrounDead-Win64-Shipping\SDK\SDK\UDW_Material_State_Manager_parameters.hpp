﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDW_Material_State_Manager

#include "Basic.hpp"

#include "Engine_structs.hpp"


namespace SDK::Params
{

// Function UDW_Material_State_Manager.UDW_Material_State_Manager_C.Apply New State
// 0x0018 (0x0018 - 0x0000)
struct UDW_Material_State_Manager_C_Apply_New_State final
{
public:
	double                                        Snow;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Wetness;                                           // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust;                                              // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, <PERSON>rm, ZeroConstructor, Is<PERSON><PERSON>OldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Material_State_Manager_C_Apply_New_State) == 0x000008, "Wrong alignment on UDW_Material_State_Manager_C_Apply_New_State");
static_assert(sizeof(UDW_Material_State_Manager_C_Apply_New_State) == 0x000018, "Wrong size on UDW_Material_State_Manager_C_Apply_New_State");
static_assert(offsetof(UDW_Material_State_Manager_C_Apply_New_State, Snow) == 0x000000, "Member 'UDW_Material_State_Manager_C_Apply_New_State::Snow' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Apply_New_State, Wetness) == 0x000008, "Member 'UDW_Material_State_Manager_C_Apply_New_State::Wetness' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Apply_New_State, Dust) == 0x000010, "Member 'UDW_Material_State_Manager_C_Apply_New_State::Dust' has a wrong offset!");

// Function UDW_Material_State_Manager.UDW_Material_State_Manager_C.Current Dust Change Speed
// 0x0070 (0x0070 - 0x0000)
struct UDW_Material_State_Manager_C_Current_Dust_Change_Speed final
{
public:
	double                                        Out;                                               // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Lerp_ReturnValue;                         // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMax_ReturnValue;                         // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_2;        // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39[0x7];                                       // 0x0039(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMax_ReturnValue_1;                       // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_3;        // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_1;       // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed) == 0x000008, "Wrong alignment on UDW_Material_State_Manager_C_Current_Dust_Change_Speed");
static_assert(sizeof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed) == 0x000070, "Wrong size on UDW_Material_State_Manager_C_Current_Dust_Change_Speed");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, Out) == 0x000000, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::Out' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000008, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000010, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000018, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Lerp_ReturnValue) == 0x000020, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Lerp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_FMax_ReturnValue) == 0x000028, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_FMax_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue_2) == 0x000030, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000038, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000040, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000048, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_FMax_ReturnValue_1) == 0x000050, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_FMax_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue_3) == 0x000058, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Greater_DoubleDouble_ReturnValue_1) == 0x000060, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Greater_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Dust_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x000068, "Member 'UDW_Material_State_Manager_C_Current_Dust_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");

// Function UDW_Material_State_Manager.UDW_Material_State_Manager_C.Current Snow Change Speed
// 0x0080 (0x0080 - 0x0000)
struct UDW_Material_State_Manager_C_Current_Snow_Change_Speed final
{
public:
	double                                        Out;                                               // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Get_Temperature_Out;                      // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Convert_Temperature_Scale_Output;         // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_SelectFloat_ReturnValue;                  // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMax_ReturnValue;                         // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_1;       // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39[0x7];                                       // 0x0039(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51[0x7];                                       // 0x0051(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMax_ReturnValue_1;                       // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_2;       // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_69[0x7];                                       // 0x0069(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_2;        // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed) == 0x000008, "Wrong alignment on UDW_Material_State_Manager_C_Current_Snow_Change_Speed");
static_assert(sizeof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed) == 0x000080, "Wrong size on UDW_Material_State_Manager_C_Current_Snow_Change_Speed");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, Out) == 0x000000, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::Out' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Get_Temperature_Out) == 0x000008, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Get_Temperature_Out' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Convert_Temperature_Scale_Output) == 0x000010, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Convert_Temperature_Scale_Output' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000018, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_SelectFloat_ReturnValue) == 0x000020, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_SelectFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_FMax_ReturnValue) == 0x000028, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_FMax_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000030, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Greater_DoubleDouble_ReturnValue_1) == 0x000038, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Greater_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000040, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000048, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000050, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000058, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_FMax_ReturnValue_1) == 0x000060, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_FMax_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Greater_DoubleDouble_ReturnValue_2) == 0x000068, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Greater_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue_2) == 0x000070, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Snow_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x000078, "Member 'UDW_Material_State_Manager_C_Current_Snow_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");

// Function UDW_Material_State_Manager.UDW_Material_State_Manager_C.Current Wetness Change Speed
// 0x00A0 (0x00A0 - 0x0000)
struct UDW_Material_State_Manager_C_Current_Wetness_Change_Speed final
{
public:
	double                                        Out;                                               // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Is_it_Daytime__Yes;                       // 0x0029(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2A[0x6];                                       // 0x002A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_SelectFloat_ReturnValue;                  // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Lerp_ReturnValue;                         // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_1;       // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_49[0x7];                                       // 0x0049(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FMax_ReturnValue;                         // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_1;          // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_3;      // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMax_ReturnValue_1;                       // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_2;        // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_2;       // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_91[0x7];                                       // 0x0091(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_4;      // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed) == 0x000008, "Wrong alignment on UDW_Material_State_Manager_C_Current_Wetness_Change_Speed");
static_assert(sizeof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed) == 0x0000A0, "Wrong size on UDW_Material_State_Manager_C_Current_Wetness_Change_Speed");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, Out) == 0x000000, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::Out' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000008, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000010, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000018, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000020, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_BooleanAND_ReturnValue) == 0x000028, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Is_it_Daytime__Yes) == 0x000029, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Is_it_Daytime__Yes' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_SelectFloat_ReturnValue) == 0x000030, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_SelectFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_MapRangeClamped_ReturnValue) == 0x000038, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Lerp_ReturnValue) == 0x000040, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Lerp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Greater_DoubleDouble_ReturnValue_1) == 0x000048, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Greater_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_FMax_ReturnValue) == 0x000050, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_FMax_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000058, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Less_DoubleDouble_ReturnValue_1) == 0x000060, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Less_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x000068, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue_3) == 0x000070, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000078, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_FMax_ReturnValue_1) == 0x000080, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_FMax_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Divide_DoubleDouble_ReturnValue_2) == 0x000088, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Divide_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Greater_DoubleDouble_ReturnValue_2) == 0x000090, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Greater_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Current_Wetness_Change_Speed, CallFunc_Multiply_DoubleDouble_ReturnValue_4) == 0x000098, "Member 'UDW_Material_State_Manager_C_Current_Wetness_Change_Speed::CallFunc_Multiply_DoubleDouble_ReturnValue_4' has a wrong offset!");

// Function UDW_Material_State_Manager.UDW_Material_State_Manager_C.ExecuteUbergraph_UDW_Material_State_Manager
// 0x0004 (0x0004 - 0x0000)
struct UDW_Material_State_Manager_C_ExecuteUbergraph_UDW_Material_State_Manager final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Material_State_Manager_C_ExecuteUbergraph_UDW_Material_State_Manager) == 0x000004, "Wrong alignment on UDW_Material_State_Manager_C_ExecuteUbergraph_UDW_Material_State_Manager");
static_assert(sizeof(UDW_Material_State_Manager_C_ExecuteUbergraph_UDW_Material_State_Manager) == 0x000004, "Wrong size on UDW_Material_State_Manager_C_ExecuteUbergraph_UDW_Material_State_Manager");
static_assert(offsetof(UDW_Material_State_Manager_C_ExecuteUbergraph_UDW_Material_State_Manager, EntryPoint) == 0x000000, "Member 'UDW_Material_State_Manager_C_ExecuteUbergraph_UDW_Material_State_Manager::EntryPoint' has a wrong offset!");

// Function UDW_Material_State_Manager.UDW_Material_State_Manager_C.Increment Material State
// 0x00E0 (0x00E0 - 0x0000)
struct UDW_Material_State_Manager_C_Increment_Material_State final
{
public:
	bool                                          Changed;                                           // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AActor*                                 CallFunc_GetOwner_ReturnValue;                     // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasAuthority_ReturnValue;                 // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_DoubleDouble_ReturnValue;    // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12[0x6];                                       // 0x0012(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_22[0x6];                                       // 0x0022(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DoubleDouble_ReturnValue;      // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39[0x7];                                       // 0x0039(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DoubleDouble_ReturnValue_1;    // 0x0049(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_1;          // 0x004A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x004B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_1;       // 0x004C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x004D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4E[0x2];                                       // 0x004E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_1;           // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DoubleDouble_ReturnValue_2;    // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_59[0x7];                                       // 0x0059(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FClamp_ReturnValue_1;                     // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DoubleDouble_ReturnValue_3;    // 0x0069(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_3;                 // 0x006A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6B[0x5];                                       // 0x006B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Abs_ReturnValue;                          // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Abs_ReturnValue_1;                        // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Abs_ReturnValue_2;                        // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_2;           // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_3;           // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_2;          // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_2;       // 0x00A9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_3;       // 0x00AA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AB[0x5];                                       // 0x00AB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_4;           // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue_2;                     // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DoubleDouble_ReturnValue_4;    // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_4;                 // 0x00C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DoubleDouble_ReturnValue_5;    // 0x00C2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_5;                 // 0x00C3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_2;                  // 0x00C4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C5[0x3];                                       // 0x00C5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_A_ImplicitCast;     // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_A_ImplicitCast_1;   // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_A_ImplicitCast_2;   // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Material_State_Manager_C_Increment_Material_State) == 0x000008, "Wrong alignment on UDW_Material_State_Manager_C_Increment_Material_State");
static_assert(sizeof(UDW_Material_State_Manager_C_Increment_Material_State) == 0x0000E0, "Wrong size on UDW_Material_State_Manager_C_Increment_Material_State");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, Changed) == 0x000000, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::Changed' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_GetOwner_ReturnValue) == 0x000008, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_GetOwner_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_HasAuthority_ReturnValue) == 0x000010, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_HasAuthority_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_GreaterEqual_DoubleDouble_ReturnValue) == 0x000011, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_GreaterEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000018, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000020, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000021, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000028, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Add_DoubleDouble_ReturnValue) == 0x000030, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_EqualEqual_DoubleDouble_ReturnValue) == 0x000038, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_EqualEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_FClamp_ReturnValue) == 0x000040, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_BooleanAND_ReturnValue) == 0x000048, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_EqualEqual_DoubleDouble_ReturnValue_1) == 0x000049, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_EqualEqual_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Less_DoubleDouble_ReturnValue_1) == 0x00004A, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Less_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_BooleanAND_ReturnValue_1) == 0x00004B, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Greater_DoubleDouble_ReturnValue_1) == 0x00004C, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Greater_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_BooleanOR_ReturnValue) == 0x00004D, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Add_DoubleDouble_ReturnValue_1) == 0x000050, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Add_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_EqualEqual_DoubleDouble_ReturnValue_2) == 0x000058, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_EqualEqual_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_FClamp_ReturnValue_1) == 0x000060, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_FClamp_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_BooleanAND_ReturnValue_2) == 0x000068, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_EqualEqual_DoubleDouble_ReturnValue_3) == 0x000069, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_EqualEqual_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_BooleanAND_ReturnValue_3) == 0x00006A, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_BooleanAND_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Abs_ReturnValue) == 0x000070, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Abs_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_BooleanOR_ReturnValue_1) == 0x000078, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Abs_ReturnValue_1) == 0x000080, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Abs_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Abs_ReturnValue_2) == 0x000088, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Abs_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Add_DoubleDouble_ReturnValue_2) == 0x000090, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Add_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x000098, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Add_DoubleDouble_ReturnValue_3) == 0x0000A0, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Add_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Less_DoubleDouble_ReturnValue_2) == 0x0000A8, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Less_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Greater_DoubleDouble_ReturnValue_2) == 0x0000A9, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Greater_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Greater_DoubleDouble_ReturnValue_3) == 0x0000AA, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Greater_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Add_DoubleDouble_ReturnValue_4) == 0x0000B0, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Add_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_FClamp_ReturnValue_2) == 0x0000B8, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_FClamp_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_EqualEqual_DoubleDouble_ReturnValue_4) == 0x0000C0, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_EqualEqual_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_BooleanAND_ReturnValue_4) == 0x0000C1, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_BooleanAND_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_EqualEqual_DoubleDouble_ReturnValue_5) == 0x0000C2, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_EqualEqual_DoubleDouble_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_BooleanAND_ReturnValue_5) == 0x0000C3, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_BooleanAND_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_BooleanOR_ReturnValue_2) == 0x0000C4, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_BooleanOR_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Multiply_DoubleDouble_A_ImplicitCast) == 0x0000C8, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Multiply_DoubleDouble_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Multiply_DoubleDouble_A_ImplicitCast_1) == 0x0000D0, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Multiply_DoubleDouble_A_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Increment_Material_State, CallFunc_Multiply_DoubleDouble_A_ImplicitCast_2) == 0x0000D8, "Member 'UDW_Material_State_Manager_C_Increment_Material_State::CallFunc_Multiply_DoubleDouble_A_ImplicitCast_2' has a wrong offset!");

// Function UDW_Material_State_Manager.UDW_Material_State_Manager_C.Start Material State Sim
// 0x0038 (0x0038 - 0x0000)
struct UDW_Material_State_Manager_C_Start_Material_State_Sim final
{
public:
	class AUltra_Dynamic_Weather_C*               UDW_0;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Weather_State_0;                                   // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUDW_Temperature_Manager_C*             Temp_Manager;                                      // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 CallFunc_GetOwner_ReturnValue;                     // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasAuthority_ReturnValue;                 // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           CallFunc_K2_SetTimer_ReturnValue;                  // 0x0028(0x0008)(NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           CallFunc_K2_SetTimer_ReturnValue_1;                // 0x0030(0x0008)(NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Material_State_Manager_C_Start_Material_State_Sim) == 0x000008, "Wrong alignment on UDW_Material_State_Manager_C_Start_Material_State_Sim");
static_assert(sizeof(UDW_Material_State_Manager_C_Start_Material_State_Sim) == 0x000038, "Wrong size on UDW_Material_State_Manager_C_Start_Material_State_Sim");
static_assert(offsetof(UDW_Material_State_Manager_C_Start_Material_State_Sim, UDW_0) == 0x000000, "Member 'UDW_Material_State_Manager_C_Start_Material_State_Sim::UDW_0' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Start_Material_State_Sim, Weather_State_0) == 0x000008, "Member 'UDW_Material_State_Manager_C_Start_Material_State_Sim::Weather_State_0' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Start_Material_State_Sim, Temp_Manager) == 0x000010, "Member 'UDW_Material_State_Manager_C_Start_Material_State_Sim::Temp_Manager' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Start_Material_State_Sim, CallFunc_GetOwner_ReturnValue) == 0x000018, "Member 'UDW_Material_State_Manager_C_Start_Material_State_Sim::CallFunc_GetOwner_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Start_Material_State_Sim, CallFunc_HasAuthority_ReturnValue) == 0x000020, "Member 'UDW_Material_State_Manager_C_Start_Material_State_Sim::CallFunc_HasAuthority_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Start_Material_State_Sim, CallFunc_K2_SetTimer_ReturnValue) == 0x000028, "Member 'UDW_Material_State_Manager_C_Start_Material_State_Sim::CallFunc_K2_SetTimer_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Start_Material_State_Sim, CallFunc_K2_SetTimer_ReturnValue_1) == 0x000030, "Member 'UDW_Material_State_Manager_C_Start_Material_State_Sim::CallFunc_K2_SetTimer_ReturnValue_1' has a wrong offset!");

// Function UDW_Material_State_Manager.UDW_Material_State_Manager_C.Update Change Speeds
// 0x0018 (0x0018 - 0x0000)
struct UDW_Material_State_Manager_C_Update_Change_Speeds final
{
public:
	double                                        CallFunc_Current_Dust_Change_Speed_Out;            // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Current_Wetness_Change_Speed_Out;         // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Current_Snow_Change_Speed_Out;            // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Material_State_Manager_C_Update_Change_Speeds) == 0x000008, "Wrong alignment on UDW_Material_State_Manager_C_Update_Change_Speeds");
static_assert(sizeof(UDW_Material_State_Manager_C_Update_Change_Speeds) == 0x000018, "Wrong size on UDW_Material_State_Manager_C_Update_Change_Speeds");
static_assert(offsetof(UDW_Material_State_Manager_C_Update_Change_Speeds, CallFunc_Current_Dust_Change_Speed_Out) == 0x000000, "Member 'UDW_Material_State_Manager_C_Update_Change_Speeds::CallFunc_Current_Dust_Change_Speed_Out' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Update_Change_Speeds, CallFunc_Current_Wetness_Change_Speed_Out) == 0x000008, "Member 'UDW_Material_State_Manager_C_Update_Change_Speeds::CallFunc_Current_Wetness_Change_Speed_Out' has a wrong offset!");
static_assert(offsetof(UDW_Material_State_Manager_C_Update_Change_Speeds, CallFunc_Current_Snow_Change_Speed_Out) == 0x000010, "Member 'UDW_Material_State_Manager_C_Update_Change_Speeds::CallFunc_Current_Snow_Change_Speed_Out' has a wrong offset!");

}

