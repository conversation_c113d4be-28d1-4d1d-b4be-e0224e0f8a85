﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_Chase

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AIModule_structs.hpp"
#include "AIModule_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass Zombie_Chase.Zombie_Chase_C
// 0x0030 (0x00D8 - 0x00A8)
class UZombie_Chase_C final : public UBTTask_BlueprintBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x00A8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	struct FBlackboardKeySelector                 Target;                                            // 0x00B0(0x0028)(Edit, BlueprintVisible)

public:
	void ExecuteUbergraph_Zombie_Chase(int32 EntryPoint);
	void ReceiveExecuteAI(class AAIController* OwnerController, class APawn* ControlledPawn);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Zombie_Chase_C">();
	}
	static class UZombie_Chase_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UZombie_Chase_C>();
	}
};
static_assert(alignof(UZombie_Chase_C) == 0x000008, "Wrong alignment on UZombie_Chase_C");
static_assert(sizeof(UZombie_Chase_C) == 0x0000D8, "Wrong size on UZombie_Chase_C");
static_assert(offsetof(UZombie_Chase_C, UberGraphFrame) == 0x0000A8, "Member 'UZombie_Chase_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UZombie_Chase_C, Target) == 0x0000B0, "Member 'UZombie_Chase_C::Target' has a wrong offset!");

}

