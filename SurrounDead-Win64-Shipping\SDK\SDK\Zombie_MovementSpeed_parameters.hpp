﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_MovementSpeed

#include "Basic.hpp"


namespace SDK::Params
{

// Function Zombie_MovementSpeed.Zombie_MovementSpeed_C.ExecuteUbergraph_Zombie_MovementSpeed
// 0x00B8 (0x00B8 - 0x0000)
struct Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TScriptInterface<class IBPI_AI_C>             K2Node_DynamicCast_AsBPI_AI;                       // 0x0008(0x0010)(ZeroConstructor, Is<PERSON><PERSON>OldData, NoDestructor, HasGetVal<PERSON>TypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TScriptInterface<class IBPI_AI_C>             K2Node_DynamicCast_AsBPI_AI_1;                     // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetMovementSpeeds_RoamingSpeed;           // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMovementSpeeds_AlertSpeed;             // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMovementSpeeds_AttackSpeed;            // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMovementSpeeds_RoamingSpeed_1;         // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMovementSpeeds_AlertSpeed_1;           // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMovementSpeeds_AttackSpeed_1;          // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TScriptInterface<class IBPI_AI_C>             K2Node_DynamicCast_AsBPI_AI_2;                     // 0x0068(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AAIController*                          K2Node_Event_OwnerController;                      // 0x0080(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  K2Node_Event_ControlledPawn;                       // 0x0088(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMovementSpeeds_RoamingSpeed_2;         // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMovementSpeeds_AlertSpeed_2;           // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetMovementSpeeds_AttackSpeed_2;          // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ABP_MasterZombie_C*                     K2Node_DynamicCast_AsBP_Master_Zombie;             // 0x00A8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_3;                     // 0x00B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x00B1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed) == 0x000008, "Wrong alignment on Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed");
static_assert(sizeof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed) == 0x0000B8, "Wrong size on Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, EntryPoint) == 0x000000, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::EntryPoint' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_DynamicCast_AsBPI_AI) == 0x000008, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_DynamicCast_AsBPI_AI' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_DynamicCast_AsBPI_AI_1) == 0x000020, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_DynamicCast_AsBPI_AI_1' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_DynamicCast_bSuccess_1) == 0x000030, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, CallFunc_GetMovementSpeeds_RoamingSpeed) == 0x000038, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::CallFunc_GetMovementSpeeds_RoamingSpeed' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, CallFunc_GetMovementSpeeds_AlertSpeed) == 0x000040, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::CallFunc_GetMovementSpeeds_AlertSpeed' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, CallFunc_GetMovementSpeeds_AttackSpeed) == 0x000048, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::CallFunc_GetMovementSpeeds_AttackSpeed' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, CallFunc_GetMovementSpeeds_RoamingSpeed_1) == 0x000050, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::CallFunc_GetMovementSpeeds_RoamingSpeed_1' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, CallFunc_GetMovementSpeeds_AlertSpeed_1) == 0x000058, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::CallFunc_GetMovementSpeeds_AlertSpeed_1' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, CallFunc_GetMovementSpeeds_AttackSpeed_1) == 0x000060, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::CallFunc_GetMovementSpeeds_AttackSpeed_1' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_DynamicCast_AsBPI_AI_2) == 0x000068, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_DynamicCast_AsBPI_AI_2' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_DynamicCast_bSuccess_2) == 0x000078, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_Event_OwnerController) == 0x000080, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_Event_OwnerController' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_Event_ControlledPawn) == 0x000088, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_Event_ControlledPawn' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, CallFunc_GetMovementSpeeds_RoamingSpeed_2) == 0x000090, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::CallFunc_GetMovementSpeeds_RoamingSpeed_2' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, CallFunc_GetMovementSpeeds_AlertSpeed_2) == 0x000098, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::CallFunc_GetMovementSpeeds_AlertSpeed_2' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, CallFunc_GetMovementSpeeds_AttackSpeed_2) == 0x0000A0, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::CallFunc_GetMovementSpeeds_AttackSpeed_2' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_DynamicCast_AsBP_Master_Zombie) == 0x0000A8, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_DynamicCast_AsBP_Master_Zombie' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_DynamicCast_bSuccess_3) == 0x0000B0, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_DynamicCast_bSuccess_3' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed, K2Node_SwitchEnum_CmpSuccess) == 0x0000B1, "Member 'Zombie_MovementSpeed_C_ExecuteUbergraph_Zombie_MovementSpeed::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");

// Function Zombie_MovementSpeed.Zombie_MovementSpeed_C.ReceiveActivationAI
// 0x0010 (0x0010 - 0x0000)
struct Zombie_MovementSpeed_C_ReceiveActivationAI final
{
public:
	class AAIController*                          OwnerController;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  ControlledPawn;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Zombie_MovementSpeed_C_ReceiveActivationAI) == 0x000008, "Wrong alignment on Zombie_MovementSpeed_C_ReceiveActivationAI");
static_assert(sizeof(Zombie_MovementSpeed_C_ReceiveActivationAI) == 0x000010, "Wrong size on Zombie_MovementSpeed_C_ReceiveActivationAI");
static_assert(offsetof(Zombie_MovementSpeed_C_ReceiveActivationAI, OwnerController) == 0x000000, "Member 'Zombie_MovementSpeed_C_ReceiveActivationAI::OwnerController' has a wrong offset!");
static_assert(offsetof(Zombie_MovementSpeed_C_ReceiveActivationAI, ControlledPawn) == 0x000008, "Member 'Zombie_MovementSpeed_C_ReceiveActivationAI::ControlledPawn' has a wrong offset!");

}

