﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NotificationBox

#include "Basic.hpp"

#include "CommonUI_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WBP_NotificationBox.WBP_NotificationBox_C
// 0x0020 (0x0308 - 0x02E8)
class UWBP_NotificationBox_C final : public UCommonUserWidget
{
public:
	class UVerticalBox*                           VerticalBox_Notifications;                         // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   Text;                                              // 0x02F0(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)

public:
	void NotificationExpired(class UWBP_NarrativeHUDNotification_C* Notification);
	void ShowNotification(const class FText& Text_0, double Duration);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WBP_NotificationBox_C">();
	}
	static class UWBP_NotificationBox_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWBP_NotificationBox_C>();
	}
};
static_assert(alignof(UWBP_NotificationBox_C) == 0x000008, "Wrong alignment on UWBP_NotificationBox_C");
static_assert(sizeof(UWBP_NotificationBox_C) == 0x000308, "Wrong size on UWBP_NotificationBox_C");
static_assert(offsetof(UWBP_NotificationBox_C, VerticalBox_Notifications) == 0x0002E8, "Member 'UWBP_NotificationBox_C::VerticalBox_Notifications' has a wrong offset!");
static_assert(offsetof(UWBP_NotificationBox_C, Text) == 0x0002F0, "Member 'UWBP_NotificationBox_C::Text' has a wrong offset!");

}

