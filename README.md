# Call of Duty: Modern Warfare Controller Mod for SurrounDead

This mod transforms SurrounDead's controller support to match the exact control scheme of Call of Duty: Modern Warfare, providing a familiar console FPS experience.

## 🎮 Features

### **Perfect COD Controls**
- **Identical Layout**: Matches COD MW Xbox controller mapping exactly
- **Advanced Input Processing**: Direct memory injection for zero input lag
- **Console-Style Aim Assist**: Optional aim assistance for controller users
- **Custom Sensitivity Curves**: Precise aiming with smooth acceleration
- **Adaptive Sensitivity**: Different sensitivity when aiming down sights

### **Advanced Controller Features**
- **Real-time Input Processing**: ~1000 FPS input polling for maximum responsiveness
- **Customizable Deadzones**: Per-stick deadzone configuration
- **Vibration Support**: Haptic feedback for immersive gameplay
- **Button Remapping**: Fully customizable control layout
- **Multi-Controller Support**: Support for multiple connected controllers

## 🎯 Control Layout

| **Xbox Button** | **Function** | **Description** |
|-----------------|--------------|-----------------|
| **Left Stick** | Movement | Forward/Back/Strafe Left/Right |
| **Right Stick** | Camera | Look/Turn/Aim |
| **A Button** | Jump | Jump/Vault over obstacles |
| **B Button** | Reload | Reload current weapon |
| **X Button** | Use/Interact | Interact with objects/doors |
| **Y Button** | Switch Weapon | Cycle through weapons |
| **LT (Left Trigger)** | Aim Down Sights | Precision aiming |
| **RT (Right Trigger)** | Fire | Shoot weapon |
| **LB (Left Bumper)** | Grenade | Throw frag grenade |
| **RB (Right Bumper)** | Tactical | Use tactical equipment |
| **LS Click** | Sprint | Hold to sprint |
| **RS Click** | Melee | Knife/melee attack |
| **D-Pad Up** | Prone | Go prone |
| **D-Pad Down** | Crouch | Crouch/slide |
| **D-Pad Left** | Last Weapon | Switch to previous weapon |
| **D-Pad Right** | Inventory | Open inventory |
| **Back/View** | Scoreboard | Show player scores |
| **Start/Menu** | Pause Menu | Open game menu |

## 🛠️ Technical Implementation

### **SDK-Based Architecture**
- Uses complete SurrounDead SDK for direct game integration
- Memory injection for real-time input processing
- Hooks into Unreal Engine's input system
- Direct access to player controller and character classes

### **Key Components**
- **CODControllerMod Class**: Main controller processing engine
- **Input Thread**: Dedicated thread for input polling
- **Memory Management**: Safe memory access with error handling
- **Settings System**: Persistent configuration storage

## 📋 Requirements

### **System Requirements**
- Windows 10/11 (64-bit)
- Xbox Controller (wired or wireless with adapter)
- SurrounDead (Steam version)
- Visual Studio 2019/2022 or compatible C++ compiler
- CMake 3.16 or higher

### **Development Requirements**
- Windows SDK
- XInput library
- C++17 compiler support

## 🔧 Building the Mod

### **1. Clone and Prepare**
```bash
# The SDK files should already be in SurrounDead-Win64-Shipping/SDK/
# Make sure all SDK files are present
```

### **2. Build with CMake**
```bash
# Create build directory
mkdir build
cd build

# Configure
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build
cmake --build . --config Release

# Install (optional)
cmake --install . --prefix "C:\Program Files (x86)\Steam\steamapps\common\SurrounDead"
```

### **3. Manual Build (Visual Studio)**
```bash
# Open Visual Studio Developer Command Prompt
# Navigate to project directory
# Build the DLL
cl /LD /EHsc /std:c++17 COD_Controller_Main.cpp /link xinput.lib user32.lib kernel32.lib /OUT:COD_Controller_Mod.dll
```

## 📦 Installation

### **Automatic Installation**
1. Build the project using CMake
2. Run the generated `install_mod.bat` script
3. Follow the on-screen instructions

### **Manual Installation**
1. Copy `COD_Controller_Mod.dll` to your SurrounDead game directory
2. Create a `COD_Controller_Config` folder in the game directory
3. Copy configuration files to the config folder
4. Use a DLL injector to load the mod when starting the game

## ⚙️ Configuration

### **Settings File Location**
```
SurrounDead/COD_Controller_Config/settings.ini
```

### **Available Settings**
```ini
[Controller]
LookSensitivity=2.5          # Camera sensitivity (0.1-10.0)
MoveSensitivity=1.0          # Movement sensitivity (0.1-5.0)
ADSSensitivityMultiplier=0.6 # ADS sensitivity reduction (0.1-1.0)
Deadzone=0.25                # Stick deadzone (0.0-0.9)
TriggerDeadzone=0.1          # Trigger deadzone (0.0-0.5)
InvertY=false                # Invert Y-axis (true/false)
VibrationEnabled=true        # Controller vibration (true/false)
AimAssistEnabled=true        # Aim assist (true/false)
AimAssistStrength=0.6        # Aim assist strength (0.0-1.0)
```

## 🎮 Usage

### **Starting the Game**
1. **Connect Xbox Controller**: Ensure controller is connected and recognized
2. **Launch Game**: Use `Launch_With_COD_Controls.bat` or inject DLL manually
3. **Verify Connection**: Check console output for "Controller connected: Yes"
4. **Play**: Enjoy COD-style controls!

### **Console Commands** (Debug Mode)
```
help                    - Show available commands
status                  - Show controller connection status
sensitivity <value>     - Set look sensitivity (0.1-10.0)
aimassist on/off       - Toggle aim assist
reload                 - Reload settings from file
exit                   - Exit the mod
```

## 🔍 Troubleshooting

### **Controller Not Detected**
- Ensure Xbox controller is properly connected
- Check Windows Device Manager for controller
- Try reconnecting the controller
- Restart the game with controller connected

### **Controls Not Working**
- Verify DLL injection was successful
- Check game console for error messages
- Ensure game is running in windowed or borderless mode
- Try running as administrator

### **Performance Issues**
- Lower input polling rate in settings
- Disable vibration if experiencing lag
- Close other applications using controller input

## 🚀 Advanced Features

### **Aim Assist System**
- **Target Acquisition**: Subtle aim assistance when near targets
- **Sensitivity Scaling**: Reduced sensitivity when aiming at enemies
- **Customizable Strength**: Adjustable from 0% to 100%

### **Sensitivity Curves**
- **Quadratic Curve**: More precision at low input values
- **Linear Scaling**: Consistent response across full range
- **Custom Profiles**: Different curves for different situations

### **Vibration Feedback**
- **Weapon Fire**: Haptic feedback when shooting
- **Damage Taken**: Controller vibration when hit
- **Environmental**: Feedback for explosions, impacts

## 📝 Known Limitations

- Requires DLL injection (may trigger antivirus)
- Some game functions may need manual mapping
- Performance depends on system specifications
- May need updates for game patches

## 🔄 Updates and Maintenance

The mod may need updates when:
- SurrounDead receives major updates
- Game engine version changes
- New controller features are added
- Bug fixes are required

## 📄 License

This mod is provided as-is for educational and personal use. Use at your own risk.

## 🤝 Contributing

Contributions welcome! Please:
- Test thoroughly before submitting
- Follow existing code style
- Document any changes
- Report bugs with detailed information

## 📞 Support

For issues or questions:
- Check troubleshooting section first
- Review console output for errors
- Provide system specifications when reporting bugs
- Include log files when possible

---

**Enjoy your Call of Duty-style gaming experience in SurrounDead!** 🎮
