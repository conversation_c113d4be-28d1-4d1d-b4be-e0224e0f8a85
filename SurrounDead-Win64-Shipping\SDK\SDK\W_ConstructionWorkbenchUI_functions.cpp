﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_ConstructionWorkbenchUI

#include "Basic.hpp"

#include "W_ConstructionWorkbenchUI_classes.hpp"
#include "W_ConstructionWorkbenchUI_parameters.hpp"


namespace SDK
{

// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.Add Required Items
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::Add_Required_Items()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "Add Required Items");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.BindEvents
// (BlueprintCallable, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::BindEvents()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "BindEvents");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.BndEvt__BP_CraftingWidget_Button_Decrease_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::BndEvt__BP_CraftingWidget_Button_Decrease_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "BndEvt__BP_CraftingWidget_Button_Decrease_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.BndEvt__BP_CraftingWidget_Button_Increase_K2Node_ComponentBoundEvent_3_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::BndEvt__BP_CraftingWidget_Button_Increase_K2Node_ComponentBoundEvent_3_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "BndEvt__BP_CraftingWidget_Button_Increase_K2Node_ComponentBoundEvent_3_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.BndEvt__Button_81_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::BndEvt__Button_81_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "BndEvt__Button_81_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.CheckIngredientsAvailability
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   Proceed                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::CheckIngredientsAvailability(bool* Proceed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "CheckIngredientsAvailability");

	Params::W_ConstructionWorkbenchUI_C_CheckIngredientsAvailability Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Proceed != nullptr)
		*Proceed = Parms.Proceed;
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.EventOnMouseButtonDown
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSIContainer_C*                  Container                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSI_Slot_C*                      SlotRef                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// const struct FKey&                      Button                                                 (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::EventOnMouseButtonDown(class UJSIContainer_C* Container, class UJSI_Slot_C* SlotRef, const struct FKey& Button)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "EventOnMouseButtonDown");

	Params::W_ConstructionWorkbenchUI_C_EventOnMouseButtonDown Parms{};

	Parms.Container = Container;
	Parms.SlotRef = SlotRef;
	Parms.Button = std::move(Button);

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.ExecuteUbergraph_W_ConstructionWorkbenchUI
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::ExecuteUbergraph_W_ConstructionWorkbenchUI(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "ExecuteUbergraph_W_ConstructionWorkbenchUI");

	Params::W_ConstructionWorkbenchUI_C_ExecuteUbergraph_W_ConstructionWorkbenchUI Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.ForceInitSpecialcontainer
// (Public, BlueprintCallable, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::ForceInitSpecialcontainer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "ForceInitSpecialcontainer");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.GetAllAttachments
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class FName>*                    Attachments                                            (Parm, OutParm)

void UW_ConstructionWorkbenchUI_C::GetAllAttachments(TArray<class FName>* Attachments)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "GetAllAttachments");

	Params::W_ConstructionWorkbenchUI_C_GetAllAttachments Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Attachments != nullptr)
		*Attachments = std::move(Parms.Attachments);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.GetContainerByAttachmentType
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGameplayTag&              Type                                                   (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
// class UJSIContainer_C**                 JigContainer                                           (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// int32*                                  ContainerIndex                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::GetContainerByAttachmentType(const struct FGameplayTag& Type, class UJSIContainer_C** JigContainer, int32* ContainerIndex)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "GetContainerByAttachmentType");

	Params::W_ConstructionWorkbenchUI_C_GetContainerByAttachmentType Parms{};

	Parms.Type = std::move(Type);

	UObject::ProcessEvent(Func, &Parms);

	if (JigContainer != nullptr)
		*JigContainer = Parms.JigContainer;

	if (ContainerIndex != nullptr)
		*ContainerIndex = Parms.ContainerIndex;
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.GetDropWidget
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UDropItemBackGwidget_C**          DropWRef                                               (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::GetDropWidget(class UDropItemBackGwidget_C** DropWRef)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "GetDropWidget");

	Params::W_ConstructionWorkbenchUI_C_GetDropWidget Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (DropWRef != nullptr)
		*DropWRef = Parms.DropWRef;
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.GetJSIContainerByPlayerSlots
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGameplayTag&              Slot_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
// class UJSIContainer_C**                 Container                                              (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSI_Slot_C**                     EquippedItem                                           (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// bool*                                   IsPending_                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::GetJSIContainerByPlayerSlots(const struct FGameplayTag& Slot_0, class UJSIContainer_C** Container, class UJSI_Slot_C** EquippedItem, bool* IsPending_)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "GetJSIContainerByPlayerSlots");

	Params::W_ConstructionWorkbenchUI_C_GetJSIContainerByPlayerSlots Parms{};

	Parms.Slot_0 = std::move(Slot_0);

	UObject::ProcessEvent(Func, &Parms);

	if (Container != nullptr)
		*Container = Parms.Container;

	if (EquippedItem != nullptr)
		*EquippedItem = Parms.EquippedItem;

	if (IsPending_ != nullptr)
		*IsPending_ = Parms.IsPending_;
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.GetListOfNonAddContainers
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class UJSIContainer_C*>*         Containers                                             (Parm, OutParm, ContainsInstancedReference)

void UW_ConstructionWorkbenchUI_C::GetListOfNonAddContainers(TArray<class UJSIContainer_C*>* Containers)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "GetListOfNonAddContainers");

	Params::W_ConstructionWorkbenchUI_C_GetListOfNonAddContainers Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Containers != nullptr)
		*Containers = std::move(Parms.Containers);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.GetLootContent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUserWidget**                     Widget                                                 (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::GetLootContent(class UUserWidget** Widget)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "GetLootContent");

	Params::W_ConstructionWorkbenchUI_C_GetLootContent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Widget != nullptr)
		*Widget = Parms.Widget;
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.GetText
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText                             ReturnValue                                            (Parm, OutParm, ReturnParm)

class FText UW_ConstructionWorkbenchUI_C::GetText()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "GetText");

	Params::W_ConstructionWorkbenchUI_C_GetText Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.GetValidReloadContainers
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class UJSIContainer_C*>*         Containers                                             (Parm, OutParm, ContainsInstancedReference)

void UW_ConstructionWorkbenchUI_C::GetValidReloadContainers(TArray<class UJSIContainer_C*>* Containers)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "GetValidReloadContainers");

	Params::W_ConstructionWorkbenchUI_C_GetValidReloadContainers Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Containers != nullptr)
		*Containers = std::move(Parms.Containers);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.HandleCraftingButtons
// (BlueprintCallable, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::HandleCraftingButtons()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "HandleCraftingButtons");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.InitializeInventory
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::InitializeInventory()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "InitializeInventory");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.JigSetLootContent
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUserWidget*                      Widget                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// const class FText&                      Name_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm)

void UW_ConstructionWorkbenchUI_C::JigSetLootContent(class UUserWidget* Widget, const class FText& Name_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "JigSetLootContent");

	Params::W_ConstructionWorkbenchUI_C_JigSetLootContent Parms{};

	Parms.Widget = Widget;
	Parms.Name_0 = std::move(Name_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.JSICheckStatus
// (Public, BlueprintCallable, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::JSICheckStatus()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "JSICheckStatus");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.JSIOnWeightUpdated
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  NewWeight                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::JSIOnWeightUpdated(double NewWeight)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "JSIOnWeightUpdated");

	Params::W_ConstructionWorkbenchUI_C_JSIOnWeightUpdated Parms{};

	Parms.NewWeight = NewWeight;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.OnCreatedFromUtility
// (Public, BlueprintCallable, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::OnCreatedFromUtility()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "OnCreatedFromUtility");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.OnInitialized
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::OnInitialized()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "OnInitialized");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.OnModularMouseButtonDown
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FKey&                      Button                                                 (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::OnModularMouseButtonDown(const struct FKey& Button)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "OnModularMouseButtonDown");

	Params::W_ConstructionWorkbenchUI_C_OnModularMouseButtonDown Parms{};

	Parms.Button = std::move(Button);

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.OnStandardMouseButtonDown
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FKey&                      Button                                                 (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::OnStandardMouseButtonDown(const struct FKey& Button)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "OnStandardMouseButtonDown");

	Params::W_ConstructionWorkbenchUI_C_OnStandardMouseButtonDown Parms{};

	Parms.Button = std::move(Button);

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.SetActionbarFollower
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSI_Slot_C*                      JigRef                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// bool*                                   Return                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::SetActionbarFollower(class UJSI_Slot_C* JigRef, bool* Return)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "SetActionbarFollower");

	Params::W_ConstructionWorkbenchUI_C_SetActionbarFollower Parms{};

	Parms.JigRef = JigRef;

	UObject::ProcessEvent(Func, &Parms);

	if (Return != nullptr)
		*Return = Parms.Return;
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.SetActorOwner
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AActor*                           ActorRef                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::SetActorOwner(class AActor* ActorRef)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "SetActorOwner");

	Params::W_ConstructionWorkbenchUI_C_SetActorOwner Parms{};

	Parms.ActorRef = ActorRef;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.SetCraftableItems
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UW_ConstructionWorkbenchUI_C::SetCraftableItems()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "SetCraftableItems");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.SetInspectorRef
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UBP_InspectorWindowWidget_C*      Inspector                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::SetInspectorRef(class UBP_InspectorWindowWidget_C* Inspector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "SetInspectorRef");

	Params::W_ConstructionWorkbenchUI_C_SetInspectorRef Parms{};

	Parms.Inspector = Inspector;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.SetItemReference
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSI_Slot_C*                      ItemRef                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_ConstructionWorkbenchUI_C::SetItemReference(class UJSI_Slot_C* ItemRef)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "SetItemReference");

	Params::W_ConstructionWorkbenchUI_C_SetItemReference Parms{};

	Parms.ItemRef = ItemRef;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C.GetListOfContainers
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, Const)
// Parameters:
// TArray<class UJSIContainer_C*>*         Containers                                             (Parm, OutParm, ContainsInstancedReference)

void UW_ConstructionWorkbenchUI_C::GetListOfContainers(TArray<class UJSIContainer_C*>* Containers) const
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_ConstructionWorkbenchUI_C", "GetListOfContainers");

	Params::W_ConstructionWorkbenchUI_C_GetListOfContainers Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Containers != nullptr)
		*Containers = std::move(Parms.Containers);
}

}

