﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_FindPlayerLocation

#include "Basic.hpp"

#include "Zombie_FindPlayerLocation_classes.hpp"
#include "Zombie_FindPlayerLocation_parameters.hpp"


namespace SDK
{

// Function Zombie_FindPlayerLocation.Zombie_FindPlayerLocation_C.ExecuteUbergraph_Zombie_FindPlayerLocation
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UZombie_FindPlayerLocation_C::ExecuteUbergraph_Zombie_FindPlayerLocation(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Zombie_FindPlayerLocation_C", "ExecuteUbergraph_Zombie_FindPlayerLocation");

	Params::Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Zombie_FindPlayerLocation.Zombie_FindPlayerLocation_C.ReceiveExecuteAI
// (Event, Protected, BlueprintEvent)
// Parameters:
// class AAIController*                    OwnerController                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class APawn*                            ControlledPawn                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UZombie_FindPlayerLocation_C::ReceiveExecuteAI(class AAIController* OwnerController, class APawn* ControlledPawn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Zombie_FindPlayerLocation_C", "ReceiveExecuteAI");

	Params::Zombie_FindPlayerLocation_C_ReceiveExecuteAI Parms{};

	Parms.OwnerController = OwnerController;
	Parms.ControlledPawn = ControlledPawn;

	UObject::ProcessEvent(Func, &Parms);
}

}

