﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_VicinityLootUI

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "S_RepAttachmentInfo_structs.hpp"
#include "InputCore_structs.hpp"


namespace SDK::Params
{

// Function W_VicinityLootUI.W_VicinityLootUI_C.AddPickups
// 0x0450 (0x0450 - 0x0000)
struct W_VicinityLootUI_C_AddPickups final
{
public:
	class UJSI_Slot_C*                            Jig;                                               // 0x0000(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UBP_JigPickupComponent_C*               CurrentP;                                          // 0x0008(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TArray<class UBP_JigPickupComponent_C*>       Keys;                                              // 0x0010(0x0010)(Edit, BlueprintVisible, ContainsInstancedReference)
	TArray<class UBP_JigPickupComponent_C*>       PComp;                                             // 0x0020(0x0010)(Edit, BlueprintVisible, ContainsInstancedReference)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_2;                   // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_2;                  // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_3;                  // 0x0054(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_3;                 // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_3;                   // 0x005C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_4;                  // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_4;                 // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UBP_JigPickupComponent_C*               CallFunc_Array_Get_Item;                           // 0x0068(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0070(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0074(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_75[0x3];                                       // 0x0075(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetCapacity_RepCapacity;                  // 0x0078(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetCount_Count;                           // 0x007C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUserWidget*                            CallFunc_GetContainerOneRef_ContainerRef;          // 0x0080(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TScriptInterface<class IJSISpecialWidgetInterface_C> K2Node_DynamicCast_AsJSISpecial_Widget_Interface; // 0x0088(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_99[0x7];                                       // 0x0099(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSIContainer_C*>                CallFunc_GetListOfContainers_Containers;           // 0x00A0(0x0010)(ReferenceParm, ContainsInstancedReference)
	struct FS_RepAttachmentInfo                   CallFunc_Array_Get_Item_1;                         // 0x00B0(0x0048)(HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_GuidGuid_ReturnValue;          // 0x00FC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x00FD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_FE[0x2];                                       // 0x00FE(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSIContainer_C*                        CallFunc_Array_Get_Item_2;                         // 0x0100(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x0108(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Added;                // 0x010C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_10D[0x3];                                      // 0x010D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_AddNewInventoryItem_SlotIndex;            // 0x0110(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_114[0x4];                                      // 0x0114(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UJigsawItem_DataAsset_C*                CallFunc_AddNewInventoryItem_ItemInfo;             // 0x0118(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            CallFunc_AddNewInventoryItem_SlotItemRef;          // 0x0120(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Stacked_;             // 0x0128(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_2;                // 0x0129(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12A[0x2];                                      // 0x012A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGuid                                  CallFunc_SetUniqueID_UniqueID;                     // 0x012C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x013C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13D[0x3];                                      // 0x013D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UBP_JigPickupComponent_C*>       CallFunc_Map_Keys_Keys;                            // 0x0140(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UBP_JigPickupComponent_C*               CallFunc_Array_Get_Item_3;                         // 0x0150(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_Contains_ReturnValue;               // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_Contains_ReturnValue_1;             // 0x0159(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15A[0x2];                                      // 0x015A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_3;               // 0x015C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_3;                // 0x0160(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_161[0x7];                                      // 0x0161(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            CallFunc_Map_Find_Value;                           // 0x0168(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0170(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Remove_ReturnValue;                   // 0x0171(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_RemoveFromJSIParent_Removed;              // 0x0172(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_173[0x5];                                      // 0x0173(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UBP_JigPickupComponent_C*>       CallFunc_Map_Keys_Keys_1;                          // 0x0178(0x0010)(ReferenceParm, ContainsInstancedReference)
	int32                                         Temp_int_Array_Index_Variable_4;                   // 0x0188(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_18C[0x4];                                      // 0x018C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSIContainer_C*                        CallFunc_Array_Get_Item_4;                         // 0x0190(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TArray<EObjectTypeQuery>                      K2Node_MakeArray_Array;                            // 0x0198(0x0010)(ConstParm, ReferenceParm)
	bool                                          CallFunc_AddNewInventoryItem_Added_1;              // 0x01A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A9[0x3];                                      // 0x01A9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_AddNewInventoryItem_SlotIndex_1;          // 0x01AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UJigsawItem_DataAsset_C*                CallFunc_AddNewInventoryItem_ItemInfo_1;           // 0x01B0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            CallFunc_AddNewInventoryItem_SlotItemRef_1;        // 0x01B8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Stacked__1;           // 0x01C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C1[0x7];                                      // 0x01C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APawn*                                  CallFunc_GetPlayerPawn_ReturnValue;                // 0x01C8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               CallFunc_K2_GetActorRotation_ReturnValue;          // 0x01D0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor)
	TArray<class AActor*>                         K2Node_MakeArray_Array_1;                          // 0x01E8(0x0010)(ConstParm, ReferenceParm)
	struct FVector                                CallFunc_K2_GetActorLocation_ReturnValue;          // 0x01F8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FHitResult>                     CallFunc_BoxTraceMultiForObjects_OutHits;          // 0x0210(0x0010)(ReferenceParm, ContainsInstancedReference)
	bool                                          CallFunc_BoxTraceMultiForObjects_ReturnValue;      // 0x0220(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_ISCapacityContainer_ReturnValue;          // 0x0221(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_222[0x6];                                      // 0x0222(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FHitResult                             CallFunc_Array_Get_Item_5;                         // 0x0228(0x00E8)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	bool                                          CallFunc_BreakHitResult_bBlockingHit;              // 0x0310(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BreakHitResult_bInitialOverlap;           // 0x0311(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_312[0x2];                                      // 0x0312(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_BreakHitResult_Time;                      // 0x0314(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakHitResult_Distance;                  // 0x0318(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31C[0x4];                                      // 0x031C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_BreakHitResult_Location;                  // 0x0320(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_ImpactPoint;               // 0x0338(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_Normal;                    // 0x0350(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_ImpactNormal;              // 0x0368(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPhysicalMaterial*                      CallFunc_BreakHitResult_PhysMat;                   // 0x0380(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 CallFunc_BreakHitResult_HitActor;                  // 0x0388(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UPrimitiveComponent*                    CallFunc_BreakHitResult_HitComponent;              // 0x0390(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_BreakHitResult_HitBoneName;               // 0x0398(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_BreakHitResult_BoneName;                  // 0x03A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakHitResult_HitItem;                   // 0x03A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakHitResult_ElementIndex;              // 0x03AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakHitResult_FaceIndex;                 // 0x03B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3B4[0x4];                                      // 0x03B4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_BreakHitResult_TraceStart;                // 0x03B8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_TraceEnd;                  // 0x03D0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_4;               // 0x03E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3EC[0x4];                                      // 0x03EC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TScriptInterface<class IBP_MpInteractInterface_C> K2Node_DynamicCast_AsBP_Mp_Interact_Interface; // 0x03F0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0400(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_4;                // 0x0401(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JigCanInteract_Result;                    // 0x0402(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_403[0x5];                                      // 0x0403(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	TScriptInterface<class IBP_MpInteractInterface_C> K2Node_DynamicCast_AsBP_Mp_Interact_Interface_1; // 0x0408(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x0418(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_419[0x7];                                      // 0x0419(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UBP_JigPickupComponent_C*               CallFunc_GetComponentByClass_ReturnValue;          // 0x0420(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UActorComponent*                        CallFunc_GetJigMultiplayerComponent_JigComp;       // 0x0428(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_AddUnique_ReturnValue;              // 0x0430(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_434[0x4];                                      // 0x0434(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TScriptInterface<class IBPI_JigCompInfo_C>    K2Node_DynamicCast_AsBPI_Jig_Comp_Info;            // 0x0438(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_3;                     // 0x0448(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JigCom_IsPickup_Result;                   // 0x0449(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x044A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_VicinityLootUI_C_AddPickups) == 0x000008, "Wrong alignment on W_VicinityLootUI_C_AddPickups");
static_assert(sizeof(W_VicinityLootUI_C_AddPickups) == 0x000450, "Wrong size on W_VicinityLootUI_C_AddPickups");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Jig) == 0x000000, "Member 'W_VicinityLootUI_C_AddPickups::Jig' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CurrentP) == 0x000008, "Member 'W_VicinityLootUI_C_AddPickups::CurrentP' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Keys) == 0x000010, "Member 'W_VicinityLootUI_C_AddPickups::Keys' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, PComp) == 0x000020, "Member 'W_VicinityLootUI_C_AddPickups::PComp' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Array_Index_Variable) == 0x000030, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Loop_Counter_Variable) == 0x000034, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Add_IntInt_ReturnValue) == 0x000038, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Array_Index_Variable_1) == 0x00003C, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Loop_Counter_Variable_1) == 0x000040, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Add_IntInt_ReturnValue_1) == 0x000044, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Array_Index_Variable_2) == 0x000048, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Array_Index_Variable_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Loop_Counter_Variable_2) == 0x00004C, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Loop_Counter_Variable_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Add_IntInt_ReturnValue_2) == 0x000050, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Loop_Counter_Variable_3) == 0x000054, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Loop_Counter_Variable_3' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Add_IntInt_ReturnValue_3) == 0x000058, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Add_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Array_Index_Variable_3) == 0x00005C, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Array_Index_Variable_3' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Loop_Counter_Variable_4) == 0x000060, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Loop_Counter_Variable_4' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Add_IntInt_ReturnValue_4) == 0x000064, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Add_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Get_Item) == 0x000068, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Length_ReturnValue) == 0x000070, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Less_IntInt_ReturnValue) == 0x000074, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_GetCapacity_RepCapacity) == 0x000078, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_GetCapacity_RepCapacity' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_GetCount_Count) == 0x00007C, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_GetCount_Count' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_GetContainerOneRef_ContainerRef) == 0x000080, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_GetContainerOneRef_ContainerRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_DynamicCast_AsJSISpecial_Widget_Interface) == 0x000088, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_DynamicCast_AsJSISpecial_Widget_Interface' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_DynamicCast_bSuccess) == 0x000098, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_GetListOfContainers_Containers) == 0x0000A0, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_GetListOfContainers_Containers' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Get_Item_1) == 0x0000B0, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Length_ReturnValue_1) == 0x0000F8, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_EqualEqual_GuidGuid_ReturnValue) == 0x0000FC, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_EqualEqual_GuidGuid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Less_IntInt_ReturnValue_1) == 0x0000FD, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Get_Item_2) == 0x000100, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Length_ReturnValue_2) == 0x000108, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_Added) == 0x00010C, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_Added' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_SlotIndex) == 0x000110, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_SlotIndex' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_ItemInfo) == 0x000118, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_ItemInfo' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_SlotItemRef) == 0x000120, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_SlotItemRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_Stacked_) == 0x000128, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_Stacked_' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Less_IntInt_ReturnValue_2) == 0x000129, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Less_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_SetUniqueID_UniqueID) == 0x00012C, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_SetUniqueID_UniqueID' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_IsValid_ReturnValue) == 0x00013C, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Map_Keys_Keys) == 0x000140, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Map_Keys_Keys' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Get_Item_3) == 0x000150, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Contains_ReturnValue) == 0x000158, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Contains_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Contains_ReturnValue_1) == 0x000159, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Contains_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Length_ReturnValue_3) == 0x00015C, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Length_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Less_IntInt_ReturnValue_3) == 0x000160, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Less_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Map_Find_Value) == 0x000168, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Map_Find_ReturnValue) == 0x000170, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Map_Remove_ReturnValue) == 0x000171, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Map_Remove_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_RemoveFromJSIParent_Removed) == 0x000172, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_RemoveFromJSIParent_Removed' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Map_Keys_Keys_1) == 0x000178, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Map_Keys_Keys_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, Temp_int_Array_Index_Variable_4) == 0x000188, "Member 'W_VicinityLootUI_C_AddPickups::Temp_int_Array_Index_Variable_4' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Get_Item_4) == 0x000190, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Get_Item_4' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_MakeArray_Array) == 0x000198, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_Added_1) == 0x0001A8, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_Added_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_SlotIndex_1) == 0x0001AC, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_SlotIndex_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_ItemInfo_1) == 0x0001B0, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_ItemInfo_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_SlotItemRef_1) == 0x0001B8, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_SlotItemRef_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_AddNewInventoryItem_Stacked__1) == 0x0001C0, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_AddNewInventoryItem_Stacked__1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_GetPlayerPawn_ReturnValue) == 0x0001C8, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_GetPlayerPawn_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_K2_GetActorRotation_ReturnValue) == 0x0001D0, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_K2_GetActorRotation_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_MakeArray_Array_1) == 0x0001E8, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_MakeArray_Array_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_K2_GetActorLocation_ReturnValue) == 0x0001F8, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_K2_GetActorLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BoxTraceMultiForObjects_OutHits) == 0x000210, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BoxTraceMultiForObjects_OutHits' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BoxTraceMultiForObjects_ReturnValue) == 0x000220, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BoxTraceMultiForObjects_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_ISCapacityContainer_ReturnValue) == 0x000221, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_ISCapacityContainer_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Get_Item_5) == 0x000228, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Get_Item_5' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_bBlockingHit) == 0x000310, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_bBlockingHit' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_bInitialOverlap) == 0x000311, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_bInitialOverlap' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_Time) == 0x000314, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_Time' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_Distance) == 0x000318, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_Distance' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_Location) == 0x000320, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_Location' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_ImpactPoint) == 0x000338, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_ImpactPoint' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_Normal) == 0x000350, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_Normal' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_ImpactNormal) == 0x000368, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_ImpactNormal' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_PhysMat) == 0x000380, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_PhysMat' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_HitActor) == 0x000388, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_HitActor' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_HitComponent) == 0x000390, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_HitComponent' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_HitBoneName) == 0x000398, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_HitBoneName' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_BoneName) == 0x0003A0, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_BoneName' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_HitItem) == 0x0003A8, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_HitItem' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_ElementIndex) == 0x0003AC, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_ElementIndex' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_FaceIndex) == 0x0003B0, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_FaceIndex' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_TraceStart) == 0x0003B8, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_TraceStart' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_BreakHitResult_TraceEnd) == 0x0003D0, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_BreakHitResult_TraceEnd' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_Length_ReturnValue_4) == 0x0003E8, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_Length_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_DynamicCast_AsBP_Mp_Interact_Interface) == 0x0003F0, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_DynamicCast_AsBP_Mp_Interact_Interface' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_DynamicCast_bSuccess_1) == 0x000400, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Less_IntInt_ReturnValue_4) == 0x000401, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Less_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_JigCanInteract_Result) == 0x000402, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_JigCanInteract_Result' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_DynamicCast_AsBP_Mp_Interact_Interface_1) == 0x000408, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_DynamicCast_AsBP_Mp_Interact_Interface_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_DynamicCast_bSuccess_2) == 0x000418, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_GetComponentByClass_ReturnValue) == 0x000420, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_GetComponentByClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_GetJigMultiplayerComponent_JigComp) == 0x000428, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_GetJigMultiplayerComponent_JigComp' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_Array_AddUnique_ReturnValue) == 0x000430, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_Array_AddUnique_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_DynamicCast_AsBPI_Jig_Comp_Info) == 0x000438, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_DynamicCast_AsBPI_Jig_Comp_Info' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, K2Node_DynamicCast_bSuccess_3) == 0x000448, "Member 'W_VicinityLootUI_C_AddPickups::K2Node_DynamicCast_bSuccess_3' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_JigCom_IsPickup_Result) == 0x000449, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_JigCom_IsPickup_Result' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_AddPickups, CallFunc_IsValid_ReturnValue_1) == 0x00044A, "Member 'W_VicinityLootUI_C_AddPickups::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");

// Function W_VicinityLootUI.W_VicinityLootUI_C.Drop_ContainerOnContainerUnhandled_Event_0
// 0x0028 (0x0028 - 0x0000)
struct W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0 final
{
public:
	class UJSIContainer_C*                        FromContainer;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        ToContainer;                                       // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            DroppedSlotRef;                                    // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            SlotReceiverRef;                                   // 0x0018(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         ToSlotIndex;                                       // 0x0020(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Rotated_;                                          // 0x0024(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0) == 0x000008, "Wrong alignment on W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0");
static_assert(sizeof(W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0) == 0x000028, "Wrong size on W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0");
static_assert(offsetof(W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0, FromContainer) == 0x000000, "Member 'W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0::FromContainer' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0, ToContainer) == 0x000008, "Member 'W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0::ToContainer' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0, DroppedSlotRef) == 0x000010, "Member 'W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0::DroppedSlotRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0, SlotReceiverRef) == 0x000018, "Member 'W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0::SlotReceiverRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0, ToSlotIndex) == 0x000020, "Member 'W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0::ToSlotIndex' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0, Rotated_) == 0x000024, "Member 'W_VicinityLootUI_C_Drop_ContainerOnContainerUnhandled_Event_0::Rotated_' has a wrong offset!");

// Function W_VicinityLootUI.W_VicinityLootUI_C.ExecuteUbergraph_W_VicinityLootUI
// 0x01B0 (0x01B0 - 0x0000)
struct W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsInputKeyDown_ReturnValue;               // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSIContainer_C*                        K2Node_CustomEvent_FromContainer;                  // 0x0018(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        K2Node_CustomEvent_ToContainer;                    // 0x0020(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            K2Node_CustomEvent_DroppedSlotRef;                 // 0x0028(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            K2Node_CustomEvent_SlotReceiverRef;                // 0x0030(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_ToSlotIndex;                    // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_Rotated_;                       // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_RemoveFromJSIParent_Removed;              // 0x003D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3E[0x2];                                       // 0x003E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class AActor*                                 CallFunc_GetActorFromKey_ReturnValue;              // 0x0040(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_JigPickupComponent_C*               CallFunc_GetActorFromKey_Comp;                     // 0x0048(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEquipTo__ReturnValue;                   // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0051(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_52[0x6];                                       // 0x0052(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           CallFunc_K2_SetTimer_ReturnValue;                  // 0x0058(0x0008)(NoDestructor, HasGetValueTypeHash)
	class APawn*                                  CallFunc_GetPlayerPawn_ReturnValue;                // 0x0060(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TArray<struct FGuid>                          K2Node_MakeArray_Array;                            // 0x0068(0x0010)(ConstParm, ReferenceParm)
	class UBP_JigMultiplayer_C*                   CallFunc_GetComponentByClass_ReturnValue;          // 0x0078(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  CallFunc_GetPlayerPawn_ReturnValue_1;              // 0x0080(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JSI_CheckIfCanAddActorToContainer_Result; // 0x0088(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_89[0x3];                                       // 0x0089(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGuid                                  CallFunc_JSI_CheckIfCanAddActorToContainer_AddtoUID; // 0x008C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGuid                                  CallFunc_JSI_CheckIfCanAddActorToContainer_TempItemUID; // 0x009C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_JSI_CheckIfCanAddActorToContainer_ToIndex; // 0x00AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JSI_CheckIfCanAddActorToContainer_FinalRotation; // 0x00B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B1[0x7];                                       // 0x00B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            CallFunc_JSI_CheckIfCanAddActorToContainer_JigItemRef; // 0x00B8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TScriptInterface<class IBP_JigCharacterInterface_C> K2Node_DynamicCast_AsBP_Jig_Character_Interface; // 0x00C0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_OnPickupInteractExecuted_Result;          // 0x00D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D2[0x2];                                       // 0x00D2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UJSIContainer_C* FromContainer, class UJSIContainer_C* ToContainer, class UJSI_Slot_C* DroppedSlotRef, class UJSI_Slot_C* SlotReceiverRef, int32 ToSlotIndex, bool Rotated_)> K2Node_CreateDelegate_OutputDelegate; // 0x00D4(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E4[0x4];                                       // 0x00E4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            K2Node_CustomEvent_DroppedItem;                    // 0x00E8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            K2Node_CustomEvent_ReceiverItem;                   // 0x00F0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FGuid                                  CallFunc_GetUniqueID_UniqueServerID;               // 0x00F8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_RemoveFromJSIParent_Removed_1;            // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_109[0x7];                                      // 0x0109(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AActor*                                 CallFunc_GetActorFromKey_ReturnValue_1;            // 0x0110(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_JigPickupComponent_C*               CallFunc_GetActorFromKey_Comp_1;                   // 0x0118(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  CallFunc_GetPlayerPawn_ReturnValue_2;              // 0x0120(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_JigMultiplayer_C*                   CallFunc_GetComponentByClass_ReturnValue_1;        // 0x0128(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UJSIContainer_C* Container, class UJSI_Slot_C* SlotRef, const struct FKey& Button)> K2Node_CreateDelegate_OutputDelegate_1; // 0x0130(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Remove_ReturnValue;                   // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Remove_ReturnValue_1;                 // 0x0141(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_142[0x2];                                      // 0x0142(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UJSI_Slot_C* DroppedItem, class UJSI_Slot_C* ReceiverItem)> K2Node_CreateDelegate_OutputDelegate_2; // 0x0144(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_154[0x4];                                      // 0x0154(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSIContainer_C*                        K2Node_CustomEvent_Container;                      // 0x0158(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            K2Node_CustomEvent_SlotRef;                        // 0x0160(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FKey                                   K2Node_CustomEvent_Button;                         // 0x0168(0x0018)(HasGetValueTypeHash)
	class APawn*                                  CallFunc_GetOwningPlayerPawn_ReturnValue;          // 0x0180(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 CallFunc_GetActorFromKey_ReturnValue_2;            // 0x0188(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_JigPickupComponent_C*               CallFunc_GetActorFromKey_Comp_2;                   // 0x0190(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TScriptInterface<class IBP_JigCharacterInterface_C> K2Node_DynamicCast_AsBP_Jig_Character_Interface_1; // 0x0198(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x01A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_RemoveFromJSIParent_Removed_2;            // 0x01A9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_OnPickupInteractExecuted_Result_1;        // 0x01AA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Remove_ReturnValue_2;                 // 0x01AB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI) == 0x000008, "Wrong alignment on W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI");
static_assert(sizeof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI) == 0x0001B0, "Wrong size on W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, EntryPoint) == 0x000000, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetOwningPlayer_ReturnValue) == 0x000008, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_IsInputKeyDown_ReturnValue) == 0x000010, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_IsInputKeyDown_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_FromContainer) == 0x000018, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_FromContainer' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_ToContainer) == 0x000020, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_ToContainer' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_DroppedSlotRef) == 0x000028, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_DroppedSlotRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_SlotReceiverRef) == 0x000030, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_SlotReceiverRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_ToSlotIndex) == 0x000038, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_ToSlotIndex' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_Rotated_) == 0x00003C, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_Rotated_' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_RemoveFromJSIParent_Removed) == 0x00003D, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_RemoveFromJSIParent_Removed' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetActorFromKey_ReturnValue) == 0x000040, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetActorFromKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetActorFromKey_Comp) == 0x000048, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetActorFromKey_Comp' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_IsEquipTo__ReturnValue) == 0x000050, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_IsEquipTo__ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_Not_PreBool_ReturnValue) == 0x000051, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_K2_SetTimer_ReturnValue) == 0x000058, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_K2_SetTimer_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetPlayerPawn_ReturnValue) == 0x000060, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetPlayerPawn_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_MakeArray_Array) == 0x000068, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetComponentByClass_ReturnValue) == 0x000078, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetComponentByClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetPlayerPawn_ReturnValue_1) == 0x000080, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetPlayerPawn_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_JSI_CheckIfCanAddActorToContainer_Result) == 0x000088, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_JSI_CheckIfCanAddActorToContainer_Result' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_JSI_CheckIfCanAddActorToContainer_AddtoUID) == 0x00008C, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_JSI_CheckIfCanAddActorToContainer_AddtoUID' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_JSI_CheckIfCanAddActorToContainer_TempItemUID) == 0x00009C, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_JSI_CheckIfCanAddActorToContainer_TempItemUID' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_JSI_CheckIfCanAddActorToContainer_ToIndex) == 0x0000AC, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_JSI_CheckIfCanAddActorToContainer_ToIndex' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_JSI_CheckIfCanAddActorToContainer_FinalRotation) == 0x0000B0, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_JSI_CheckIfCanAddActorToContainer_FinalRotation' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_JSI_CheckIfCanAddActorToContainer_JigItemRef) == 0x0000B8, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_JSI_CheckIfCanAddActorToContainer_JigItemRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_DynamicCast_AsBP_Jig_Character_Interface) == 0x0000C0, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_DynamicCast_AsBP_Jig_Character_Interface' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_DynamicCast_bSuccess) == 0x0000D0, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_OnPickupInteractExecuted_Result) == 0x0000D1, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_OnPickupInteractExecuted_Result' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CreateDelegate_OutputDelegate) == 0x0000D4, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_DroppedItem) == 0x0000E8, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_DroppedItem' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_ReceiverItem) == 0x0000F0, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_ReceiverItem' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetUniqueID_UniqueServerID) == 0x0000F8, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetUniqueID_UniqueServerID' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_RemoveFromJSIParent_Removed_1) == 0x000108, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_RemoveFromJSIParent_Removed_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetActorFromKey_ReturnValue_1) == 0x000110, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetActorFromKey_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetActorFromKey_Comp_1) == 0x000118, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetActorFromKey_Comp_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetPlayerPawn_ReturnValue_2) == 0x000120, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetPlayerPawn_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetComponentByClass_ReturnValue_1) == 0x000128, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetComponentByClass_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CreateDelegate_OutputDelegate_1) == 0x000130, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_Map_Remove_ReturnValue) == 0x000140, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_Map_Remove_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_Map_Remove_ReturnValue_1) == 0x000141, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_Map_Remove_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CreateDelegate_OutputDelegate_2) == 0x000144, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CreateDelegate_OutputDelegate_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_Container) == 0x000158, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_Container' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_SlotRef) == 0x000160, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_SlotRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_CustomEvent_Button) == 0x000168, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_CustomEvent_Button' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetOwningPlayerPawn_ReturnValue) == 0x000180, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetOwningPlayerPawn_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetActorFromKey_ReturnValue_2) == 0x000188, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetActorFromKey_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_GetActorFromKey_Comp_2) == 0x000190, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_GetActorFromKey_Comp_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_DynamicCast_AsBP_Jig_Character_Interface_1) == 0x000198, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_DynamicCast_AsBP_Jig_Character_Interface_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, K2Node_DynamicCast_bSuccess_1) == 0x0001A8, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_RemoveFromJSIParent_Removed_2) == 0x0001A9, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_RemoveFromJSIParent_Removed_2' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_OnPickupInteractExecuted_Result_1) == 0x0001AA, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_OnPickupInteractExecuted_Result_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI, CallFunc_Map_Remove_ReturnValue_2) == 0x0001AB, "Member 'W_VicinityLootUI_C_ExecuteUbergraph_W_VicinityLootUI::CallFunc_Map_Remove_ReturnValue_2' has a wrong offset!");

// Function W_VicinityLootUI.W_VicinityLootUI_C.GetActorFromKey
// 0x0078 (0x0078 - 0x0000)
struct W_VicinityLootUI_C_GetActorFromKey final
{
public:
	class UJSI_Slot_C*                            JigRef;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)
	class UBP_JigPickupComponent_C*               Comp;                                              // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 Owner;                                             // 0x0018(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSI_Slot_C*>                    CallFunc_Map_Values_Values;                        // 0x0030(0x0010)(ReferenceParm, ContainsInstancedReference)
	TArray<class UBP_JigPickupComponent_C*>       CallFunc_Map_Keys_Keys;                            // 0x0040(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UJSI_Slot_C*                            CallFunc_Array_Get_Item;                           // 0x0050(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UBP_JigPickupComponent_C*               CallFunc_Array_Get_Item_1;                         // 0x0058(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0061(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_62[0x6];                                       // 0x0062(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class AActor*                                 CallFunc_GetOwner_ReturnValue;                     // 0x0068(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0070(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0074(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_VicinityLootUI_C_GetActorFromKey) == 0x000008, "Wrong alignment on W_VicinityLootUI_C_GetActorFromKey");
static_assert(sizeof(W_VicinityLootUI_C_GetActorFromKey) == 0x000078, "Wrong size on W_VicinityLootUI_C_GetActorFromKey");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, JigRef) == 0x000000, "Member 'W_VicinityLootUI_C_GetActorFromKey::JigRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, ReturnValue) == 0x000008, "Member 'W_VicinityLootUI_C_GetActorFromKey::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, Comp) == 0x000010, "Member 'W_VicinityLootUI_C_GetActorFromKey::Comp' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, Owner) == 0x000018, "Member 'W_VicinityLootUI_C_GetActorFromKey::Owner' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, Temp_int_Array_Index_Variable) == 0x000020, "Member 'W_VicinityLootUI_C_GetActorFromKey::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, Temp_int_Loop_Counter_Variable) == 0x000024, "Member 'W_VicinityLootUI_C_GetActorFromKey::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_Add_IntInt_ReturnValue) == 0x000028, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_Map_Values_Values) == 0x000030, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_Map_Values_Values' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_Map_Keys_Keys) == 0x000040, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_Map_Keys_Keys' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_Array_Get_Item) == 0x000050, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_Array_Get_Item_1) == 0x000058, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x000060, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_IsValid_ReturnValue) == 0x000061, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_GetOwner_ReturnValue) == 0x000068, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_GetOwner_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_Array_Length_ReturnValue) == 0x000070, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_GetActorFromKey, CallFunc_Less_IntInt_ReturnValue) == 0x000074, "Member 'W_VicinityLootUI_C_GetActorFromKey::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");

// Function W_VicinityLootUI.W_VicinityLootUI_C.OnItemStackRequest_Event_0
// 0x0010 (0x0010 - 0x0000)
struct W_VicinityLootUI_C_OnItemStackRequest_Event_0 final
{
public:
	class UJSI_Slot_C*                            DroppedItem;                                       // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            ReceiverItem;                                      // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_VicinityLootUI_C_OnItemStackRequest_Event_0) == 0x000008, "Wrong alignment on W_VicinityLootUI_C_OnItemStackRequest_Event_0");
static_assert(sizeof(W_VicinityLootUI_C_OnItemStackRequest_Event_0) == 0x000010, "Wrong size on W_VicinityLootUI_C_OnItemStackRequest_Event_0");
static_assert(offsetof(W_VicinityLootUI_C_OnItemStackRequest_Event_0, DroppedItem) == 0x000000, "Member 'W_VicinityLootUI_C_OnItemStackRequest_Event_0::DroppedItem' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_OnItemStackRequest_Event_0, ReceiverItem) == 0x000008, "Member 'W_VicinityLootUI_C_OnItemStackRequest_Event_0::ReceiverItem' has a wrong offset!");

// Function W_VicinityLootUI.W_VicinityLootUI_C.OnSlotMouseButtonDown_Event_0
// 0x0028 (0x0028 - 0x0000)
struct W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0 final
{
public:
	class UJSIContainer_C*                        Container;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            SlotRef;                                           // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FKey                                   Button;                                            // 0x0010(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
};
static_assert(alignof(W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0) == 0x000008, "Wrong alignment on W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0");
static_assert(sizeof(W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0) == 0x000028, "Wrong size on W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0");
static_assert(offsetof(W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0, Container) == 0x000000, "Member 'W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0::Container' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0, SlotRef) == 0x000008, "Member 'W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0::SlotRef' has a wrong offset!");
static_assert(offsetof(W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0, Button) == 0x000010, "Member 'W_VicinityLootUI_C_OnSlotMouseButtonDown_Event_0::Button' has a wrong offset!");

}

