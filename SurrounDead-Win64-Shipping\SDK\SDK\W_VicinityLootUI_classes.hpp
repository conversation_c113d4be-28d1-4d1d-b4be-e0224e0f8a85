﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_VicinityLootUI

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_VicinityLootUI.W_VicinityLootUI_C
// 0x0078 (0x0338 - 0x02C0)
class UW_VicinityLootUI_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UJSIContainer_C*                        JSIContainer;                                      // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                VicinityContent;                                   // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMap<class UBP_JigPickupComponent_C*, class UJSI_Slot_C*> Added;                                 // 0x02D8(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	bool                                          Init;                                              // 0x0328(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_329[0x7];                                      // 0x0329(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           TimerHandle;                                       // 0x0330(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)

public:
	void AddPickups();
	void Construct();
	void Destruct();
	void Drop_ContainerOnContainerUnhandled_Event_0(class UJSIContainer_C* FromContainer, class UJSIContainer_C* ToContainer, class UJSI_Slot_C* DroppedSlotRef, class UJSI_Slot_C* SlotReceiverRef, int32 ToSlotIndex, bool Rotated_);
	void ExecuteUbergraph_W_VicinityLootUI(int32 EntryPoint);
	class AActor* GetActorFromKey(class UJSI_Slot_C* JigRef, class UBP_JigPickupComponent_C** Comp);
	void OnInitialized();
	void OnItemStackRequest_Event_0(class UJSI_Slot_C* DroppedItem, class UJSI_Slot_C* ReceiverItem);
	void OnSlotMouseButtonDown_Event_0(class UJSIContainer_C* Container, class UJSI_Slot_C* SlotRef, const struct FKey& Button);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_VicinityLootUI_C">();
	}
	static class UW_VicinityLootUI_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_VicinityLootUI_C>();
	}
};
static_assert(alignof(UW_VicinityLootUI_C) == 0x000008, "Wrong alignment on UW_VicinityLootUI_C");
static_assert(sizeof(UW_VicinityLootUI_C) == 0x000338, "Wrong size on UW_VicinityLootUI_C");
static_assert(offsetof(UW_VicinityLootUI_C, UberGraphFrame) == 0x0002C0, "Member 'UW_VicinityLootUI_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_VicinityLootUI_C, JSIContainer) == 0x0002C8, "Member 'UW_VicinityLootUI_C::JSIContainer' has a wrong offset!");
static_assert(offsetof(UW_VicinityLootUI_C, VicinityContent) == 0x0002D0, "Member 'UW_VicinityLootUI_C::VicinityContent' has a wrong offset!");
static_assert(offsetof(UW_VicinityLootUI_C, Added) == 0x0002D8, "Member 'UW_VicinityLootUI_C::Added' has a wrong offset!");
static_assert(offsetof(UW_VicinityLootUI_C, Init) == 0x000328, "Member 'UW_VicinityLootUI_C::Init' has a wrong offset!");
static_assert(offsetof(UW_VicinityLootUI_C, TimerHandle) == 0x000330, "Member 'UW_VicinityLootUI_C::TimerHandle' has a wrong offset!");

}

