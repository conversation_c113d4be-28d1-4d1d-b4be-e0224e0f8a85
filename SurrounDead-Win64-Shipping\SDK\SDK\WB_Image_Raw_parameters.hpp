﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Image_Raw

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WB_Image_Raw.WB_Image_Raw_C.ExecuteUbergraph_WB_Image_Raw
// 0x0028 (0x0028 - 0x0000)
struct WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_Color;                          // 0x0004(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_IsEnabled;                      // 0x0014(0x0001)(ZeroConstructor, <PERSON><PERSON><PERSON>OldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_BlurStrength;                   // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetBlurStrength_InStrength_ImplicitCast;  // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw) == 0x000008, "Wrong alignment on WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw");
static_assert(sizeof(WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw) == 0x000028, "Wrong size on WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw");
static_assert(offsetof(WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw, EntryPoint) == 0x000000, "Member 'WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw, K2Node_CustomEvent_Color) == 0x000004, "Member 'WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw::K2Node_CustomEvent_Color' has a wrong offset!");
static_assert(offsetof(WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw, K2Node_CustomEvent_IsEnabled) == 0x000014, "Member 'WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw::K2Node_CustomEvent_IsEnabled' has a wrong offset!");
static_assert(offsetof(WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw, K2Node_CustomEvent_BlurStrength) == 0x000018, "Member 'WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw::K2Node_CustomEvent_BlurStrength' has a wrong offset!");
static_assert(offsetof(WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw, CallFunc_SetBlurStrength_InStrength_ImplicitCast) == 0x000020, "Member 'WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw::CallFunc_SetBlurStrength_InStrength_ImplicitCast' has a wrong offset!");

// Function WB_Image_Raw.WB_Image_Raw_C.SetBackgroundBlur
// 0x0010 (0x0010 - 0x0000)
struct WB_Image_Raw_C_SetBackgroundBlur final
{
public:
	bool                                          IsEnabled;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        BlurStrength;                                      // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Image_Raw_C_SetBackgroundBlur) == 0x000008, "Wrong alignment on WB_Image_Raw_C_SetBackgroundBlur");
static_assert(sizeof(WB_Image_Raw_C_SetBackgroundBlur) == 0x000010, "Wrong size on WB_Image_Raw_C_SetBackgroundBlur");
static_assert(offsetof(WB_Image_Raw_C_SetBackgroundBlur, IsEnabled) == 0x000000, "Member 'WB_Image_Raw_C_SetBackgroundBlur::IsEnabled' has a wrong offset!");
static_assert(offsetof(WB_Image_Raw_C_SetBackgroundBlur, BlurStrength) == 0x000008, "Member 'WB_Image_Raw_C_SetBackgroundBlur::BlurStrength' has a wrong offset!");

// Function WB_Image_Raw.WB_Image_Raw_C.SetColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Image_Raw_C_SetColor final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Image_Raw_C_SetColor) == 0x000004, "Wrong alignment on WB_Image_Raw_C_SetColor");
static_assert(sizeof(WB_Image_Raw_C_SetColor) == 0x000010, "Wrong size on WB_Image_Raw_C_SetColor");
static_assert(offsetof(WB_Image_Raw_C_SetColor, Color) == 0x000000, "Member 'WB_Image_Raw_C_SetColor::Color' has a wrong offset!");

}

