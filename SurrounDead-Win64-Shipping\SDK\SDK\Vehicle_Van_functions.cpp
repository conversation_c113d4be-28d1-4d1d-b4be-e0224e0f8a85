﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_Van

#include "Basic.hpp"

#include "Vehicle_Van_classes.hpp"
#include "Vehicle_Van_parameters.hpp"


namespace SDK
{

// Function Vehicle_Van.Vehicle_Van_C.UserConstructionScript
// (Event, Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AVehicle_Van_C::UserConstructionScript()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_Van_C", "UserConstructionScript");

	UObject::ProcessEvent(Func, nullptr);
}

}

