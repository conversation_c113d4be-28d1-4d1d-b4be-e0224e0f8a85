﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UBPP_E_AddingType

#include "Basic.hpp"


namespace SDK
{

// UserDefinedEnum UBPP_E_AddingType.UBPP_E_AddingType
// NumValues: 0x0004
enum class EUBPP_E_AddingType : uint8
{
	NewEnumerator2                           = 0,
	NewEnumerator0                           = 1,
	NewEnumerator1                           = 2,
	UBPP_E_MAX                               = 3,
};

}

