﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_DeadAIUI

#include "Basic.hpp"

#include "GameplayTags_structs.hpp"
#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "InputCore_structs.hpp"


namespace SDK::Params
{

// Function W_DeadAIUI.W_DeadAIUI_C.ExecuteUbergraph_W_DeadAIUI
// 0x01A8 (0x01A8 - 0x0000)
struct W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            K2Node_Event_ItemRef;                              // 0x0008(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 K2Node_Event_ActorRef;                             // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_NewWeight;                            // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUserWidget*                            K2Node_Event_Widget;                               // 0x0020(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   K2Node_Event_Name;                                 // 0x0028(0x0018)()
	class UBP_InspectorWindowWidget_C*            K2Node_Event_Inspector;                            // 0x0040(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FKey                                   CallFunc_GetKey_Key;                               // 0x0048(0x0018)(HasGetValueTypeHash)
	class FText                                   CallFunc_Key_GetDisplayName_ReturnValue;           // 0x0060(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0078(0x0050)(HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x00C8(0x0010)(ReferenceParm)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_DC[0x4];                                       // 0x00DC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x00E0(0x0018)()
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F9[0x7];                                       // 0x00F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APawn*                                  CallFunc_GetOwningPlayerPawn_ReturnValue;          // 0x0100(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_109[0x7];                                      // 0x0109(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UBP_JigMultiplayer_C*                   CallFunc_GetComponentByClass_ReturnValue;          // 0x0110(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0118(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_119[0x3];                                      // 0x0119(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x011C(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x0154(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsKeyDown_Down;                           // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_159[0x3];                                      // 0x0159(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x015C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0160(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_LastIndex_ReturnValue;              // 0x0164(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetCount_Count;                           // 0x0168(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x016C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x016D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JSI_CheckIfCanAddItemToContainer_Result;  // 0x016E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_16F[0x1];                                      // 0x016F(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSIContainer_C*                        CallFunc_JSI_CheckIfCanAddItemToContainer_AddToContainer; // 0x0170(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_JSI_CheckIfCanAddItemToContainer_ToIndex; // 0x0178(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JSI_CheckIfCanAddItemToContainer_FinalRotation; // 0x017C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JSI_CheckIfCanAddStackItemToContainer_Result; // 0x017D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_17E[0x2];                                      // 0x017E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            CallFunc_JSI_CheckIfCanAddStackItemToContainer_WithItem; // 0x0180(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CanStack__CanStack;                       // 0x0188(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CanStack__FreeSpaceAvailable;             // 0x0189(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_18A[0x6];                                      // 0x018A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class APawn*                                  CallFunc_GetOwningPlayerPawn_ReturnValue_1;        // 0x0190(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_JigMultiplayer_C*                   CallFunc_GetComponentByClass_ReturnValue_1;        // 0x0198(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsEmpty_ReturnValue;                // 0x01A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI) == 0x000008, "Wrong alignment on W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI");
static_assert(sizeof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI) == 0x0001A8, "Wrong size on W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, EntryPoint) == 0x000000, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, Temp_bool_IsClosed_Variable) == 0x000004, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, Temp_bool_Has_Been_Initd_Variable) == 0x000005, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_Event_ItemRef) == 0x000008, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_Event_ItemRef' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_Event_ActorRef) == 0x000010, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_Event_ActorRef' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_Event_NewWeight) == 0x000018, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_Event_NewWeight' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_Event_Widget) == 0x000020, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_Event_Widget' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_Event_Name) == 0x000028, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_Event_Name' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_Event_Inspector) == 0x000040, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_Event_Inspector' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_GetKey_Key) == 0x000048, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_GetKey_Key' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_Key_GetDisplayName_ReturnValue) == 0x000060, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_Key_GetDisplayName_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_MakeStruct_FormatArgumentData) == 0x000078, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_MakeArray_Array) == 0x0000C8, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_Array_Length_ReturnValue) == 0x0000D8, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_Format_ReturnValue) == 0x0000E0, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_Greater_IntInt_ReturnValue) == 0x0000F8, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_GetOwningPlayerPawn_ReturnValue) == 0x000100, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_GetOwningPlayerPawn_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, Temp_bool_Variable) == 0x000108, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_GetComponentByClass_ReturnValue) == 0x000110, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_GetComponentByClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_Not_PreBool_ReturnValue) == 0x000118, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_Event_MyGeometry) == 0x00011C, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, K2Node_Event_InDeltaTime) == 0x000154, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_IsKeyDown_Down) == 0x000158, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_IsKeyDown_Down' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, Temp_int_Variable) == 0x00015C, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_Add_IntInt_ReturnValue) == 0x000160, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_Array_LastIndex_ReturnValue) == 0x000164, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_Array_LastIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_GetCount_Count) == 0x000168, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_GetCount_Count' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_LessEqual_IntInt_ReturnValue) == 0x00016C, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_BooleanAND_ReturnValue) == 0x00016D, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_JSI_CheckIfCanAddItemToContainer_Result) == 0x00016E, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_JSI_CheckIfCanAddItemToContainer_Result' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_JSI_CheckIfCanAddItemToContainer_AddToContainer) == 0x000170, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_JSI_CheckIfCanAddItemToContainer_AddToContainer' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_JSI_CheckIfCanAddItemToContainer_ToIndex) == 0x000178, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_JSI_CheckIfCanAddItemToContainer_ToIndex' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_JSI_CheckIfCanAddItemToContainer_FinalRotation) == 0x00017C, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_JSI_CheckIfCanAddItemToContainer_FinalRotation' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_JSI_CheckIfCanAddStackItemToContainer_Result) == 0x00017D, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_JSI_CheckIfCanAddStackItemToContainer_Result' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_JSI_CheckIfCanAddStackItemToContainer_WithItem) == 0x000180, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_JSI_CheckIfCanAddStackItemToContainer_WithItem' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_CanStack__CanStack) == 0x000188, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_CanStack__CanStack' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_CanStack__FreeSpaceAvailable) == 0x000189, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_CanStack__FreeSpaceAvailable' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_GetOwningPlayerPawn_ReturnValue_1) == 0x000190, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_GetOwningPlayerPawn_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_GetComponentByClass_ReturnValue_1) == 0x000198, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_GetComponentByClass_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI, CallFunc_Array_IsEmpty_ReturnValue) == 0x0001A0, "Member 'W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI::CallFunc_Array_IsEmpty_ReturnValue' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.GetAllAttachments
// 0x0010 (0x0010 - 0x0000)
struct W_DeadAIUI_C_GetAllAttachments final
{
public:
	TArray<class FName>                           Attachments;                                       // 0x0000(0x0010)(Parm, OutParm)
};
static_assert(alignof(W_DeadAIUI_C_GetAllAttachments) == 0x000008, "Wrong alignment on W_DeadAIUI_C_GetAllAttachments");
static_assert(sizeof(W_DeadAIUI_C_GetAllAttachments) == 0x000010, "Wrong size on W_DeadAIUI_C_GetAllAttachments");
static_assert(offsetof(W_DeadAIUI_C_GetAllAttachments, Attachments) == 0x000000, "Member 'W_DeadAIUI_C_GetAllAttachments::Attachments' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.GetContainerByAttachmentType
// 0x0018 (0x0018 - 0x0000)
struct W_DeadAIUI_C_GetContainerByAttachmentType final
{
public:
	struct FGameplayTag                           Type;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        JigContainer;                                      // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         ContainerIndex;                                    // 0x0010(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_GetContainerByAttachmentType) == 0x000008, "Wrong alignment on W_DeadAIUI_C_GetContainerByAttachmentType");
static_assert(sizeof(W_DeadAIUI_C_GetContainerByAttachmentType) == 0x000018, "Wrong size on W_DeadAIUI_C_GetContainerByAttachmentType");
static_assert(offsetof(W_DeadAIUI_C_GetContainerByAttachmentType, Type) == 0x000000, "Member 'W_DeadAIUI_C_GetContainerByAttachmentType::Type' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_GetContainerByAttachmentType, JigContainer) == 0x000008, "Member 'W_DeadAIUI_C_GetContainerByAttachmentType::JigContainer' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_GetContainerByAttachmentType, ContainerIndex) == 0x000010, "Member 'W_DeadAIUI_C_GetContainerByAttachmentType::ContainerIndex' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.GetDropWidget
// 0x0008 (0x0008 - 0x0000)
struct W_DeadAIUI_C_GetDropWidget final
{
public:
	class UDropItemBackGwidget_C*                 DropWRef;                                          // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_GetDropWidget) == 0x000008, "Wrong alignment on W_DeadAIUI_C_GetDropWidget");
static_assert(sizeof(W_DeadAIUI_C_GetDropWidget) == 0x000008, "Wrong size on W_DeadAIUI_C_GetDropWidget");
static_assert(offsetof(W_DeadAIUI_C_GetDropWidget, DropWRef) == 0x000000, "Member 'W_DeadAIUI_C_GetDropWidget::DropWRef' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.GetJSIContainerByPlayerSlots
// 0x0020 (0x0020 - 0x0000)
struct W_DeadAIUI_C_GetJSIContainerByPlayerSlots final
{
public:
	struct FGameplayTag                           Slot_0;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        Container;                                         // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            EquippedItem;                                      // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          IsPending_;                                        // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_GetJSIContainerByPlayerSlots) == 0x000008, "Wrong alignment on W_DeadAIUI_C_GetJSIContainerByPlayerSlots");
static_assert(sizeof(W_DeadAIUI_C_GetJSIContainerByPlayerSlots) == 0x000020, "Wrong size on W_DeadAIUI_C_GetJSIContainerByPlayerSlots");
static_assert(offsetof(W_DeadAIUI_C_GetJSIContainerByPlayerSlots, Slot_0) == 0x000000, "Member 'W_DeadAIUI_C_GetJSIContainerByPlayerSlots::Slot_0' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_GetJSIContainerByPlayerSlots, Container) == 0x000008, "Member 'W_DeadAIUI_C_GetJSIContainerByPlayerSlots::Container' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_GetJSIContainerByPlayerSlots, EquippedItem) == 0x000010, "Member 'W_DeadAIUI_C_GetJSIContainerByPlayerSlots::EquippedItem' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_GetJSIContainerByPlayerSlots, IsPending_) == 0x000018, "Member 'W_DeadAIUI_C_GetJSIContainerByPlayerSlots::IsPending_' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.GetListOfNonAddContainers
// 0x0010 (0x0010 - 0x0000)
struct W_DeadAIUI_C_GetListOfNonAddContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_DeadAIUI_C_GetListOfNonAddContainers) == 0x000008, "Wrong alignment on W_DeadAIUI_C_GetListOfNonAddContainers");
static_assert(sizeof(W_DeadAIUI_C_GetListOfNonAddContainers) == 0x000010, "Wrong size on W_DeadAIUI_C_GetListOfNonAddContainers");
static_assert(offsetof(W_DeadAIUI_C_GetListOfNonAddContainers, Containers) == 0x000000, "Member 'W_DeadAIUI_C_GetListOfNonAddContainers::Containers' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.GetLootContent
// 0x0008 (0x0008 - 0x0000)
struct W_DeadAIUI_C_GetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_GetLootContent) == 0x000008, "Wrong alignment on W_DeadAIUI_C_GetLootContent");
static_assert(sizeof(W_DeadAIUI_C_GetLootContent) == 0x000008, "Wrong size on W_DeadAIUI_C_GetLootContent");
static_assert(offsetof(W_DeadAIUI_C_GetLootContent, Widget) == 0x000000, "Member 'W_DeadAIUI_C_GetLootContent::Widget' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.GetValidReloadContainers
// 0x0010 (0x0010 - 0x0000)
struct W_DeadAIUI_C_GetValidReloadContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_DeadAIUI_C_GetValidReloadContainers) == 0x000008, "Wrong alignment on W_DeadAIUI_C_GetValidReloadContainers");
static_assert(sizeof(W_DeadAIUI_C_GetValidReloadContainers) == 0x000010, "Wrong size on W_DeadAIUI_C_GetValidReloadContainers");
static_assert(offsetof(W_DeadAIUI_C_GetValidReloadContainers, Containers) == 0x000000, "Member 'W_DeadAIUI_C_GetValidReloadContainers::Containers' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.JigSetLootContent
// 0x0020 (0x0020 - 0x0000)
struct W_DeadAIUI_C_JigSetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   Name_0;                                            // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(W_DeadAIUI_C_JigSetLootContent) == 0x000008, "Wrong alignment on W_DeadAIUI_C_JigSetLootContent");
static_assert(sizeof(W_DeadAIUI_C_JigSetLootContent) == 0x000020, "Wrong size on W_DeadAIUI_C_JigSetLootContent");
static_assert(offsetof(W_DeadAIUI_C_JigSetLootContent, Widget) == 0x000000, "Member 'W_DeadAIUI_C_JigSetLootContent::Widget' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_JigSetLootContent, Name_0) == 0x000008, "Member 'W_DeadAIUI_C_JigSetLootContent::Name_0' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.JSIOnWeightUpdated
// 0x0008 (0x0008 - 0x0000)
struct W_DeadAIUI_C_JSIOnWeightUpdated final
{
public:
	double                                        NewWeight;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong alignment on W_DeadAIUI_C_JSIOnWeightUpdated");
static_assert(sizeof(W_DeadAIUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong size on W_DeadAIUI_C_JSIOnWeightUpdated");
static_assert(offsetof(W_DeadAIUI_C_JSIOnWeightUpdated, NewWeight) == 0x000000, "Member 'W_DeadAIUI_C_JSIOnWeightUpdated::NewWeight' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.SetActionbarFollower
// 0x0010 (0x0010 - 0x0000)
struct W_DeadAIUI_C_SetActionbarFollower final
{
public:
	class UJSI_Slot_C*                            JigRef;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Return;                                            // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_SetActionbarFollower) == 0x000008, "Wrong alignment on W_DeadAIUI_C_SetActionbarFollower");
static_assert(sizeof(W_DeadAIUI_C_SetActionbarFollower) == 0x000010, "Wrong size on W_DeadAIUI_C_SetActionbarFollower");
static_assert(offsetof(W_DeadAIUI_C_SetActionbarFollower, JigRef) == 0x000000, "Member 'W_DeadAIUI_C_SetActionbarFollower::JigRef' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_SetActionbarFollower, Return) == 0x000008, "Member 'W_DeadAIUI_C_SetActionbarFollower::Return' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.SetActorOwner
// 0x0008 (0x0008 - 0x0000)
struct W_DeadAIUI_C_SetActorOwner final
{
public:
	class AActor*                                 ActorRef;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_SetActorOwner) == 0x000008, "Wrong alignment on W_DeadAIUI_C_SetActorOwner");
static_assert(sizeof(W_DeadAIUI_C_SetActorOwner) == 0x000008, "Wrong size on W_DeadAIUI_C_SetActorOwner");
static_assert(offsetof(W_DeadAIUI_C_SetActorOwner, ActorRef) == 0x000000, "Member 'W_DeadAIUI_C_SetActorOwner::ActorRef' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.SetInspectorRef
// 0x0008 (0x0008 - 0x0000)
struct W_DeadAIUI_C_SetInspectorRef final
{
public:
	class UBP_InspectorWindowWidget_C*            Inspector;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_SetInspectorRef) == 0x000008, "Wrong alignment on W_DeadAIUI_C_SetInspectorRef");
static_assert(sizeof(W_DeadAIUI_C_SetInspectorRef) == 0x000008, "Wrong size on W_DeadAIUI_C_SetInspectorRef");
static_assert(offsetof(W_DeadAIUI_C_SetInspectorRef, Inspector) == 0x000000, "Member 'W_DeadAIUI_C_SetInspectorRef::Inspector' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.SetItemReference
// 0x0008 (0x0008 - 0x0000)
struct W_DeadAIUI_C_SetItemReference final
{
public:
	class UJSI_Slot_C*                            ItemRef;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_SetItemReference) == 0x000008, "Wrong alignment on W_DeadAIUI_C_SetItemReference");
static_assert(sizeof(W_DeadAIUI_C_SetItemReference) == 0x000008, "Wrong size on W_DeadAIUI_C_SetItemReference");
static_assert(offsetof(W_DeadAIUI_C_SetItemReference, ItemRef) == 0x000000, "Member 'W_DeadAIUI_C_SetItemReference::ItemRef' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.Tick
// 0x003C (0x003C - 0x0000)
struct W_DeadAIUI_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_DeadAIUI_C_Tick) == 0x000004, "Wrong alignment on W_DeadAIUI_C_Tick");
static_assert(sizeof(W_DeadAIUI_C_Tick) == 0x00003C, "Wrong size on W_DeadAIUI_C_Tick");
static_assert(offsetof(W_DeadAIUI_C_Tick, MyGeometry) == 0x000000, "Member 'W_DeadAIUI_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_Tick, InDeltaTime) == 0x000038, "Member 'W_DeadAIUI_C_Tick::InDeltaTime' has a wrong offset!");

// Function W_DeadAIUI.W_DeadAIUI_C.GetListOfContainers
// 0x0020 (0x0020 - 0x0000)
struct W_DeadAIUI_C_GetListOfContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
	TArray<class UJSIContainer_C*>                K2Node_MakeArray_Array;                            // 0x0010(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(W_DeadAIUI_C_GetListOfContainers) == 0x000008, "Wrong alignment on W_DeadAIUI_C_GetListOfContainers");
static_assert(sizeof(W_DeadAIUI_C_GetListOfContainers) == 0x000020, "Wrong size on W_DeadAIUI_C_GetListOfContainers");
static_assert(offsetof(W_DeadAIUI_C_GetListOfContainers, Containers) == 0x000000, "Member 'W_DeadAIUI_C_GetListOfContainers::Containers' has a wrong offset!");
static_assert(offsetof(W_DeadAIUI_C_GetListOfContainers, K2Node_MakeArray_Array) == 0x000010, "Member 'W_DeadAIUI_C_GetListOfContainers::K2Node_MakeArray_Array' has a wrong offset!");

}

