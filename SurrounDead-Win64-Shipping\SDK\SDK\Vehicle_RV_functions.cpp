﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_RV

#include "Basic.hpp"

#include "Vehicle_RV_classes.hpp"
#include "Vehicle_RV_parameters.hpp"


namespace SDK
{

// Function Vehicle_RV.Vehicle_RV_C.ExecuteUbergraph_Vehicle_RV
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AVehicle_RV_C::ExecuteUbergraph_Vehicle_RV(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_RV_C", "ExecuteUbergraph_Vehicle_RV");

	Params::Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Vehicle_RV.Vehicle_RV_C.SleepInteractionOption
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class AActor*                           InteractingActor                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void AVehicle_RV_C::SleepInteractionOption(class AActor* InteractingActor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_RV_C", "SleepInteractionOption");

	Params::Vehicle_RV_C_SleepInteractionOption Parms{};

	Parms.InteractingActor = InteractingActor;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Vehicle_RV.Vehicle_RV_C.UserConstructionScript
// (Event, Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AVehicle_RV_C::UserConstructionScript()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_RV_C", "UserConstructionScript");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Vehicle_RV.Vehicle_RV_C.GetInteractOptions
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// TMap<struct FGameplayTag, class FText>* Options                                                (Parm, OutParm)

void AVehicle_RV_C::GetInteractOptions(TMap<struct FGameplayTag, class FText>* Options)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_RV_C", "GetInteractOptions");

	Params::Vehicle_RV_C_GetInteractOptions Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Options != nullptr)
		*Options = std::move(Parms.Options);
}


// Function Vehicle_RV.Vehicle_RV_C.ReceiveBeginPlay
// (Event, Protected, BlueprintEvent)

void AVehicle_RV_C::ReceiveBeginPlay()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_RV_C", "ReceiveBeginPlay");

	UObject::ProcessEvent(Func, nullptr);
}

}

