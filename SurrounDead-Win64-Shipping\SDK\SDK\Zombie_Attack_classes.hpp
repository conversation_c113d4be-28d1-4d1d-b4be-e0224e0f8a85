﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_Attack

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AIModule_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass Zombie_Attack.Zombie_Attack_C
// 0x0008 (0x00B0 - 0x00A8)
class UZombie_Attack_C final : public UBTTask_BlueprintBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x00A8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)

public:
	void ExecuteUbergraph_Zombie_Attack(int32 EntryPoint);
	void ReceiveExecuteAI(class AAIController* OwnerController, class APawn* ControlledPawn);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Zombie_Attack_C">();
	}
	static class UZombie_Attack_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UZombie_Attack_C>();
	}
};
static_assert(alignof(UZombie_Attack_C) == 0x000008, "Wrong alignment on UZombie_Attack_C");
static_assert(sizeof(UZombie_Attack_C) == 0x0000B0, "Wrong size on UZombie_Attack_C");
static_assert(offsetof(UZombie_Attack_C, UberGraphFrame) == 0x0000A8, "Member 'UZombie_Attack_C::UberGraphFrame' has a wrong offset!");

}

