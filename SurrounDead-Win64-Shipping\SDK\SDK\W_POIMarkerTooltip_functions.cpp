﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_POIMarkerTooltip

#include "Basic.hpp"

#include "W_POIMarkerTooltip_classes.hpp"
#include "W_POIMarkerTooltip_parameters.hpp"


namespace SDK
{

// Function W_POIMarkerTooltip.W_POIMarkerTooltip_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_POIMarkerTooltip_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_POIMarkerTooltip_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_POIMarkerTooltip.W_POIMarkerTooltip_C.ExecuteUbergraph_W_POIMarkerTooltip
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_POIMarkerTooltip_C::ExecuteUbergraph_W_POIMarkerTooltip(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_POIMarkerTooltip_C", "ExecuteUbergraph_W_POIMarkerTooltip");

	Params::W_POIMarkerTooltip_C_ExecuteUbergraph_W_POIMarkerTooltip Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}

}

