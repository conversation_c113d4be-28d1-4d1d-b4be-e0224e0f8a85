﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Effect_Circular

#include "Basic.hpp"

#include "S_Effects_structs.hpp"
#include "EEffectType_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "ETriggerMethod_structs.hpp"
#include "UMG_structs.hpp"


namespace SDK::Params
{

// Function WB_Effect_Circular.WB_Effect_Circular_C.AddAttributes
// 0x0060 (0x0060 - 0x0000)
struct WB_Effect_Circular_C_AddAttributes final
{
public:
	class UImage*                                 Image;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectShear_Effect_Shear;              // 0x0008(0x0010)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetEffectAngle_Effect_Angle;              // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UObject*                                CallFunc_GetEffectTexture_Custom_Effect_Texture;   // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_GetEffectColor_Effect_Color;              // 0x0028(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectTranslation_Transition;          // 0x0038(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectScale_EffectScale;               // 0x0048(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetRenderTransformAngle_Angle_ImplicitCast; // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_AddAttributes) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_AddAttributes");
static_assert(sizeof(WB_Effect_Circular_C_AddAttributes) == 0x000060, "Wrong size on WB_Effect_Circular_C_AddAttributes");
static_assert(offsetof(WB_Effect_Circular_C_AddAttributes, Image) == 0x000000, "Member 'WB_Effect_Circular_C_AddAttributes::Image' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_AddAttributes, CallFunc_GetEffectShear_Effect_Shear) == 0x000008, "Member 'WB_Effect_Circular_C_AddAttributes::CallFunc_GetEffectShear_Effect_Shear' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_AddAttributes, CallFunc_GetEffectAngle_Effect_Angle) == 0x000018, "Member 'WB_Effect_Circular_C_AddAttributes::CallFunc_GetEffectAngle_Effect_Angle' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_AddAttributes, CallFunc_GetEffectTexture_Custom_Effect_Texture) == 0x000020, "Member 'WB_Effect_Circular_C_AddAttributes::CallFunc_GetEffectTexture_Custom_Effect_Texture' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_AddAttributes, CallFunc_GetEffectColor_Effect_Color) == 0x000028, "Member 'WB_Effect_Circular_C_AddAttributes::CallFunc_GetEffectColor_Effect_Color' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_AddAttributes, CallFunc_GetEffectTranslation_Transition) == 0x000038, "Member 'WB_Effect_Circular_C_AddAttributes::CallFunc_GetEffectTranslation_Transition' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_AddAttributes, CallFunc_GetEffectScale_EffectScale) == 0x000048, "Member 'WB_Effect_Circular_C_AddAttributes::CallFunc_GetEffectScale_EffectScale' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_AddAttributes, CallFunc_SetRenderTransformAngle_Angle_ImplicitCast) == 0x000058, "Member 'WB_Effect_Circular_C_AddAttributes::CallFunc_SetRenderTransformAngle_Angle_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.AddEffect
// 0x0088 (0x0088 - 0x0000)
struct WB_Effect_Circular_C_AddEffect final
{
public:
	struct FS_Effects                             Effect_0;                                          // 0x0000(0x0088)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_AddEffect) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_AddEffect");
static_assert(sizeof(WB_Effect_Circular_C_AddEffect) == 0x000088, "Wrong size on WB_Effect_Circular_C_AddEffect");
static_assert(offsetof(WB_Effect_Circular_C_AddEffect, Effect_0) == 0x000000, "Member 'WB_Effect_Circular_C_AddEffect::Effect_0' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.Anim_Fade
// 0x0038 (0x0038 - 0x0000)
struct WB_Effect_Circular_C_Anim_Fade final
{
public:
	bool                                          FadeIn_0;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        PlaybackSpeed;                                     // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable_1;                              // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13[0x1];                                       // 0x0013(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_Select_Default_1;                           // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_25[0x3];                                       // 0x0025(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast; // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_Anim_Fade) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_Anim_Fade");
static_assert(sizeof(WB_Effect_Circular_C_Anim_Fade) == 0x000038, "Wrong size on WB_Effect_Circular_C_Anim_Fade");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, FadeIn_0) == 0x000000, "Member 'WB_Effect_Circular_C_Anim_Fade::FadeIn_0' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, PlaybackSpeed) == 0x000008, "Member 'WB_Effect_Circular_C_Anim_Fade::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, Temp_byte_Variable) == 0x000010, "Member 'WB_Effect_Circular_C_Anim_Fade::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, Temp_byte_Variable_1) == 0x000011, "Member 'WB_Effect_Circular_C_Anim_Fade::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, Temp_bool_Variable) == 0x000012, "Member 'WB_Effect_Circular_C_Anim_Fade::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, Temp_int_Variable) == 0x000014, "Member 'WB_Effect_Circular_C_Anim_Fade::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, Temp_int_Variable_1) == 0x000018, "Member 'WB_Effect_Circular_C_Anim_Fade::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, Temp_bool_Variable_1) == 0x00001C, "Member 'WB_Effect_Circular_C_Anim_Fade::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, K2Node_Select_Default) == 0x000020, "Member 'WB_Effect_Circular_C_Anim_Fade::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, K2Node_Select_Default_1) == 0x000024, "Member 'WB_Effect_Circular_C_Anim_Fade::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, CallFunc_PlayAnimation_ReturnValue) == 0x000028, "Member 'WB_Effect_Circular_C_Anim_Fade::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Fade, CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast) == 0x000030, "Member 'WB_Effect_Circular_C_Anim_Fade::CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.Anim_Highlight1
// 0x0030 (0x0030 - 0x0000)
struct WB_Effect_Circular_C_Anim_Highlight1 final
{
public:
	double                                        PlaybackSpeed;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable_1;                              // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B[0x1];                                        // 0x000B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_Select_Default_1;                           // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast; // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_Anim_Highlight1) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_Anim_Highlight1");
static_assert(sizeof(WB_Effect_Circular_C_Anim_Highlight1) == 0x000030, "Wrong size on WB_Effect_Circular_C_Anim_Highlight1");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, PlaybackSpeed) == 0x000000, "Member 'WB_Effect_Circular_C_Anim_Highlight1::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, Temp_byte_Variable) == 0x000008, "Member 'WB_Effect_Circular_C_Anim_Highlight1::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, Temp_byte_Variable_1) == 0x000009, "Member 'WB_Effect_Circular_C_Anim_Highlight1::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, Temp_bool_Variable) == 0x00000A, "Member 'WB_Effect_Circular_C_Anim_Highlight1::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, Temp_int_Variable) == 0x00000C, "Member 'WB_Effect_Circular_C_Anim_Highlight1::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, Temp_int_Variable_1) == 0x000010, "Member 'WB_Effect_Circular_C_Anim_Highlight1::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, Temp_bool_Variable_1) == 0x000014, "Member 'WB_Effect_Circular_C_Anim_Highlight1::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, K2Node_Select_Default) == 0x000018, "Member 'WB_Effect_Circular_C_Anim_Highlight1::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, K2Node_Select_Default_1) == 0x00001C, "Member 'WB_Effect_Circular_C_Anim_Highlight1::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, CallFunc_PlayAnimation_ReturnValue) == 0x000020, "Member 'WB_Effect_Circular_C_Anim_Highlight1::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight1, CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast) == 0x000028, "Member 'WB_Effect_Circular_C_Anim_Highlight1::CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.Anim_Highlight2
// 0x0030 (0x0030 - 0x0000)
struct WB_Effect_Circular_C_Anim_Highlight2 final
{
public:
	double                                        PlaybackSpeed;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable_1;                              // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B[0x1];                                        // 0x000B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_Select_Default_1;                           // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast; // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_Anim_Highlight2) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_Anim_Highlight2");
static_assert(sizeof(WB_Effect_Circular_C_Anim_Highlight2) == 0x000030, "Wrong size on WB_Effect_Circular_C_Anim_Highlight2");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, PlaybackSpeed) == 0x000000, "Member 'WB_Effect_Circular_C_Anim_Highlight2::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, Temp_byte_Variable) == 0x000008, "Member 'WB_Effect_Circular_C_Anim_Highlight2::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, Temp_byte_Variable_1) == 0x000009, "Member 'WB_Effect_Circular_C_Anim_Highlight2::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, Temp_bool_Variable) == 0x00000A, "Member 'WB_Effect_Circular_C_Anim_Highlight2::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, Temp_int_Variable) == 0x00000C, "Member 'WB_Effect_Circular_C_Anim_Highlight2::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, Temp_int_Variable_1) == 0x000010, "Member 'WB_Effect_Circular_C_Anim_Highlight2::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, Temp_bool_Variable_1) == 0x000014, "Member 'WB_Effect_Circular_C_Anim_Highlight2::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, K2Node_Select_Default) == 0x000018, "Member 'WB_Effect_Circular_C_Anim_Highlight2::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, K2Node_Select_Default_1) == 0x00001C, "Member 'WB_Effect_Circular_C_Anim_Highlight2::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, CallFunc_PlayAnimation_ReturnValue) == 0x000020, "Member 'WB_Effect_Circular_C_Anim_Highlight2::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_Highlight2, CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast) == 0x000028, "Member 'WB_Effect_Circular_C_Anim_Highlight2::CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.Anim_HighlightLoop
// 0x0038 (0x0038 - 0x0000)
struct WB_Effect_Circular_C_Anim_HighlightLoop final
{
public:
	bool                                          StartStop;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        PlaybackSpeed;                                     // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          Temp_byte_Variable_1;                              // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13[0x1];                                       // 0x0013(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUMGSequencePlayMode                          K2Node_Select_Default_1;                           // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_25[0x3];                                       // 0x0025(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast; // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_Anim_HighlightLoop) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_Anim_HighlightLoop");
static_assert(sizeof(WB_Effect_Circular_C_Anim_HighlightLoop) == 0x000038, "Wrong size on WB_Effect_Circular_C_Anim_HighlightLoop");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, StartStop) == 0x000000, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::StartStop' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, PlaybackSpeed) == 0x000008, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, Temp_byte_Variable) == 0x000010, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, Temp_byte_Variable_1) == 0x000011, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, Temp_bool_Variable) == 0x000012, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, Temp_int_Variable) == 0x000014, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, Temp_int_Variable_1) == 0x000018, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, Temp_bool_Variable_1) == 0x00001C, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, K2Node_Select_Default) == 0x000020, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, K2Node_Select_Default_1) == 0x000024, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, CallFunc_PlayAnimation_ReturnValue) == 0x000028, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Anim_HighlightLoop, CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast) == 0x000030, "Member 'WB_Effect_Circular_C_Anim_HighlightLoop::CallFunc_PlayAnimation_PlaybackSpeed_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.CreateParticle
// 0x0004 (0x0004 - 0x0000)
struct WB_Effect_Circular_C_CreateParticle final
{
public:
	int32                                         NumParticles;                                      // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_CreateParticle) == 0x000004, "Wrong alignment on WB_Effect_Circular_C_CreateParticle");
static_assert(sizeof(WB_Effect_Circular_C_CreateParticle) == 0x000004, "Wrong size on WB_Effect_Circular_C_CreateParticle");
static_assert(offsetof(WB_Effect_Circular_C_CreateParticle, NumParticles) == 0x000000, "Member 'WB_Effect_Circular_C_CreateParticle::NumParticles' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.EventPreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_Circular_C_EventPreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_EventPreConstruct) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_EventPreConstruct");
static_assert(sizeof(WB_Effect_Circular_C_EventPreConstruct) == 0x000001, "Wrong size on WB_Effect_Circular_C_EventPreConstruct");
static_assert(offsetof(WB_Effect_Circular_C_EventPreConstruct, IsDesignTime) == 0x000000, "Member 'WB_Effect_Circular_C_EventPreConstruct::IsDesignTime' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.ExecuteUbergraph_WB_Effect_Circular
// 0x0420 (0x0420 - 0x0000)
struct WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_1;               // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_2;               // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x3];                                        // 0x0009(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_3;               // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_4;               // 0x001D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_1;                     // 0x001E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_2;                     // 0x001F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_5;               // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_3;                     // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_22[0x6];                                       // 0x0022(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_6;               // 0x0031(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_32[0x2];                                       // 0x0032(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_2;                               // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_4;                     // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetUseParticleStretched_Use_Stretched_Particle; // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_42[0x6];                                       // 0x0042(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelWidget*                           K2Node_Select_Default;                             // 0x0048(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0050(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0060(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Particle_C*                         K2Node_DynamicCast_AsWB_Particle;                  // 0x0070(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0079(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x007A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_5;                     // 0x007B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_6;                     // 0x007C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetUseParticleStretched_Use_Stretched_Particle_1; // 0x007D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7E[0x2];                                       // 0x007E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(double Percent)>               K2Node_CreateDelegate_OutputDelegate_1;            // 0x0080(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_7;                     // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_7;               // 0x0091(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_8;               // 0x0092(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_8;                     // 0x0093(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_9;               // 0x0094(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_2;                              // 0x0095(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_9;                     // 0x0096(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_97[0x1];                                       // 0x0097(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_2;            // 0x0098(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value; // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_1; // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x00B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x00B9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_BA[0x6];                                       // 0x00BA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Particle_C*                         CallFunc_Create_ReturnValue;                       // 0x00C0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue;       // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_3;                              // 0x00C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_10;              // 0x00CA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue_1;     // 0x00CB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_CC[0x4];                                       // 0x00CC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_2; // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue_2;     // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E1[0x7];                                       // 0x00E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_3; // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue_3;     // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_10;                    // 0x00F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_1;          // 0x00F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F3[0x5];                                       // 0x00F3(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_4; // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue_4;     // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_4;                              // 0x0101(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_2;          // 0x0102(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_5;                              // 0x0103(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_DoubleDouble_ReturnValue;        // 0x0104(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_3;          // 0x0105(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_6;                              // 0x0106(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_4;          // 0x0107(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_11;              // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_7;                              // 0x0109(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_10A[0x6];                                      // 0x010A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Size;                           // 0x0110(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Thickness;                      // 0x0118(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DoubleDouble_ReturnValue;      // 0x0120(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_121[0x7];                                      // 0x0121(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FS_Effects                             K2Node_CustomEvent_Effect;                         // 0x0128(0x0088)(NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_1;                           // 0x01B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x01B8(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x01F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x01F4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x01F5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_IsDesignTime;                   // 0x01F6(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_11;                    // 0x01F7(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_12;              // 0x01F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_12;                    // 0x01F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x01FA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1FB[0x1];                                      // 0x01FB(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_CustomEvent_NumParticles;                   // 0x01FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x0200(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_201[0x7];                                      // 0x0201(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                CallFunc_GetEffectTexture_Custom_Effect_Texture;   // 0x0208(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetPlaybackSpeed_Playback_Speed;          // 0x0210(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_GetEffectColor_Effect_Color;              // 0x0218(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectScale_EffectScale;               // 0x0228(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectTranslation_Transition;          // 0x0238(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetEffectAngle_Effect_Angle;              // 0x0248(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetEffectShear_Effect_Shear;              // 0x0250(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_3;                               // 0x0260(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_264[0x4];                                      // 0x0264(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0268(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger;                 // 0x0278(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_279[0x7];                                      // 0x0279(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetParticleSpread_Particle_Spread;        // 0x0280(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetAddRotationToParticles_Add_Rotation_to_Particles; // 0x0288(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_289[0x7];                                      // 0x0289(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class USoundBase*                             CallFunc_GetSoundEffect_Sound_Effect;              // 0x0290(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffect_ReturnValue;      // 0x0298(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_299[0x7];                                      // 0x0299(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSoundVolume_Sound_Volume_Multiplier;   // 0x02A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue; // 0x02A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2A9[0x7];                                      // 0x02A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAudioComponent*                        CallFunc_CreateSound2D_ReturnValue;                // 0x02B0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x02B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffect_ReturnValue_1;    // 0x02B9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_1; // 0x02BA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_13;              // 0x02BB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x02BC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2BD[0x3];                                      // 0x02BD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetDecayTime_Decay_Time;                  // 0x02C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_GetEffectColor_Effect_Color_1;            // 0x02C8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x02D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeProgressChangeColor_ReturnValue; // 0x02D9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_5;          // 0x02DA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2DB[0x5];                                      // 0x02DB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x02E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_2;                           // 0x02E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x02F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x02F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x0300(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetUseParticleStretched_Use_Stretched_Particle_2; // 0x0310(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetUseParticleStretched_Use_Stretched_Particle_3; // 0x0311(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_13;                    // 0x0312(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x0313(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeParticle_ReturnValue;         // 0x0314(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_14;              // 0x0315(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_316[0x2];                                      // 0x0316(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSpecificPercentValue_Specific_Percent_Value_5; // 0x0318(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsTriggerSpecificPercent_ReturnValue;     // 0x0320(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_321[0x7];                                      // 0x0321(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default_3;                           // 0x0328(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_4;                               // 0x0330(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_334[0x4];                                      // 0x0334(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0338(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_1;            // 0x0340(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_14;                    // 0x0348(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_15;                    // 0x0349(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34A[0x6];                                      // 0x034A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue;         // 0x0350(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_8;                              // 0x0358(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_2; // 0x0359(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_35A[0x2];                                      // 0x035A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default_4;                           // 0x035C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_5;                           // 0x0360(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_15;              // 0x0368(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_16;              // 0x0369(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_16;                    // 0x036A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_17;                    // 0x036B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_18;                    // 0x036C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger_1;               // 0x036D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x036E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetUseParticleStretched_Use_Stretched_Particle_4; // 0x036F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_9;                              // 0x0370(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_371[0x7];                                      // 0x0371(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UOverlay*                               K2Node_Select_Default_6;                           // 0x0378(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UOverlaySlot*                           CallFunc_AddChildToOverlay_ReturnValue;            // 0x0380(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_17;              // 0x0388(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsAnimationPlayingForward_ReturnValue;    // 0x0389(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_10;                             // 0x038A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_3;                // 0x038B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Select_Default_7;                           // 0x038C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_19;                    // 0x038D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_38E[0x2];                                      // 0x038E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetNumParticles_NumParticles;             // 0x0390(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeParticle_ReturnValue_1;       // 0x0394(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_11;                             // 0x0395(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_18;              // 0x0396(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_397[0x1];                                      // 0x0397(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBarCircular_C*                 K2Node_CustomEvent_ProgressBar;                    // 0x0398(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Percent;                        // 0x03A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_8;                           // 0x03A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_19;              // 0x03B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3B1[0x7];                                      // 0x03B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FInterpTo_Constant_ReturnValue;           // 0x03B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_9;                           // 0x03C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_20;                    // 0x03C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_4;                // 0x03C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_20;              // 0x03CA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_12;                             // 0x03CB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Select_Default_10;                          // 0x03CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x03CD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsPlaying_ReturnValue;                    // 0x03CE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffect_ReturnValue_2;    // 0x03CF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_13;                             // 0x03D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeParticle_ReturnValue_2;       // 0x03D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3D2[0x6];                                      // 0x03D2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelWidget*                           K2Node_Select_Default_11;                          // 0x03D8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_3; // 0x03E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3E1[0x7];                                      // 0x03E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_1;                 // 0x03E8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue_1;           // 0x03F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3F4[0x4];                                      // 0x03F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Particle_C*                         K2Node_DynamicCast_AsWB_Particle_1;                // 0x03F8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0400(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_2;           // 0x0401(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_3;                    // 0x0402(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeSoundEffect_ReturnValue_3;    // 0x0403(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsEffectTypeProgressChangeColor_ReturnValue_1; // 0x0404(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsTriggeredAlwaysOnSpecificPercentValue_ReturnValue; // 0x0405(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_2;                  // 0x0406(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsTriggeredAlways_ReturnValue;            // 0x0407(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_3;                  // 0x0408(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_4;                  // 0x0409(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_5;                  // 0x040A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_5;                // 0x040B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x040C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_21;              // 0x040D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_21;                    // 0x040E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_40F[0x1];                                      // 0x040F(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_CreateSound2D_VolumeMultiplier_ImplicitCast; // 0x0410(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetRenderTransformAngle_Angle_ImplicitCast; // 0x0414(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetRenderTransformAngle_Angle_ImplicitCast_1; // 0x0418(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Delay_Duration_ImplicitCast;              // 0x041C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular");
static_assert(sizeof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular) == 0x000420, "Wrong size on WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, EntryPoint) == 0x000000, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable) == 0x000004, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable) == 0x000005, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable) == 0x000006, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_1) == 0x000007, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_2) == 0x000008, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_int_Variable) == 0x00000C, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Add_IntInt_ReturnValue) == 0x000010, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_int_Variable_1) == 0x000014, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Add_IntInt_ReturnValue_1) == 0x000018, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_3) == 0x00001C, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_4) == 0x00001D, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_1) == 0x00001E, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_2) == 0x00001F, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_5) == 0x000020, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_3) == 0x000021, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_real_Variable) == 0x000028, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_1) == 0x000030, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_6) == 0x000031, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_int_Variable_2) == 0x000034, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_int_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsValid_ReturnValue) == 0x000038, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Add_IntInt_ReturnValue_2) == 0x00003C, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_4) == 0x000040, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetUseParticleStretched_Use_Stretched_Particle) == 0x000041, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetUseParticleStretched_Use_Stretched_Particle' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default) == 0x000048, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CreateDelegate_OutputDelegate) == 0x000050, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetChildAt_ReturnValue) == 0x000060, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetChildrenCount_ReturnValue) == 0x000068, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_DynamicCast_AsWB_Particle) == 0x000070, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_DynamicCast_AsWB_Particle' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_DynamicCast_bSuccess) == 0x000078, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000079, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsValid_ReturnValue_1) == 0x00007A, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_5) == 0x00007B, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_6) == 0x00007C, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetUseParticleStretched_Use_Stretched_Particle_1) == 0x00007D, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetUseParticleStretched_Use_Stretched_Particle_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CreateDelegate_OutputDelegate_1) == 0x000080, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_7) == 0x000090, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_7) == 0x000091, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_8) == 0x000092, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_8) == 0x000093, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_9) == 0x000094, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_9' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_2) == 0x000095, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_9) == 0x000096, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_9' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CreateDelegate_OutputDelegate_2) == 0x000098, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CreateDelegate_OutputDelegate_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetSpecificPercentValue_Specific_Percent_Value) == 0x0000A8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetSpecificPercentValue_Specific_Percent_Value' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_1) == 0x0000B0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x0000B8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Less_DoubleDouble_ReturnValue) == 0x0000B9, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Create_ReturnValue) == 0x0000C0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_NearlyEqual_FloatFloat_ReturnValue) == 0x0000C8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_NearlyEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_3) == 0x0000C9, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_10) == 0x0000CA, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_10' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_NearlyEqual_FloatFloat_ReturnValue_1) == 0x0000CB, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_NearlyEqual_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_2) == 0x0000D0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_real_Variable_1) == 0x0000D8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_NearlyEqual_FloatFloat_ReturnValue_2) == 0x0000E0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_NearlyEqual_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_3) == 0x0000E8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_NearlyEqual_FloatFloat_ReturnValue_3) == 0x0000F0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_NearlyEqual_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_10) == 0x0000F1, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_10' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Less_DoubleDouble_ReturnValue_1) == 0x0000F2, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Less_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_4) == 0x0000F8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_NearlyEqual_FloatFloat_ReturnValue_4) == 0x000100, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_NearlyEqual_FloatFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_4) == 0x000101, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Less_DoubleDouble_ReturnValue_2) == 0x000102, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Less_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_5) == 0x000103, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_NotEqual_DoubleDouble_ReturnValue) == 0x000104, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_NotEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Less_DoubleDouble_ReturnValue_3) == 0x000105, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Less_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_6) == 0x000106, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Less_DoubleDouble_ReturnValue_4) == 0x000107, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Less_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_11) == 0x000108, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_11' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_7) == 0x000109, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CustomEvent_Size) == 0x000110, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CustomEvent_Size' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CustomEvent_Thickness) == 0x000118, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CustomEvent_Thickness' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_EqualEqual_DoubleDouble_ReturnValue) == 0x000120, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_EqualEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CustomEvent_Effect) == 0x000128, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CustomEvent_Effect' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_1) == 0x0001B0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Event_MyGeometry) == 0x0001B8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Event_InDeltaTime) == 0x0001F0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetEffectType_Effect_Texture_Type) == 0x0001F4, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_SwitchEnum_CmpSuccess) == 0x0001F5, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CustomEvent_IsDesignTime) == 0x0001F6, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CustomEvent_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_11) == 0x0001F7, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_11' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_12) == 0x0001F8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_12' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_12) == 0x0001F9, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_12' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Not_PreBool_ReturnValue) == 0x0001FA, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CustomEvent_NumParticles) == 0x0001FC, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CustomEvent_NumParticles' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x000200, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetEffectTexture_Custom_Effect_Texture) == 0x000208, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetEffectTexture_Custom_Effect_Texture' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetPlaybackSpeed_Playback_Speed) == 0x000210, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetPlaybackSpeed_Playback_Speed' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetEffectColor_Effect_Color) == 0x000218, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetEffectColor_Effect_Color' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetEffectScale_EffectScale) == 0x000228, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetEffectScale_EffectScale' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetEffectTranslation_Transition) == 0x000238, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetEffectTranslation_Transition' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetEffectAngle_Effect_Angle) == 0x000248, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetEffectAngle_Effect_Angle' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetEffectShear_Effect_Shear) == 0x000250, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetEffectShear_Effect_Shear' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_int_Variable_3) == 0x000260, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_int_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_MakeVector2D_ReturnValue) == 0x000268, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetTriggerMethod_Trigger) == 0x000278, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetTriggerMethod_Trigger' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetParticleSpread_Particle_Spread) == 0x000280, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetParticleSpread_Particle_Spread' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetAddRotationToParticles_Add_Rotation_to_Particles) == 0x000288, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetAddRotationToParticles_Add_Rotation_to_Particles' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetSoundEffect_Sound_Effect) == 0x000290, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetSoundEffect_Sound_Effect' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeSoundEffect_ReturnValue) == 0x000298, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeSoundEffect_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetSoundVolume_Sound_Volume_Multiplier) == 0x0002A0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetSoundVolume_Sound_Volume_Multiplier' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue) == 0x0002A8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_CreateSound2D_ReturnValue) == 0x0002B0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_CreateSound2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_BooleanOR_ReturnValue) == 0x0002B8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeSoundEffect_ReturnValue_1) == 0x0002B9, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeSoundEffect_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_1) == 0x0002BA, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_13) == 0x0002BB, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_13' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_BooleanOR_ReturnValue_1) == 0x0002BC, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetDecayTime_Decay_Time) == 0x0002C0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetDecayTime_Decay_Time' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetEffectColor_Effect_Color_1) == 0x0002C8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetEffectColor_Effect_Color_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Not_PreBool_ReturnValue_1) == 0x0002D8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeProgressChangeColor_ReturnValue) == 0x0002D9, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeProgressChangeColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Less_DoubleDouble_ReturnValue_5) == 0x0002DA, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Less_DoubleDouble_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x0002E0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_2) == 0x0002E8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0002F0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x0002F8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_MakeVector2D_ReturnValue_1) == 0x000300, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetUseParticleStretched_Use_Stretched_Particle_2) == 0x000310, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetUseParticleStretched_Use_Stretched_Particle_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetUseParticleStretched_Use_Stretched_Particle_3) == 0x000311, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetUseParticleStretched_Use_Stretched_Particle_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_13) == 0x000312, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_13' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Not_PreBool_ReturnValue_2) == 0x000313, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeParticle_ReturnValue) == 0x000314, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeParticle_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_14) == 0x000315, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_14' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetSpecificPercentValue_Specific_Percent_Value_5) == 0x000318, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetSpecificPercentValue_Specific_Percent_Value_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsTriggerSpecificPercent_ReturnValue) == 0x000320, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsTriggerSpecificPercent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_3) == 0x000328, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_int_Variable_4) == 0x000330, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_int_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_MapRangeClamped_ReturnValue) == 0x000338, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_MapRangeClamped_ReturnValue_1) == 0x000340, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_MapRangeClamped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_14) == 0x000348, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_14' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_15) == 0x000349, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_15' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetWorldDeltaSeconds_ReturnValue) == 0x000350, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetWorldDeltaSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_8) == 0x000358, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_2) == 0x000359, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_4) == 0x00035C, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_5) == 0x000360, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_15) == 0x000368, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_15' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_16) == 0x000369, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_16' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_16) == 0x00036A, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_16' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_17) == 0x00036B, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_17' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_18) == 0x00036C, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_18' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetTriggerMethod_Trigger_1) == 0x00036D, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetTriggerMethod_Trigger_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_SwitchEnum_CmpSuccess_1) == 0x00036E, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetUseParticleStretched_Use_Stretched_Particle_4) == 0x00036F, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetUseParticleStretched_Use_Stretched_Particle_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_9) == 0x000370, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_9' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_6) == 0x000378, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_6' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_AddChildToOverlay_ReturnValue) == 0x000380, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_AddChildToOverlay_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_17) == 0x000388, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_17' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsAnimationPlayingForward_ReturnValue) == 0x000389, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsAnimationPlayingForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_10) == 0x00038A, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_10' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Not_PreBool_ReturnValue_3) == 0x00038B, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Not_PreBool_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_7) == 0x00038C, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_7' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_19) == 0x00038D, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_19' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetNumParticles_NumParticles) == 0x000390, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetNumParticles_NumParticles' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeParticle_ReturnValue_1) == 0x000394, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeParticle_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_11) == 0x000395, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_11' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_18) == 0x000396, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_18' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CustomEvent_ProgressBar) == 0x000398, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CustomEvent_ProgressBar' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_CustomEvent_Percent) == 0x0003A0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_CustomEvent_Percent' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_8) == 0x0003A8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_8' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_19) == 0x0003B0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_19' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_FInterpTo_Constant_ReturnValue) == 0x0003B8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_FInterpTo_Constant_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_9) == 0x0003C0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_9' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_20) == 0x0003C8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_20' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Not_PreBool_ReturnValue_4) == 0x0003C9, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Not_PreBool_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_20) == 0x0003CA, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_20' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_12) == 0x0003CB, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_12' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_10) == 0x0003CC, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_10' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsValid_ReturnValue_2) == 0x0003CD, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsPlaying_ReturnValue) == 0x0003CE, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeSoundEffect_ReturnValue_2) == 0x0003CF, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeSoundEffect_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Variable_13) == 0x0003D0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Variable_13' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeParticle_ReturnValue_2) == 0x0003D1, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeParticle_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_Select_Default_11) == 0x0003D8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_Select_Default_11' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_3) == 0x0003E0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeSoundEffectLooped_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetChildAt_ReturnValue_1) == 0x0003E8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetChildAt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_GetChildrenCount_ReturnValue_1) == 0x0003F0, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_GetChildrenCount_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_DynamicCast_AsWB_Particle_1) == 0x0003F8, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_DynamicCast_AsWB_Particle_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, K2Node_DynamicCast_bSuccess_1) == 0x000400, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_LessEqual_IntInt_ReturnValue_2) == 0x000401, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_LessEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsValid_ReturnValue_3) == 0x000402, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsValid_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeSoundEffect_ReturnValue_3) == 0x000403, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeSoundEffect_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsEffectTypeProgressChangeColor_ReturnValue_1) == 0x000404, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsEffectTypeProgressChangeColor_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsTriggeredAlwaysOnSpecificPercentValue_ReturnValue) == 0x000405, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsTriggeredAlwaysOnSpecificPercentValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_BooleanOR_ReturnValue_2) == 0x000406, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_BooleanOR_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_IsTriggeredAlways_ReturnValue) == 0x000407, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_IsTriggeredAlways_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_BooleanOR_ReturnValue_3) == 0x000408, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_BooleanOR_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_BooleanOR_ReturnValue_4) == 0x000409, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_BooleanOR_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_BooleanOR_ReturnValue_5) == 0x00040A, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_BooleanOR_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Not_PreBool_ReturnValue_5) == 0x00040B, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Not_PreBool_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_BooleanAND_ReturnValue) == 0x00040C, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_Has_Been_Initd_Variable_21) == 0x00040D, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_Has_Been_Initd_Variable_21' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, Temp_bool_IsClosed_Variable_21) == 0x00040E, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::Temp_bool_IsClosed_Variable_21' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_CreateSound2D_VolumeMultiplier_ImplicitCast) == 0x000410, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_CreateSound2D_VolumeMultiplier_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_SetRenderTransformAngle_Angle_ImplicitCast) == 0x000414, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_SetRenderTransformAngle_Angle_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_SetRenderTransformAngle_Angle_ImplicitCast_1) == 0x000418, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_SetRenderTransformAngle_Angle_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular, CallFunc_Delay_Duration_ImplicitCast) == 0x00041C, "Member 'WB_Effect_Circular_C_ExecuteUbergraph_WB_Effect_Circular::CallFunc_Delay_Duration_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.FindMinSize
// 0x0040 (0x0040 - 0x0000)
struct WB_Effect_Circular_C_FindMinSize final
{
public:
	struct FVector2D                              Size_0;                                            // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        ReturnValue;                                       // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue;       // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_FindMinSize) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_FindMinSize");
static_assert(sizeof(WB_Effect_Circular_C_FindMinSize) == 0x000040, "Wrong size on WB_Effect_Circular_C_FindMinSize");
static_assert(offsetof(WB_Effect_Circular_C_FindMinSize, Size_0) == 0x000000, "Member 'WB_Effect_Circular_C_FindMinSize::Size_0' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_FindMinSize, ReturnValue) == 0x000010, "Member 'WB_Effect_Circular_C_FindMinSize::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_FindMinSize, Temp_bool_Variable) == 0x000018, "Member 'WB_Effect_Circular_C_FindMinSize::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_FindMinSize, CallFunc_BreakVector2D_X) == 0x000020, "Member 'WB_Effect_Circular_C_FindMinSize::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_FindMinSize, CallFunc_BreakVector2D_Y) == 0x000028, "Member 'WB_Effect_Circular_C_FindMinSize::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_FindMinSize, CallFunc_LessEqual_DoubleDouble_ReturnValue) == 0x000030, "Member 'WB_Effect_Circular_C_FindMinSize::CallFunc_LessEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_FindMinSize, K2Node_Select_Default) == 0x000038, "Member 'WB_Effect_Circular_C_FindMinSize::K2Node_Select_Default' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetAddRotationToParticles
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_Circular_C_GetAddRotationToParticles final
{
public:
	bool                                          Add_Rotation_to_Particles;                         // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetAddRotationToParticles) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_GetAddRotationToParticles");
static_assert(sizeof(WB_Effect_Circular_C_GetAddRotationToParticles) == 0x000001, "Wrong size on WB_Effect_Circular_C_GetAddRotationToParticles");
static_assert(offsetof(WB_Effect_Circular_C_GetAddRotationToParticles, Add_Rotation_to_Particles) == 0x000000, "Member 'WB_Effect_Circular_C_GetAddRotationToParticles::Add_Rotation_to_Particles' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetDecayTime
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_Circular_C_GetDecayTime final
{
public:
	double                                        Decay_Time;                                        // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_FunctionResult_Decay_Time_ImplicitCast;     // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetDecayTime) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetDecayTime");
static_assert(sizeof(WB_Effect_Circular_C_GetDecayTime) == 0x000010, "Wrong size on WB_Effect_Circular_C_GetDecayTime");
static_assert(offsetof(WB_Effect_Circular_C_GetDecayTime, Decay_Time) == 0x000000, "Member 'WB_Effect_Circular_C_GetDecayTime::Decay_Time' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetDecayTime, K2Node_FunctionResult_Decay_Time_ImplicitCast) == 0x000008, "Member 'WB_Effect_Circular_C_GetDecayTime::K2Node_FunctionResult_Decay_Time_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetEffectAngle
// 0x0018 (0x0018 - 0x0000)
struct WB_Effect_Circular_C_GetEffectAngle final
{
public:
	double                                        Effect_Angle;                                      // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_Value_ImplicitCast;                // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetEffectAngle) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetEffectAngle");
static_assert(sizeof(WB_Effect_Circular_C_GetEffectAngle) == 0x000018, "Wrong size on WB_Effect_Circular_C_GetEffectAngle");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectAngle, Effect_Angle) == 0x000000, "Member 'WB_Effect_Circular_C_GetEffectAngle::Effect_Angle' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectAngle, CallFunc_FClamp_ReturnValue) == 0x000008, "Member 'WB_Effect_Circular_C_GetEffectAngle::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectAngle, CallFunc_FClamp_Value_ImplicitCast) == 0x000010, "Member 'WB_Effect_Circular_C_GetEffectAngle::CallFunc_FClamp_Value_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetEffectColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_Circular_C_GetEffectColor final
{
public:
	struct FLinearColor                           Effect_Color;                                      // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetEffectColor) == 0x000004, "Wrong alignment on WB_Effect_Circular_C_GetEffectColor");
static_assert(sizeof(WB_Effect_Circular_C_GetEffectColor) == 0x000010, "Wrong size on WB_Effect_Circular_C_GetEffectColor");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectColor, Effect_Color) == 0x000000, "Member 'WB_Effect_Circular_C_GetEffectColor::Effect_Color' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetEffectScale
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_Circular_C_GetEffectScale final
{
public:
	struct FVector2D                              EffectScale;                                       // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetEffectScale) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetEffectScale");
static_assert(sizeof(WB_Effect_Circular_C_GetEffectScale) == 0x000010, "Wrong size on WB_Effect_Circular_C_GetEffectScale");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectScale, EffectScale) == 0x000000, "Member 'WB_Effect_Circular_C_GetEffectScale::EffectScale' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetEffectShear
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_Circular_C_GetEffectShear final
{
public:
	struct FVector2D                              Effect_Shear;                                      // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetEffectShear) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetEffectShear");
static_assert(sizeof(WB_Effect_Circular_C_GetEffectShear) == 0x000010, "Wrong size on WB_Effect_Circular_C_GetEffectShear");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectShear, Effect_Shear) == 0x000000, "Member 'WB_Effect_Circular_C_GetEffectShear::Effect_Shear' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetEffectTexture
// 0x0060 (0x0060 - 0x0000)
struct WB_Effect_Circular_C_GetEffectTexture final
{
public:
	class UObject*                                Custom_Effect_Texture;                             // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable;                              // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable_1;                            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable_2;                            // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable_3;                            // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                Temp_object_Variable_4;                            // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                Temp_object_Variable_5;                            // 0x0038(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   Temp_byte_Variable;                                // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_42[0x6];                                       // 0x0042(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                K2Node_Select_Default;                             // 0x0048(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51[0x7];                                       // 0x0051(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                K2Node_Select_Default_1;                           // 0x0058(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetEffectTexture) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetEffectTexture");
static_assert(sizeof(WB_Effect_Circular_C_GetEffectTexture) == 0x000060, "Wrong size on WB_Effect_Circular_C_GetEffectTexture");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, Custom_Effect_Texture) == 0x000000, "Member 'WB_Effect_Circular_C_GetEffectTexture::Custom_Effect_Texture' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, Temp_object_Variable) == 0x000008, "Member 'WB_Effect_Circular_C_GetEffectTexture::Temp_object_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, Temp_object_Variable_1) == 0x000010, "Member 'WB_Effect_Circular_C_GetEffectTexture::Temp_object_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, Temp_object_Variable_2) == 0x000018, "Member 'WB_Effect_Circular_C_GetEffectTexture::Temp_object_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, Temp_object_Variable_3) == 0x000020, "Member 'WB_Effect_Circular_C_GetEffectTexture::Temp_object_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, Temp_object_Variable_4) == 0x000028, "Member 'WB_Effect_Circular_C_GetEffectTexture::Temp_object_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, Temp_bool_Variable) == 0x000030, "Member 'WB_Effect_Circular_C_GetEffectTexture::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, Temp_object_Variable_5) == 0x000038, "Member 'WB_Effect_Circular_C_GetEffectTexture::Temp_object_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, Temp_byte_Variable) == 0x000040, "Member 'WB_Effect_Circular_C_GetEffectTexture::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000041, "Member 'WB_Effect_Circular_C_GetEffectTexture::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, K2Node_Select_Default) == 0x000048, "Member 'WB_Effect_Circular_C_GetEffectTexture::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, CallFunc_IsValid_ReturnValue) == 0x000050, "Member 'WB_Effect_Circular_C_GetEffectTexture::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTexture, K2Node_Select_Default_1) == 0x000058, "Member 'WB_Effect_Circular_C_GetEffectTexture::K2Node_Select_Default_1' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetEffectTranslation
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_Circular_C_GetEffectTranslation final
{
public:
	struct FVector2D                              Transition;                                        // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetEffectTranslation) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetEffectTranslation");
static_assert(sizeof(WB_Effect_Circular_C_GetEffectTranslation) == 0x000010, "Wrong size on WB_Effect_Circular_C_GetEffectTranslation");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectTranslation, Transition) == 0x000000, "Member 'WB_Effect_Circular_C_GetEffectTranslation::Transition' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetEffectType
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_Circular_C_GetEffectType final
{
public:
	EEffectType                                   Effect_Texture_Type;                               // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetEffectType) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_GetEffectType");
static_assert(sizeof(WB_Effect_Circular_C_GetEffectType) == 0x000001, "Wrong size on WB_Effect_Circular_C_GetEffectType");
static_assert(offsetof(WB_Effect_Circular_C_GetEffectType, Effect_Texture_Type) == 0x000000, "Member 'WB_Effect_Circular_C_GetEffectType::Effect_Texture_Type' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetNumParticles
// 0x000C (0x000C - 0x0000)
struct WB_Effect_Circular_C_GetNumParticles final
{
public:
	int32                                         NumParticles;                                      // 0x0000(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Clamp_ReturnValue;                        // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetNumParticles) == 0x000004, "Wrong alignment on WB_Effect_Circular_C_GetNumParticles");
static_assert(sizeof(WB_Effect_Circular_C_GetNumParticles) == 0x00000C, "Wrong size on WB_Effect_Circular_C_GetNumParticles");
static_assert(offsetof(WB_Effect_Circular_C_GetNumParticles, NumParticles) == 0x000000, "Member 'WB_Effect_Circular_C_GetNumParticles::NumParticles' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetNumParticles, CallFunc_Subtract_IntInt_ReturnValue) == 0x000004, "Member 'WB_Effect_Circular_C_GetNumParticles::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetNumParticles, CallFunc_Clamp_ReturnValue) == 0x000008, "Member 'WB_Effect_Circular_C_GetNumParticles::CallFunc_Clamp_ReturnValue' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetParticleSpread
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_Circular_C_GetParticleSpread final
{
public:
	double                                        Particle_Spread;                                   // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_FunctionResult_Particle_Spread_ImplicitCast; // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetParticleSpread) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetParticleSpread");
static_assert(sizeof(WB_Effect_Circular_C_GetParticleSpread) == 0x000010, "Wrong size on WB_Effect_Circular_C_GetParticleSpread");
static_assert(offsetof(WB_Effect_Circular_C_GetParticleSpread, Particle_Spread) == 0x000000, "Member 'WB_Effect_Circular_C_GetParticleSpread::Particle_Spread' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetParticleSpread, K2Node_FunctionResult_Particle_Spread_ImplicitCast) == 0x000008, "Member 'WB_Effect_Circular_C_GetParticleSpread::K2Node_FunctionResult_Particle_Spread_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetPlaybackSpeed
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_Circular_C_GetPlaybackSpeed final
{
public:
	double                                        Playback_Speed;                                    // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_FunctionResult_Playback_Speed_ImplicitCast; // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetPlaybackSpeed) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetPlaybackSpeed");
static_assert(sizeof(WB_Effect_Circular_C_GetPlaybackSpeed) == 0x000010, "Wrong size on WB_Effect_Circular_C_GetPlaybackSpeed");
static_assert(offsetof(WB_Effect_Circular_C_GetPlaybackSpeed, Playback_Speed) == 0x000000, "Member 'WB_Effect_Circular_C_GetPlaybackSpeed::Playback_Speed' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetPlaybackSpeed, K2Node_FunctionResult_Playback_Speed_ImplicitCast) == 0x000008, "Member 'WB_Effect_Circular_C_GetPlaybackSpeed::K2Node_FunctionResult_Playback_Speed_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetProgressBar
// 0x0008 (0x0008 - 0x0000)
struct WB_Effect_Circular_C_GetProgressBar final
{
public:
	class UProgressBarCircular_C*                 ProgressBar;                                       // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetProgressBar) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetProgressBar");
static_assert(sizeof(WB_Effect_Circular_C_GetProgressBar) == 0x000008, "Wrong size on WB_Effect_Circular_C_GetProgressBar");
static_assert(offsetof(WB_Effect_Circular_C_GetProgressBar, ProgressBar) == 0x000000, "Member 'WB_Effect_Circular_C_GetProgressBar::ProgressBar' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetSoundEffect
// 0x0008 (0x0008 - 0x0000)
struct WB_Effect_Circular_C_GetSoundEffect final
{
public:
	class USoundBase*                             Sound_Effect;                                      // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetSoundEffect) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetSoundEffect");
static_assert(sizeof(WB_Effect_Circular_C_GetSoundEffect) == 0x000008, "Wrong size on WB_Effect_Circular_C_GetSoundEffect");
static_assert(offsetof(WB_Effect_Circular_C_GetSoundEffect, Sound_Effect) == 0x000000, "Member 'WB_Effect_Circular_C_GetSoundEffect::Sound_Effect' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetSoundVolume
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_Circular_C_GetSoundVolume final
{
public:
	double                                        Sound_Volume_Multiplier;                           // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_FunctionResult_Sound_Volume_Multiplier_ImplicitCast; // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetSoundVolume) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetSoundVolume");
static_assert(sizeof(WB_Effect_Circular_C_GetSoundVolume) == 0x000010, "Wrong size on WB_Effect_Circular_C_GetSoundVolume");
static_assert(offsetof(WB_Effect_Circular_C_GetSoundVolume, Sound_Volume_Multiplier) == 0x000000, "Member 'WB_Effect_Circular_C_GetSoundVolume::Sound_Volume_Multiplier' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetSoundVolume, K2Node_FunctionResult_Sound_Volume_Multiplier_ImplicitCast) == 0x000008, "Member 'WB_Effect_Circular_C_GetSoundVolume::K2Node_FunctionResult_Sound_Volume_Multiplier_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetSpecificPercentValue
// 0x0018 (0x0018 - 0x0000)
struct WB_Effect_Circular_C_GetSpecificPercentValue final
{
public:
	double                                        Specific_Percent_Value;                            // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_Value_ImplicitCast;                // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetSpecificPercentValue) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_GetSpecificPercentValue");
static_assert(sizeof(WB_Effect_Circular_C_GetSpecificPercentValue) == 0x000018, "Wrong size on WB_Effect_Circular_C_GetSpecificPercentValue");
static_assert(offsetof(WB_Effect_Circular_C_GetSpecificPercentValue, Specific_Percent_Value) == 0x000000, "Member 'WB_Effect_Circular_C_GetSpecificPercentValue::Specific_Percent_Value' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetSpecificPercentValue, CallFunc_FClamp_ReturnValue) == 0x000008, "Member 'WB_Effect_Circular_C_GetSpecificPercentValue::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_GetSpecificPercentValue, CallFunc_FClamp_Value_ImplicitCast) == 0x000010, "Member 'WB_Effect_Circular_C_GetSpecificPercentValue::CallFunc_FClamp_Value_ImplicitCast' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetTriggerMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_Circular_C_GetTriggerMethod final
{
public:
	ETriggerMethod                                Trigger;                                           // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetTriggerMethod) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_GetTriggerMethod");
static_assert(sizeof(WB_Effect_Circular_C_GetTriggerMethod) == 0x000001, "Wrong size on WB_Effect_Circular_C_GetTriggerMethod");
static_assert(offsetof(WB_Effect_Circular_C_GetTriggerMethod, Trigger) == 0x000000, "Member 'WB_Effect_Circular_C_GetTriggerMethod::Trigger' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.GetUseParticleStretched
// 0x0001 (0x0001 - 0x0000)
struct WB_Effect_Circular_C_GetUseParticleStretched final
{
public:
	bool                                          Use_Stretched_Particle;                            // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_GetUseParticleStretched) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_GetUseParticleStretched");
static_assert(sizeof(WB_Effect_Circular_C_GetUseParticleStretched) == 0x000001, "Wrong size on WB_Effect_Circular_C_GetUseParticleStretched");
static_assert(offsetof(WB_Effect_Circular_C_GetUseParticleStretched, Use_Stretched_Particle) == 0x000000, "Member 'WB_Effect_Circular_C_GetUseParticleStretched::Use_Stretched_Particle' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.IsEffectTypeParticle
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_Circular_C_IsEffectTypeParticle final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_IsEffectTypeParticle) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_IsEffectTypeParticle");
static_assert(sizeof(WB_Effect_Circular_C_IsEffectTypeParticle) == 0x000003, "Wrong size on WB_Effect_Circular_C_IsEffectTypeParticle");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeParticle, ReturnValue) == 0x000000, "Member 'WB_Effect_Circular_C_IsEffectTypeParticle::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeParticle, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000001, "Member 'WB_Effect_Circular_C_IsEffectTypeParticle::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeParticle, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_Circular_C_IsEffectTypeParticle::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.IsEffectTypeProgressChangeColor
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_Circular_C_IsEffectTypeProgressChangeColor final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_IsEffectTypeProgressChangeColor) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_IsEffectTypeProgressChangeColor");
static_assert(sizeof(WB_Effect_Circular_C_IsEffectTypeProgressChangeColor) == 0x000003, "Wrong size on WB_Effect_Circular_C_IsEffectTypeProgressChangeColor");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeProgressChangeColor, ReturnValue) == 0x000000, "Member 'WB_Effect_Circular_C_IsEffectTypeProgressChangeColor::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeProgressChangeColor, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000001, "Member 'WB_Effect_Circular_C_IsEffectTypeProgressChangeColor::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeProgressChangeColor, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_Circular_C_IsEffectTypeProgressChangeColor::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.IsEffectTypeSoundEffect
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_Circular_C_IsEffectTypeSoundEffect final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_IsEffectTypeSoundEffect) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_IsEffectTypeSoundEffect");
static_assert(sizeof(WB_Effect_Circular_C_IsEffectTypeSoundEffect) == 0x000003, "Wrong size on WB_Effect_Circular_C_IsEffectTypeSoundEffect");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeSoundEffect, ReturnValue) == 0x000000, "Member 'WB_Effect_Circular_C_IsEffectTypeSoundEffect::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeSoundEffect, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000001, "Member 'WB_Effect_Circular_C_IsEffectTypeSoundEffect::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeSoundEffect, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_Circular_C_IsEffectTypeSoundEffect::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.IsEffectTypeSoundEffectLooped
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEffectType                                   CallFunc_GetEffectType_Effect_Texture_Type;        // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped");
static_assert(sizeof(WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped) == 0x000003, "Wrong size on WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped, ReturnValue) == 0x000000, "Member 'WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped, CallFunc_GetEffectType_Effect_Texture_Type) == 0x000001, "Member 'WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped::CallFunc_GetEffectType_Effect_Texture_Type' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_Circular_C_IsEffectTypeSoundEffectLooped::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.IsTriggeredAlways
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_Circular_C_IsTriggeredAlways final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger;                 // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_IsTriggeredAlways) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_IsTriggeredAlways");
static_assert(sizeof(WB_Effect_Circular_C_IsTriggeredAlways) == 0x000003, "Wrong size on WB_Effect_Circular_C_IsTriggeredAlways");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggeredAlways, ReturnValue) == 0x000000, "Member 'WB_Effect_Circular_C_IsTriggeredAlways::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggeredAlways, CallFunc_GetTriggerMethod_Trigger) == 0x000001, "Member 'WB_Effect_Circular_C_IsTriggeredAlways::CallFunc_GetTriggerMethod_Trigger' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggeredAlways, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_Circular_C_IsTriggeredAlways::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.IsTriggeredAlwaysOnSpecificPercentValue
// 0x0003 (0x0003 - 0x0000)
struct WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger;                 // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue");
static_assert(sizeof(WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue) == 0x000003, "Wrong size on WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue, ReturnValue) == 0x000000, "Member 'WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue, CallFunc_GetTriggerMethod_Trigger) == 0x000001, "Member 'WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue::CallFunc_GetTriggerMethod_Trigger' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_Circular_C_IsTriggeredAlwaysOnSpecificPercentValue::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.IsTriggerSpecificPercent
// 0x0009 (0x0009 - 0x0000)
struct WB_Effect_Circular_C_IsTriggerSpecificPercent final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ETriggerMethod                                CallFunc_GetTriggerMethod_Trigger;                 // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_2;        // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_3;        // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_2;                  // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_IsTriggerSpecificPercent) == 0x000001, "Wrong alignment on WB_Effect_Circular_C_IsTriggerSpecificPercent");
static_assert(sizeof(WB_Effect_Circular_C_IsTriggerSpecificPercent) == 0x000009, "Wrong size on WB_Effect_Circular_C_IsTriggerSpecificPercent");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggerSpecificPercent, ReturnValue) == 0x000000, "Member 'WB_Effect_Circular_C_IsTriggerSpecificPercent::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggerSpecificPercent, CallFunc_GetTriggerMethod_Trigger) == 0x000001, "Member 'WB_Effect_Circular_C_IsTriggerSpecificPercent::CallFunc_GetTriggerMethod_Trigger' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggerSpecificPercent, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Effect_Circular_C_IsTriggerSpecificPercent::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggerSpecificPercent, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000003, "Member 'WB_Effect_Circular_C_IsTriggerSpecificPercent::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggerSpecificPercent, CallFunc_EqualEqual_ByteByte_ReturnValue_2) == 0x000004, "Member 'WB_Effect_Circular_C_IsTriggerSpecificPercent::CallFunc_EqualEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggerSpecificPercent, CallFunc_EqualEqual_ByteByte_ReturnValue_3) == 0x000005, "Member 'WB_Effect_Circular_C_IsTriggerSpecificPercent::CallFunc_EqualEqual_ByteByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggerSpecificPercent, CallFunc_BooleanOR_ReturnValue) == 0x000006, "Member 'WB_Effect_Circular_C_IsTriggerSpecificPercent::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggerSpecificPercent, CallFunc_BooleanOR_ReturnValue_1) == 0x000007, "Member 'WB_Effect_Circular_C_IsTriggerSpecificPercent::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_IsTriggerSpecificPercent, CallFunc_BooleanOR_ReturnValue_2) == 0x000008, "Member 'WB_Effect_Circular_C_IsTriggerSpecificPercent::CallFunc_BooleanOR_ReturnValue_2' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.RemoveParticleFromOverlay
// 0x0030 (0x0030 - 0x0000)
struct WB_Effect_Circular_C_RemoveParticleFromOverlay final
{
public:
	class UOverlay*                               Overlay;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0018(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UWB_Particle_C*                         K2Node_DynamicCast_AsWB_Particle;                  // 0x0020(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0029(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x002A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_RemoveParticleFromOverlay) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_RemoveParticleFromOverlay");
static_assert(sizeof(WB_Effect_Circular_C_RemoveParticleFromOverlay) == 0x000030, "Wrong size on WB_Effect_Circular_C_RemoveParticleFromOverlay");
static_assert(offsetof(WB_Effect_Circular_C_RemoveParticleFromOverlay, Overlay) == 0x000000, "Member 'WB_Effect_Circular_C_RemoveParticleFromOverlay::Overlay' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_RemoveParticleFromOverlay, CallFunc_GetChildrenCount_ReturnValue) == 0x000008, "Member 'WB_Effect_Circular_C_RemoveParticleFromOverlay::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_RemoveParticleFromOverlay, Temp_int_Variable) == 0x00000C, "Member 'WB_Effect_Circular_C_RemoveParticleFromOverlay::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_RemoveParticleFromOverlay, CallFunc_Add_IntInt_ReturnValue) == 0x000010, "Member 'WB_Effect_Circular_C_RemoveParticleFromOverlay::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_RemoveParticleFromOverlay, CallFunc_GetChildAt_ReturnValue) == 0x000018, "Member 'WB_Effect_Circular_C_RemoveParticleFromOverlay::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_RemoveParticleFromOverlay, K2Node_DynamicCast_AsWB_Particle) == 0x000020, "Member 'WB_Effect_Circular_C_RemoveParticleFromOverlay::K2Node_DynamicCast_AsWB_Particle' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_RemoveParticleFromOverlay, K2Node_DynamicCast_bSuccess) == 0x000028, "Member 'WB_Effect_Circular_C_RemoveParticleFromOverlay::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_RemoveParticleFromOverlay, CallFunc_IsValid_ReturnValue) == 0x000029, "Member 'WB_Effect_Circular_C_RemoveParticleFromOverlay::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_RemoveParticleFromOverlay, CallFunc_LessEqual_IntInt_ReturnValue) == 0x00002A, "Member 'WB_Effect_Circular_C_RemoveParticleFromOverlay::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.SetSize
// 0x0010 (0x0010 - 0x0000)
struct WB_Effect_Circular_C_SetSize final
{
public:
	double                                        Size_0;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Thickness_0;                                       // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_SetSize) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_SetSize");
static_assert(sizeof(WB_Effect_Circular_C_SetSize) == 0x000010, "Wrong size on WB_Effect_Circular_C_SetSize");
static_assert(offsetof(WB_Effect_Circular_C_SetSize, Size_0) == 0x000000, "Member 'WB_Effect_Circular_C_SetSize::Size_0' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_SetSize, Thickness_0) == 0x000008, "Member 'WB_Effect_Circular_C_SetSize::Thickness_0' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.SwitchEffectType
// 0x0004 (0x0004 - 0x0000)
struct WB_Effect_Circular_C_SwitchEffectType final
{
public:
	int32                                         Index_0;                                           // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_SwitchEffectType) == 0x000004, "Wrong alignment on WB_Effect_Circular_C_SwitchEffectType");
static_assert(sizeof(WB_Effect_Circular_C_SwitchEffectType) == 0x000004, "Wrong size on WB_Effect_Circular_C_SwitchEffectType");
static_assert(offsetof(WB_Effect_Circular_C_SwitchEffectType, Index_0) == 0x000000, "Member 'WB_Effect_Circular_C_SwitchEffectType::Index_0' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.SwitchPunctiform
// 0x0004 (0x0004 - 0x0000)
struct WB_Effect_Circular_C_SwitchPunctiform final
{
public:
	int32                                         Index_0;                                           // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_SwitchPunctiform) == 0x000004, "Wrong alignment on WB_Effect_Circular_C_SwitchPunctiform");
static_assert(sizeof(WB_Effect_Circular_C_SwitchPunctiform) == 0x000004, "Wrong size on WB_Effect_Circular_C_SwitchPunctiform");
static_assert(offsetof(WB_Effect_Circular_C_SwitchPunctiform, Index_0) == 0x000000, "Member 'WB_Effect_Circular_C_SwitchPunctiform::Index_0' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.Tick
// 0x003C (0x003C - 0x0000)
struct WB_Effect_Circular_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_Tick) == 0x000004, "Wrong alignment on WB_Effect_Circular_C_Tick");
static_assert(sizeof(WB_Effect_Circular_C_Tick) == 0x00003C, "Wrong size on WB_Effect_Circular_C_Tick");
static_assert(offsetof(WB_Effect_Circular_C_Tick, MyGeometry) == 0x000000, "Member 'WB_Effect_Circular_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Effect_Circular_C_Tick, InDeltaTime) == 0x000038, "Member 'WB_Effect_Circular_C_Tick::InDeltaTime' has a wrong offset!");

// Function WB_Effect_Circular.WB_Effect_Circular_C.UpdatePercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Effect_Circular_C_UpdatePercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Effect_Circular_C_UpdatePercent) == 0x000008, "Wrong alignment on WB_Effect_Circular_C_UpdatePercent");
static_assert(sizeof(WB_Effect_Circular_C_UpdatePercent) == 0x000008, "Wrong size on WB_Effect_Circular_C_UpdatePercent");
static_assert(offsetof(WB_Effect_Circular_C_UpdatePercent, Percent) == 0x000000, "Member 'WB_Effect_Circular_C_UpdatePercent::Percent' has a wrong offset!");

}

