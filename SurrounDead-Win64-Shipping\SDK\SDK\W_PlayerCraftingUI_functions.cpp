﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_PlayerCraftingUI

#include "Basic.hpp"

#include "W_PlayerCraftingUI_classes.hpp"
#include "W_PlayerCraftingUI_parameters.hpp"


namespace SDK
{

// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.AddRequiredItems
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UW_PlayerCraftingUI_C::AddRequiredItems()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "AddRequiredItems");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.BindEvents
// (BlueprintCallable, BlueprintEvent)

void UW_PlayerCraftingUI_C::BindEvents()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "BindEvents");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.BndEvt__BP_CraftingWidget_Button_Decrease_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UW_PlayerCraftingUI_C::BndEvt__BP_CraftingWidget_Button_Decrease_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "BndEvt__BP_CraftingWidget_Button_Decrease_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.BndEvt__BP_CraftingWidget_Button_Increase_K2Node_ComponentBoundEvent_3_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UW_PlayerCraftingUI_C::BndEvt__BP_CraftingWidget_Button_Increase_K2Node_ComponentBoundEvent_3_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "BndEvt__BP_CraftingWidget_Button_Increase_K2Node_ComponentBoundEvent_3_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.BndEvt__Button_81_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UW_PlayerCraftingUI_C::BndEvt__Button_81_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "BndEvt__Button_81_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.CheckIngredientsAvailability
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   Proceed                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_PlayerCraftingUI_C::CheckIngredientsAvailability(bool* Proceed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "CheckIngredientsAvailability");

	Params::W_PlayerCraftingUI_C_CheckIngredientsAvailability Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Proceed != nullptr)
		*Proceed = Parms.Proceed;
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_PlayerCraftingUI_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.EventOnMouseButtonDown
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSIContainer_C*                  Container                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSI_Slot_C*                      SlotRef                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// const struct FKey&                      Button                                                 (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)

void UW_PlayerCraftingUI_C::EventOnMouseButtonDown(class UJSIContainer_C* Container, class UJSI_Slot_C* SlotRef, const struct FKey& Button)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "EventOnMouseButtonDown");

	Params::W_PlayerCraftingUI_C_EventOnMouseButtonDown Parms{};

	Parms.Container = Container;
	Parms.SlotRef = SlotRef;
	Parms.Button = std::move(Button);

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.ExecuteUbergraph_W_PlayerCraftingUI
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_PlayerCraftingUI_C::ExecuteUbergraph_W_PlayerCraftingUI(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "ExecuteUbergraph_W_PlayerCraftingUI");

	Params::W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.GetText
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText                             ReturnValue                                            (Parm, OutParm, ReturnParm)

class FText UW_PlayerCraftingUI_C::GetText()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "GetText");

	Params::W_PlayerCraftingUI_C_GetText Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.InitializeInventory
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UW_PlayerCraftingUI_C::InitializeInventory()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "InitializeInventory");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.SetCraftableItems
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UW_PlayerCraftingUI_C::SetCraftableItems()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_PlayerCraftingUI_C", "SetCraftableItems");

	UObject::ProcessEvent(Func, nullptr);
}

}

