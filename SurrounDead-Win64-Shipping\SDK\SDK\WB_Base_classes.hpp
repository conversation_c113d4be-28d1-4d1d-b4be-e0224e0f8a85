﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Base

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "EProgressMethod_structs.hpp"
#include "UMG_classes.hpp"
#include "EGradientTypes_structs.hpp"
#include "EMarqueeMask_structs.hpp"
#include "Slate_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_Base.WB_Base_C
// 0x0008 (0x02C8 - 0x02C0)
class UWB_Base_C : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)

public:
	void ExecuteUbergraph_WB_Base(int32 EntryPoint);
	void PB_AddEffect(const struct FS_Effects& Effect, int32* Index_0);
	void PB_GetBackgroundColor(struct FLinearColor* Color);
	void PB_GetEffects(TArray<struct FS_Effects>* Effects);
	void PB_GetFillColor(struct FLinearColor* Color);
	void PB_GetInterpTimeCurrent(double* CurrentInterpTime);
	void PB_GetInterpTimeTarget(double* TargetInterpTime);
	void PB_GetIsCustomMarquee(bool* IsMarquee);
	void PB_GetIsMarquee(bool* IsMarquee);
	void PB_GetPercent(double* Percent);
	void PB_GetProgressMethod(EProgressMethod* EProgressMethod);
	void PB_GetSeparationSteps(int32* Steps);
	void PB_GetSize(struct FVector2D* Size);
	void PB_GetTargetFillColor_Negative(struct FLinearColor* Color);
	void PB_GetTargetFillColor_Positive(struct FLinearColor* Color);
	void PB_GetTargetPercent(double* TargetPercent);
	void PB_GetThickness(double* Thickness);
	void PB_GetUseGradientFillColor(bool* UseGradientFillColor);
	void PB_GetUseTargetPercent(bool* UseTargetPercent);
	void PB_RemoveEffect(int32 Index_0);
	void PB_SetAllEffectsEnabled(bool IsEnabled);
	void PB_SetBackgroundBlurStrength(double BlurStrength);
	void PB_SetBackgroundBrushTiling(ESlateBrushTileType Tiling);
	void PB_SetBackgroundColor(const struct FLinearColor& Color);
	void PB_SetBackgroundColorMask(class UTexture2D* Mask);
	void PB_SetBlendMask(class UTexture2D* BlendMask);
	void PB_SetCustomMarqueeImage(class UTexture2D* Image);
	void PB_SetCustomMarqueeMaskType(EMarqueeMask MaskType);
	void PB_SetEffectEnabled(int32 Index_0, bool IsEnabled);
	void PB_SetEffects(const TArray<struct FS_Effects>& Effects);
	void PB_SetFillColor(const struct FLinearColor& Color);
	void PB_SetFillColorBrushTiling(ESlateBrushTileType Tiling);
	void PB_SetFillColorGradientPower(double GradientPower);
	void PB_SetFillColorGradientType(EGradientTypes GradientType);
	void PB_SetFillColorMask(class UObject* Mask);
	void PB_SetFillFromCenterSpacing(double Spacing);
	void PB_SetFillType(EProgressBarFillType FillType);
	void PB_SetInterpTimeCurrent(double CurrentInterpTime);
	void PB_SetInterpTimeTarget(double TargetInterpTime);
	void PB_SetIsCustomMarquee(bool IsMarquee);
	void PB_SetIsMarquee(bool IsMarquee);
	void PB_SetMarqueeImage(class UTexture2D* Image);
	void PB_SetPercent(double Value);
	void PB_SetProgressMethod(EProgressMethod EProgressMethod);
	void PB_SetSeparationAbsoluteFill(bool AbsoluteFillMethod);
	void PB_SetSeparationSteps(int32 Steps);
	void PB_SetSeparationStepsSpacing(double Spacing);
	void PB_SetSize(const struct FVector2D& Size);
	void PB_SetTargetFillColor_Negative(const struct FLinearColor& Color);
	void PB_SetTargetFillColor_Positive(const struct FLinearColor& Color);
	void PB_SetTargetPercent(double TargetPercent);
	void PB_SetThickness(double Thickness);
	void PB_SetUseGradientFillColor(bool UseGradientFillColor);
	void PB_SetUseTargetPercent(bool UseTargetPercent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_Base_C">();
	}
	static class UWB_Base_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_Base_C>();
	}
};
static_assert(alignof(UWB_Base_C) == 0x000008, "Wrong alignment on UWB_Base_C");
static_assert(sizeof(UWB_Base_C) == 0x0002C8, "Wrong size on UWB_Base_C");
static_assert(offsetof(UWB_Base_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_Base_C::UberGraphFrame' has a wrong offset!");

}

