﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: TypedElementFramework

#include "Basic.hpp"

#include "TypedElementFramework_structs.hpp"


namespace SDK::Params
{

// Function TypedElementFramework.TypedElementHandleLibrary.Equal
// 0x0018 (0x0018 - 0x0000)
struct TypedElementHandleLibrary_Equal final
{
public:
	struct FScriptTypedElementHandle              LHS;                                               // 0x0000(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	struct FScriptTypedElementHandle              RHS;                                               // 0x0008(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0010(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementHandleLibrary_Equal) == 0x000008, "Wrong alignment on TypedElementHandleLibrary_Equal");
static_assert(sizeof(TypedElementHandleLibrary_Equal) == 0x000018, "Wrong size on TypedElementHandleLibrary_Equal");
static_assert(offsetof(TypedElementHandleLibrary_Equal, LHS) == 0x000000, "Member 'TypedElementHandleLibrary_Equal::LHS' has a wrong offset!");
static_assert(offsetof(TypedElementHandleLibrary_Equal, RHS) == 0x000008, "Member 'TypedElementHandleLibrary_Equal::RHS' has a wrong offset!");
static_assert(offsetof(TypedElementHandleLibrary_Equal, ReturnValue) == 0x000010, "Member 'TypedElementHandleLibrary_Equal::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementHandleLibrary.IsSet
// 0x0010 (0x0010 - 0x0000)
struct TypedElementHandleLibrary_IsSet final
{
public:
	struct FScriptTypedElementHandle              ElementHandle;                                     // 0x0000(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementHandleLibrary_IsSet) == 0x000008, "Wrong alignment on TypedElementHandleLibrary_IsSet");
static_assert(sizeof(TypedElementHandleLibrary_IsSet) == 0x000010, "Wrong size on TypedElementHandleLibrary_IsSet");
static_assert(offsetof(TypedElementHandleLibrary_IsSet, ElementHandle) == 0x000000, "Member 'TypedElementHandleLibrary_IsSet::ElementHandle' has a wrong offset!");
static_assert(offsetof(TypedElementHandleLibrary_IsSet, ReturnValue) == 0x000008, "Member 'TypedElementHandleLibrary_IsSet::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementHandleLibrary.NotEqual
// 0x0018 (0x0018 - 0x0000)
struct TypedElementHandleLibrary_NotEqual final
{
public:
	struct FScriptTypedElementHandle              LHS;                                               // 0x0000(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	struct FScriptTypedElementHandle              RHS;                                               // 0x0008(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0010(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementHandleLibrary_NotEqual) == 0x000008, "Wrong alignment on TypedElementHandleLibrary_NotEqual");
static_assert(sizeof(TypedElementHandleLibrary_NotEqual) == 0x000018, "Wrong size on TypedElementHandleLibrary_NotEqual");
static_assert(offsetof(TypedElementHandleLibrary_NotEqual, LHS) == 0x000000, "Member 'TypedElementHandleLibrary_NotEqual::LHS' has a wrong offset!");
static_assert(offsetof(TypedElementHandleLibrary_NotEqual, RHS) == 0x000008, "Member 'TypedElementHandleLibrary_NotEqual::RHS' has a wrong offset!");
static_assert(offsetof(TypedElementHandleLibrary_NotEqual, ReturnValue) == 0x000010, "Member 'TypedElementHandleLibrary_NotEqual::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementHandleLibrary.Release
// 0x0008 (0x0008 - 0x0000)
struct TypedElementHandleLibrary_Release final
{
public:
	struct FScriptTypedElementHandle              ElementHandle;                                     // 0x0000(0x0008)(Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementHandleLibrary_Release) == 0x000008, "Wrong alignment on TypedElementHandleLibrary_Release");
static_assert(sizeof(TypedElementHandleLibrary_Release) == 0x000008, "Wrong size on TypedElementHandleLibrary_Release");
static_assert(offsetof(TypedElementHandleLibrary_Release, ElementHandle) == 0x000000, "Member 'TypedElementHandleLibrary_Release::ElementHandle' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Add
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_Add final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(Parm, NativeAccessSpecifierPublic)
	struct FScriptTypedElementHandle              ElementHandle;                                     // 0x0010(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_Add) == 0x000008, "Wrong alignment on TypedElementListLibrary_Add");
static_assert(sizeof(TypedElementListLibrary_Add) == 0x000020, "Wrong size on TypedElementListLibrary_Add");
static_assert(offsetof(TypedElementListLibrary_Add, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Add::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Add, ElementHandle) == 0x000010, "Member 'TypedElementListLibrary_Add::ElementHandle' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Add, ReturnValue) == 0x000018, "Member 'TypedElementListLibrary_Add::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Append
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_Append final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(Parm, NativeAccessSpecifierPublic)
	TArray<struct FScriptTypedElementHandle>      ElementHandles;                                    // 0x0010(0x0010)(ConstParm, Parm, OutParm, ZeroConstructor, ReferenceParm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementListLibrary_Append) == 0x000008, "Wrong alignment on TypedElementListLibrary_Append");
static_assert(sizeof(TypedElementListLibrary_Append) == 0x000020, "Wrong size on TypedElementListLibrary_Append");
static_assert(offsetof(TypedElementListLibrary_Append, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Append::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Append, ElementHandles) == 0x000010, "Member 'TypedElementListLibrary_Append::ElementHandles' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.AppendList
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_AppendList final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(Parm, NativeAccessSpecifierPublic)
	struct FScriptTypedElementListProxy           OtherElementList;                                  // 0x0010(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementListLibrary_AppendList) == 0x000008, "Wrong alignment on TypedElementListLibrary_AppendList");
static_assert(sizeof(TypedElementListLibrary_AppendList) == 0x000020, "Wrong size on TypedElementListLibrary_AppendList");
static_assert(offsetof(TypedElementListLibrary_AppendList, ElementList) == 0x000000, "Member 'TypedElementListLibrary_AppendList::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_AppendList, OtherElementList) == 0x000010, "Member 'TypedElementListLibrary_AppendList::OtherElementList' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Clone
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_Clone final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	struct FScriptTypedElementListProxy           ReturnValue;                                       // 0x0010(0x0010)(Parm, OutParm, ReturnParm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementListLibrary_Clone) == 0x000008, "Wrong alignment on TypedElementListLibrary_Clone");
static_assert(sizeof(TypedElementListLibrary_Clone) == 0x000020, "Wrong size on TypedElementListLibrary_Clone");
static_assert(offsetof(TypedElementListLibrary_Clone, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Clone::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Clone, ReturnValue) == 0x000010, "Member 'TypedElementListLibrary_Clone::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Contains
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_Contains final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	struct FScriptTypedElementHandle              ElementHandle;                                     // 0x0010(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_Contains) == 0x000008, "Wrong alignment on TypedElementListLibrary_Contains");
static_assert(sizeof(TypedElementListLibrary_Contains) == 0x000020, "Wrong size on TypedElementListLibrary_Contains");
static_assert(offsetof(TypedElementListLibrary_Contains, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Contains::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Contains, ElementHandle) == 0x000010, "Member 'TypedElementListLibrary_Contains::ElementHandle' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Contains, ReturnValue) == 0x000018, "Member 'TypedElementListLibrary_Contains::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.CountElements
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_CountElements final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	TSubclassOf<class IInterface>                 BaseInterfaceType;                                 // 0x0010(0x0008)(ConstParm, Parm, ZeroConstructor, NoDestructor, UObjectWrapper, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	int32                                         ReturnValue;                                       // 0x0018(0x0004)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_CountElements) == 0x000008, "Wrong alignment on TypedElementListLibrary_CountElements");
static_assert(sizeof(TypedElementListLibrary_CountElements) == 0x000020, "Wrong size on TypedElementListLibrary_CountElements");
static_assert(offsetof(TypedElementListLibrary_CountElements, ElementList) == 0x000000, "Member 'TypedElementListLibrary_CountElements::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_CountElements, BaseInterfaceType) == 0x000010, "Member 'TypedElementListLibrary_CountElements::BaseInterfaceType' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_CountElements, ReturnValue) == 0x000018, "Member 'TypedElementListLibrary_CountElements::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.CountElementsOfType
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_CountElementsOfType final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	class FName                                   ElementTypeName;                                   // 0x0010(0x0008)(ConstParm, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	int32                                         ReturnValue;                                       // 0x0018(0x0004)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_CountElementsOfType) == 0x000008, "Wrong alignment on TypedElementListLibrary_CountElementsOfType");
static_assert(sizeof(TypedElementListLibrary_CountElementsOfType) == 0x000020, "Wrong size on TypedElementListLibrary_CountElementsOfType");
static_assert(offsetof(TypedElementListLibrary_CountElementsOfType, ElementList) == 0x000000, "Member 'TypedElementListLibrary_CountElementsOfType::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_CountElementsOfType, ElementTypeName) == 0x000010, "Member 'TypedElementListLibrary_CountElementsOfType::ElementTypeName' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_CountElementsOfType, ReturnValue) == 0x000018, "Member 'TypedElementListLibrary_CountElementsOfType::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.CreateScriptElementList
// 0x0018 (0x0018 - 0x0000)
struct TypedElementListLibrary_CreateScriptElementList final
{
public:
	class UTypedElementRegistry*                  Registry;                                          // 0x0000(0x0008)(Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	struct FScriptTypedElementListProxy           ReturnValue;                                       // 0x0008(0x0010)(Parm, OutParm, ReturnParm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementListLibrary_CreateScriptElementList) == 0x000008, "Wrong alignment on TypedElementListLibrary_CreateScriptElementList");
static_assert(sizeof(TypedElementListLibrary_CreateScriptElementList) == 0x000018, "Wrong size on TypedElementListLibrary_CreateScriptElementList");
static_assert(offsetof(TypedElementListLibrary_CreateScriptElementList, Registry) == 0x000000, "Member 'TypedElementListLibrary_CreateScriptElementList::Registry' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_CreateScriptElementList, ReturnValue) == 0x000008, "Member 'TypedElementListLibrary_CreateScriptElementList::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Empty
// 0x0018 (0x0018 - 0x0000)
struct TypedElementListLibrary_Empty final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(Parm, NativeAccessSpecifierPublic)
	int32                                         Slack;                                             // 0x0010(0x0004)(ConstParm, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_Empty) == 0x000008, "Wrong alignment on TypedElementListLibrary_Empty");
static_assert(sizeof(TypedElementListLibrary_Empty) == 0x000018, "Wrong size on TypedElementListLibrary_Empty");
static_assert(offsetof(TypedElementListLibrary_Empty, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Empty::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Empty, Slack) == 0x000010, "Member 'TypedElementListLibrary_Empty::Slack' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.GetElementHandleAt
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_GetElementHandleAt final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	int32                                         Index_0;                                           // 0x0010(0x0004)(ConstParm, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FScriptTypedElementHandle              ReturnValue;                                       // 0x0018(0x0008)(Parm, OutParm, ReturnParm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementListLibrary_GetElementHandleAt) == 0x000008, "Wrong alignment on TypedElementListLibrary_GetElementHandleAt");
static_assert(sizeof(TypedElementListLibrary_GetElementHandleAt) == 0x000020, "Wrong size on TypedElementListLibrary_GetElementHandleAt");
static_assert(offsetof(TypedElementListLibrary_GetElementHandleAt, ElementList) == 0x000000, "Member 'TypedElementListLibrary_GetElementHandleAt::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_GetElementHandleAt, Index_0) == 0x000010, "Member 'TypedElementListLibrary_GetElementHandleAt::Index_0' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_GetElementHandleAt, ReturnValue) == 0x000018, "Member 'TypedElementListLibrary_GetElementHandleAt::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.GetElementHandles
// 0x0028 (0x0028 - 0x0000)
struct TypedElementListLibrary_GetElementHandles final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	TSubclassOf<class IInterface>                 BaseInterfaceType;                                 // 0x0010(0x0008)(ConstParm, Parm, ZeroConstructor, NoDestructor, UObjectWrapper, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	TArray<struct FScriptTypedElementHandle>      ReturnValue;                                       // 0x0018(0x0010)(Parm, OutParm, ZeroConstructor, ReturnParm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementListLibrary_GetElementHandles) == 0x000008, "Wrong alignment on TypedElementListLibrary_GetElementHandles");
static_assert(sizeof(TypedElementListLibrary_GetElementHandles) == 0x000028, "Wrong size on TypedElementListLibrary_GetElementHandles");
static_assert(offsetof(TypedElementListLibrary_GetElementHandles, ElementList) == 0x000000, "Member 'TypedElementListLibrary_GetElementHandles::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_GetElementHandles, BaseInterfaceType) == 0x000010, "Member 'TypedElementListLibrary_GetElementHandles::BaseInterfaceType' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_GetElementHandles, ReturnValue) == 0x000018, "Member 'TypedElementListLibrary_GetElementHandles::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.GetElementInterface
// 0x0028 (0x0028 - 0x0000)
struct TypedElementListLibrary_GetElementInterface final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	struct FScriptTypedElementHandle              ElementHandle;                                     // 0x0010(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	TSubclassOf<class IInterface>                 BaseInterfaceType;                                 // 0x0018(0x0008)(ConstParm, Parm, ZeroConstructor, NoDestructor, UObjectWrapper, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	class UObject*                                ReturnValue;                                       // 0x0020(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementListLibrary_GetElementInterface) == 0x000008, "Wrong alignment on TypedElementListLibrary_GetElementInterface");
static_assert(sizeof(TypedElementListLibrary_GetElementInterface) == 0x000028, "Wrong size on TypedElementListLibrary_GetElementInterface");
static_assert(offsetof(TypedElementListLibrary_GetElementInterface, ElementList) == 0x000000, "Member 'TypedElementListLibrary_GetElementInterface::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_GetElementInterface, ElementHandle) == 0x000010, "Member 'TypedElementListLibrary_GetElementInterface::ElementHandle' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_GetElementInterface, BaseInterfaceType) == 0x000018, "Member 'TypedElementListLibrary_GetElementInterface::BaseInterfaceType' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_GetElementInterface, ReturnValue) == 0x000020, "Member 'TypedElementListLibrary_GetElementInterface::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.HasElements
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_HasElements final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	TSubclassOf<class IInterface>                 BaseInterfaceType;                                 // 0x0010(0x0008)(ConstParm, Parm, ZeroConstructor, NoDestructor, UObjectWrapper, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_HasElements) == 0x000008, "Wrong alignment on TypedElementListLibrary_HasElements");
static_assert(sizeof(TypedElementListLibrary_HasElements) == 0x000020, "Wrong size on TypedElementListLibrary_HasElements");
static_assert(offsetof(TypedElementListLibrary_HasElements, ElementList) == 0x000000, "Member 'TypedElementListLibrary_HasElements::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_HasElements, BaseInterfaceType) == 0x000010, "Member 'TypedElementListLibrary_HasElements::BaseInterfaceType' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_HasElements, ReturnValue) == 0x000018, "Member 'TypedElementListLibrary_HasElements::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.HasElementsOfType
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_HasElementsOfType final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	class FName                                   ElementTypeName;                                   // 0x0010(0x0008)(ConstParm, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_HasElementsOfType) == 0x000008, "Wrong alignment on TypedElementListLibrary_HasElementsOfType");
static_assert(sizeof(TypedElementListLibrary_HasElementsOfType) == 0x000020, "Wrong size on TypedElementListLibrary_HasElementsOfType");
static_assert(offsetof(TypedElementListLibrary_HasElementsOfType, ElementList) == 0x000000, "Member 'TypedElementListLibrary_HasElementsOfType::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_HasElementsOfType, ElementTypeName) == 0x000010, "Member 'TypedElementListLibrary_HasElementsOfType::ElementTypeName' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_HasElementsOfType, ReturnValue) == 0x000018, "Member 'TypedElementListLibrary_HasElementsOfType::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.IsValidIndex
// 0x0018 (0x0018 - 0x0000)
struct TypedElementListLibrary_IsValidIndex final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	int32                                         Index_0;                                           // 0x0010(0x0004)(ConstParm, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0014(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_IsValidIndex) == 0x000008, "Wrong alignment on TypedElementListLibrary_IsValidIndex");
static_assert(sizeof(TypedElementListLibrary_IsValidIndex) == 0x000018, "Wrong size on TypedElementListLibrary_IsValidIndex");
static_assert(offsetof(TypedElementListLibrary_IsValidIndex, ElementList) == 0x000000, "Member 'TypedElementListLibrary_IsValidIndex::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_IsValidIndex, Index_0) == 0x000010, "Member 'TypedElementListLibrary_IsValidIndex::Index_0' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_IsValidIndex, ReturnValue) == 0x000014, "Member 'TypedElementListLibrary_IsValidIndex::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Num
// 0x0018 (0x0018 - 0x0000)
struct TypedElementListLibrary_Num final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(ConstParm, Parm, NativeAccessSpecifierPublic)
	int32                                         ReturnValue;                                       // 0x0010(0x0004)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_Num) == 0x000008, "Wrong alignment on TypedElementListLibrary_Num");
static_assert(sizeof(TypedElementListLibrary_Num) == 0x000018, "Wrong size on TypedElementListLibrary_Num");
static_assert(offsetof(TypedElementListLibrary_Num, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Num::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Num, ReturnValue) == 0x000010, "Member 'TypedElementListLibrary_Num::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Remove
// 0x0020 (0x0020 - 0x0000)
struct TypedElementListLibrary_Remove final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(Parm, NativeAccessSpecifierPublic)
	struct FScriptTypedElementHandle              ElementHandle;                                     // 0x0010(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_Remove) == 0x000008, "Wrong alignment on TypedElementListLibrary_Remove");
static_assert(sizeof(TypedElementListLibrary_Remove) == 0x000020, "Wrong size on TypedElementListLibrary_Remove");
static_assert(offsetof(TypedElementListLibrary_Remove, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Remove::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Remove, ElementHandle) == 0x000010, "Member 'TypedElementListLibrary_Remove::ElementHandle' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Remove, ReturnValue) == 0x000018, "Member 'TypedElementListLibrary_Remove::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Reserve
// 0x0018 (0x0018 - 0x0000)
struct TypedElementListLibrary_Reserve final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(Parm, NativeAccessSpecifierPublic)
	int32                                         Size;                                              // 0x0010(0x0004)(ConstParm, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TypedElementListLibrary_Reserve) == 0x000008, "Wrong alignment on TypedElementListLibrary_Reserve");
static_assert(sizeof(TypedElementListLibrary_Reserve) == 0x000018, "Wrong size on TypedElementListLibrary_Reserve");
static_assert(offsetof(TypedElementListLibrary_Reserve, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Reserve::ElementList' has a wrong offset!");
static_assert(offsetof(TypedElementListLibrary_Reserve, Size) == 0x000010, "Member 'TypedElementListLibrary_Reserve::Size' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Reset
// 0x0010 (0x0010 - 0x0000)
struct TypedElementListLibrary_Reset final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(Parm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementListLibrary_Reset) == 0x000008, "Wrong alignment on TypedElementListLibrary_Reset");
static_assert(sizeof(TypedElementListLibrary_Reset) == 0x000010, "Wrong size on TypedElementListLibrary_Reset");
static_assert(offsetof(TypedElementListLibrary_Reset, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Reset::ElementList' has a wrong offset!");

// Function TypedElementFramework.TypedElementListLibrary.Shrink
// 0x0010 (0x0010 - 0x0000)
struct TypedElementListLibrary_Shrink final
{
public:
	struct FScriptTypedElementListProxy           ElementList;                                       // 0x0000(0x0010)(Parm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementListLibrary_Shrink) == 0x000008, "Wrong alignment on TypedElementListLibrary_Shrink");
static_assert(sizeof(TypedElementListLibrary_Shrink) == 0x000010, "Wrong size on TypedElementListLibrary_Shrink");
static_assert(offsetof(TypedElementListLibrary_Shrink, ElementList) == 0x000000, "Member 'TypedElementListLibrary_Shrink::ElementList' has a wrong offset!");

// Function TypedElementFramework.TypedElementRegistry.GetInstance
// 0x0008 (0x0008 - 0x0000)
struct TypedElementRegistry_GetInstance final
{
public:
	class UTypedElementRegistry*                  ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementRegistry_GetInstance) == 0x000008, "Wrong alignment on TypedElementRegistry_GetInstance");
static_assert(sizeof(TypedElementRegistry_GetInstance) == 0x000008, "Wrong size on TypedElementRegistry_GetInstance");
static_assert(offsetof(TypedElementRegistry_GetInstance, ReturnValue) == 0x000000, "Member 'TypedElementRegistry_GetInstance::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TypedElementRegistry.GetElementInterface
// 0x0018 (0x0018 - 0x0000)
struct TypedElementRegistry_GetElementInterface final
{
public:
	struct FScriptTypedElementHandle              InElementHandle;                                   // 0x0000(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	TSubclassOf<class IInterface>                 InBaseInterfaceType;                               // 0x0008(0x0008)(ConstParm, Parm, ZeroConstructor, NoDestructor, UObjectWrapper, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	class UObject*                                ReturnValue;                                       // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(TypedElementRegistry_GetElementInterface) == 0x000008, "Wrong alignment on TypedElementRegistry_GetElementInterface");
static_assert(sizeof(TypedElementRegistry_GetElementInterface) == 0x000018, "Wrong size on TypedElementRegistry_GetElementInterface");
static_assert(offsetof(TypedElementRegistry_GetElementInterface, InElementHandle) == 0x000000, "Member 'TypedElementRegistry_GetElementInterface::InElementHandle' has a wrong offset!");
static_assert(offsetof(TypedElementRegistry_GetElementInterface, InBaseInterfaceType) == 0x000008, "Member 'TypedElementRegistry_GetElementInterface::InBaseInterfaceType' has a wrong offset!");
static_assert(offsetof(TypedElementRegistry_GetElementInterface, ReturnValue) == 0x000010, "Member 'TypedElementRegistry_GetElementInterface::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TestTypedElementInterfaceA.GetDisplayName
// 0x0020 (0x0020 - 0x0000)
struct TestTypedElementInterfaceA_GetDisplayName final
{
public:
	struct FScriptTypedElementHandle              InElementHandle;                                   // 0x0000(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	class FText                                   ReturnValue;                                       // 0x0008(0x0018)(Parm, OutParm, ReturnParm, NativeAccessSpecifierPublic)
};
static_assert(alignof(TestTypedElementInterfaceA_GetDisplayName) == 0x000008, "Wrong alignment on TestTypedElementInterfaceA_GetDisplayName");
static_assert(sizeof(TestTypedElementInterfaceA_GetDisplayName) == 0x000020, "Wrong size on TestTypedElementInterfaceA_GetDisplayName");
static_assert(offsetof(TestTypedElementInterfaceA_GetDisplayName, InElementHandle) == 0x000000, "Member 'TestTypedElementInterfaceA_GetDisplayName::InElementHandle' has a wrong offset!");
static_assert(offsetof(TestTypedElementInterfaceA_GetDisplayName, ReturnValue) == 0x000008, "Member 'TestTypedElementInterfaceA_GetDisplayName::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TestTypedElementInterfaceA.SetDisplayName
// 0x0028 (0x0028 - 0x0000)
struct TestTypedElementInterfaceA_SetDisplayName final
{
public:
	struct FScriptTypedElementHandle              InElementHandle;                                   // 0x0000(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	class FText                                   InNewName;                                         // 0x0008(0x0018)(Parm, NativeAccessSpecifierPublic)
	bool                                          bNotify;                                           // 0x0020(0x0001)(Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0021(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_22[0x6];                                       // 0x0022(0x0006)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TestTypedElementInterfaceA_SetDisplayName) == 0x000008, "Wrong alignment on TestTypedElementInterfaceA_SetDisplayName");
static_assert(sizeof(TestTypedElementInterfaceA_SetDisplayName) == 0x000028, "Wrong size on TestTypedElementInterfaceA_SetDisplayName");
static_assert(offsetof(TestTypedElementInterfaceA_SetDisplayName, InElementHandle) == 0x000000, "Member 'TestTypedElementInterfaceA_SetDisplayName::InElementHandle' has a wrong offset!");
static_assert(offsetof(TestTypedElementInterfaceA_SetDisplayName, InNewName) == 0x000008, "Member 'TestTypedElementInterfaceA_SetDisplayName::InNewName' has a wrong offset!");
static_assert(offsetof(TestTypedElementInterfaceA_SetDisplayName, bNotify) == 0x000020, "Member 'TestTypedElementInterfaceA_SetDisplayName::bNotify' has a wrong offset!");
static_assert(offsetof(TestTypedElementInterfaceA_SetDisplayName, ReturnValue) == 0x000021, "Member 'TestTypedElementInterfaceA_SetDisplayName::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TestTypedElementInterfaceB.MarkAsTested
// 0x0010 (0x0010 - 0x0000)
struct TestTypedElementInterfaceB_MarkAsTested final
{
public:
	struct FScriptTypedElementHandle              InElementHandle;                                   // 0x0000(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TestTypedElementInterfaceB_MarkAsTested) == 0x000008, "Wrong alignment on TestTypedElementInterfaceB_MarkAsTested");
static_assert(sizeof(TestTypedElementInterfaceB_MarkAsTested) == 0x000010, "Wrong size on TestTypedElementInterfaceB_MarkAsTested");
static_assert(offsetof(TestTypedElementInterfaceB_MarkAsTested, InElementHandle) == 0x000000, "Member 'TestTypedElementInterfaceB_MarkAsTested::InElementHandle' has a wrong offset!");
static_assert(offsetof(TestTypedElementInterfaceB_MarkAsTested, ReturnValue) == 0x000008, "Member 'TestTypedElementInterfaceB_MarkAsTested::ReturnValue' has a wrong offset!");

// Function TypedElementFramework.TestTypedElementInterfaceC.GetIsTested
// 0x0010 (0x0010 - 0x0000)
struct TestTypedElementInterfaceC_GetIsTested final
{
public:
	struct FScriptTypedElementHandle              InElementHandle;                                   // 0x0000(0x0008)(ConstParm, Parm, OutParm, ReferenceParm, NativeAccessSpecifierPublic)
	bool                                          ReturnValue;                                       // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(TestTypedElementInterfaceC_GetIsTested) == 0x000008, "Wrong alignment on TestTypedElementInterfaceC_GetIsTested");
static_assert(sizeof(TestTypedElementInterfaceC_GetIsTested) == 0x000010, "Wrong size on TestTypedElementInterfaceC_GetIsTested");
static_assert(offsetof(TestTypedElementInterfaceC_GetIsTested, InElementHandle) == 0x000000, "Member 'TestTypedElementInterfaceC_GetIsTested::InElementHandle' has a wrong offset!");
static_assert(offsetof(TestTypedElementInterfaceC_GetIsTested, ReturnValue) == 0x000008, "Member 'TestTypedElementInterfaceC_GetIsTested::ReturnValue' has a wrong offset!");

}

