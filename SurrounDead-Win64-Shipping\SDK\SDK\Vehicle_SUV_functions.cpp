﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_SUV

#include "Basic.hpp"

#include "Vehicle_SUV_classes.hpp"
#include "Vehicle_SUV_parameters.hpp"


namespace SDK
{

// Function Vehicle_SUV.Vehicle_SUV_C.UserConstructionScript
// (Event, Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AVehicle_SUV_C::UserConstructionScript()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_SUV_C", "UserConstructionScript");

	UObject::ProcessEvent(Func, nullptr);
}

}

