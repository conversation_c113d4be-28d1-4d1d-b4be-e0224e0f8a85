﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Weather_Mask_Projection_Box_Component

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Calculate Masking At Location
// 0x0410 (0x0410 - 0x0000)
struct Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location final
{
public:
	struct FVector                                Location;                                          // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Mask;                                              // 0x0018(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cancel_All_Masks;                                  // 0x0028(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Total_Occlusion;                                   // 0x0030(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FVector>                        Nine_Trace_Vectors;                                // 0x0038(0x0010)(Edit, BlueprintVisible)
	TArray<struct FVector>                        Five_Trace_Vectors;                                // 0x0048(0x0010)(Edit, BlueprintVisible)
	TArray<struct FVector>                        One_Trace_Vector;                                  // 0x0058(0x0010)(Edit, BlueprintVisible)
	int32                                         Number_of_Traces;                                  // 0x0068(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FVector>                        K2Node_MakeArray_Array;                            // 0x0070(0x0010)(ReferenceParm)
	double                                        CallFunc_BreakVector_X;                            // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FVector>                        K2Node_MakeArray_Array_1;                          // 0x0098(0x0010)(ReferenceParm)
	TArray<struct FVector>                        K2Node_MakeArray_Array_2;                          // 0x00A8(0x0010)(ReferenceParm)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x00BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x00C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C4[0xC];                                       // 0x00C4(0x000C)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTransform                             CallFunc_K2_GetComponentToWorld_ReturnValue;       // 0x00D0(0x0060)(IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_InverseTransformLocation_ReturnValue;     // 0x0130(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Vector_GetAbs_ReturnValue;                // 0x0148(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0160(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_161[0x7];                                      // 0x0161(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector_X_1;                          // 0x0168(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y_1;                          // 0x0170(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z_1;                          // 0x0178(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0180(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_184[0x4];                                      // 0x0184(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0190(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0198(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FCeil_ReturnValue;                        // 0x01A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Clamp_ReturnValue;                        // 0x01A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FVector>                        K2Node_Select_Default;                             // 0x01A8(0x0010)(ReferenceParm)
	struct FVector                                CallFunc_Array_Get_Item;                           // 0x01B8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x01D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x01D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1DC[0x4];                                      // 0x01DC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x01E0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x01F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1F1[0x3];                                      // 0x01F1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x01F4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x01F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_DoubleToVector_ReturnValue;          // 0x0200(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0218(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Multiply_VectorVector_ReturnValue;        // 0x0220(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0238(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue;             // 0x0240(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_DoubleToVector_ReturnValue_1;        // 0x0258(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue_1;           // 0x0270(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Divide_VectorVector_ReturnValue;          // 0x0288(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X_2;                          // 0x02A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y_2;                          // 0x02A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z_2;                          // 0x02B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X_3;                          // 0x02B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y_3;                          // 0x02C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z_3;                          // 0x02C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_1;          // 0x02D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2D1[0x7];                                      // 0x02D1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x02D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_2;          // 0x02E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x02E1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2E2[0x6];                                      // 0x02E2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<EObjectTypeQuery>                      K2Node_MakeArray_Array_3;                          // 0x02E8(0x0010)(ConstParm, ReferenceParm)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x02F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2F9[0x7];                                      // 0x02F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_MakeVector_ReturnValue;                   // 0x0300(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHitResult                             CallFunc_LineTraceSingleForObjects_OutHit;         // 0x0318(0x00E8)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	bool                                          CallFunc_LineTraceSingleForObjects_ReturnValue;    // 0x0400(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location) == 0x000010, "Wrong alignment on Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location");
static_assert(sizeof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location) == 0x000410, "Wrong size on Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Location) == 0x000000, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Location' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Mask) == 0x000018, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Mask' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Cancel_All_Masks) == 0x000028, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Cancel_All_Masks' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Total_Occlusion) == 0x000030, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Total_Occlusion' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Nine_Trace_Vectors) == 0x000038, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Nine_Trace_Vectors' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Five_Trace_Vectors) == 0x000048, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Five_Trace_Vectors' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, One_Trace_Vector) == 0x000058, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::One_Trace_Vector' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Number_of_Traces) == 0x000068, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Number_of_Traces' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, K2Node_MakeArray_Array) == 0x000070, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_X) == 0x000080, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_Y) == 0x000088, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_Z) == 0x000090, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, K2Node_MakeArray_Array_1) == 0x000098, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::K2Node_MakeArray_Array_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, K2Node_MakeArray_Array_2) == 0x0000A8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::K2Node_MakeArray_Array_2' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Temp_int_Array_Index_Variable) == 0x0000B8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Temp_int_Loop_Counter_Variable) == 0x0000BC, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Add_IntInt_ReturnValue) == 0x0000C0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_K2_GetComponentToWorld_ReturnValue) == 0x0000D0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_K2_GetComponentToWorld_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_InverseTransformLocation_ReturnValue) == 0x000130, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_InverseTransformLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Vector_GetAbs_ReturnValue) == 0x000148, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Vector_GetAbs_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000160, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_X_1) == 0x000168, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_X_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_Y_1) == 0x000170, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_Y_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_Z_1) == 0x000178, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_Z_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, Temp_int_Variable) == 0x000180, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000188, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000190, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000198, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_FCeil_ReturnValue) == 0x0001A0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_FCeil_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Clamp_ReturnValue) == 0x0001A4, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Clamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, K2Node_Select_Default) == 0x0001A8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Array_Get_Item) == 0x0001B8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x0001D0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Array_Length_ReturnValue) == 0x0001D8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_MakeVector2D_ReturnValue) == 0x0001E0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Less_IntInt_ReturnValue) == 0x0001F0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Array_Length_ReturnValue_1) == 0x0001F4, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x0001F8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Conv_DoubleToVector_ReturnValue) == 0x000200, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Conv_DoubleToVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000218, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Multiply_VectorVector_ReturnValue) == 0x000220, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Multiply_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000238, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Add_VectorVector_ReturnValue) == 0x000240, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Add_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Conv_DoubleToVector_ReturnValue_1) == 0x000258, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Conv_DoubleToVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Add_VectorVector_ReturnValue_1) == 0x000270, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Add_VectorVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Divide_VectorVector_ReturnValue) == 0x000288, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Divide_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_X_2) == 0x0002A0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_X_2' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_Y_2) == 0x0002A8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_Y_2' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_Z_2) == 0x0002B0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_Z_2' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_X_3) == 0x0002B8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_X_3' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_Y_3) == 0x0002C0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_Y_3' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BreakVector_Z_3) == 0x0002C8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BreakVector_Z_3' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Less_DoubleDouble_ReturnValue_1) == 0x0002D0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Less_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Add_DoubleDouble_ReturnValue) == 0x0002D8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_Less_DoubleDouble_ReturnValue_2) == 0x0002E0, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_Less_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BooleanAND_ReturnValue) == 0x0002E1, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, K2Node_MakeArray_Array_3) == 0x0002E8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::K2Node_MakeArray_Array_3' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_BooleanAND_ReturnValue_1) == 0x0002F8, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_MakeVector_ReturnValue) == 0x000300, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_MakeVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_LineTraceSingleForObjects_OutHit) == 0x000318, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_LineTraceSingleForObjects_OutHit' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location, CallFunc_LineTraceSingleForObjects_ReturnValue) == 0x000400, "Member 'Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location::CallFunc_LineTraceSingleForObjects_ReturnValue' has a wrong offset!");

// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.ExecuteUbergraph_Weather_Mask_Projection_Box_Component
// 0x0004 (0x0004 - 0x0000)
struct Weather_Mask_Projection_Box_Component_C_ExecuteUbergraph_Weather_Mask_Projection_Box_Component final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Mask_Projection_Box_Component_C_ExecuteUbergraph_Weather_Mask_Projection_Box_Component) == 0x000004, "Wrong alignment on Weather_Mask_Projection_Box_Component_C_ExecuteUbergraph_Weather_Mask_Projection_Box_Component");
static_assert(sizeof(Weather_Mask_Projection_Box_Component_C_ExecuteUbergraph_Weather_Mask_Projection_Box_Component) == 0x000004, "Wrong size on Weather_Mask_Projection_Box_Component_C_ExecuteUbergraph_Weather_Mask_Projection_Box_Component");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_ExecuteUbergraph_Weather_Mask_Projection_Box_Component, EntryPoint) == 0x000000, "Member 'Weather_Mask_Projection_Box_Component_C_ExecuteUbergraph_Weather_Mask_Projection_Box_Component::EntryPoint' has a wrong offset!");

// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Get Brush Scale
// 0x0020 (0x0020 - 0x0000)
struct Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale final
{
public:
	struct FVector2D                              Out;                                               // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue;        // 0x0010(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale) == 0x000008, "Wrong alignment on Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale");
static_assert(sizeof(Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale) == 0x000020, "Wrong size on Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale, Out) == 0x000000, "Member 'Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale::Out' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale, CallFunc_Conv_VectorToVector2D_ReturnValue) == 0x000010, "Member 'Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale::CallFunc_Conv_VectorToVector2D_ReturnValue' has a wrong offset!");

// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Prepare for Drawing
// 0x0110 (0x0110 - 0x0000)
struct Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing final
{
public:
	bool                                          Runtime;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Weather_C*               UDW_0;                                             // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class USceneCaptureComponent2D*               CallFunc_Get_Projection_Box_Scene_Capture_2D_Out;  // 0x0010(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_CreateDynamicMaterialInstance_ReturnValue; // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39[0x7];                                       // 0x0039(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector_X;                            // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_GetVectorParameterValue_ReturnValue;      // 0x0058(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_1;                        // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_1;                        // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_2;        // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_3;        // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_SafeDivide_ReturnValue;                   // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue;      // 0x00A8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_CreateDynamicMaterialInstance_ReturnValue_1; // 0x00C0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X_1;                          // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y_1;                          // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z_1;                          // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E1[0x3];                                       // 0x00E1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x00E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_1; // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_2; // 0x00EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_SafeDivide_B_ImplicitCast;                // 0x00F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_3; // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_4; // 0x00FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_5; // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_6; // 0x0104(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_7; // 0x0108(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast_8; // 0x010C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing) == 0x000008, "Wrong alignment on Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing");
static_assert(sizeof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing) == 0x000110, "Wrong size on Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, Runtime) == 0x000000, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::Runtime' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, UDW_0) == 0x000008, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::UDW_0' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_Get_Projection_Box_Scene_Capture_2D_Out) == 0x000010, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_Get_Projection_Box_Scene_Capture_2D_Out' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_Not_PreBool_ReturnValue) == 0x000018, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000020, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000028, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_CreateDynamicMaterialInstance_ReturnValue) == 0x000030, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_CreateDynamicMaterialInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_IsValid_ReturnValue) == 0x000038, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector_X) == 0x000040, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector_Y) == 0x000048, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector_Z) == 0x000050, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_GetVectorParameterValue_ReturnValue) == 0x000058, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_GetVectorParameterValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector2D_X) == 0x000068, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector2D_Y) == 0x000070, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000078, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector2D_X_1) == 0x000080, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector2D_Y_1) == 0x000088, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_Divide_DoubleDouble_ReturnValue_2) == 0x000090, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_Divide_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_Divide_DoubleDouble_ReturnValue_3) == 0x000098, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_Divide_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SafeDivide_ReturnValue) == 0x0000A0, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SafeDivide_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_K2_GetComponentLocation_ReturnValue) == 0x0000A8, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_K2_GetComponentLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_CreateDynamicMaterialInstance_ReturnValue_1) == 0x0000C0, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_CreateDynamicMaterialInstance_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector_X_1) == 0x0000C8, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector_X_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector_Y_1) == 0x0000D0, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector_Y_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_BreakVector_Z_1) == 0x0000D8, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_BreakVector_Z_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_Not_PreBool_ReturnValue_1) == 0x0000E0, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x0000E4, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SetScalarParameterValue_Value_ImplicitCast_1) == 0x0000E8, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SetScalarParameterValue_Value_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SetScalarParameterValue_Value_ImplicitCast_2) == 0x0000EC, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SetScalarParameterValue_Value_ImplicitCast_2' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SafeDivide_B_ImplicitCast) == 0x0000F0, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SafeDivide_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SetScalarParameterValue_Value_ImplicitCast_3) == 0x0000F8, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SetScalarParameterValue_Value_ImplicitCast_3' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SetScalarParameterValue_Value_ImplicitCast_4) == 0x0000FC, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SetScalarParameterValue_Value_ImplicitCast_4' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SetScalarParameterValue_Value_ImplicitCast_5) == 0x000100, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SetScalarParameterValue_Value_ImplicitCast_5' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SetScalarParameterValue_Value_ImplicitCast_6) == 0x000104, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SetScalarParameterValue_Value_ImplicitCast_6' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SetScalarParameterValue_Value_ImplicitCast_7) == 0x000108, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SetScalarParameterValue_Value_ImplicitCast_7' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing, CallFunc_SetScalarParameterValue_Value_ImplicitCast_8) == 0x00010C, "Member 'Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing::CallFunc_SetScalarParameterValue_Value_ImplicitCast_8' has a wrong offset!");

// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Recycle Render Target
// 0x0001 (0x0001 - 0x0000)
struct Weather_Mask_Projection_Box_Component_C_Recycle_Render_Target final
{
public:
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Mask_Projection_Box_Component_C_Recycle_Render_Target) == 0x000001, "Wrong alignment on Weather_Mask_Projection_Box_Component_C_Recycle_Render_Target");
static_assert(sizeof(Weather_Mask_Projection_Box_Component_C_Recycle_Render_Target) == 0x000001, "Wrong size on Weather_Mask_Projection_Box_Component_C_Recycle_Render_Target");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Recycle_Render_Target, CallFunc_IsValid_ReturnValue) == 0x000000, "Member 'Weather_Mask_Projection_Box_Component_C_Recycle_Render_Target::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Update Capture
// 0x0330 (0x0330 - 0x0000)
struct Weather_Mask_Projection_Box_Component_C_Update_Capture final
{
public:
	struct FVector                                CallFunc_Conv_DoubleToVector_ReturnValue;          // 0x0000(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X;                            // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               CallFunc_K2_GetComponentRotation_ReturnValue;      // 0x0030(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue;      // 0x0048(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakRotator_Roll;                        // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakRotator_Pitch;                       // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakRotator_Yaw;                         // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FRotator                               CallFunc_MakeRotator_ReturnValue;                  // 0x0070(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FVector                                CallFunc_Conv_DoubleToVector_ReturnValue_1;        // 0x0088(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Multiply_VectorVector_ReturnValue;        // 0x00A0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue;             // 0x00B8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue_1;           // 0x00D8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_MakeVector_ReturnValue;                   // 0x00F0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_108[0x8];                                      // 0x0108(0x0008)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTransform                             CallFunc_MakeTransform_ReturnValue;                // 0x0110(0x0060)(IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetComponentScale_ReturnValue;         // 0x0170(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHitResult                             CallFunc_K2_SetWorldTransform_SweepHitResult;      // 0x0188(0x00E8)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	struct FVector                                CallFunc_Multiply_VectorVector_ReturnValue_1;      // 0x0270(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue_2;           // 0x0288(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Divide_VectorVector_ReturnValue;          // 0x02A0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_IntToVector_ReturnValue;             // 0x02B8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x02D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2D1[0x7];                                      // 0x02D1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Divide_VectorVector_ReturnValue_1;        // 0x02D8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X_1;                          // 0x02F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y_1;                          // 0x02F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z_1;                          // 0x0300(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue;                       // 0x0308(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Clamp_ReturnValue;                        // 0x030C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTextureRenderTarget2D*                 CallFunc_CreateRenderTarget2D_ReturnValue;         // 0x0310(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_1;                     // 0x0318(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Clamp_ReturnValue_1;                      // 0x031C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_VariableSet_OrthoWidth_ImplicitCast;        // 0x0320(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Mask_Projection_Box_Component_C_Update_Capture) == 0x000010, "Wrong alignment on Weather_Mask_Projection_Box_Component_C_Update_Capture");
static_assert(sizeof(Weather_Mask_Projection_Box_Component_C_Update_Capture) == 0x000330, "Wrong size on Weather_Mask_Projection_Box_Component_C_Update_Capture");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Conv_DoubleToVector_ReturnValue) == 0x000000, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Conv_DoubleToVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_BreakVector_X) == 0x000018, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_BreakVector_Y) == 0x000020, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_BreakVector_Z) == 0x000028, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_K2_GetComponentRotation_ReturnValue) == 0x000030, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_K2_GetComponentRotation_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_K2_GetComponentLocation_ReturnValue) == 0x000048, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_K2_GetComponentLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_BreakRotator_Roll) == 0x000060, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_BreakRotator_Roll' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_BreakRotator_Pitch) == 0x000064, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_BreakRotator_Pitch' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_BreakRotator_Yaw) == 0x000068, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_BreakRotator_Yaw' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_MakeRotator_ReturnValue) == 0x000070, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_MakeRotator_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Conv_DoubleToVector_ReturnValue_1) == 0x000088, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Conv_DoubleToVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Multiply_VectorVector_ReturnValue) == 0x0000A0, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Multiply_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Add_VectorVector_ReturnValue) == 0x0000B8, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Add_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0000D0, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Add_VectorVector_ReturnValue_1) == 0x0000D8, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Add_VectorVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_MakeVector_ReturnValue) == 0x0000F0, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_MakeVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_MakeTransform_ReturnValue) == 0x000110, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_MakeTransform_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_K2_GetComponentScale_ReturnValue) == 0x000170, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_K2_GetComponentScale_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_K2_SetWorldTransform_SweepHitResult) == 0x000188, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_K2_SetWorldTransform_SweepHitResult' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Multiply_VectorVector_ReturnValue_1) == 0x000270, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Multiply_VectorVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Add_VectorVector_ReturnValue_2) == 0x000288, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Add_VectorVector_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Divide_VectorVector_ReturnValue) == 0x0002A0, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Divide_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Conv_IntToVector_ReturnValue) == 0x0002B8, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Conv_IntToVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_IsValid_ReturnValue) == 0x0002D0, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Divide_VectorVector_ReturnValue_1) == 0x0002D8, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Divide_VectorVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_BreakVector_X_1) == 0x0002F0, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_BreakVector_X_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_BreakVector_Y_1) == 0x0002F8, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_BreakVector_Y_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_BreakVector_Z_1) == 0x000300, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_BreakVector_Z_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_FTrunc_ReturnValue) == 0x000308, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_FTrunc_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Clamp_ReturnValue) == 0x00030C, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Clamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_CreateRenderTarget2D_ReturnValue) == 0x000310, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_CreateRenderTarget2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_FTrunc_ReturnValue_1) == 0x000318, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_FTrunc_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, CallFunc_Clamp_ReturnValue_1) == 0x00031C, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::CallFunc_Clamp_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Mask_Projection_Box_Component_C_Update_Capture, K2Node_VariableSet_OrthoWidth_ImplicitCast) == 0x000320, "Member 'Weather_Mask_Projection_Box_Component_C_Update_Capture::K2Node_VariableSet_OrthoWidth_ImplicitCast' has a wrong offset!");

}

