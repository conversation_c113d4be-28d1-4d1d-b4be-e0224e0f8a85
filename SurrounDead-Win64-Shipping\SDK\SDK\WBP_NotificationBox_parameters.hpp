﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NotificationBox

#include "Basic.hpp"


namespace SDK::Params
{

// Function WBP_NotificationBox.WBP_NotificationBox_C.NotificationExpired
// 0x0010 (0x0010 - 0x0000)
struct WBP_NotificationBox_C_NotificationExpired final
{
public:
	class UWBP_NarrativeHUDNotification_C*        Notification;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_RemoveChild_ReturnValue;                  // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NotificationBox_C_NotificationExpired) == 0x000008, "Wrong alignment on WBP_NotificationBox_C_NotificationExpired");
static_assert(sizeof(WBP_NotificationBox_C_NotificationExpired) == 0x000010, "Wrong size on WBP_NotificationBox_C_NotificationExpired");
static_assert(offsetof(WBP_NotificationBox_C_NotificationExpired, Notification) == 0x000000, "Member 'WBP_NotificationBox_C_NotificationExpired::Notification' has a wrong offset!");
static_assert(offsetof(WBP_NotificationBox_C_NotificationExpired, CallFunc_RemoveChild_ReturnValue) == 0x000008, "Member 'WBP_NotificationBox_C_NotificationExpired::CallFunc_RemoveChild_ReturnValue' has a wrong offset!");

// Function WBP_NotificationBox.WBP_NotificationBox_C.ShowNotification
// 0x0040 (0x0040 - 0x0000)
struct WBP_NotificationBox_C_ShowNotification final
{
public:
	class FText                                   Text_0;                                            // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	double                                        Duration;                                          // 0x0018(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWBP_NarrativeHUDNotification_C*        CallFunc_Create_ReturnValue;                       // 0x0020(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UWBP_NarrativeHUDNotification_C* Notification)> K2Node_CreateDelegate_OutputDelegate; // 0x0028(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UVerticalBoxSlot*                       CallFunc_AddChildToVerticalBox_ReturnValue;        // 0x0038(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NotificationBox_C_ShowNotification) == 0x000008, "Wrong alignment on WBP_NotificationBox_C_ShowNotification");
static_assert(sizeof(WBP_NotificationBox_C_ShowNotification) == 0x000040, "Wrong size on WBP_NotificationBox_C_ShowNotification");
static_assert(offsetof(WBP_NotificationBox_C_ShowNotification, Text_0) == 0x000000, "Member 'WBP_NotificationBox_C_ShowNotification::Text_0' has a wrong offset!");
static_assert(offsetof(WBP_NotificationBox_C_ShowNotification, Duration) == 0x000018, "Member 'WBP_NotificationBox_C_ShowNotification::Duration' has a wrong offset!");
static_assert(offsetof(WBP_NotificationBox_C_ShowNotification, CallFunc_Create_ReturnValue) == 0x000020, "Member 'WBP_NotificationBox_C_ShowNotification::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NotificationBox_C_ShowNotification, K2Node_CreateDelegate_OutputDelegate) == 0x000028, "Member 'WBP_NotificationBox_C_ShowNotification::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WBP_NotificationBox_C_ShowNotification, CallFunc_AddChildToVerticalBox_ReturnValue) == 0x000038, "Member 'WBP_NotificationBox_C_ShowNotification::CallFunc_AddChildToVerticalBox_ReturnValue' has a wrong offset!");

}

