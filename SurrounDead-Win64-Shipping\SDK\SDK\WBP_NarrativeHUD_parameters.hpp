﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeHUD

#include "Basic.hpp"


namespace SDK::Params
{

// Function WBP_NarrativeHUD.WBP_NarrativeHUD_C.ExecuteUbergraph_WBP_NarrativeHUD
// 0x0030 (0x0030 - 0x0000)
struct WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_Event_NotificationText;                     // 0x0008(0x0018)(ConstParm)
	float                                         K2Node_Event_Duration;                             // 0x0020(0x0004)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24[0x4];                                       // 0x0024(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_ShowNotification_Duration_ImplicitCast;   // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD) == 0x000008, "Wrong alignment on WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD");
static_assert(sizeof(WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD) == 0x000030, "Wrong size on WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD");
static_assert(offsetof(WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD, EntryPoint) == 0x000000, "Member 'WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD::EntryPoint' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD, K2Node_Event_NotificationText) == 0x000008, "Member 'WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD::K2Node_Event_NotificationText' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD, K2Node_Event_Duration) == 0x000020, "Member 'WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD::K2Node_Event_Duration' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD, CallFunc_ShowNotification_Duration_ImplicitCast) == 0x000028, "Member 'WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD::CallFunc_ShowNotification_Duration_ImplicitCast' has a wrong offset!");

// Function WBP_NarrativeHUD.WBP_NarrativeHUD_C.Open Quantity Selector
// 0x0040 (0x0040 - 0x0000)
struct WBP_NarrativeHUD_C_Open_Quantity_Selector final
{
public:
	int32                                         MinAmount;                                         // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         MaxAmount;                                         // 0x0004(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   InstructionText;                                   // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	class UWBP_QuantitySelector_C*                Quantity_Selector;                                 // 0x0020(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UWBP_NarrativeMenu_C*                   CallFunc_OpenMenu_ReturnValue;                     // 0x0028(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UWBP_QuantitySelector_C*                K2Node_DynamicCast_AsWBP_Quantity_Selector;        // 0x0030(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeHUD_C_Open_Quantity_Selector) == 0x000008, "Wrong alignment on WBP_NarrativeHUD_C_Open_Quantity_Selector");
static_assert(sizeof(WBP_NarrativeHUD_C_Open_Quantity_Selector) == 0x000040, "Wrong size on WBP_NarrativeHUD_C_Open_Quantity_Selector");
static_assert(offsetof(WBP_NarrativeHUD_C_Open_Quantity_Selector, MinAmount) == 0x000000, "Member 'WBP_NarrativeHUD_C_Open_Quantity_Selector::MinAmount' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_Open_Quantity_Selector, MaxAmount) == 0x000004, "Member 'WBP_NarrativeHUD_C_Open_Quantity_Selector::MaxAmount' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_Open_Quantity_Selector, InstructionText) == 0x000008, "Member 'WBP_NarrativeHUD_C_Open_Quantity_Selector::InstructionText' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_Open_Quantity_Selector, Quantity_Selector) == 0x000020, "Member 'WBP_NarrativeHUD_C_Open_Quantity_Selector::Quantity_Selector' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_Open_Quantity_Selector, CallFunc_OpenMenu_ReturnValue) == 0x000028, "Member 'WBP_NarrativeHUD_C_Open_Quantity_Selector::CallFunc_OpenMenu_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_Open_Quantity_Selector, K2Node_DynamicCast_AsWBP_Quantity_Selector) == 0x000030, "Member 'WBP_NarrativeHUD_C_Open_Quantity_Selector::K2Node_DynamicCast_AsWBP_Quantity_Selector' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_Open_Quantity_Selector, K2Node_DynamicCast_bSuccess) == 0x000038, "Member 'WBP_NarrativeHUD_C_Open_Quantity_Selector::K2Node_DynamicCast_bSuccess' has a wrong offset!");

// Function WBP_NarrativeHUD.WBP_NarrativeHUD_C.OpenMenu
// 0x0030 (0x0030 - 0x0000)
struct WBP_NarrativeHUD_C_OpenMenu final
{
public:
	TSubclassOf<class UWBP_NarrativeMenu_C>       ActivatableWidgetClass;                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	class UWBP_NarrativeMenu_C*                   ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UCommonActivatableWidget*               CallFunc_GetActiveWidget_ReturnValue;              // 0x0010(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UClass*                                 CallFunc_GetObjectClass_ReturnValue;               // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ClassClass_ReturnValue;        // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWBP_NarrativeMenu_C*                   CallFunc_BP_AddWidget_ReturnValue;                 // 0x0028(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeHUD_C_OpenMenu) == 0x000008, "Wrong alignment on WBP_NarrativeHUD_C_OpenMenu");
static_assert(sizeof(WBP_NarrativeHUD_C_OpenMenu) == 0x000030, "Wrong size on WBP_NarrativeHUD_C_OpenMenu");
static_assert(offsetof(WBP_NarrativeHUD_C_OpenMenu, ActivatableWidgetClass) == 0x000000, "Member 'WBP_NarrativeHUD_C_OpenMenu::ActivatableWidgetClass' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_OpenMenu, ReturnValue) == 0x000008, "Member 'WBP_NarrativeHUD_C_OpenMenu::ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_OpenMenu, CallFunc_GetActiveWidget_ReturnValue) == 0x000010, "Member 'WBP_NarrativeHUD_C_OpenMenu::CallFunc_GetActiveWidget_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_OpenMenu, CallFunc_GetObjectClass_ReturnValue) == 0x000018, "Member 'WBP_NarrativeHUD_C_OpenMenu::CallFunc_GetObjectClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_OpenMenu, CallFunc_EqualEqual_ClassClass_ReturnValue) == 0x000020, "Member 'WBP_NarrativeHUD_C_OpenMenu::CallFunc_EqualEqual_ClassClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_OpenMenu, CallFunc_BP_AddWidget_ReturnValue) == 0x000028, "Member 'WBP_NarrativeHUD_C_OpenMenu::CallFunc_BP_AddWidget_ReturnValue' has a wrong offset!");

// Function WBP_NarrativeHUD.WBP_NarrativeHUD_C.ShowNotification
// 0x0020 (0x0020 - 0x0000)
struct WBP_NarrativeHUD_C_ShowNotification final
{
public:
	class FText                                   NotificationText;                                  // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	float                                         Duration;                                          // 0x0018(0x0004)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeHUD_C_ShowNotification) == 0x000008, "Wrong alignment on WBP_NarrativeHUD_C_ShowNotification");
static_assert(sizeof(WBP_NarrativeHUD_C_ShowNotification) == 0x000020, "Wrong size on WBP_NarrativeHUD_C_ShowNotification");
static_assert(offsetof(WBP_NarrativeHUD_C_ShowNotification, NotificationText) == 0x000000, "Member 'WBP_NarrativeHUD_C_ShowNotification::NotificationText' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeHUD_C_ShowNotification, Duration) == 0x000018, "Member 'WBP_NarrativeHUD_C_ShowNotification::Duration' has a wrong offset!");

}

