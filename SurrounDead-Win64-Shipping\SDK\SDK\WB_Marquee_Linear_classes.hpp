﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Marquee_Linear

#include "Basic.hpp"

#include "EMarqueeMethod_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_Marquee_Linear.WB_Marquee_Linear_C
// 0x0028 (0x02E8 - 0x02C0)
class UWB_Marquee_Linear_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 Marquee;                                           // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               SB_Marquee;                                        // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FVector2D                              Size;                                              // 0x02D8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WB_Marquee_Linear(int32 EntryPoint);
	void SetMarqueeMask(class UTexture2D* Value);
	void SetMarqueeMethod(EMarqueeMethod MarqueeMethod);
	void SetMarqueeSize(const struct FVector2D& Size_0);
	void SetMarqueeSpeed(double Value);
	void SetMarqueeTiling(ESlateBrushTileType Tiling);
	void SetPercent(double Percent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_Marquee_Linear_C">();
	}
	static class UWB_Marquee_Linear_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_Marquee_Linear_C>();
	}
};
static_assert(alignof(UWB_Marquee_Linear_C) == 0x000008, "Wrong alignment on UWB_Marquee_Linear_C");
static_assert(sizeof(UWB_Marquee_Linear_C) == 0x0002E8, "Wrong size on UWB_Marquee_Linear_C");
static_assert(offsetof(UWB_Marquee_Linear_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_Marquee_Linear_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_Marquee_Linear_C, Marquee) == 0x0002C8, "Member 'UWB_Marquee_Linear_C::Marquee' has a wrong offset!");
static_assert(offsetof(UWB_Marquee_Linear_C, SB_Marquee) == 0x0002D0, "Member 'UWB_Marquee_Linear_C::SB_Marquee' has a wrong offset!");
static_assert(offsetof(UWB_Marquee_Linear_C, Size) == 0x0002D8, "Member 'UWB_Marquee_Linear_C::Size' has a wrong offset!");

}

