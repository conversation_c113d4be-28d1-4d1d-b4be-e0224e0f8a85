﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_Attack

#include "Basic.hpp"


namespace SDK::Params
{

// Function Zombie_Attack.Zombie_Attack_C.ExecuteUbergraph_Zombie_Attack
// 0x0028 (0x0028 - 0x0000)
struct Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AAIController*                          K2Node_Event_OwnerController;                      // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  K2Node_Event_ControlledPawn;                       // 0x0010(0x0008)(ZeroConstructor, NoD<PERSON>ru<PERSON>, HasGetValueTypeHash)
	class ABP_MasterZombie_C*                     K2Node_DynamicCast_AsBP_Master_Zombie;             // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack) == 0x000008, "Wrong alignment on Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack");
static_assert(sizeof(Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack) == 0x000028, "Wrong size on Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack");
static_assert(offsetof(Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack, EntryPoint) == 0x000000, "Member 'Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack::EntryPoint' has a wrong offset!");
static_assert(offsetof(Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack, K2Node_Event_OwnerController) == 0x000008, "Member 'Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack::K2Node_Event_OwnerController' has a wrong offset!");
static_assert(offsetof(Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack, K2Node_Event_ControlledPawn) == 0x000010, "Member 'Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack::K2Node_Event_ControlledPawn' has a wrong offset!");
static_assert(offsetof(Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack, K2Node_DynamicCast_AsBP_Master_Zombie) == 0x000018, "Member 'Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack::K2Node_DynamicCast_AsBP_Master_Zombie' has a wrong offset!");
static_assert(offsetof(Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack, K2Node_DynamicCast_bSuccess) == 0x000020, "Member 'Zombie_Attack_C_ExecuteUbergraph_Zombie_Attack::K2Node_DynamicCast_bSuccess' has a wrong offset!");

// Function Zombie_Attack.Zombie_Attack_C.ReceiveExecuteAI
// 0x0010 (0x0010 - 0x0000)
struct Zombie_Attack_C_ReceiveExecuteAI final
{
public:
	class AAIController*                          OwnerController;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  ControlledPawn;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Zombie_Attack_C_ReceiveExecuteAI) == 0x000008, "Wrong alignment on Zombie_Attack_C_ReceiveExecuteAI");
static_assert(sizeof(Zombie_Attack_C_ReceiveExecuteAI) == 0x000010, "Wrong size on Zombie_Attack_C_ReceiveExecuteAI");
static_assert(offsetof(Zombie_Attack_C_ReceiveExecuteAI, OwnerController) == 0x000000, "Member 'Zombie_Attack_C_ReceiveExecuteAI::OwnerController' has a wrong offset!");
static_assert(offsetof(Zombie_Attack_C_ReceiveExecuteAI, ControlledPawn) == 0x000008, "Member 'Zombie_Attack_C_ReceiveExecuteAI::ControlledPawn' has a wrong offset!");

}

