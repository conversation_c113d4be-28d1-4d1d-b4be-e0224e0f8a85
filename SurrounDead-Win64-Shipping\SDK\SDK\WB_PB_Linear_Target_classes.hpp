﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_PB_Linear_Target

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"
#include "Slate_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_PB_Linear_Target.WB_PB_Linear_Target_C
// 0x0018 (0x02D8 - 0x02C0)
class UWB_PB_Linear_Target_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UProgressBar*                           PB_Base;                                           // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWB_PB_Raw_C*                           WB_PB_Raw;                                         // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WB_PB_Linear_Target(int32 EntryPoint);
	void GetPercent(double* Percent);
	void SetBarFillType(EProgressBarFillType BarFillType, bool bUseShader);
	void SetDefaultValues();
	void SetFillColor(const struct FLinearColor& InColor);
	void SetPercent(double InPercent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_PB_Linear_Target_C">();
	}
	static class UWB_PB_Linear_Target_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_PB_Linear_Target_C>();
	}
};
static_assert(alignof(UWB_PB_Linear_Target_C) == 0x000008, "Wrong alignment on UWB_PB_Linear_Target_C");
static_assert(sizeof(UWB_PB_Linear_Target_C) == 0x0002D8, "Wrong size on UWB_PB_Linear_Target_C");
static_assert(offsetof(UWB_PB_Linear_Target_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_PB_Linear_Target_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Target_C, PB_Base) == 0x0002C8, "Member 'UWB_PB_Linear_Target_C::PB_Base' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Target_C, WB_PB_Raw) == 0x0002D0, "Member 'UWB_PB_Linear_Target_C::WB_PB_Raw' has a wrong offset!");

}

