﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UMG_BasicRadialIcon

#include "Basic.hpp"

#include "UMG_BasicRadialIcon_classes.hpp"
#include "UMG_BasicRadialIcon_parameters.hpp"


namespace SDK
{

// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.ExecuteUbergraph_UMG_BasicRadialIcon
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUMG_BasicRadialIcon_C::ExecuteUbergraph_UMG_BasicRadialIcon(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UMG_BasicRadialIcon_C", "ExecuteUbergraph_UMG_BasicRadialIcon");

	Params::UMG_BasicRadialIcon_C_ExecuteUbergraph_UMG_BasicRadialIcon Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.Get_Icon_ColorAndOpacity_0
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor UUMG_BasicRadialIcon_C::Get_Icon_ColorAndOpacity_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UMG_BasicRadialIcon_C", "Get_Icon_ColorAndOpacity_0");

	Params::UMG_BasicRadialIcon_C_Get_Icon_ColorAndOpacity_0 Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.OnHighlight
// (Public, BlueprintCallable, BlueprintEvent)

void UUMG_BasicRadialIcon_C::OnHighlight()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UMG_BasicRadialIcon_C", "OnHighlight");

	UObject::ProcessEvent(Func, nullptr);
}


// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.OnUnhighlight
// (Public, BlueprintCallable, BlueprintEvent)

void UUMG_BasicRadialIcon_C::OnUnhighlight()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UMG_BasicRadialIcon_C", "OnUnhighlight");

	UObject::ProcessEvent(Func, nullptr);
}


// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUMG_BasicRadialIcon_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UMG_BasicRadialIcon_C", "PreConstruct");

	Params::UMG_BasicRadialIcon_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function UMG_BasicRadialIcon.UMG_BasicRadialIcon_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUMG_BasicRadialIcon_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UMG_BasicRadialIcon_C", "Tick");

	Params::UMG_BasicRadialIcon_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}

}

