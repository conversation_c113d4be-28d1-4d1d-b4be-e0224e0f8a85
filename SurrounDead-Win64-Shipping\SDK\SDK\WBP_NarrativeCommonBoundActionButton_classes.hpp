﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeCommonBoundActionButton

#include "Basic.hpp"

#include "CommonUI_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WBP_NarrativeCommonBoundActionButton.WBP_NarrativeCommonBoundActionButton_C
// 0x0000 (0x15A0 - 0x15A0)
class UWBP_NarrativeCommonBoundActionButton_C final : public UCommonBoundActionButton
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WBP_NarrativeCommonBoundActionButton_C">();
	}
	static class UWBP_NarrativeCommonBoundActionButton_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWBP_NarrativeCommonBoundActionButton_C>();
	}
};
static_assert(alignof(UWBP_NarrativeCommonBoundActionButton_C) == 0x000010, "Wrong alignment on UWBP_NarrativeCommonBoundActionButton_C");
static_assert(sizeof(UWBP_NarrativeCommonBoundActionButton_C) == 0x0015A0, "Wrong size on UWBP_NarrativeCommonBoundActionButton_C");

}

