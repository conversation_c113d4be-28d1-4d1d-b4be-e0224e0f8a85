/*
* Simple DLL Injector for COD Controller Mod
* Injects the controller mod DLL into SurrounDead process
*/

#include <Windows.h>
#include <TlHelp32.h>
#include <iostream>
#include <string>

class DLLInjector {
private:
    DWORD GetProcessId(const std::wstring& processName) {
        PROCESSENTRY32 processEntry;
        processEntry.dwSize = sizeof(PROCESSENTRY32);
        
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) {
            return 0;
        }
        
        if (Process32First(snapshot, &processEntry)) {
            do {
                if (processName == processEntry.szExeFile) {
                    CloseHandle(snapshot);
                    return processEntry.th32ProcessID;
                }
            } while (Process32Next(snapshot, &processEntry));
        }
        
        CloseHandle(snapshot);
        return 0;
    }
    
    bool InjectDLL(DWORD processId, const std::string& dllPath) {
        // Open the target process
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) {
            std::cout << "Failed to open process. Error: " << GetLastError() << std::endl;
            return false;
        }
        
        // Allocate memory in the target process
        LPVOID pDllPath = VirtualAllocEx(hProcess, 0, dllPath.length() + 1, MEM_COMMIT, PAGE_READWRITE);
        if (!pDllPath) {
            std::cout << "Failed to allocate memory. Error: " << GetLastError() << std::endl;
            CloseHandle(hProcess);
            return false;
        }
        
        // Write the DLL path to the allocated memory
        if (!WriteProcessMemory(hProcess, pDllPath, dllPath.c_str(), dllPath.length() + 1, nullptr)) {
            std::cout << "Failed to write memory. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Get the address of LoadLibraryA
        HMODULE hKernel32 = GetModuleHandle(L"kernel32.dll");
        if (!hKernel32) {
            std::cout << "Failed to get kernel32.dll handle" << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        LPVOID pLoadLibrary = GetProcAddress(hKernel32, "LoadLibraryA");
        if (!pLoadLibrary) {
            std::cout << "Failed to get LoadLibraryA address" << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Create a remote thread to load the DLL
        HANDLE hThread = CreateRemoteThread(hProcess, nullptr, 0, 
            (LPTHREAD_START_ROUTINE)pLoadLibrary, pDllPath, 0, nullptr);
        if (!hThread) {
            std::cout << "Failed to create remote thread. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }
        
        // Wait for the thread to complete
        WaitForSingleObject(hThread, INFINITE);
        
        // Get the exit code (should be the module handle if successful)
        DWORD exitCode;
        GetExitCodeThread(hThread, &exitCode);
        
        // Cleanup
        CloseHandle(hThread);
        VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        
        return exitCode != 0;
    }

public:
    bool InjectIntoSurrounDead(const std::string& dllPath) {
        std::cout << "Looking for SurrounDead process..." << std::endl;
        
        DWORD processId = GetProcessId(L"SurrounDead.exe");
        if (processId == 0) {
            std::cout << "SurrounDead.exe not found! Make sure the game is running." << std::endl;
            return false;
        }
        
        std::cout << "Found SurrounDead process (PID: " << processId << ")" << std::endl;
        std::cout << "Injecting DLL: " << dllPath << std::endl;
        
        if (InjectDLL(processId, dllPath)) {
            std::cout << "DLL injected successfully!" << std::endl;
            return true;
        } else {
            std::cout << "Failed to inject DLL!" << std::endl;
            return false;
        }
    }
};

int main(int argc, char* argv[]) {
    std::cout << "==================================" << std::endl;
    std::cout << " COD Controller Mod DLL Injector" << std::endl;
    std::cout << "==================================" << std::endl;
    std::cout << std::endl;
    
    std::string dllPath;
    
    if (argc > 1) {
        dllPath = argv[1];
    } else {
        // Default DLL path
        char currentDir[MAX_PATH];
        GetCurrentDirectoryA(MAX_PATH, currentDir);
        dllPath = std::string(currentDir) + "\\COD_Controller_Mod.dll";
    }
    
    // Check if DLL exists
    DWORD fileAttributes = GetFileAttributesA(dllPath.c_str());
    if (fileAttributes == INVALID_FILE_ATTRIBUTES) {
        std::cout << "Error: DLL file not found: " << dllPath << std::endl;
        std::cout << "Make sure COD_Controller_Mod.dll is in the same directory as this injector." << std::endl;
        std::cout << std::endl;
        std::cout << "Press any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    DLLInjector injector;
    
    std::cout << "Instructions:" << std::endl;
    std::cout << "1. Start SurrounDead" << std::endl;
    std::cout << "2. Connect your Xbox controller" << std::endl;
    std::cout << "3. Press Enter to inject the mod" << std::endl;
    std::cout << std::endl;
    std::cout << "Press Enter when ready..." << std::endl;
    std::cin.get();
    
    if (injector.InjectIntoSurrounDead(dllPath)) {
        std::cout << std::endl;
        std::cout << "SUCCESS! The COD Controller Mod has been loaded." << std::endl;
        std::cout << std::endl;
        std::cout << "Your Xbox controller should now work with Call of Duty controls:" << std::endl;
        std::cout << "- Left Stick: Movement" << std::endl;
        std::cout << "- Right Stick: Camera/Look" << std::endl;
        std::cout << "- A: Jump, B: Reload, X: Use, Y: Switch Weapon" << std::endl;
        std::cout << "- LT: Aim, RT: Fire" << std::endl;
        std::cout << "- LB: Grenade, RB: Tactical" << std::endl;
        std::cout << "- Stick clicks: Sprint (LS), Melee (RS)" << std::endl;
        std::cout << "- D-Pad: Crouch, Prone, Last Weapon, Inventory" << std::endl;
        std::cout << std::endl;
        std::cout << "Enjoy your COD-style gaming experience!" << std::endl;
    } else {
        std::cout << std::endl;
        std::cout << "FAILED to inject the mod." << std::endl;
        std::cout << std::endl;
        std::cout << "Troubleshooting:" << std::endl;
        std::cout << "- Make sure SurrounDead is running" << std::endl;
        std::cout << "- Try running this injector as Administrator" << std::endl;
        std::cout << "- Check if antivirus is blocking the injection" << std::endl;
        std::cout << "- Ensure the DLL file is not corrupted" << std::endl;
    }
    
    std::cout << std::endl;
    std::cout << "Press any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}
