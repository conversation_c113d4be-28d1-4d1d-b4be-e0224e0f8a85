﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Ultra_Dynamic_Weather

#include "Basic.hpp"

#include "Ultra_Dynamic_Weather_classes.hpp"
#include "Ultra_Dynamic_Weather_parameters.hpp"


namespace SDK
{

// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Active Occlusion State
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UUDS_OcclusionState_C**           State                                                  (Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Active_Occlusion_State(class UUDS_OcclusionState_C** State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Active Occlusion State");

	Params::Ultra_Dynamic_Weather_C_Active_Occlusion_State Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (State != nullptr)
		*State = Parms.State;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Add Constructed Components
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Add_Constructed_Components()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Add Constructed Components");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Add Weather Override Volume to Array
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AWeather_Override_Volume_C*       Weather_Override_Volume                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Add_Weather_Override_Volume_to_Array(class AWeather_Override_Volume_C* Weather_Override_Volume)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Add Weather Override Volume to Array");

	Params::Ultra_Dynamic_Weather_C_Add_Weather_Override_Volume_to_Array Parms{};

	Parms.Weather_Override_Volume = Weather_Override_Volume;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Allow Render Target Drawing
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool AUltra_Dynamic_Weather_C::Allow_Render_Target_Drawing()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Allow Render Target Drawing");

	Params::Ultra_Dynamic_Weather_C_Allow_Render_Target_Drawing Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Any Manual Overrides Applied
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool AUltra_Dynamic_Weather_C::Any_Manual_Overrides_Applied()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Any Manual Overrides Applied");

	Params::Ultra_Dynamic_Weather_C_Any_Manual_Overrides_Applied Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Climate Preset Object
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Climate_Preset_C*            Climate                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Apply_Climate_Preset_Object(class UUDS_Climate_Preset_C* Climate)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Climate Preset Object");

	Params::Ultra_Dynamic_Weather_C_Apply_Climate_Preset_Object Parms{};

	Parms.Climate = Climate;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Interior Temperature with Occlusion
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Temp                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Occlusion                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Apply_Interior_Temperature_with_Occlusion(double Temp, double Occlusion, double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Interior Temperature with Occlusion");

	Params::Ultra_Dynamic_Weather_C_Apply_Interior_Temperature_with_Occlusion Parms{};

	Parms.Temp = Temp;
	Parms.Occlusion = Occlusion;

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Local Temperature Effects
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Temp                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Occlusion                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Temp_Out                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Apply_Local_Temperature_Effects(double Temp, double Occlusion, const struct FVector& Location, double* Temp_Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Local Temperature Effects");

	Params::Ultra_Dynamic_Weather_C_Apply_Local_Temperature_Effects Parms{};

	Parms.Temp = Temp;
	Parms.Occlusion = Occlusion;
	Parms.Location = std::move(Location);

	UObject::ProcessEvent(Func, &Parms);

	if (Temp_Out != nullptr)
		*Temp_Out = Parms.Temp_Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Manual State Override Values
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    For_Local_Weather                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Apply_Manual_State_Override_Values(bool For_Local_Weather)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Manual State Override Values");

	Params::Ultra_Dynamic_Weather_C_Apply_Manual_State_Override_Values Parms{};

	Parms.For_Local_Weather = For_Local_Weather;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Max to Material Effects
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UUDS_Weather_Settings_C*          Weather_0                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Snow_Coverage                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Wetness                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Param_Dust_0                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Apply_Max_to_Material_Effects(class UUDS_Weather_Settings_C* Weather_0, double* Snow_Coverage, double* Wetness, double* Param_Dust_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Max to Material Effects");

	Params::Ultra_Dynamic_Weather_C_Apply_Max_to_Material_Effects Parms{};

	Parms.Weather_0 = Weather_0;

	UObject::ProcessEvent(Func, &Parms);

	if (Snow_Coverage != nullptr)
		*Snow_Coverage = Parms.Snow_Coverage;

	if (Wetness != nullptr)
		*Wetness = Parms.Wetness;

	if (Param_Dust_0 != nullptr)
		*Param_Dust_0 = Parms.Param_Dust_0;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Radial Storm Actors to Local Weather
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    For_Local_Weather                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Apply_Radial_Storm_Actors_to_Local_Weather(bool For_Local_Weather)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Radial Storm Actors to Local Weather");

	Params::Ultra_Dynamic_Weather_C_Apply_Radial_Storm_Actors_to_Local_Weather Parms{};

	Parms.For_Local_Weather = For_Local_Weather;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Sound Effects Volume Levels
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Apply_Sound_Effects_Volume_Levels()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Sound Effects Volume Levels");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Sound Update Periods
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Apply_Sound_Update_Periods()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Sound Update Periods");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Weather Changes Above Cloud Layer
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    For_Local_Weather                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Apply_Weather_Changes_Above_Cloud_Layer(bool For_Local_Weather)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Weather Changes Above Cloud Layer");

	Params::Ultra_Dynamic_Weather_C_Apply_Weather_Changes_Above_Cloud_Layer Parms{};

	Parms.For_Local_Weather = For_Local_Weather;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Weather Override Volume State
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AWeather_Override_Volume_C*       WOV                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Alpha                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    For_Local_Weather                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Apply_Weather_Override_Volume_State(class AWeather_Override_Volume_C* WOV, double Alpha, bool For_Local_Weather)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Weather Override Volume State");

	Params::Ultra_Dynamic_Weather_C_Apply_Weather_Override_Volume_State Parms{};

	Parms.WOV = WOV;
	Parms.Alpha = Alpha;
	Parms.For_Local_Weather = For_Local_Weather;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Apply Weather Override Volumes
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    For_Local_Weather                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// TMap<class AWeather_Override_Volume_C*, double>*WOVs_Applied                                           (Parm, OutParm)

void AUltra_Dynamic_Weather_C::Apply_Weather_Override_Volumes(bool For_Local_Weather, TMap<class AWeather_Override_Volume_C*, double>* WOVs_Applied)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Apply Weather Override Volumes");

	Params::Ultra_Dynamic_Weather_C_Apply_Weather_Override_Volumes Parms{};

	Parms.For_Local_Weather = For_Local_Weather;

	UObject::ProcessEvent(Func, &Parms);

	if (WOVs_Applied != nullptr)
		*WOVs_Applied = std::move(Parms.WOVs_Applied);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Bind to Game User Settings
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Bind_to_Game_User_Settings()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Bind to Game User Settings");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Bind to UDS Dispatchers
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Bind_to_UDS_Dispatchers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Bind to UDS Dispatchers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Call Custom Weather Behavior
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Call_Custom_Weather_Behavior()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Call Custom Weather Behavior");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Call Editor Dispatchers
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Call_Editor_Dispatchers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Call Editor Dispatchers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Change to Random Weather Variation
// (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Time_to_Transition_to_Random_Weather__Seconds_         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EUDS_RandomWeatherTiming                Random_Weather_Mode                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Change_to_Random_Weather_Variation(double Time_to_Transition_to_Random_Weather__Seconds_, EUDS_RandomWeatherTiming Random_Weather_Mode)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Change to Random Weather Variation");

	Params::Ultra_Dynamic_Weather_C_Change_to_Random_Weather_Variation Parms{};

	Parms.Time_to_Transition_to_Random_Weather__Seconds_ = Time_to_Transition_to_Random_Weather__Seconds_;
	Parms.Random_Weather_Mode = Random_Weather_Mode;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Change Weather
// (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*          New_Weather_Type                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Time_To_Transition_To_New_Weather__Seconds_            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Change_Weather(class UUDS_Weather_Settings_C* New_Weather_Type, double Time_To_Transition_To_New_Weather__Seconds_)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Change Weather");

	Params::Ultra_Dynamic_Weather_C_Change_Weather Parms{};

	Parms.New_Weather_Type = New_Weather_Type;
	Parms.Time_To_Transition_To_New_Weather__Seconds_ = Time_To_Transition_To_New_Weather__Seconds_;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Change Wind Direction
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  New_Wind_Direction                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Change_Duration                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Change_Wind_Direction(double New_Wind_Direction, double Change_Duration)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Change Wind Direction");

	Params::Ultra_Dynamic_Weather_C_Change_Wind_Direction Parms{};

	Parms.New_Wind_Direction = New_Wind_Direction;
	Parms.Change_Duration = Change_Duration;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Check for Events to Dispatch
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Check_for_Events_to_Dispatch()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Check for Events to Dispatch");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Check for Render Target Recentering
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Check_for_Render_Target_Recentering()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Check for Render Target Recentering");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Check for Weather Value Update Threshold
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  New_Value                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double&                                 Buffer_Value                                           (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Threshold                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Range_Max                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool&                                   Update_Needed_Bool                                     (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EUDS_Weather_State_Variable             State_Variable                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Check_for_Weather_Value_Update_Threshold(double New_Value, double& Buffer_Value, double Threshold, double Range_Max, bool& Update_Needed_Bool, EUDS_Weather_State_Variable State_Variable)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Check for Weather Value Update Threshold");

	Params::Ultra_Dynamic_Weather_C_Check_for_Weather_Value_Update_Threshold Parms{};

	Parms.New_Value = New_Value;
	Parms.Buffer_Value = Buffer_Value;
	Parms.Threshold = Threshold;
	Parms.Range_Max = Range_Max;
	Parms.Update_Needed_Bool = Update_Needed_Bool;
	Parms.State_Variable = State_Variable;

	UObject::ProcessEvent(Func, &Parms);

	Buffer_Value = Parms.Buffer_Value;
	Update_Needed_Bool = Parms.Update_Needed_Bool;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Check Point for Puddles Snow Or Dust
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Ground_Normal                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UPhysicalMaterial*                Physical_Material                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Puddle_Depth                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Snow_Depth_0                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Dust_Depth_0                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Check_Point_for_Puddles_Snow_Or_Dust(const struct FVector& Location, const struct FVector& Ground_Normal, class UPhysicalMaterial* Physical_Material, double* Puddle_Depth, double* Snow_Depth_0, double* Dust_Depth_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Check Point for Puddles Snow Or Dust");

	Params::Ultra_Dynamic_Weather_C_Check_Point_for_Puddles_Snow_Or_Dust Parms{};

	Parms.Location = std::move(Location);
	Parms.Ground_Normal = std::move(Ground_Normal);
	Parms.Physical_Material = Physical_Material;

	UObject::ProcessEvent(Func, &Parms);

	if (Puddle_Depth != nullptr)
		*Puddle_Depth = Parms.Puddle_Depth;

	if (Snow_Depth_0 != nullptr)
		*Snow_Depth_0 = Parms.Snow_Depth_0;

	if (Dust_Depth_0 != nullptr)
		*Dust_Depth_0 = Parms.Dust_Depth_0;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Check to Change Temperature Scale
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Check_to_Change_Temperature_Scale()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Check to Change Temperature Scale");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Check UDS Version
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Check_UDS_Version()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Check UDS Version");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Clear All Material Parameters to Zero Coverage
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Clear_All_Material_Parameters_to_Zero_Coverage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Clear All Material Parameters to Zero Coverage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Clear All Render Targets
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Clear_All_Render_Targets()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Clear All Render Targets");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Clients Transition Start
// (Net, NetReliable, NetMulticast, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Duration                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Clients_Transition_Start(double Duration)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Clients Transition Start");

	Params::Ultra_Dynamic_Weather_C_Clients_Transition_Start Parms{};

	Parms.Duration = Duration;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Close Thunder Sound Delay
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Delay                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Close_Thunder_Sound_Delay(double* Delay)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Close Thunder Sound Delay");

	Params::Ultra_Dynamic_Weather_C_Close_Thunder_Sound_Delay Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Delay != nullptr)
		*Delay = Parms.Delay;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Combined Wind Direction
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Combined_Wind_Direction()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Combined Wind Direction");

	Params::Ultra_Dynamic_Weather_C_Combined_Wind_Direction Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Dust
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Size                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Func_Dust_0(const struct FVector& Location, double Size)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Dust");

	Params::Ultra_Dynamic_Weather_C_Func_Dust_0 Parms{};

	Parms.Location = std::move(Location);
	Parms.Size = Size;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Construct All Weather State Objects
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Construct_All_Weather_State_Objects()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Construct All Weather State Objects");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Construct Weather Mask Target State
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Construct_Weather_Mask_Target_State()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Construct Weather Mask Target State");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Construct Weather State Object if Invalid
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*&         State                                                  (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Construct_Weather_State_Object_if_Invalid(class UUDS_Weather_Settings_C*& State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Construct Weather State Object if Invalid");

	Params::Ultra_Dynamic_Weather_C_Construct_Weather_State_Object_if_Invalid Parms{};

	Parms.State = State;

	UObject::ProcessEvent(Func, &Parms);

	State = Parms.State;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Construct WOV Render Target State
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Construct_WOV_Render_Target_State()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Construct WOV Render Target State");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Construction Script Function
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Construction_Script_Function()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Construction Script Function");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Convert Temperature Scale
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Input                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EUDS_TemperatureType                    In_Scale                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EUDS_TemperatureType                    Out_Scale                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Relative_Degrees                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Snap                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Output                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Convert_Temperature_Scale(double Input, EUDS_TemperatureType In_Scale, EUDS_TemperatureType Out_Scale, bool Relative_Degrees, bool Snap, double* Output)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Convert Temperature Scale");

	Params::Ultra_Dynamic_Weather_C_Convert_Temperature_Scale Parms{};

	Parms.Input = Input;
	Parms.In_Scale = In_Scale;
	Parms.Out_Scale = Out_Scale;
	Parms.Relative_Degrees = Relative_Degrees;
	Parms.Snap = Snap;

	UObject::ProcessEvent(Func, &Parms);

	if (Output != nullptr)
		*Output = Parms.Output;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Copy Manual State Object to Variables
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Copy_Manual_State_Object_to_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Copy Manual State Object to Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Copy Weather State
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*          Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class UUDS_Weather_Settings_C*          Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// bool                                    Set_Material_Effects                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Copy_Sources                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Copy_Weather_State(class UUDS_Weather_Settings_C* Source, class UUDS_Weather_Settings_C* Target, bool Set_Material_Effects, bool Copy_Sources)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Copy Weather State");

	Params::Ultra_Dynamic_Weather_C_Copy_Weather_State Parms{};

	Parms.Source = Source;
	Parms.Target = Target;
	Parms.Set_Material_Effects = Set_Material_Effects;
	Parms.Copy_Sources = Copy_Sources;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Copy Weather State Structure to Object
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*          State                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// const struct FUDW_WeatherState_Structure&Struct                                                 (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Copy_Weather_State_Structure_to_Object(class UUDS_Weather_Settings_C* State, const struct FUDW_WeatherState_Structure& Struct)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Copy Weather State Structure to Object");

	Params::Ultra_Dynamic_Weather_C_Copy_Weather_State_Structure_to_Object Parms{};

	Parms.State = State;
	Parms.Struct = std::move(Struct);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Create Cloud Reference Array
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Create_Cloud_Reference_Array()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Create Cloud Reference Array");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Create Current Local Weather State
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Test_Location                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Create_Current_Local_Weather_State(const struct FVector& Test_Location)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Create Current Local Weather State");

	Params::Ultra_Dynamic_Weather_C_Create_Current_Local_Weather_State Parms{};

	Parms.Test_Location = std::move(Test_Location);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Current Dust Velocity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Velocity                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Current_Dust_Velocity(struct FVector* Velocity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Current Dust Velocity");

	Params::Ultra_Dynamic_Weather_C_Current_Dust_Velocity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Velocity != nullptr)
		*Velocity = std::move(Parms.Velocity);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Current Rain Velocity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Velocity                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Current_Rain_Velocity(struct FVector* Velocity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Current Rain Velocity");

	Params::Ultra_Dynamic_Weather_C_Current_Rain_Velocity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Velocity != nullptr)
		*Velocity = std::move(Parms.Velocity);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Current Snow Velocity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Velocity                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Current_Snow_Velocity(struct FVector* Velocity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Current Snow Velocity");

	Params::Ultra_Dynamic_Weather_C_Current_Snow_Velocity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Velocity != nullptr)
		*Velocity = std::move(Parms.Velocity);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Current Wind Debris Velocity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Velocity                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Current_Wind_Debris_Velocity(struct FVector* Velocity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Current Wind Debris Velocity");

	Params::Ultra_Dynamic_Weather_C_Current_Wind_Debris_Velocity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Velocity != nullptr)
		*Velocity = std::move(Parms.Velocity);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Currently Cloudy
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool*                                   Yes                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Currently_Cloudy(bool* Yes)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Currently Cloudy");

	Params::Ultra_Dynamic_Weather_C_Currently_Cloudy Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Yes != nullptr)
		*Yes = Parms.Yes;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Currently Dusty
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool*                                   Yes                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Currently_Dusty(bool* Yes)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Currently Dusty");

	Params::Ultra_Dynamic_Weather_C_Currently_Dusty Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Yes != nullptr)
		*Yes = Parms.Yes;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Currently Raining
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool*                                   Yes                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Currently_Raining(bool* Yes)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Currently Raining");

	Params::Ultra_Dynamic_Weather_C_Currently_Raining Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Yes != nullptr)
		*Yes = Parms.Yes;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Currently Snowing
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool*                                   Yes                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Currently_Snowing(bool* Yes)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Currently Snowing");

	Params::Ultra_Dynamic_Weather_C_Currently_Snowing Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Yes != nullptr)
		*Yes = Parms.Yes;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Daily Season Update
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Daily_Season_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Daily Season Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.DLWE Active Update
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::DLWE_Active_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "DLWE Active Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Dust Niagara System Asset
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TSoftObjectPtr<class UNiagaraSystem>*   Out                                                    (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Dust_Niagara_System_Asset(TSoftObjectPtr<class UNiagaraSystem>* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Dust Niagara System Asset");

	Params::Ultra_Dynamic_Weather_C_Dust_Niagara_System_Asset Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Dust Spawn Rate
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Dust_Spawn_Rate()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Dust Spawn Rate");

	Params::Ultra_Dynamic_Weather_C_Dust_Spawn_Rate Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Dust Sprite Alpha
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Dust_Sprite_Alpha()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Dust Sprite Alpha");

	Params::Ultra_Dynamic_Weather_C_Dust_Sprite_Alpha Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Dust System Finished
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UNiagaraComponent*                PSystem                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Dust_System_Finished(class UNiagaraComponent* PSystem)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Dust System Finished");

	Params::Ultra_Dynamic_Weather_C_Dust_System_Finished Parms{};

	Parms.PSystem = PSystem;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Editor Lightning Internal
// (BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Editor_Lightning_Internal()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Editor Lightning Internal");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Editor Tick
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Editor_Camera_Location                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FRotator&                  Editor_Camera_Rotation_0                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// double                                  Delta_Time                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Completed                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Editor_Tick(const struct FVector& Editor_Camera_Location, const struct FRotator& Editor_Camera_Rotation_0, double Delta_Time, bool* Completed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Editor Tick");

	Params::Ultra_Dynamic_Weather_C_Editor_Tick Parms{};

	Parms.Editor_Camera_Location = std::move(Editor_Camera_Location);
	Parms.Editor_Camera_Rotation_0 = std::move(Editor_Camera_Rotation_0);
	Parms.Delta_Time = Delta_Time;

	UObject::ProcessEvent(Func, &Parms);

	if (Completed != nullptr)
		*Completed = Parms.Completed;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.ExecuteUbergraph_Ultra_Dynamic_Weather
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::ExecuteUbergraph_Ultra_Dynamic_Weather(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "ExecuteUbergraph_Ultra_Dynamic_Weather");

	Params::Ultra_Dynamic_Weather_C_ExecuteUbergraph_Ultra_Dynamic_Weather Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Fade DLWE Target Over Time
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Fade_DLWE_Target_Over_Time()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Fade DLWE Target Over Time");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Fill Starting Update Buffer
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Fill_Starting_Update_Buffer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Fill Starting Update Buffer");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Filter Probability Map
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const TMap<class UUDS_Weather_Settings_C*, double>&Probability_Map                                        (BlueprintVisible, BlueprintReadOnly, Parm)
// class UUDS_Weather_Settings_C*          Current_Random_Type                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// TMap<class UUDS_Weather_Settings_C*, double>*Filtered_Probability_Map                               (Parm, OutParm)

void AUltra_Dynamic_Weather_C::Filter_Probability_Map(const TMap<class UUDS_Weather_Settings_C*, double>& Probability_Map, class UUDS_Weather_Settings_C* Current_Random_Type, TMap<class UUDS_Weather_Settings_C*, double>* Filtered_Probability_Map)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Filter Probability Map");

	Params::Ultra_Dynamic_Weather_C_Filter_Probability_Map Parms{};

	Parms.Probability_Map = std::move(Probability_Map);
	Parms.Current_Random_Type = Current_Random_Type;

	UObject::ProcessEvent(Func, &Parms);

	if (Filtered_Probability_Map != nullptr)
		*Filtered_Probability_Map = std::move(Parms.Filtered_Probability_Map);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Filter Radial Storm Array
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Filter_Radial_Storm_Array()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Filter Radial Storm Array");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Filter Weather Override Volumes Array
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Filter_Weather_Override_Volumes_Array()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Filter Weather Override Volumes Array");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Flash Lightning
// (Net, NetMulticast, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Angle                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Use_Custom_Lightning_Location_0                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Custom_Lightning_Location_0                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Custom_Target_Location                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Lightning_Bolt_Seed                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Flash_Lightning(double Angle, bool Use_Custom_Lightning_Location_0, const struct FVector& Custom_Lightning_Location_0, const struct FVector& Custom_Target_Location, int32 Lightning_Bolt_Seed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Flash Lightning");

	Params::Ultra_Dynamic_Weather_C_Flash_Lightning Parms{};

	Parms.Angle = Angle;
	Parms.Use_Custom_Lightning_Location_0 = Use_Custom_Lightning_Location_0;
	Parms.Custom_Lightning_Location_0 = std::move(Custom_Lightning_Location_0);
	Parms.Custom_Target_Location = std::move(Custom_Target_Location);
	Parms.Lightning_Bolt_Seed = Lightning_Bolt_Seed;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Fog Vertical Velocity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Fog_Vertical_Velocity(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Fog Vertical Velocity");

	Params::Ultra_Dynamic_Weather_C_Fog_Vertical_Velocity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Force Tick
// (BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Force_Tick()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Force Tick");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Game User Settings Update
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Game_User_Settings_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Game User Settings Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Generate Weather State At Location
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UUDS_Weather_Settings_C*          Settings_Object                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// TMap<class AWeather_Override_Volume_C*, double>*WOVs_Applied_at_Location                               (Parm, OutParm)

void AUltra_Dynamic_Weather_C::Generate_Weather_State_At_Location(const struct FVector& Location, class UUDS_Weather_Settings_C* Settings_Object, TMap<class AWeather_Override_Volume_C*, double>* WOVs_Applied_at_Location)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Generate Weather State At Location");

	Params::Ultra_Dynamic_Weather_C_Generate_Weather_State_At_Location Parms{};

	Parms.Location = std::move(Location);
	Parms.Settings_Object = Settings_Object;

	UObject::ProcessEvent(Func, &Parms);

	if (WOVs_Applied_at_Location != nullptr)
		*WOVs_Applied_at_Location = std::move(Parms.WOVs_Applied_at_Location);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Control Point Location
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FVector*                         Location                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Control_Point_Location(struct FVector* Location)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Control Point Location");

	Params::Ultra_Dynamic_Weather_C_Get_Control_Point_Location Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Location != nullptr)
		*Location = std::move(Parms.Location);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C. Pawn Locations
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::_Pawn_Locations()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", " Pawn Locations");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Current Sound Occlusion Values
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Non_Directional_Occlusion                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 X__Occlusion                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Y__Occlusion                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 X__Occlusion_0                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Y__Occlusion_0                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Upward_Occlusion                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Current_Sound_Occlusion_Values(double* Non_Directional_Occlusion, double* X__Occlusion, double* Y__Occlusion, double* X__Occlusion_0, double* Y__Occlusion_0, double* Upward_Occlusion)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Current Sound Occlusion Values");

	Params::Ultra_Dynamic_Weather_C_Get_Current_Sound_Occlusion_Values Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Non_Directional_Occlusion != nullptr)
		*Non_Directional_Occlusion = Parms.Non_Directional_Occlusion;

	if (X__Occlusion != nullptr)
		*X__Occlusion = Parms.X__Occlusion;

	if (Y__Occlusion != nullptr)
		*Y__Occlusion = Parms.Y__Occlusion;

	if (X__Occlusion_0 != nullptr)
		*X__Occlusion_0 = Parms.X__Occlusion_0;

	if (Y__Occlusion_0 != nullptr)
		*Y__Occlusion_0 = Parms.Y__Occlusion_0;

	if (Upward_Occlusion != nullptr)
		*Upward_Occlusion = Parms.Upward_Occlusion;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Current Temperature
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// EUDS_Temperature_Sample_Location        Sample_Location                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Custom_Sample_Location                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EUDS_TemperatureType                    Scale                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Output                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Current_Temperature(EUDS_Temperature_Sample_Location Sample_Location, const struct FVector& Custom_Sample_Location, EUDS_TemperatureType Scale, double* Output)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Current Temperature");

	Params::Ultra_Dynamic_Weather_C_Get_Current_Temperature Parms{};

	Parms.Sample_Location = Sample_Location;
	Parms.Custom_Sample_Location = std::move(Custom_Sample_Location);
	Parms.Scale = Scale;

	UObject::ProcessEvent(Func, &Parms);

	if (Output != nullptr)
		*Output = Parms.Output;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Display Name for Current Weather
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FString*                          As_String                                              (Parm, OutParm, ZeroConstructor, HasGetValueTypeHash)
// EUDS_Weather_Display_Names*             As_Enumerator                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Display_Name_for_Current_Weather(class FString* As_String, EUDS_Weather_Display_Names* As_Enumerator)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Display Name for Current Weather");

	Params::Ultra_Dynamic_Weather_C_Get_Display_Name_for_Current_Weather Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (As_String != nullptr)
		*As_String = std::move(Parms.As_String);

	if (As_Enumerator != nullptr)
		*As_Enumerator = Parms.As_Enumerator;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Local Weather State Values
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 Cloud_Coverage_0                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Wind_Intensity_0                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Rain_0                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Snow_0                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Param_Dust_0                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Param_Fog_0                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Param_Lightning_0                                      (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Local_Weather_State_Values(double* Cloud_Coverage_0, double* Wind_Intensity_0, double* Rain_0, double* Snow_0, double* Param_Dust_0, double* Param_Fog_0, double* Param_Lightning_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Local Weather State Values");

	Params::Ultra_Dynamic_Weather_C_Get_Local_Weather_State_Values Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Cloud_Coverage_0 != nullptr)
		*Cloud_Coverage_0 = Parms.Cloud_Coverage_0;

	if (Wind_Intensity_0 != nullptr)
		*Wind_Intensity_0 = Parms.Wind_Intensity_0;

	if (Rain_0 != nullptr)
		*Rain_0 = Parms.Rain_0;

	if (Snow_0 != nullptr)
		*Snow_0 = Parms.Snow_0;

	if (Param_Dust_0 != nullptr)
		*Param_Dust_0 = Parms.Param_Dust_0;

	if (Param_Fog_0 != nullptr)
		*Param_Fog_0 = Parms.Param_Fog_0;

	if (Param_Lightning_0 != nullptr)
		*Param_Lightning_0 = Parms.Param_Lightning_0;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get New Target Global Weather State
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C**         Out                                                    (Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// bool*                                   Changing                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Manual_State                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_New_Target_Global_Weather_State(class UUDS_Weather_Settings_C** Out, bool* Changing, bool* Manual_State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get New Target Global Weather State");

	Params::Ultra_Dynamic_Weather_C_Get_New_Target_Global_Weather_State Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;

	if (Changing != nullptr)
		*Changing = Parms.Changing;

	if (Manual_State != nullptr)
		*Manual_State = Parms.Manual_State;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Normalized Wind Direction
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Wind_Vector                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Normalized_Wind_Direction(struct FVector* Wind_Vector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Normalized Wind Direction");

	Params::Ultra_Dynamic_Weather_C_Get_Normalized_Wind_Direction Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Wind_Vector != nullptr)
		*Wind_Vector = std::move(Parms.Wind_Vector);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Projection Box Scene Capture 2D
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class USceneCaptureComponent2D**        Out                                                    (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Projection_Box_Scene_Capture_2D(class USceneCaptureComponent2D** Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Projection Box Scene Capture 2D");

	Params::Ultra_Dynamic_Weather_C_Get_Projection_Box_Scene_Capture_2D Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Random Weather Forecast
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TArray<class UUDS_Weather_Settings_C*>* Upcoming_Weather_Objects                               (Parm, OutParm)

void AUltra_Dynamic_Weather_C::Get_Random_Weather_Forecast(TArray<class UUDS_Weather_Settings_C*>* Upcoming_Weather_Objects)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Random Weather Forecast");

	Params::Ultra_Dynamic_Weather_C_Get_Random_Weather_Forecast Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Upcoming_Weather_Objects != nullptr)
		*Upcoming_Weather_Objects = std::move(Parms.Upcoming_Weather_Objects);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Season
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Season_0                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EUDS_Season*                            Season_Enum                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Season(double* Season_0, EUDS_Season* Season_Enum)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Season");

	Params::Ultra_Dynamic_Weather_C_Get_Season Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Season_0 != nullptr)
		*Season_0 = Parms.Season_0;

	if (Season_Enum != nullptr)
		*Season_Enum = Parms.Season_Enum;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Sound Directional Occlusion
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TArray<double>*                         Out                                                    (Parm, OutParm)

void AUltra_Dynamic_Weather_C::Get_Sound_Directional_Occlusion(TArray<double>* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Sound Directional Occlusion");

	Params::Ultra_Dynamic_Weather_C_Get_Sound_Directional_Occlusion Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Sound Global Occlusion
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Sound_Global_Occlusion(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Sound Global Occlusion");

	Params::Ultra_Dynamic_Weather_C_Get_Sound_Global_Occlusion Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Sound Upward Occlusion
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_Sound_Upward_Occlusion(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Sound Upward Occlusion");

	Params::Ultra_Dynamic_Weather_C_Get_Sound_Upward_Occlusion Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Starting Radial Storms
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Get_Starting_Radial_Storms()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Starting Radial Storms");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Starting Weather Override Volumes
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Get_Starting_Weather_Override_Volumes()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Starting Weather Override Volumes");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Target Heat Distortion
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Get_Target_Heat_Distortion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Target Heat Distortion");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get UDS Values Controlled by UDW
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 Cloud_Coverage_0                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Param_Fog_0                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Dust_Amount                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Cloud_Direction                                        (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Wind_Speed_Multiplier                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Fog_Vertical_Velocity                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_UDS_Values_Controlled_by_UDW(double* Cloud_Coverage_0, double* Param_Fog_0, double* Dust_Amount, double* Cloud_Direction, double* Wind_Speed_Multiplier, double* Fog_Vertical_Velocity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get UDS Values Controlled by UDW");

	Params::Ultra_Dynamic_Weather_C_Get_UDS_Values_Controlled_by_UDW Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Cloud_Coverage_0 != nullptr)
		*Cloud_Coverage_0 = Parms.Cloud_Coverage_0;

	if (Param_Fog_0 != nullptr)
		*Param_Fog_0 = Parms.Param_Fog_0;

	if (Dust_Amount != nullptr)
		*Dust_Amount = Parms.Dust_Amount;

	if (Cloud_Direction != nullptr)
		*Cloud_Direction = Parms.Cloud_Direction;

	if (Wind_Speed_Multiplier != nullptr)
		*Wind_Speed_Multiplier = Parms.Wind_Speed_Multiplier;

	if (Fog_Vertical_Velocity != nullptr)
		*Fog_Vertical_Velocity = Parms.Fog_Vertical_Velocity;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get UDS Weather Override Bool
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   Cloud_Coverage_0                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Param_Fog_0                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Param_Dust_0                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_UDS_Weather_Override_Bool(bool* Cloud_Coverage_0, bool* Param_Fog_0, bool* Param_Dust_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get UDS Weather Override Bool");

	Params::Ultra_Dynamic_Weather_C_Get_UDS_Weather_Override_Bool Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Cloud_Coverage_0 != nullptr)
		*Cloud_Coverage_0 = Parms.Cloud_Coverage_0;

	if (Param_Fog_0 != nullptr)
		*Param_Fog_0 = Parms.Param_Fog_0;

	if (Param_Dust_0 != nullptr)
		*Param_Dust_0 = Parms.Param_Dust_0;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get UDW State for Saving
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FUDS_and_UDW_State*              UDW_State                                              (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Get_UDW_State_for_Saving(struct FUDS_and_UDW_State* UDW_State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get UDW State for Saving");

	Params::Ultra_Dynamic_Weather_C_Get_UDW_State_for_Saving Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (UDW_State != nullptr)
		*UDW_State = std::move(Parms.UDW_State);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get Weather Presets Used By Local Weather
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TMap<class UUDS_Weather_Settings_C*, double>*Sources                                                (Parm, OutParm)

void AUltra_Dynamic_Weather_C::Get_Weather_Presets_Used_By_Local_Weather(TMap<class UUDS_Weather_Settings_C*, double>* Sources)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get Weather Presets Used By Local Weather");

	Params::Ultra_Dynamic_Weather_C_Get_Weather_Presets_Used_By_Local_Weather Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Sources != nullptr)
		*Sources = std::move(Parms.Sources);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Get WOVs Applied to Location
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// TArray<class AWeather_Override_Volume_C*>&WOV_Array                                              (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// TMap<class AWeather_Override_Volume_C*, double>*Applied_WOV_Map                                        (Parm, OutParm)

void AUltra_Dynamic_Weather_C::Get_WOVs_Applied_to_Location(const struct FVector& Location, TArray<class AWeather_Override_Volume_C*>& WOV_Array, TMap<class AWeather_Override_Volume_C*, double>* Applied_WOV_Map)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Get WOVs Applied to Location");

	Params::Ultra_Dynamic_Weather_C_Get_WOVs_Applied_to_Location Parms{};

	Parms.Location = std::move(Location);
	Parms.WOV_Array = std::move(WOV_Array);

	UObject::ProcessEvent(Func, &Parms);

	WOV_Array = std::move(Parms.WOV_Array);

	if (Applied_WOV_Map != nullptr)
		*Applied_WOV_Map = std::move(Parms.Applied_WOV_Map);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Global Lightning Internal
// (Net, NetMulticast, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Angle                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Lightning_Threshold                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Global_Lightning_Internal(double Angle, double Lightning_Threshold)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Global Lightning Internal");

	Params::Ultra_Dynamic_Weather_C_Global_Lightning_Internal Parms{};

	Parms.Angle = Angle;
	Parms.Lightning_Threshold = Lightning_Threshold;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Hourly Season Update
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Hour                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Hourly_Season_Update(int32 Hour)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Hourly Season Update");

	Params::Ultra_Dynamic_Weather_C_Hourly_Season_Update Parms{};

	Parms.Hour = Hour;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C. Dust at Location
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Radius                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Speed                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Param_Dust_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Affect_Puddles                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Hit_Puddle                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Param_Dust_1                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::_Dust_at_Location(const struct FVector& Location, double Radius, double Speed, bool Param_Dust_0, bool Affect_Puddles, bool* Hit_Puddle, bool* Param_Dust_1)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", " Dust at Location");

	Params::Ultra_Dynamic_Weather_C__Dust_at_Location Parms{};

	Parms.Location = std::move(Location);
	Parms.Radius = Radius;
	Parms.Speed = Speed;
	Parms.Param_Dust_0 = Param_Dust_0;
	Parms.Affect_Puddles = Affect_Puddles;

	UObject::ProcessEvent(Func, &Parms);

	if (Hit_Puddle != nullptr)
		*Hit_Puddle = Parms.Hit_Puddle;

	if (Param_Dust_1 != nullptr)
		*Param_Dust_1 = Parms.Param_Dust_1;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Increment Global Material Effects
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Increment_Global_Material_Effects()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Increment Global Material Effects");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Increment Transition Between States
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Increment_Transition_Between_States()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Increment Transition Between States");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Increment Wind Direction Transition
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Increment_Wind_Direction_Transition()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Increment Wind Direction Transition");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Initialize Occlusion
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Initialize_Occlusion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Initialize Occlusion");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Initialize Random Weather Variation
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Initialize_Random_Weather_Variation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Initialize Random Weather Variation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Initialize Weather
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AUltra_Dynamic_Sky_C*             UDS                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Initialize_Weather(class AUltra_Dynamic_Sky_C* UDS)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Initialize Weather");

	Params::Ultra_Dynamic_Weather_C_Initialize_Weather Parms{};

	Parms.UDS = UDS;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Instant Sound Update
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Instant_Sound_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Instant Sound Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Invalidate VHFM Level
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Threshold                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Distance                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector&                         Buffer                                                 (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Current                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Continue                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Invalidate_VHFM_Level(double Threshold, double Distance, struct FVector& Buffer, const struct FVector& Current, bool* Continue)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Invalidate VHFM Level");

	Params::Ultra_Dynamic_Weather_C_Invalidate_VHFM_Level Parms{};

	Parms.Threshold = Threshold;
	Parms.Distance = Distance;
	Parms.Buffer = std::move(Buffer);
	Parms.Current = std::move(Current);

	UObject::ProcessEvent(Func, &Parms);

	Buffer = std::move(Parms.Buffer);

	if (Continue != nullptr)
		*Continue = Parms.Continue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Invalidate VHFM With Material States
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Invalidate_VHFM_With_Material_States()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Invalidate VHFM With Material States");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Latent Weather Mask Update
// (BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Latent_Weather_Mask_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Latent Weather Mask Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Latent WOV Target Update
// (BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Latent_WOV_Target_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Latent WOV Target Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Lerp Between Weather States
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*          A                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class UUDS_Weather_Settings_C*          B                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Alpha                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UUDS_Weather_Settings_C*          Target_State                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// bool                                    Set_Material_Effects                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Use_Bias_for_Material_Effects                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Lerp_Sources                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Lerp_Between_Weather_States(class UUDS_Weather_Settings_C* A, class UUDS_Weather_Settings_C* B, double Alpha, class UUDS_Weather_Settings_C* Target_State, bool Set_Material_Effects, bool Use_Bias_for_Material_Effects, bool Lerp_Sources)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Lerp Between Weather States");

	Params::Ultra_Dynamic_Weather_C_Lerp_Between_Weather_States Parms{};

	Parms.A = A;
	Parms.B = B;
	Parms.Alpha = Alpha;
	Parms.Target_State = Target_State;
	Parms.Set_Material_Effects = Set_Material_Effects;
	Parms.Use_Bias_for_Material_Effects = Use_Bias_for_Material_Effects;
	Parms.Lerp_Sources = Lerp_Sources;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Lerp State Source Maps
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*          A                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class UUDS_Weather_Settings_C*          B                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Alpha                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UUDS_Weather_Settings_C*          Target_Object                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Lerp_State_Source_Maps(class UUDS_Weather_Settings_C* A, class UUDS_Weather_Settings_C* B, double Alpha, class UUDS_Weather_Settings_C* Target_Object)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Lerp State Source Maps");

	Params::Ultra_Dynamic_Weather_C_Lerp_State_Source_Maps Parms{};

	Parms.A = A;
	Parms.B = B;
	Parms.Alpha = Alpha;
	Parms.Target_Object = Target_Object;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Lerp Yaw Angles
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Angle_A                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Angle_B                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Alpha                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Lerp_Yaw_Angles(double Angle_A, double Angle_B, double Alpha, double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Lerp Yaw Angles");

	Params::Ultra_Dynamic_Weather_C_Lerp_Yaw_Angles Parms{};

	Parms.Angle_A = Angle_A;
	Parms.Angle_B = Angle_B;
	Parms.Alpha = Alpha;

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Level Editor Lightning Flash Spawning
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Level_Editor_Lightning_Flash_Spawning()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Level Editor Lightning Flash Spawning");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Level Editor WOV Update
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Level_Editor_WOV_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Level Editor WOV Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Lightning Bolt Target Offset
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Lightning_Bolt_Target_Offset(struct FVector* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Lightning Bolt Target Offset");

	Params::Ultra_Dynamic_Weather_C_Lightning_Bolt_Target_Offset Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Lightning Distance Range
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Range                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Lightning_Distance_Range(double* Range)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Lightning Distance Range");

	Params::Ultra_Dynamic_Weather_C_Lightning_Distance_Range Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Range != nullptr)
		*Range = Parms.Range;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Lightning Flash Location
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   Found_Valid_Location                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector*                         Loc                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Lightning_Flash_Location(bool* Found_Valid_Location, struct FVector* Loc)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Lightning Flash Location");

	Params::Ultra_Dynamic_Weather_C_Lightning_Flash_Location Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Found_Valid_Location != nullptr)
		*Found_Valid_Location = Parms.Found_Valid_Location;

	if (Loc != nullptr)
		*Loc = std::move(Parms.Loc);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Lightning Flash Period
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Period                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Lightning_Flash_Period(double* Period)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Lightning Flash Period");

	Params::Ultra_Dynamic_Weather_C_Lightning_Flash_Period Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Period != nullptr)
		*Period = Parms.Period;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Night Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Night_Scale()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Night Scale");

	Params::Ultra_Dynamic_Weather_C_Night_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Lightning World Height
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Height                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Lightning_World_Height(double* Height)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Lightning World Height");

	Params::Ultra_Dynamic_Weather_C_Lightning_World_Height Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Height != nullptr)
		*Height = Parms.Height;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Load Required Assets
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Load_Required_Assets()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Load Required Assets");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Load Soft Object Array
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<TSoftObjectPtr<class UObject>>&  In                                                     (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void AUltra_Dynamic_Weather_C::Load_Soft_Object_Array(TArray<TSoftObjectPtr<class UObject>>& In)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Load Soft Object Array");

	Params::Ultra_Dynamic_Weather_C_Load_Soft_Object_Array Parms{};

	Parms.In = std::move(In);

	UObject::ProcessEvent(Func, &Parms);

	In = std::move(Parms.In);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Make Dust Component
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Make_Dust_Component()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Make Dust Component");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Make Lightning Flash Systems
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Make_Lightning_Flash_Systems()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Make Lightning Flash Systems");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Make Manual State
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Make_Manual_State()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Make Manual State");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Make Obscured Lightning Component
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Make_Obscured_Lightning_Component()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Make Obscured Lightning Component");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Make Outdoor Sound Mix
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Make_Outdoor_Sound_Mix()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Make Outdoor Sound Mix");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Make Rain Component
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Make_Rain_Component()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Make Rain Component");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Make Random Stream
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Make_Random_Stream()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Make Random Stream");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Make Snow Component
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Make_Snow_Component()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Make Snow Component");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Make Wind Debris Component
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Make_Wind_Debris_Component()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Make Wind Debris Component");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Manual Override to State Value
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*          Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// int32                                   Index_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Manual_Override_to_State_Value(class UUDS_Weather_Settings_C* Target, int32 Index_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Manual Override to State Value");

	Params::Ultra_Dynamic_Weather_C_Manual_Override_to_State_Value Parms{};

	Parms.Target = Target;
	Parms.Index_0 = Index_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Material Effect Draw Color from State
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UUDS_Weather_Settings_C*          State                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Alpha                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Color                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Material_Effect_Draw_Color_from_State(class UUDS_Weather_Settings_C* State, double Alpha, struct FLinearColor* Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Material Effect Draw Color from State");

	Params::Ultra_Dynamic_Weather_C_Material_Effect_Draw_Color_from_State Parms{};

	Parms.State = State;
	Parms.Alpha = Alpha;

	UObject::ProcessEvent(Func, &Parms);

	if (Color != nullptr)
		*Color = std::move(Parms.Color);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Monitor Local Weather Changes
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Monitor_Local_Weather_Changes()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Monitor Local Weather Changes");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Obscured Lightning Current Spawn Rate
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Obscured_Lightning_Current_Spawn_Rate()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Obscured Lightning Current Spawn Rate");

	Params::Ultra_Dynamic_Weather_C_Obscured_Lightning_Current_Spawn_Rate Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Obscured Lightning System Finished
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UNiagaraComponent*                PSystem                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Obscured_Lightning_System_Finished(class UNiagaraComponent* PSystem)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Obscured Lightning System Finished");

	Params::Ultra_Dynamic_Weather_C_Obscured_Lightning_System_Finished Parms{};

	Parms.PSystem = PSystem;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Open Editor Readme Entry
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FString&                    Entry_Row                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Open_Editor_Readme_Entry(const class FString& Entry_Row)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Open Editor Readme Entry");

	Params::Ultra_Dynamic_Weather_C_Open_Editor_Readme_Entry Parms{};

	Parms.Entry_Row = std::move(Entry_Row);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Open Editor Readme Entry Set
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const TArray<class FName>&              Entries                                                (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void AUltra_Dynamic_Weather_C::Open_Editor_Readme_Entry_Set(const TArray<class FName>& Entries)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Open Editor Readme Entry Set");

	Params::Ultra_Dynamic_Weather_C_Open_Editor_Readme_Entry_Set Parms{};

	Parms.Entries = std::move(Entries);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Populate Weather State
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*          State                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Cloud_Coverage_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Rain_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Snow_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Param_Lightning_0                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Wind_Intensity_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Param_Fog_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Param_Dust_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Material_Wetness_0                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Material_Snow                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Material_Dust                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Populate_Weather_State(class UUDS_Weather_Settings_C* State, double Cloud_Coverage_0, double Rain_0, double Snow_0, double Param_Lightning_0, double Wind_Intensity_0, double Param_Fog_0, double Param_Dust_0, double Material_Wetness_0, double Material_Snow, double Material_Dust)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Populate Weather State");

	Params::Ultra_Dynamic_Weather_C_Populate_Weather_State Parms{};

	Parms.State = State;
	Parms.Cloud_Coverage_0 = Cloud_Coverage_0;
	Parms.Rain_0 = Rain_0;
	Parms.Snow_0 = Snow_0;
	Parms.Param_Lightning_0 = Param_Lightning_0;
	Parms.Wind_Intensity_0 = Wind_Intensity_0;
	Parms.Param_Fog_0 = Param_Fog_0;
	Parms.Param_Dust_0 = Param_Dust_0;
	Parms.Material_Wetness_0 = Material_Wetness_0;
	Parms.Material_Snow = Material_Snow;
	Parms.Material_Dust = Material_Dust;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.PPWF Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::PPWF_Intensity(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "PPWF Intensity");

	Params::Ultra_Dynamic_Weather_C_PPWF_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Query Project Settings
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Query_Project_Settings()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Query Project Settings");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Rain Niagara System Asset
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TSoftObjectPtr<class UNiagaraSystem>*   Out                                                    (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Rain_Niagara_System_Asset(TSoftObjectPtr<class UNiagaraSystem>* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Rain Niagara System Asset");

	Params::Ultra_Dynamic_Weather_C_Rain_Niagara_System_Asset Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Rain Spawn Rate
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Rain_Spawn_Rate()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Rain Spawn Rate");

	Params::Ultra_Dynamic_Weather_C_Rain_Spawn_Rate Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Rain System Finished
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UNiagaraComponent*                PSystem                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Rain_System_Finished(class UNiagaraComponent* PSystem)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Rain System Finished");

	Params::Ultra_Dynamic_Weather_C_Rain_System_Finished Parms{};

	Parms.PSystem = PSystem;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Rainbow Strength
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Rainbow_Strength()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Rainbow Strength");

	Params::Ultra_Dynamic_Weather_C_Rainbow_Strength Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Randomize Screen Frost Offset
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Randomize_Screen_Frost_Offset()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Randomize Screen Frost Offset");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.ReceiveBeginPlay
// (Event, Protected, BlueprintEvent)

void AUltra_Dynamic_Weather_C::ReceiveBeginPlay()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "ReceiveBeginPlay");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.ReceiveEndPlay
// (Event, Protected, BlueprintEvent)
// Parameters:
// EEndPlayReason                          EndPlayReason                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::ReceiveEndPlay(EEndPlayReason EndPlayReason)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "ReceiveEndPlay");

	Params::Ultra_Dynamic_Weather_C_ReceiveEndPlay Parms{};

	Parms.EndPlayReason = EndPlayReason;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Recenter DLWE Render Target
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Recenter_DLWE_Render_Target()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Recenter DLWE Render Target");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Report Removal Of Mask Component
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWeatherMask_C*                   Component                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Report_Removal_Of_Mask_Component(class UWeatherMask_C* Component)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Report Removal Of Mask Component");

	Params::Ultra_Dynamic_Weather_C_Report_Removal_Of_Mask_Component Parms{};

	Parms.Component = Component;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Report Removed Radial Storm
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AActor*                           Storm                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// bool*                                   Success                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Report_Removed_Radial_Storm(class AActor* Storm, bool* Success)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Report Removed Radial Storm");

	Params::Ultra_Dynamic_Weather_C_Report_Removed_Radial_Storm Parms{};

	Parms.Storm = Storm;

	UObject::ProcessEvent(Func, &Parms);

	if (Success != nullptr)
		*Success = Parms.Success;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Restart Random Weather Variation
// (Net, NetReliable, NetServer, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Restart_Random_Weather_Variation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Restart Random Weather Variation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Runtime Or Initializing
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool AUltra_Dynamic_Weather_C::Runtime_Or_Initializing()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Runtime Or Initializing");

	Params::Ultra_Dynamic_Weather_C_Runtime_Or_Initializing Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Screen Droplets Periodic Updates
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Screen_Droplets_Periodic_Updates()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Screen Droplets Periodic Updates");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Screen Frost Startup
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Screen_Frost_Startup()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Screen Frost Startup");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Second Frame Startup Functions
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Second_Frame_Startup_Functions()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Second Frame Startup Functions");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set All Update Checks
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    On                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Set_All_Update_Checks(bool On)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set All Update Checks");

	Params::Ultra_Dynamic_Weather_C_Set_All_Update_Checks Parms{};

	Parms.On = On;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set Current Control Point Location
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Set_Current_Control_Point_Location()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set Current Control Point Location");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set Current Global Weather State
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Set_Current_Global_Weather_State()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set Current Global Weather State");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Dust Parameters
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Dust_Parameters()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Dust Parameters");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set Random Time Offset
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Set_Random_Time_Offset()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set Random Time Offset");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set Replicated Old Weather State
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Set_Replicated_Old_Weather_State()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set Replicated Old Weather State");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set Season
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Season_0                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Set_Season(double Season_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set Season");

	Params::Ultra_Dynamic_Weather_C_Set_Season Parms{};

	Parms.Season_0 = Season_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set Shared Weather Particle Parameters
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UNiagaraComponent*                System                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Set_Shared_Weather_Particle_Parameters(class UNiagaraComponent* System)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set Shared Weather Particle Parameters");

	Params::Ultra_Dynamic_Weather_C_Set_Shared_Weather_Particle_Parameters Parms{};

	Parms.System = System;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set Startup Variables
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Set_Startup_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set Startup Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set UDS Reference
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Set_UDS_Reference()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set UDS Reference");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set Weather Values Prepped for UDS
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Set_Weather_Values_Prepped_for_UDS()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set Weather Values Prepped for UDS");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Set WOV Render Target Mapping
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Set_WOV_Render_Target_Mapping()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Set WOV Render Target Mapping");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Sky Cloud Speed
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Sky_Cloud_Speed()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Sky Cloud Speed");

	Params::Ultra_Dynamic_Weather_C_Sky_Cloud_Speed Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C. Dust Velocity Randomization
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Low_Wind                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  High_Wind                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::_Dust_Velocity_Randomization(double Low_Wind, double High_Wind)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", " Dust Velocity Randomization");

	Params::Ultra_Dynamic_Weather_C__Dust_Velocity_Randomization Parms{};

	Parms.Low_Wind = Low_Wind;
	Parms.High_Wind = High_Wind;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Snow Niagara System Asset
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TSoftObjectPtr<class UNiagaraSystem>*   Out                                                    (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Snow_Niagara_System_Asset(TSoftObjectPtr<class UNiagaraSystem>* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Snow Niagara System Asset");

	Params::Ultra_Dynamic_Weather_C_Snow_Niagara_System_Asset Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Snow Spawn Rate
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Snow_Spawn_Rate()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Snow Spawn Rate");

	Params::Ultra_Dynamic_Weather_C_Snow_Spawn_Rate Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Snow System Finished
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UNiagaraComponent*                PSystem                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Snow_System_Finished(class UNiagaraComponent* PSystem)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Snow System Finished");

	Params::Ultra_Dynamic_Weather_C_Snow_System_Finished Parms{};

	Parms.PSystem = PSystem;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Sort Weather Override Volumes
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Sort_Weather_Override_Volumes()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Sort Weather Override Volumes");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Sparse Movement Updates
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Sparse_Movement_Updates()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Sparse Movement Updates");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Start Active Timers
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Start_Active_Timers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Start Active Timers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Start Lightning Flash
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Start_Lightning_Flash()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Start Lightning Flash");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Start Up DLWE Interaction System
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Start_Up_DLWE_Interaction_System()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Start Up DLWE Interaction System");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Start Up Render Targets
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Start_Up_Render_Targets()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Start Up Render Targets");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Startup Static Mode
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Startup_Static_Mode()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Startup Static Mode");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Mode Tick
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Mode_Tick()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Mode Tick");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - DLWE
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___DLWE()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - DLWE");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Dust
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Dust()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Dust");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Heat Distortion
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Heat_Distortion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Heat Distortion");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Lightning
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Lightning()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Lightning");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Material Effects
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Material_Effects()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Material Effects");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Occlusion
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Occlusion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Occlusion");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Post Process Wind Fog
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Post_Process_Wind_Fog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Post Process Wind Fog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Post Processing
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Post_Processing()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Post Processing");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Rain
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Rain()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Rain");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Rainbow
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Rainbow()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Rainbow");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Screen Droplets
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Screen_Droplets()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Screen Droplets");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Screen Frost
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Screen_Frost()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Screen Frost");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Shared Particles
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Shared_Particles()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Shared Particles");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Snow
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Snow()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Snow");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Sound Effects
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Sound_Effects()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Sound Effects");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Static Properties - Wind Debris
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Static_Properties___Wind_Debris()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Static Properties - Wind Debris");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Target Screen Frost Strength
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Target_Screen_Frost_Strength()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Target Screen Frost Strength");

	Params::Ultra_Dynamic_Weather_C_Target_Screen_Frost_Strength Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Test Actor for Weather Exposure
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AActor*                           Actor                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// bool                                    Test_Colliding_Components_Only                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class USceneComponent*                  Custom_Component_for_Bounds                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// double*                                 Rain_Exposure                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Snow_Exposure                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Wind_Exposure                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Dust_Exposure                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Test_Actor_for_Weather_Exposure(class AActor* Actor, bool Test_Colliding_Components_Only, class USceneComponent* Custom_Component_for_Bounds, double* Rain_Exposure, double* Snow_Exposure, double* Wind_Exposure, double* Dust_Exposure)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Test Actor for Weather Exposure");

	Params::Ultra_Dynamic_Weather_C_Test_Actor_for_Weather_Exposure Parms{};

	Parms.Actor = Actor;
	Parms.Test_Colliding_Components_Only = Test_Colliding_Components_Only;
	Parms.Custom_Component_for_Bounds = Custom_Component_for_Bounds;

	UObject::ProcessEvent(Func, &Parms);

	if (Rain_Exposure != nullptr)
		*Rain_Exposure = Parms.Rain_Exposure;

	if (Snow_Exposure != nullptr)
		*Snow_Exposure = Parms.Snow_Exposure;

	if (Wind_Exposure != nullptr)
		*Wind_Exposure = Parms.Wind_Exposure;

	if (Dust_Exposure != nullptr)
		*Dust_Exposure = Parms.Dust_Exposure;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Test Component for Wind Exposure
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UPrimitiveComponent*              Component                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// double*                                 Wind_Exposure                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Test_Component_for_Wind_Exposure(class UPrimitiveComponent* Component, double* Wind_Exposure)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Test Component for Wind Exposure");

	Params::Ultra_Dynamic_Weather_C_Test_Component_for_Wind_Exposure Parms{};

	Parms.Component = Component;

	UObject::ProcessEvent(Func, &Parms);

	if (Wind_Exposure != nullptr)
		*Wind_Exposure = Parms.Wind_Exposure;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Tick Function
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Tick_Function()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Tick Function");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Toggle Post Process Material
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Index_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Enabled                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Toggle_Post_Process_Material(int32 Index_0, bool Enabled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Toggle Post Process Material");

	Params::Ultra_Dynamic_Weather_C_Toggle_Post_Process_Material Parms{};

	Parms.Index_0 = Index_0;
	Parms.Enabled = Enabled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Trace Bounds for Exposure Values
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Bounds_Origin                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Bounds_Extent                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// TArray<class AActor*>&                  Actors_to_Ignore                                       (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// bool                                    Test_Weather                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Exposure_Value                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Trace_Bounds_for_Exposure_Values(const struct FVector& Bounds_Origin, const struct FVector& Bounds_Extent, TArray<class AActor*>& Actors_to_Ignore, bool Test_Weather, double* Exposure_Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Trace Bounds for Exposure Values");

	Params::Ultra_Dynamic_Weather_C_Trace_Bounds_for_Exposure_Values Parms{};

	Parms.Bounds_Origin = std::move(Bounds_Origin);
	Parms.Bounds_Extent = std::move(Bounds_Extent);
	Parms.Actors_to_Ignore = std::move(Actors_to_Ignore);
	Parms.Test_Weather = Test_Weather;

	UObject::ProcessEvent(Func, &Parms);

	Actors_to_Ignore = std::move(Parms.Actors_to_Ignore);

	if (Exposure_Value != nullptr)
		*Exposure_Value = Parms.Exposure_Value;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.UDS Reconstruct
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   Success                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::UDS_Reconstruct(bool* Success)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "UDS Reconstruct");

	Params::Ultra_Dynamic_Weather_C_UDS_Reconstruct Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Success != nullptr)
		*Success = Parms.Success;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.UDS Weather Variable Overrides
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Override_Clouds                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Cloud_Coverage_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Override_Fog                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Param_Fog_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Override_Dust                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Param_Dust_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Success                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::UDS_Weather_Variable_Overrides(bool Override_Clouds, double Cloud_Coverage_0, bool Override_Fog, double Param_Fog_0, bool Override_Dust, double Param_Dust_0, bool* Success)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "UDS Weather Variable Overrides");

	Params::Ultra_Dynamic_Weather_C_UDS_Weather_Variable_Overrides Parms{};

	Parms.Override_Clouds = Override_Clouds;
	Parms.Cloud_Coverage_0 = Cloud_Coverage_0;
	Parms.Override_Fog = Override_Fog;
	Parms.Param_Fog_0 = Param_Fog_0;
	Parms.Override_Dust = Override_Dust;
	Parms.Param_Dust_0 = Param_Dust_0;

	UObject::ProcessEvent(Func, &Parms);

	if (Success != nullptr)
		*Success = Parms.Success;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.UDW Runtime Tick
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Delta_Time                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::UDW_Runtime_Tick(double Delta_Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "UDW Runtime Tick");

	Params::Ultra_Dynamic_Weather_C_UDW_Runtime_Tick Parms{};

	Parms.Delta_Time = Delta_Time;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.UDW State Apply
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FUDS_and_UDW_State&        State                                                  (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
// bool*                                   Completed                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::UDW_State_Apply(const struct FUDS_and_UDW_State& State, bool* Completed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "UDW State Apply");

	Params::Ultra_Dynamic_Weather_C_UDW_State_Apply Parms{};

	Parms.State = std::move(State);

	UObject::ProcessEvent(Func, &Parms);

	if (Completed != nullptr)
		*Completed = Parms.Completed;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Active Dust Parameters
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Active_Dust_Parameters()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Active Dust Parameters");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Active Rain Parameters
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Active_Rain_Parameters()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Active Rain Parameters");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Active Snow Parameters
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Active_Snow_Parameters()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Active Snow Parameters");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Active Variables
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Active_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Active Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Active Wind Debris Parameters
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Active_Wind_Debris_Parameters()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Active Wind Debris Parameters");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Current Global And Local Weather State
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Current_Global_And_Local_Weather_State()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Current Global And Local Weather State");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Custom Weather Particle Camera
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Custom_Weather_Particle_Camera()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Custom Weather Particle Camera");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update DLWE Interaction Mode
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_DLWE_Interaction_Mode()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update DLWE Interaction Mode");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update DLWE Snow Compressions
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_DLWE_Snow_Compressions()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update DLWE Snow Compressions");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Fog Particle Parameters
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UFXSystemComponent*               Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// double                                  Max_Particle_Percentage                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Weather_Intensity                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Particle_Intensity                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Update_Fog_Particle_Parameters(class UFXSystemComponent* Target, double Max_Particle_Percentage, double Weather_Intensity, double Particle_Intensity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Fog Particle Parameters");

	Params::Ultra_Dynamic_Weather_C_Update_Fog_Particle_Parameters Parms{};

	Parms.Target = Target;
	Parms.Max_Particle_Percentage = Max_Particle_Percentage;
	Parms.Weather_Intensity = Weather_Intensity;
	Parms.Particle_Intensity = Particle_Intensity;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Heat Distortion
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Heat_Distortion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Heat Distortion");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Lightning Flash Light
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Lightning_Flash_Light()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Lightning Flash Light");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Material Effect Parameters
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Material_Effect_Parameters()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Material Effect Parameters");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Obscured Lightning Parameters
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Obscured_Lightning_Parameters()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Obscured Lightning Parameters");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Old State With Replicated Variables
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Old_State_With_Replicated_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Old State With Replicated Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Outdoor Sound Mix
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Outdoor_Sound_Mix()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Outdoor Sound Mix");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Post Process Wind Fog
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Post_Process_Wind_Fog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Post Process Wind Fog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Rainbow
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Rainbow()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Rainbow");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Screen Droplets
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Screen_Droplets()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Screen Droplets");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Screen Frost
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Screen_Frost()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Screen Frost");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Season
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Season()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Season");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Sound Occlusion Parameters
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Sound_Occlusion_Parameters()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Sound Occlusion Parameters");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Sounds With Weather State
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Sounds_With_Weather_State()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Sounds With Weather State");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Static Variables
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Static_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Static Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Target Screen Frost
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Target_Screen_Frost()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Target Screen Frost");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Underwater State
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Underwater_State()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Underwater State");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Weather Mask Target
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Weather_Mask_Target()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Weather Mask Target");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update Wind Directional Source Actor
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_Wind_Directional_Source_Actor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update Wind Directional Source Actor");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Update WOV Render Target
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Update_WOV_Render_Target()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Update WOV Render Target");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.UserConstructionScript
// (Event, Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::UserConstructionScript()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "UserConstructionScript");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Version Specific Correction
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Version_Specific_Correction()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Version Specific Correction");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Warm Up Niagara Systems
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Warm_Up_Niagara_Systems()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Warm Up Niagara Systems");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Weather Startup Functions
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::Weather_Startup_Functions()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Weather Startup Functions");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Weather State Object to Structure
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UUDS_Weather_Settings_C*          State                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// struct FUDW_WeatherState_Structure*     Structure                                              (Parm, OutParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Weather_State_Object_to_Structure(class UUDS_Weather_Settings_C* State, struct FUDW_WeatherState_Structure* Structure)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Weather State Object to Structure");

	Params::Ultra_Dynamic_Weather_C_Weather_State_Object_to_Structure Parms{};

	Parms.State = State;

	UObject::ProcessEvent(Func, &Parms);

	if (Structure != nullptr)
		*Structure = std::move(Parms.Structure);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Wind Debris Niagara System Asset
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TSoftObjectPtr<class UNiagaraSystem>*   Out                                                    (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Wind_Debris_Niagara_System_Asset(TSoftObjectPtr<class UNiagaraSystem>* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Wind Debris Niagara System Asset");

	Params::Ultra_Dynamic_Weather_C_Wind_Debris_Niagara_System_Asset Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Wind Debris Spawn Rate
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Weather_C::Wind_Debris_Spawn_Rate()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Wind Debris Spawn Rate");

	Params::Ultra_Dynamic_Weather_C_Wind_Debris_Spawn_Rate Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Wind Debris System Finished
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UNiagaraComponent*                PSystem                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Weather_C::Wind_Debris_System_Finished(class UNiagaraComponent* PSystem)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Wind Debris System Finished");

	Params::Ultra_Dynamic_Weather_C_Wind_Debris_System_Finished Parms{};

	Parms.PSystem = PSystem;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Wind Force Vector
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FVector AUltra_Dynamic_Weather_C::Wind_Force_Vector()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Wind Force Vector");

	Params::Ultra_Dynamic_Weather_C_Wind_Force_Vector Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.Wind Rotation
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FRotator*                        Rot                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)

void AUltra_Dynamic_Weather_C::Wind_Rotation(struct FRotator* Rot)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "Wind Rotation");

	Params::Ultra_Dynamic_Weather_C_Wind_Rotation Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Rot != nullptr)
		*Rot = std::move(Parms.Rot);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Dust Particles
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Dust_Particles()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Dust Particles");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Dynamic Landscape Weather Effects
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Dynamic_Landscape_Weather_Effects()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Dynamic Landscape Weather Effects");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Event Dispatchers
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Event_Dispatchers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Event Dispatchers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Heat Distortion
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Heat_Distortion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Heat Distortion");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Lightning
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Lightning()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Lightning");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Manual Weather State
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Manual_Weather_State()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Manual Weather State");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Material Effects
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Material_Effects()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Material Effects");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Post Process Wind Fog
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Post_Process_Wind_Fog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Post Process Wind Fog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Rain Particles
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Rain_Particles()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Rain Particles");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Rainbow
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Rainbow()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Rainbow");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Random Weather Variation
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Random_Weather_Variation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Random Weather Variation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Screen Droplets
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Screen_Droplets()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Screen Droplets");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Screen Frost
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Screen_Frost()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Screen Frost");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Season
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Season()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Season");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Snow Particles
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Snow_Particles()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Snow Particles");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Sound Effects
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Sound_Effects()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Sound Effects");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Sound Occlusion
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Sound_Occlusion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Sound Occlusion");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Temperature
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Temperature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Temperature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Volumetric Fog Particles
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Volumetric_Fog_Particles()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Volumetric Fog Particles");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Water Level
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Water_Level()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Water Level");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Weather Above Volumetric Clouds
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Weather_Above_Volumetric_Clouds()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Weather Above Volumetric Clouds");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Weather Documentation
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Weather_Documentation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Weather Documentation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Weather Mask Tools
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Weather_Mask_Tools()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Weather Mask Tools");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Weather Override Volumes
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Weather_Override_Volumes()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Weather Override Volumes");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Weather Particles
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Weather_Particles()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Weather Particles");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Wind Debris
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Wind_Debris()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Wind Debris");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Wind Direction
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Wind_Direction()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Wind Direction");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C.📘 Wind Directional Source
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Weather_C::__Wind_Directional_Source()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Weather_C", "📘 Wind Directional Source");

	UObject::ProcessEvent(Func, nullptr);
}

}

