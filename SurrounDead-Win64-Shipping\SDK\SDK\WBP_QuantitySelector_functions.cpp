﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_QuantitySelector

#include "Basic.hpp"

#include "WBP_QuantitySelector_classes.hpp"
#include "WBP_QuantitySelector_parameters.hpp"


namespace SDK
{

// Function WBP_QuantitySelector.WBP_QuantitySelector_C.BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// float                                   Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_QuantitySelector_C::BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature(float Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_QuantitySelector_C", "BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature");

	Params::WBP_QuantitySelector_C_BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_QuantitySelector.WBP_QuantitySelector_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWBP_QuantitySelector_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_QuantitySelector_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WBP_QuantitySelector.WBP_QuantitySelector_C.ExecuteUbergraph_WBP_QuantitySelector
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_QuantitySelector_C::ExecuteUbergraph_WBP_QuantitySelector(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_QuantitySelector_C", "ExecuteUbergraph_WBP_QuantitySelector");

	Params::WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_QuantitySelector.WBP_QuantitySelector_C.Initialize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   MinAmount_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   MaxAmount_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const class FText&                      InstructionText_0                                      (BlueprintVisible, BlueprintReadOnly, Parm)

void UWBP_QuantitySelector_C::Initialize(int32 MinAmount_0, int32 MaxAmount_0, const class FText& InstructionText_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_QuantitySelector_C", "Initialize");

	Params::WBP_QuantitySelector_C_Initialize Parms{};

	Parms.MinAmount_0 = MinAmount_0;
	Parms.MaxAmount_0 = MaxAmount_0;
	Parms.InstructionText_0 = std::move(InstructionText_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_QuantitySelector.WBP_QuantitySelector_C.OnConfirm
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class FName                             ActionName                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_QuantitySelector_C::OnConfirm(class FName ActionName)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_QuantitySelector_C", "OnConfirm");

	Params::WBP_QuantitySelector_C_OnConfirm Parms{};

	Parms.ActionName = ActionName;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_QuantitySelector.WBP_QuantitySelector_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_QuantitySelector_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_QuantitySelector_C", "Tick");

	Params::WBP_QuantitySelector_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_QuantitySelector.WBP_QuantitySelector_C.Update Drop Amount
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  A                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_QuantitySelector_C::Update_Drop_Amount(double A)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_QuantitySelector_C", "Update Drop Amount");

	Params::WBP_QuantitySelector_C_Update_Drop_Amount Parms{};

	Parms.A = A;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_QuantitySelector.WBP_QuantitySelector_C.RegisterActions
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWBP_QuantitySelector_C::RegisterActions()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_QuantitySelector_C", "RegisterActions");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WBP_QuantitySelector.WBP_QuantitySelector_C.BP_GetDesiredFocusTarget
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent, Const)
// Parameters:
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)

class UWidget* UWBP_QuantitySelector_C::BP_GetDesiredFocusTarget() const
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_QuantitySelector_C", "BP_GetDesiredFocusTarget");

	Params::WBP_QuantitySelector_C_BP_GetDesiredFocusTarget Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

