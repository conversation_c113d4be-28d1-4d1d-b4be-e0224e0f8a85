﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Effect

#include "Basic.hpp"

#include "WB_Effect_classes.hpp"
#include "WB_Effect_parameters.hpp"


namespace SDK
{

// Function WB_Effect.WB_Effect_C.AddAttributes
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           Image                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::AddAttributes(class UImage* Image)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "AddAttributes");

	Params::WB_Effect_C_AddAttributes Parms{};

	Parms.Image = Image;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.AddEffect
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FS_Effects&                Effect_0                                               (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::AddEffect(const struct FS_Effects& Effect_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "AddEffect");

	Params::WB_Effect_C_AddEffect Parms{};

	Parms.Effect_0 = std::move(Effect_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.AddEffectTextureType
// (BlueprintCallable, BlueprintEvent)

void UWB_Effect_C::AddEffectTextureType()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "AddEffectTextureType");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Effect.WB_Effect_C.Anim_Fade
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    FadeIn_0                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  PlaybackSpeed                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::Anim_Fade(bool FadeIn_0, double PlaybackSpeed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "Anim_Fade");

	Params::WB_Effect_C_Anim_Fade Parms{};

	Parms.FadeIn_0 = FadeIn_0;
	Parms.PlaybackSpeed = PlaybackSpeed;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.Anim_Highlight1
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  PlaybackSpeed                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::Anim_Highlight1(double PlaybackSpeed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "Anim_Highlight1");

	Params::WB_Effect_C_Anim_Highlight1 Parms{};

	Parms.PlaybackSpeed = PlaybackSpeed;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.Anim_Highlight2
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  PlaybackSpeed                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::Anim_Highlight2(double PlaybackSpeed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "Anim_Highlight2");

	Params::WB_Effect_C_Anim_Highlight2 Parms{};

	Parms.PlaybackSpeed = PlaybackSpeed;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.Anim_HighlightLoop
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    StartStop                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  PlaybackSpeed                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::Anim_HighlightLoop(bool StartStop, double PlaybackSpeed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "Anim_HighlightLoop");

	Params::WB_Effect_C_Anim_HighlightLoop Parms{};

	Parms.StartStop = StartStop;
	Parms.PlaybackSpeed = PlaybackSpeed;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.ClearParticles
// (BlueprintCallable, BlueprintEvent)

void UWB_Effect_C::ClearParticles()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "ClearParticles");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Effect.WB_Effect_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWB_Effect_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Effect.WB_Effect_C.CreateParticle
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   NumParticles                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::CreateParticle(int32 NumParticles)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "CreateParticle");

	Params::WB_Effect_C_CreateParticle Parms{};

	Parms.NumParticles = NumParticles;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.EventPreConstruct
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::EventPreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "EventPreConstruct");

	Params::WB_Effect_C_EventPreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.ExecuteUbergraph_WB_Effect
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::ExecuteUbergraph_WB_Effect(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "ExecuteUbergraph_WB_Effect");

	Params::WB_Effect_C_ExecuteUbergraph_WB_Effect Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.FindMinSize
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// const struct FVector2D&                 Size_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double UWB_Effect_C::FindMinSize(const struct FVector2D& Size_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "FindMinSize");

	Params::WB_Effect_C_FindMinSize Parms{};

	Parms.Size_0 = std::move(Size_0);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Effect.WB_Effect_C.GetAddRotationToParticles
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool*                                   Add_Rotation_to_Particles                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetAddRotationToParticles(bool* Add_Rotation_to_Particles)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetAddRotationToParticles");

	Params::WB_Effect_C_GetAddRotationToParticles Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Add_Rotation_to_Particles != nullptr)
		*Add_Rotation_to_Particles = Parms.Add_Rotation_to_Particles;
}


// Function WB_Effect.WB_Effect_C.GetDecayTime
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Decay_Time                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetDecayTime(double* Decay_Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetDecayTime");

	Params::WB_Effect_C_GetDecayTime Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Decay_Time != nullptr)
		*Decay_Time = Parms.Decay_Time;
}


// Function WB_Effect.WB_Effect_C.GetEffectAngle
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Effect_Angle                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetEffectAngle(double* Effect_Angle)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetEffectAngle");

	Params::WB_Effect_C_GetEffectAngle Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Effect_Angle != nullptr)
		*Effect_Angle = Parms.Effect_Angle;
}


// Function WB_Effect.WB_Effect_C.GetEffectColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Effect_Color                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetEffectColor(struct FLinearColor* Effect_Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetEffectColor");

	Params::WB_Effect_C_GetEffectColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Effect_Color != nullptr)
		*Effect_Color = std::move(Parms.Effect_Color);
}


// Function WB_Effect.WB_Effect_C.GetEffectScale
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector2D*                       EffectScale                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetEffectScale(struct FVector2D* EffectScale)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetEffectScale");

	Params::WB_Effect_C_GetEffectScale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (EffectScale != nullptr)
		*EffectScale = std::move(Parms.EffectScale);
}


// Function WB_Effect.WB_Effect_C.GetEffectShear
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector2D*                       Effect_Shear                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetEffectShear(struct FVector2D* Effect_Shear)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetEffectShear");

	Params::WB_Effect_C_GetEffectShear Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Effect_Shear != nullptr)
		*Effect_Shear = std::move(Parms.Effect_Shear);
}


// Function WB_Effect.WB_Effect_C.GetEffectTexture
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject**                         Custom_Effect_Texture                                  (Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetEffectTexture(class UObject** Custom_Effect_Texture)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetEffectTexture");

	Params::WB_Effect_C_GetEffectTexture Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Custom_Effect_Texture != nullptr)
		*Custom_Effect_Texture = Parms.Custom_Effect_Texture;
}


// Function WB_Effect.WB_Effect_C.GetEffectTranslation
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector2D*                       Transition                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetEffectTranslation(struct FVector2D* Transition)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetEffectTranslation");

	Params::WB_Effect_C_GetEffectTranslation Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Transition != nullptr)
		*Transition = std::move(Parms.Transition);
}


// Function WB_Effect.WB_Effect_C.GetEffectType
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// EEffectType*                            Effect_Texture_Type                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetEffectType(EEffectType* Effect_Texture_Type)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetEffectType");

	Params::WB_Effect_C_GetEffectType Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Effect_Texture_Type != nullptr)
		*Effect_Texture_Type = Parms.Effect_Texture_Type;
}


// Function WB_Effect.WB_Effect_C.GetNumParticles
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32*                                  NumParticles                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetNumParticles(int32* NumParticles)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetNumParticles");

	Params::WB_Effect_C_GetNumParticles Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (NumParticles != nullptr)
		*NumParticles = Parms.NumParticles;
}


// Function WB_Effect.WB_Effect_C.GetParticleSpread
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Particle_Spread                                        (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetParticleSpread(double* Particle_Spread)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetParticleSpread");

	Params::WB_Effect_C_GetParticleSpread Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Particle_Spread != nullptr)
		*Particle_Spread = Parms.Particle_Spread;
}


// Function WB_Effect.WB_Effect_C.GetPlaybackSpeed
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Playback_Speed                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetPlaybackSpeed(double* Playback_Speed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetPlaybackSpeed");

	Params::WB_Effect_C_GetPlaybackSpeed Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Playback_Speed != nullptr)
		*Playback_Speed = Parms.Playback_Speed;
}


// Function WB_Effect.WB_Effect_C.GetProgressBar
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UProgressBarLinear_C*             ProgressBar                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetProgressBar(class UProgressBarLinear_C* ProgressBar)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetProgressBar");

	Params::WB_Effect_C_GetProgressBar Parms{};

	Parms.ProgressBar = ProgressBar;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.GetSoundEffect
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class USoundBase**                      Sound_Effect                                           (Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetSoundEffect(class USoundBase** Sound_Effect)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetSoundEffect");

	Params::WB_Effect_C_GetSoundEffect Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Sound_Effect != nullptr)
		*Sound_Effect = Parms.Sound_Effect;
}


// Function WB_Effect.WB_Effect_C.GetSoundVolume
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Sound_Volume_Multiplier                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetSoundVolume(double* Sound_Volume_Multiplier)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetSoundVolume");

	Params::WB_Effect_C_GetSoundVolume Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Sound_Volume_Multiplier != nullptr)
		*Sound_Volume_Multiplier = Parms.Sound_Volume_Multiplier;
}


// Function WB_Effect.WB_Effect_C.GetSpecificPercentValue
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Specific_Percent_Value                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetSpecificPercentValue(double* Specific_Percent_Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetSpecificPercentValue");

	Params::WB_Effect_C_GetSpecificPercentValue Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Specific_Percent_Value != nullptr)
		*Specific_Percent_Value = Parms.Specific_Percent_Value;
}


// Function WB_Effect.WB_Effect_C.GetTriggerMethod
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// ETriggerMethod*                         Trigger                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::GetTriggerMethod(ETriggerMethod* Trigger)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "GetTriggerMethod");

	Params::WB_Effect_C_GetTriggerMethod Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Trigger != nullptr)
		*Trigger = Parms.Trigger;
}


// Function WB_Effect.WB_Effect_C.IsEffectTypeParticle
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Effect_C::IsEffectTypeParticle()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "IsEffectTypeParticle");

	Params::WB_Effect_C_IsEffectTypeParticle Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Effect.WB_Effect_C.IsEffectTypeProgressChangeColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Effect_C::IsEffectTypeProgressChangeColor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "IsEffectTypeProgressChangeColor");

	Params::WB_Effect_C_IsEffectTypeProgressChangeColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Effect.WB_Effect_C.IsEffectTypeSoundEffect
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Effect_C::IsEffectTypeSoundEffect()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "IsEffectTypeSoundEffect");

	Params::WB_Effect_C_IsEffectTypeSoundEffect Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Effect.WB_Effect_C.IsEffectTypeSoundEffectLooped
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Effect_C::IsEffectTypeSoundEffectLooped()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "IsEffectTypeSoundEffectLooped");

	Params::WB_Effect_C_IsEffectTypeSoundEffectLooped Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Effect.WB_Effect_C.IsTriggeredAlways
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Effect_C::IsTriggeredAlways()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "IsTriggeredAlways");

	Params::WB_Effect_C_IsTriggeredAlways Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Effect.WB_Effect_C.IsTriggeredAlwaysOnSpecificPercentValue
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Effect_C::IsTriggeredAlwaysOnSpecificPercentValue()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "IsTriggeredAlwaysOnSpecificPercentValue");

	Params::WB_Effect_C_IsTriggeredAlwaysOnSpecificPercentValue Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Effect.WB_Effect_C.IsTriggerSpecificPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWB_Effect_C::IsTriggerSpecificPercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "IsTriggerSpecificPercent");

	Params::WB_Effect_C_IsTriggerSpecificPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WB_Effect.WB_Effect_C.OnAnimationFinished_Event_0
// (BlueprintCallable, BlueprintEvent)

void UWB_Effect_C::OnAnimationFinished_Event_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "OnAnimationFinished_Event_0");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Effect.WB_Effect_C.OnAudioFinished_Event_0
// (BlueprintCallable, BlueprintEvent)

void UWB_Effect_C::OnAudioFinished_Event_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "OnAudioFinished_Event_0");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Effect.WB_Effect_C.SetFillType
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressBarFillType                    FillType_0                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::SetFillType(EProgressBarFillType FillType_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "SetFillType");

	Params::WB_Effect_C_SetFillType Parms{};

	Parms.FillType_0 = FillType_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.SetSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 Size_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::SetSize(const struct FVector2D& Size_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "SetSize");

	Params::WB_Effect_C_SetSize Parms{};

	Parms.Size_0 = std::move(Size_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.StartEffect
// (BlueprintCallable, BlueprintEvent)

void UWB_Effect_C::StartEffect()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "StartEffect");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Effect.WB_Effect_C.StopEffect
// (BlueprintCallable, BlueprintEvent)

void UWB_Effect_C::StopEffect()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "StopEffect");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Effect.WB_Effect_C.SwitchEffectType
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Index_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::SwitchEffectType(int32 Index_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "SwitchEffectType");

	Params::WB_Effect_C_SwitchEffectType Parms{};

	Parms.Index_0 = Index_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "Tick");

	Params::WB_Effect_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Effect.WB_Effect_C.TriggerEffect
// (BlueprintCallable, BlueprintEvent)

void UWB_Effect_C::TriggerEffect()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "TriggerEffect");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_Effect.WB_Effect_C.UpdatePercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Percent                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Effect_C::UpdatePercent(double Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Effect_C", "UpdatePercent");

	Params::WB_Effect_C_UpdatePercent Parms{};

	Parms.Percent = Percent;

	UObject::ProcessEvent(Func, &Parms);
}

}

