﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_QuantitySelector

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "NarrativeCommonUI_structs.hpp"


namespace SDK::Params
{

// Function WBP_QuantitySelector.WBP_QuantitySelector_C.BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature
// 0x0004 (0x0004 - 0x0000)
struct WBP_QuantitySelector_C_BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature final
{
public:
	float                                         Value;                                             // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroC<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
};
static_assert(alignof(WBP_QuantitySelector_C_BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature) == 0x000004, "Wrong alignment on WBP_QuantitySelector_C_BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature");
static_assert(sizeof(WBP_QuantitySelector_C_BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature) == 0x000004, "Wrong size on WBP_QuantitySelector_C_BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature");
static_assert(offsetof(WBP_QuantitySelector_C_BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature, Value) == 0x000000, "Member 'WBP_QuantitySelector_C_BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature::Value' has a wrong offset!");

// Function WBP_QuantitySelector.WBP_QuantitySelector_C.ExecuteUbergraph_WBP_QuantitySelector
// 0x0110 (0x0110 - 0x0000)
struct WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0008(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_1;             // 0x0020(0x0018)()
	float                                         K2Node_ComponentBoundEvent_Value;                  // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3C[0x4];                                       // 0x003C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_2;             // 0x0040(0x0018)()
	int32                                         K2Node_CustomEvent_MinAmount;                      // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_MaxAmount;                      // 0x005C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   K2Node_CustomEvent_InstructionText;                // 0x0060(0x0018)()
	float                                         CallFunc_GetValue_ReturnValue;                     // 0x0078(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Round_ReturnValue;                        // 0x007C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_3;             // 0x0080(0x0018)()
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0098(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x00D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D4[0x4];                                       // 0x00D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetDesiredFocusTarget_ReturnValue;        // 0x00D8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_A;                              // 0x00E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x00F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Round_ReturnValue_1;                      // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Max_ReturnValue;                          // 0x00FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Update_Drop_Amount_A_ImplicitCast;        // 0x0100(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Round_A_ImplicitCast;                     // 0x0108(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector) == 0x000008, "Wrong alignment on WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector");
static_assert(sizeof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector) == 0x000110, "Wrong size on WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, EntryPoint) == 0x000000, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::EntryPoint' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Conv_IntToText_ReturnValue) == 0x000008, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Conv_IntToText_ReturnValue_1) == 0x000020, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Conv_IntToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, K2Node_ComponentBoundEvent_Value) == 0x000038, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::K2Node_ComponentBoundEvent_Value' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Conv_IntToText_ReturnValue_2) == 0x000040, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Conv_IntToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, K2Node_CustomEvent_MinAmount) == 0x000058, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::K2Node_CustomEvent_MinAmount' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, K2Node_CustomEvent_MaxAmount) == 0x00005C, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::K2Node_CustomEvent_MaxAmount' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, K2Node_CustomEvent_InstructionText) == 0x000060, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::K2Node_CustomEvent_InstructionText' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_GetValue_ReturnValue) == 0x000078, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_GetValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Round_ReturnValue) == 0x00007C, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Round_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Conv_IntToText_ReturnValue_3) == 0x000080, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Conv_IntToText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, K2Node_Event_MyGeometry) == 0x000098, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, K2Node_Event_InDeltaTime) == 0x0000D0, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_GetDesiredFocusTarget_ReturnValue) == 0x0000D8, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_GetDesiredFocusTarget_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, K2Node_CustomEvent_A) == 0x0000E0, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::K2Node_CustomEvent_A' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Conv_IntToDouble_ReturnValue) == 0x0000E8, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0000F0, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Round_ReturnValue_1) == 0x0000F8, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Round_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Max_ReturnValue) == 0x0000FC, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Max_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Update_Drop_Amount_A_ImplicitCast) == 0x000100, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Update_Drop_Amount_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector, CallFunc_Round_A_ImplicitCast) == 0x000108, "Member 'WBP_QuantitySelector_C_ExecuteUbergraph_WBP_QuantitySelector::CallFunc_Round_A_ImplicitCast' has a wrong offset!");

// Function WBP_QuantitySelector.WBP_QuantitySelector_C.Initialize
// 0x0020 (0x0020 - 0x0000)
struct WBP_QuantitySelector_C_Initialize final
{
public:
	int32                                         MinAmount_0;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         MaxAmount_0;                                       // 0x0004(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   InstructionText_0;                                 // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WBP_QuantitySelector_C_Initialize) == 0x000008, "Wrong alignment on WBP_QuantitySelector_C_Initialize");
static_assert(sizeof(WBP_QuantitySelector_C_Initialize) == 0x000020, "Wrong size on WBP_QuantitySelector_C_Initialize");
static_assert(offsetof(WBP_QuantitySelector_C_Initialize, MinAmount_0) == 0x000000, "Member 'WBP_QuantitySelector_C_Initialize::MinAmount_0' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_Initialize, MaxAmount_0) == 0x000004, "Member 'WBP_QuantitySelector_C_Initialize::MaxAmount_0' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_Initialize, InstructionText_0) == 0x000008, "Member 'WBP_QuantitySelector_C_Initialize::InstructionText_0' has a wrong offset!");

// Function WBP_QuantitySelector.WBP_QuantitySelector_C.OnConfirm
// 0x0008 (0x0008 - 0x0000)
struct WBP_QuantitySelector_C_OnConfirm final
{
public:
	class FName                                   ActionName;                                        // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_QuantitySelector_C_OnConfirm) == 0x000004, "Wrong alignment on WBP_QuantitySelector_C_OnConfirm");
static_assert(sizeof(WBP_QuantitySelector_C_OnConfirm) == 0x000008, "Wrong size on WBP_QuantitySelector_C_OnConfirm");
static_assert(offsetof(WBP_QuantitySelector_C_OnConfirm, ActionName) == 0x000000, "Member 'WBP_QuantitySelector_C_OnConfirm::ActionName' has a wrong offset!");

// Function WBP_QuantitySelector.WBP_QuantitySelector_C.Tick
// 0x003C (0x003C - 0x0000)
struct WBP_QuantitySelector_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_QuantitySelector_C_Tick) == 0x000004, "Wrong alignment on WBP_QuantitySelector_C_Tick");
static_assert(sizeof(WBP_QuantitySelector_C_Tick) == 0x00003C, "Wrong size on WBP_QuantitySelector_C_Tick");
static_assert(offsetof(WBP_QuantitySelector_C_Tick, MyGeometry) == 0x000000, "Member 'WBP_QuantitySelector_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_Tick, InDeltaTime) == 0x000038, "Member 'WBP_QuantitySelector_C_Tick::InDeltaTime' has a wrong offset!");

// Function WBP_QuantitySelector.WBP_QuantitySelector_C.Update Drop Amount
// 0x0008 (0x0008 - 0x0000)
struct WBP_QuantitySelector_C_Update_Drop_Amount final
{
public:
	double                                        A;                                                 // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_QuantitySelector_C_Update_Drop_Amount) == 0x000008, "Wrong alignment on WBP_QuantitySelector_C_Update_Drop_Amount");
static_assert(sizeof(WBP_QuantitySelector_C_Update_Drop_Amount) == 0x000008, "Wrong size on WBP_QuantitySelector_C_Update_Drop_Amount");
static_assert(offsetof(WBP_QuantitySelector_C_Update_Drop_Amount, A) == 0x000000, "Member 'WBP_QuantitySelector_C_Update_Drop_Amount::A' has a wrong offset!");

// Function WBP_QuantitySelector.WBP_QuantitySelector_C.RegisterActions
// 0x0014 (0x0014 - 0x0000)
struct WBP_QuantitySelector_C_RegisterActions final
{
public:
	TDelegate<void(class FName ActionName)>       K2Node_CreateDelegate_OutputDelegate;              // 0x0000(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FInputActionBindingHandle              CallFunc_RegisterBinding_BindingHandle;            // 0x0010(0x0004)(NoDestructor)
};
static_assert(alignof(WBP_QuantitySelector_C_RegisterActions) == 0x000004, "Wrong alignment on WBP_QuantitySelector_C_RegisterActions");
static_assert(sizeof(WBP_QuantitySelector_C_RegisterActions) == 0x000014, "Wrong size on WBP_QuantitySelector_C_RegisterActions");
static_assert(offsetof(WBP_QuantitySelector_C_RegisterActions, K2Node_CreateDelegate_OutputDelegate) == 0x000000, "Member 'WBP_QuantitySelector_C_RegisterActions::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WBP_QuantitySelector_C_RegisterActions, CallFunc_RegisterBinding_BindingHandle) == 0x000010, "Member 'WBP_QuantitySelector_C_RegisterActions::CallFunc_RegisterBinding_BindingHandle' has a wrong offset!");

// Function WBP_QuantitySelector.WBP_QuantitySelector_C.BP_GetDesiredFocusTarget
// 0x0008 (0x0008 - 0x0000)
struct WBP_QuantitySelector_C_BP_GetDesiredFocusTarget final
{
public:
	class UWidget*                                ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_QuantitySelector_C_BP_GetDesiredFocusTarget) == 0x000008, "Wrong alignment on WBP_QuantitySelector_C_BP_GetDesiredFocusTarget");
static_assert(sizeof(WBP_QuantitySelector_C_BP_GetDesiredFocusTarget) == 0x000008, "Wrong size on WBP_QuantitySelector_C_BP_GetDesiredFocusTarget");
static_assert(offsetof(WBP_QuantitySelector_C_BP_GetDesiredFocusTarget, ReturnValue) == 0x000000, "Member 'WBP_QuantitySelector_C_BP_GetDesiredFocusTarget::ReturnValue' has a wrong offset!");

}

