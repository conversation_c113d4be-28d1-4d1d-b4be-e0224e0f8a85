﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Weather_Mask_Brush_Component

#include "Basic.hpp"

#include "Weather_Mask_Brush_Component_classes.hpp"
#include "Weather_Mask_Brush_Component_parameters.hpp"


namespace SDK
{

// Function Weather_Mask_Brush_Component.Weather_Mask_Brush_Component_C.Calculate Masking At Location
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector2D*                       Mask                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Cancel_All_Masks                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoD<PERSON>ructor, HasGetValueTypeHash)

void UWeather_Mask_Brush_Component_C::Calculate_Masking_At_Location(const struct FVector& Location, struct FVector2D* Mask, bool* Cancel_All_Masks)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Brush_Component_C", "Calculate Masking At Location");

	Params::Weather_Mask_Brush_Component_C_Calculate_Masking_At_Location Parms{};

	Parms.Location = std::move(Location);

	UObject::ProcessEvent(Func, &Parms);

	if (Mask != nullptr)
		*Mask = std::move(Parms.Mask);

	if (Cancel_All_Masks != nullptr)
		*Cancel_All_Masks = Parms.Cancel_All_Masks;
}


// Function Weather_Mask_Brush_Component.Weather_Mask_Brush_Component_C.Prepare for Drawing
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Runtime                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class AUltra_Dynamic_Weather_C*         UDW_0                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWeather_Mask_Brush_Component_C::Prepare_for_Drawing(bool Runtime, class AUltra_Dynamic_Weather_C* UDW_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Brush_Component_C", "Prepare for Drawing");

	Params::Weather_Mask_Brush_Component_C_Prepare_for_Drawing Parms{};

	Parms.Runtime = Runtime;
	Parms.UDW_0 = UDW_0;

	UObject::ProcessEvent(Func, &Parms);
}

}

