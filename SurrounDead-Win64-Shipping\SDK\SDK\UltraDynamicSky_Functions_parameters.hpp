﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UltraDynamicSky_Functions

#include "Basic.hpp"

#include "UDS_and_UDW_State_structs.hpp"
#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Apply Saved UDS and UDW State · 𝖴𝖣𝖲
// 0x0218 (0x0218 - 0x0000)
struct UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲 final
{
public:
	struct FUDS_and_UDW_State                     State;                                             // 0x0000(0x0200)(BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
	class UObject*                                __WorldContext;                                    // 0x0200(0x0008)(BlueprintVisible, BlueprintReadOnly, <PERSON><PERSON>, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0208(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0210(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲) == 0x000218, "Wrong size on UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲, State) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲::State' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲, __WorldContext) == 0x000200, "Member 'UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000208, "Member 'UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000210, "Member 'UltraDynamicSky_Functions_C_Apply_Saved_UDS_and_UDW_State_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Create UDS and UDW State for Saving · 𝖴𝖣𝖲
// 0x0418 (0x0418 - 0x0000)
struct UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FUDS_and_UDW_State                     Packaged_State;                                    // 0x0008(0x0200)(Parm, OutParm, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0208(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0210(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_211[0x7];                                      // 0x0211(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FUDS_and_UDW_State                     CallFunc_Create_UDS_and_UDW_State_for_Saving_Packaged_State; // 0x0218(0x0200)(HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲) == 0x000418, "Wrong size on UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲, __WorldContext) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲, Packaged_State) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲::Packaged_State' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000208, "Member 'UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000210, "Member 'UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲, CallFunc_Create_UDS_and_UDW_State_for_Saving_Packaged_State) == 0x000218, "Member 'UltraDynamicSky_Functions_C_Create_UDS_and_UDW_State_for_Saving_·_𝖴𝖣𝖲::CallFunc_Create_UDS_and_UDW_State_for_Saving_Packaged_State' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Get DateTime · 𝖴𝖣𝖲
// 0x0028 (0x0028 - 0x0000)
struct UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FDateTime                              DateTime;                                          // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDateTime                              CallFunc_Get_DateTime_Current_Date_and_Time;       // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲) == 0x000028, "Wrong size on UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲, __WorldContext) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲, DateTime) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲::DateTime' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000010, "Member 'UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000018, "Member 'UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲, CallFunc_Get_DateTime_Current_Date_and_Time) == 0x000020, "Member 'UltraDynamicSky_Functions_C_Get_DateTime_·_𝖴𝖣𝖲::CallFunc_Get_DateTime_Current_Date_and_Time' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Get Day of the Week · 𝖴𝖣𝖲
// 0x0040 (0x0040 - 0x0000)
struct UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         Index_0;                                           // 0x0008(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C[0x4];                                        // 0x000C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 Name_0;                                            // 0x0010(0x0010)(Parm, OutParm, ZeroConstructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x3];                                       // 0x0029(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Get_Day_of_the_Week_Index;                // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Get_Day_of_the_Week_Name;                 // 0x0030(0x0010)(ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲) == 0x000040, "Wrong size on UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲, __WorldContext) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲, Index_0) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲::Index_0' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲, Name_0) == 0x000010, "Member 'UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲::Name_0' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000020, "Member 'UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000028, "Member 'UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲, CallFunc_Get_Day_of_the_Week_Index) == 0x00002C, "Member 'UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲::CallFunc_Get_Day_of_the_Week_Index' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲, CallFunc_Get_Day_of_the_Week_Name) == 0x000030, "Member 'UltraDynamicSky_Functions_C_Get_Day_of_the_Week_·_𝖴𝖣𝖲::CallFunc_Get_Day_of_the_Week_Name' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Get Time of Day · 𝖴𝖣𝖲
// 0x0028 (0x0028 - 0x0000)
struct UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Time_of_Day;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Percent_FloatFloat_ReturnValue;           // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲) == 0x000028, "Wrong size on UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲, __WorldContext) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲, Time_of_Day) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲::Time_of_Day' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000010, "Member 'UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000018, "Member 'UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲, CallFunc_Percent_FloatFloat_ReturnValue) == 0x000020, "Member 'UltraDynamicSky_Functions_C_Get_Time_of_Day_·_𝖴𝖣𝖲::CallFunc_Percent_FloatFloat_ReturnValue' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Get TimeCode · 𝖴𝖣𝖲
// 0x0040 (0x0040 - 0x0000)
struct UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FTimecode                              Timecode;                                          // 0x0008(0x0014)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x3];                                       // 0x0029(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimecode                              CallFunc_Get_TimeCode_Time;                        // 0x002C(0x0014)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲) == 0x000040, "Wrong size on UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲, __WorldContext) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲, Timecode) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲::Timecode' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000020, "Member 'UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000028, "Member 'UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲, CallFunc_Get_TimeCode_Time) == 0x00002C, "Member 'UltraDynamicSky_Functions_C_Get_TimeCode_·_𝖴𝖣𝖲::CallFunc_Get_TimeCode_Time' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Get Ultra Dynamic Sky
// 0x0028 (0x0028 - 0x0000)
struct UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   UDS;                                               // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Valid;                                             // 0x0010(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Sky_C*                   CallFunc_GetActorOfClass_ReturnValue;              // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky");
static_assert(sizeof(UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky) == 0x000028, "Wrong size on UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky, __WorldContext) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky, UDS) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky::UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky, Valid) == 0x000010, "Member 'UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky::Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky, CallFunc_GetActorOfClass_ReturnValue) == 0x000018, "Member 'UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky::CallFunc_GetActorOfClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky, CallFunc_IsValid_ReturnValue) == 0x000020, "Member 'UltraDynamicSky_Functions_C_Get_Ultra_Dynamic_Sky::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Is It Daytime? · 𝖴𝖣𝖲
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲 final
{
public:
	class UObject*                                __WorldContext;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Daytime;                                           // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Is_it_Daytime__Yes;                       // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲) == 0x000020, "Wrong size on UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲, __WorldContext) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲, Daytime) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲::Daytime' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000010, "Member 'UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000018, "Member 'UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲, CallFunc_Is_it_Daytime__Yes) == 0x000019, "Member 'UltraDynamicSky_Functions_C_Is_It_Daytime__·_𝖴𝖣𝖲::CallFunc_Is_it_Daytime__Yes' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Random Value in Float Range Structure
// 0x0038 (0x0038 - 0x0000)
struct UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure final
{
public:
	struct FFloatRange                            Range;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRandomStream                          Stream;                                            // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor)
	class UObject*                                __WorldContext;                                    // 0x0018(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Out;                                               // 0x0020(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_RandomFloatInRangeFromStream_ReturnValue; // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_FunctionResult_Out_ImplicitCast;            // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure");
static_assert(sizeof(UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure) == 0x000038, "Wrong size on UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure");
static_assert(offsetof(UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure, Range) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure::Range' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure, Stream) == 0x000010, "Member 'UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure::Stream' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure, __WorldContext) == 0x000018, "Member 'UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure, Out) == 0x000020, "Member 'UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure::Out' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure, CallFunc_RandomFloatInRangeFromStream_ReturnValue) == 0x000028, "Member 'UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure::CallFunc_RandomFloatInRangeFromStream_ReturnValue' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure, K2Node_FunctionResult_Out_ImplicitCast) == 0x000030, "Member 'UltraDynamicSky_Functions_C_Random_Value_in_Float_Range_Structure::K2Node_FunctionResult_Out_ImplicitCast' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Set Date and Time · 𝖴𝖣𝖲
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲 final
{
public:
	struct FDateTime                              DateTime;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                __WorldContext;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲) == 0x000020, "Wrong size on UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲, DateTime) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲::DateTime' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲, __WorldContext) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000010, "Member 'UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000018, "Member 'UltraDynamicSky_Functions_C_Set_Date_and_Time_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Set Time of Day · 𝖴𝖣𝖲
// 0x0020 (0x0020 - 0x0000)
struct UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲 final
{
public:
	double                                        Time_of_Day;                                       // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UObject*                                __WorldContext;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲) == 0x000020, "Wrong size on UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲, Time_of_Day) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲::Time_of_Day' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲, __WorldContext) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000010, "Member 'UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000018, "Member 'UltraDynamicSky_Functions_C_Set_Time_of_Day_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Set Time with Time Code · 𝖴𝖣𝖲
// 0x0030 (0x0030 - 0x0000)
struct UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲 final
{
public:
	struct FTimecode                              Timecode;                                          // 0x0000(0x0014)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                __WorldContext;                                    // 0x0018(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲) == 0x000030, "Wrong size on UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲, Timecode) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲::Timecode' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲, __WorldContext) == 0x000018, "Member 'UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000020, "Member 'UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000028, "Member 'UltraDynamicSky_Functions_C_Set_Time_with_Time_Code_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");

// Function UltraDynamicSky_Functions.UltraDynamicSky_Functions_C.Transition Time of Day · 𝖴𝖣𝖲
// 0x0040 (0x0040 - 0x0000)
struct UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲 final
{
public:
	double                                        New_Time_of_Day;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Transition_Duration__Seconds_;                     // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEasingFunc                                   Easing_Function;                                   // 0x0010(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Easing_Exponent;                                   // 0x0018(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Allow_Time_Going_Backwards;                        // 0x0020(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                __WorldContext;                                    // 0x0028(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_Get_Ultra_Dynamic_Sky_UDS;                // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Sky_Valid;              // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲) == 0x000008, "Wrong alignment on UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲");
static_assert(sizeof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲) == 0x000040, "Wrong size on UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲");
static_assert(offsetof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲, New_Time_of_Day) == 0x000000, "Member 'UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲::New_Time_of_Day' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲, Transition_Duration__Seconds_) == 0x000008, "Member 'UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲::Transition_Duration__Seconds_' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲, Easing_Function) == 0x000010, "Member 'UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲::Easing_Function' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲, Easing_Exponent) == 0x000018, "Member 'UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲::Easing_Exponent' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲, Allow_Time_Going_Backwards) == 0x000020, "Member 'UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲::Allow_Time_Going_Backwards' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲, __WorldContext) == 0x000028, "Member 'UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲::__WorldContext' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_UDS) == 0x000030, "Member 'UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_UDS' has a wrong offset!");
static_assert(offsetof(UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲, CallFunc_Get_Ultra_Dynamic_Sky_Valid) == 0x000038, "Member 'UltraDynamicSky_Functions_C_Transition_Time_of_Day_·_𝖴𝖣𝖲::CallFunc_Get_Ultra_Dynamic_Sky_Valid' has a wrong offset!");

}

