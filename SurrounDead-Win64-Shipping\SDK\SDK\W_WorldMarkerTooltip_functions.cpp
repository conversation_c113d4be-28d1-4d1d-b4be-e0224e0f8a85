﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_WorldMarkerTooltip

#include "Basic.hpp"

#include "W_WorldMarkerTooltip_classes.hpp"
#include "W_WorldMarkerTooltip_parameters.hpp"


namespace SDK
{

// Function W_WorldMarkerTooltip.W_WorldMarkerTooltip_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_WorldMarkerTooltip_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_WorldMarkerTooltip_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_WorldMarkerTooltip.W_WorldMarkerTooltip_C.ExecuteUbergraph_W_WorldMarkerTooltip
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintRead<PERSON><PERSON>ly, <PERSON>rm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_WorldMarkerTooltip_C::ExecuteUbergraph_W_WorldMarkerTooltip(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_WorldMarkerTooltip_C", "ExecuteUbergraph_W_WorldMarkerTooltip");

	Params::W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}

}

