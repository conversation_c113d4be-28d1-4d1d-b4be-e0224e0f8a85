﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Weather_Override_Volume

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UDW_WOV_State_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "UDW_WeatherState_Structure_structs.hpp"
#include "MeshDescription_structs.hpp"
#include "RandomWeatherVariation_State_structs.hpp"
#include "UDS_RandomWeatherTiming_structs.hpp"


namespace SDK::Params
{

// Function Weather_Override_Volume.Weather_Override_Volume_C.Add Quad
// 0x0078 (0x0078 - 0x0000)
struct Weather_Override_Volume_C_Add_Quad final
{
public:
	struct FVector                                Vert_1;                                            // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Vert_2;                                            // 0x0018(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Vert_3;                                            // 0x0030(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Vert_4;                                            // 0x0048(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_1;                  // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_2;                  // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_3;                  // 0x006C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_4;                  // 0x0070(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_5;                  // 0x0074(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Add_Quad) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Add_Quad");
static_assert(sizeof(Weather_Override_Volume_C_Add_Quad) == 0x000078, "Wrong size on Weather_Override_Volume_C_Add_Quad");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, Vert_1) == 0x000000, "Member 'Weather_Override_Volume_C_Add_Quad::Vert_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, Vert_2) == 0x000018, "Member 'Weather_Override_Volume_C_Add_Quad::Vert_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, Vert_3) == 0x000030, "Member 'Weather_Override_Volume_C_Add_Quad::Vert_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, Vert_4) == 0x000048, "Member 'Weather_Override_Volume_C_Add_Quad::Vert_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, CallFunc_Array_Add_ReturnValue) == 0x000060, "Member 'Weather_Override_Volume_C_Add_Quad::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, CallFunc_Array_Add_ReturnValue_1) == 0x000064, "Member 'Weather_Override_Volume_C_Add_Quad::CallFunc_Array_Add_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, CallFunc_Array_Add_ReturnValue_2) == 0x000068, "Member 'Weather_Override_Volume_C_Add_Quad::CallFunc_Array_Add_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, CallFunc_Array_Add_ReturnValue_3) == 0x00006C, "Member 'Weather_Override_Volume_C_Add_Quad::CallFunc_Array_Add_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, CallFunc_Array_Add_ReturnValue_4) == 0x000070, "Member 'Weather_Override_Volume_C_Add_Quad::CallFunc_Array_Add_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Quad, CallFunc_Array_Add_ReturnValue_5) == 0x000074, "Member 'Weather_Override_Volume_C_Add_Quad::CallFunc_Array_Add_ReturnValue_5' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Add Triangle
// 0x0058 (0x0058 - 0x0000)
struct Weather_Override_Volume_C_Add_Triangle final
{
public:
	struct FVector                                Vert_1;                                            // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Vert_2;                                            // 0x0018(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Vert_3;                                            // 0x0030(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_1;                  // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_2;                  // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Add_Triangle) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Add_Triangle");
static_assert(sizeof(Weather_Override_Volume_C_Add_Triangle) == 0x000058, "Wrong size on Weather_Override_Volume_C_Add_Triangle");
static_assert(offsetof(Weather_Override_Volume_C_Add_Triangle, Vert_1) == 0x000000, "Member 'Weather_Override_Volume_C_Add_Triangle::Vert_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Triangle, Vert_2) == 0x000018, "Member 'Weather_Override_Volume_C_Add_Triangle::Vert_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Triangle, Vert_3) == 0x000030, "Member 'Weather_Override_Volume_C_Add_Triangle::Vert_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Triangle, CallFunc_Array_Add_ReturnValue) == 0x000048, "Member 'Weather_Override_Volume_C_Add_Triangle::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Triangle, CallFunc_Array_Add_ReturnValue_1) == 0x00004C, "Member 'Weather_Override_Volume_C_Add_Triangle::CallFunc_Array_Add_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Add_Triangle, CallFunc_Array_Add_ReturnValue_2) == 0x000050, "Member 'Weather_Override_Volume_C_Add_Triangle::CallFunc_Array_Add_ReturnValue_2' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Apply Climate Preset Object
// 0x01B0 (0x01B0 - 0x0000)
struct Weather_Override_Volume_C_Apply_Climate_Preset_Object final
{
public:
	class UUDS_Climate_Preset_C*                  Climate_Preset;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TMap<class UUDS_Weather_Settings_C*, double>  CallFunc_Make_Climate_Probability_Map_Probability_Map; // 0x0010(0x0050)()
	TMap<class UUDS_Weather_Settings_C*, double>  CallFunc_Make_Climate_Probability_Map_Probability_Map_1; // 0x0060(0x0050)()
	TMap<class UUDS_Weather_Settings_C*, double>  CallFunc_Make_Climate_Probability_Map_Probability_Map_2; // 0x00B0(0x0050)()
	TMap<class UUDS_Weather_Settings_C*, double>  CallFunc_Make_Climate_Probability_Map_Probability_Map_3; // 0x0100(0x0050)()
	struct FVector2D                              CallFunc_Get_Climate_Temperature_Ranges_Summer_Range; // 0x0150(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Get_Climate_Temperature_Ranges_Autumn_Range; // 0x0160(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Get_Climate_Temperature_Ranges_Winter_Range; // 0x0170(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Get_Climate_Temperature_Ranges_Spring_Range; // 0x0180(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetObjectName_ReturnValue;                // 0x0190(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Replace_ReturnValue;                      // 0x01A0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Apply_Climate_Preset_Object) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Apply_Climate_Preset_Object");
static_assert(sizeof(Weather_Override_Volume_C_Apply_Climate_Preset_Object) == 0x0001B0, "Wrong size on Weather_Override_Volume_C_Apply_Climate_Preset_Object");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, Climate_Preset) == 0x000000, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::Climate_Preset' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_IsValid_ReturnValue) == 0x000008, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_Make_Climate_Probability_Map_Probability_Map) == 0x000010, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_Make_Climate_Probability_Map_Probability_Map' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_Make_Climate_Probability_Map_Probability_Map_1) == 0x000060, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_Make_Climate_Probability_Map_Probability_Map_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_Make_Climate_Probability_Map_Probability_Map_2) == 0x0000B0, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_Make_Climate_Probability_Map_Probability_Map_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_Make_Climate_Probability_Map_Probability_Map_3) == 0x000100, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_Make_Climate_Probability_Map_Probability_Map_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_Get_Climate_Temperature_Ranges_Summer_Range) == 0x000150, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_Get_Climate_Temperature_Ranges_Summer_Range' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_Get_Climate_Temperature_Ranges_Autumn_Range) == 0x000160, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_Get_Climate_Temperature_Ranges_Autumn_Range' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_Get_Climate_Temperature_Ranges_Winter_Range) == 0x000170, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_Get_Climate_Temperature_Ranges_Winter_Range' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_Get_Climate_Temperature_Ranges_Spring_Range) == 0x000180, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_Get_Climate_Temperature_Ranges_Spring_Range' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_GetObjectName_ReturnValue) == 0x000190, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_GetObjectName_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Climate_Preset_Object, CallFunc_Replace_ReturnValue) == 0x0001A0, "Member 'Weather_Override_Volume_C_Apply_Climate_Preset_Object::CallFunc_Replace_ReturnValue' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Apply Saved WOV State
// 0x0108 (0x0108 - 0x0000)
struct Weather_Override_Volume_C_Apply_Saved_WOV_State final
{
public:
	struct FUDW_WOV_State                         State;                                             // 0x0000(0x00D0)(BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x00D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x00D2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D3[0x5];                                       // 0x00D3(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                K2Node_DynamicCast_AsUDS_Weather_Settings;         // 0x00D8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E1[0x7];                                       // 0x00E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                K2Node_DynamicCast_AsUDS_Weather_Settings_1;       // 0x00E8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F1[0x7];                                       // 0x00F1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                K2Node_DynamicCast_AsUDS_Weather_Settings_2;       // 0x00F8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Apply_Saved_WOV_State) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Apply_Saved_WOV_State");
static_assert(sizeof(Weather_Override_Volume_C_Apply_Saved_WOV_State) == 0x000108, "Wrong size on Weather_Override_Volume_C_Apply_Saved_WOV_State");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, State) == 0x000000, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::State' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, CallFunc_Not_PreBool_ReturnValue) == 0x0000D0, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x0000D1, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, CallFunc_BooleanAND_ReturnValue) == 0x0000D2, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, K2Node_DynamicCast_AsUDS_Weather_Settings) == 0x0000D8, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::K2Node_DynamicCast_AsUDS_Weather_Settings' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, K2Node_DynamicCast_bSuccess) == 0x0000E0, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, K2Node_DynamicCast_AsUDS_Weather_Settings_1) == 0x0000E8, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::K2Node_DynamicCast_AsUDS_Weather_Settings_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, K2Node_DynamicCast_bSuccess_1) == 0x0000F0, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, K2Node_DynamicCast_AsUDS_Weather_Settings_2) == 0x0000F8, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::K2Node_DynamicCast_AsUDS_Weather_Settings_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Apply_Saved_WOV_State, K2Node_DynamicCast_bSuccess_2) == 0x000100, "Member 'Weather_Override_Volume_C_Apply_Saved_WOV_State::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Calculate Spline Bounds
// 0x00C8 (0x00C8 - 0x0000)
struct Weather_Override_Volume_C_Calculate_Spline_Bounds final
{
public:
	double                                        Bounds;                                            // 0x0000(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetActorLocation_ReturnValue;          // 0x0008(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24[0x4];                                       // 0x0024(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_1;           // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue;             // 0x0048(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetSplineLength_ReturnValue;              // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetNumberOfSplinePoints_ReturnValue;      // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Multiply_IntInt_ReturnValue;              // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_IntFloat_ReturnValue;            // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_GetLocationAtDistanceAlongSpline_ReturnValue; // 0x0090(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Vector_Distance_ReturnValue;              // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x00B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B1[0x7];                                       // 0x00B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_A_ImplicitCast;       // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetLocationAtDistanceAlongSpline_Distance_ImplicitCast; // 0x00C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Calculate_Spline_Bounds) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Calculate_Spline_Bounds");
static_assert(sizeof(Weather_Override_Volume_C_Calculate_Spline_Bounds) == 0x0000C8, "Wrong size on Weather_Override_Volume_C_Calculate_Spline_Bounds");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, Bounds) == 0x000000, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::Bounds' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_K2_GetActorLocation_ReturnValue) == 0x000008, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_K2_GetActorLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, Temp_int_Variable) == 0x000020, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000028, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Add_IntInt_ReturnValue) == 0x000030, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Add_DoubleDouble_ReturnValue) == 0x000038, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Add_DoubleDouble_ReturnValue_1) == 0x000040, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Add_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Add_VectorVector_ReturnValue) == 0x000048, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Add_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_GetSplineLength_ReturnValue) == 0x000060, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_GetSplineLength_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_GetNumberOfSplinePoints_ReturnValue) == 0x000064, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_GetNumberOfSplinePoints_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Multiply_IntInt_ReturnValue) == 0x000068, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Multiply_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000070, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000078, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000080, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Multiply_IntFloat_ReturnValue) == 0x000088, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_GetLocationAtDistanceAlongSpline_ReturnValue) == 0x000090, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_GetLocationAtDistanceAlongSpline_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Vector_Distance_ReturnValue) == 0x0000A8, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Vector_Distance_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x0000B0, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_Divide_DoubleDouble_A_ImplicitCast) == 0x0000B8, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_Divide_DoubleDouble_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Calculate_Spline_Bounds, CallFunc_GetLocationAtDistanceAlongSpline_Distance_ImplicitCast) == 0x0000C0, "Member 'Weather_Override_Volume_C_Calculate_Spline_Bounds::CallFunc_GetLocationAtDistanceAlongSpline_Distance_ImplicitCast' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Change to Random Weather Variation
// 0x0010 (0x0010 - 0x0000)
struct Weather_Override_Volume_C_Change_to_Random_Weather_Variation final
{
public:
	double                                        Time_to_Transition_to_Random_Weather__Seconds_;    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_RandomWeatherTiming                      Random_Weather_Mode;                               // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Change_to_Random_Weather_Variation) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Change_to_Random_Weather_Variation");
static_assert(sizeof(Weather_Override_Volume_C_Change_to_Random_Weather_Variation) == 0x000010, "Wrong size on Weather_Override_Volume_C_Change_to_Random_Weather_Variation");
static_assert(offsetof(Weather_Override_Volume_C_Change_to_Random_Weather_Variation, Time_to_Transition_to_Random_Weather__Seconds_) == 0x000000, "Member 'Weather_Override_Volume_C_Change_to_Random_Weather_Variation::Time_to_Transition_to_Random_Weather__Seconds_' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Change_to_Random_Weather_Variation, Random_Weather_Mode) == 0x000008, "Member 'Weather_Override_Volume_C_Change_to_Random_Weather_Variation::Random_Weather_Mode' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Change Weather
// 0x0010 (0x0010 - 0x0000)
struct Weather_Override_Volume_C_Change_Weather final
{
public:
	class UUDS_Weather_Settings_C*                New_Weather_Type;                                  // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Time_To_Transition_To_New_Weather__Seconds_;       // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Change_Weather) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Change_Weather");
static_assert(sizeof(Weather_Override_Volume_C_Change_Weather) == 0x000010, "Wrong size on Weather_Override_Volume_C_Change_Weather");
static_assert(offsetof(Weather_Override_Volume_C_Change_Weather, New_Weather_Type) == 0x000000, "Member 'Weather_Override_Volume_C_Change_Weather::New_Weather_Type' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Change_Weather, Time_To_Transition_To_New_Weather__Seconds_) == 0x000008, "Member 'Weather_Override_Volume_C_Change_Weather::Time_To_Transition_To_New_Weather__Seconds_' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Check for Changing Material State to Request Target Redraw
// 0x0050 (0x0050 - 0x0000)
struct Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw final
{
public:
	struct FVector                                CallFunc_MakeVector_ReturnValue;                   // 0x0000(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A[0x6];                                       // 0x001A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue;        // 0x0020(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_SelectFloat_ReturnValue;                  // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Vector_GetAbsMax_ReturnValue;             // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw");
static_assert(sizeof(Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw) == 0x000050, "Wrong size on Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw");
static_assert(offsetof(Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw, CallFunc_MakeVector_ReturnValue) == 0x000000, "Member 'Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw::CallFunc_MakeVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x000018, "Member 'Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw, CallFunc_BooleanAND_ReturnValue) == 0x000019, "Member 'Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw, CallFunc_Subtract_VectorVector_ReturnValue) == 0x000020, "Member 'Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw::CallFunc_Subtract_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw, CallFunc_SelectFloat_ReturnValue) == 0x000038, "Member 'Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw::CallFunc_SelectFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw, CallFunc_Vector_GetAbsMax_ReturnValue) == 0x000040, "Member 'Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw::CallFunc_Vector_GetAbsMax_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000048, "Member 'Weather_Override_Volume_C_Check_for_Changing_Material_State_to_Request_Target_Redraw::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Check to Update Temperature Scale
// 0x00D8 (0x00D8 - 0x0000)
struct Weather_Override_Volume_C_Check_to_Update_Temperature_Scale final
{
public:
	double                                        CallFunc_BreakVector2D_X;                          // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Convert_Temperature_Scale_Output;         // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Convert_Temperature_Scale_Output_1;       // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_1;                        // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_1;                        // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Convert_Temperature_Scale_Output_2;       // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Convert_Temperature_Scale_Output_3;       // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x0050(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_2;                        // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_2;                        // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Convert_Temperature_Scale_Output_4;       // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Convert_Temperature_Scale_Output_5;       // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_2;               // 0x0080(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_3;                        // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_3;                        // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Convert_Temperature_Scale_Output_6;       // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue;            // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A9[0x7];                                       // 0x00A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Convert_Temperature_Scale_Output_7;       // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_3;               // 0x00B8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_GetActorOfClass_ReturnValue;              // 0x00C8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Check_to_Update_Temperature_Scale");
static_assert(sizeof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale) == 0x0000D8, "Wrong size on Weather_Override_Volume_C_Check_to_Update_Temperature_Scale");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_BreakVector2D_X) == 0x000000, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_BreakVector2D_Y) == 0x000008, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_Convert_Temperature_Scale_Output) == 0x000010, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_Convert_Temperature_Scale_Output' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_Convert_Temperature_Scale_Output_1) == 0x000018, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_Convert_Temperature_Scale_Output_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_MakeVector2D_ReturnValue) == 0x000020, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_BreakVector2D_X_1) == 0x000030, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_BreakVector2D_Y_1) == 0x000038, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_Convert_Temperature_Scale_Output_2) == 0x000040, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_Convert_Temperature_Scale_Output_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_Convert_Temperature_Scale_Output_3) == 0x000048, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_Convert_Temperature_Scale_Output_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_MakeVector2D_ReturnValue_1) == 0x000050, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_BreakVector2D_X_2) == 0x000060, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_BreakVector2D_X_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_BreakVector2D_Y_2) == 0x000068, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_BreakVector2D_Y_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_Convert_Temperature_Scale_Output_4) == 0x000070, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_Convert_Temperature_Scale_Output_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_Convert_Temperature_Scale_Output_5) == 0x000078, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_Convert_Temperature_Scale_Output_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_MakeVector2D_ReturnValue_2) == 0x000080, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_MakeVector2D_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_BreakVector2D_X_3) == 0x000090, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_BreakVector2D_X_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_BreakVector2D_Y_3) == 0x000098, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_BreakVector2D_Y_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_Convert_Temperature_Scale_Output_6) == 0x0000A0, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_Convert_Temperature_Scale_Output_6' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_NotEqual_ByteByte_ReturnValue) == 0x0000A8, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_NotEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_Convert_Temperature_Scale_Output_7) == 0x0000B0, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_Convert_Temperature_Scale_Output_7' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_MakeVector2D_ReturnValue_3) == 0x0000B8, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_MakeVector2D_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_GetActorOfClass_ReturnValue) == 0x0000C8, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_GetActorOfClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Check_to_Update_Temperature_Scale, CallFunc_IsValid_ReturnValue) == 0x0000D0, "Member 'Weather_Override_Volume_C_Check_to_Update_Temperature_Scale::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Construct Editor Only Weather Labels
// 0x0300 (0x0300 - 0x0000)
struct Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels final
{
public:
	class UTextRenderComponent*                   Text_Component;                                    // 0x0000(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FString                                 Label;                                             // 0x0008(0x0010)(Edit, BlueprintVisible, ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_1;           // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_2;           // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_3;           // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_HSVToRGB_ReturnValue;                     // 0x0048(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_SelectColor_ReturnValue;                  // 0x0060(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_HSVToRGB_ReturnValue_1;                   // 0x0070(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_SelectColor_ReturnValue_1;                // 0x0080(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FColor                                 CallFunc_Conv_LinearColorToColor_ReturnValue;      // 0x0090(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0094(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_95[0x3];                                       // 0x0095(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_SelectColor_ReturnValue_2;                // 0x0098(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x00A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_SelectColor_ReturnValue_3;                // 0x00AC(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x00BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_2;        // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C1[0x3];                                       // 0x00C1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Len_ReturnValue;                          // 0x00C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMax_ReturnValue;                         // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x00E0(0x0018)()
	struct FVector                                CallFunc_GetActorScale3D_ReturnValue;              // 0x00F8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Divide_VectorVector_ReturnValue;          // 0x0110(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0128(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetDisplayName_ReturnValue;               // 0x0130(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Replace_ReturnValue;                      // 0x0140(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_SelectString_ReturnValue;                 // 0x0150(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_2;        // 0x0160(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_SelectString_ReturnValue_1;               // 0x0168(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	float                                         CallFunc_GetSplineLength_ReturnValue;              // 0x0178(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_17C[0x4];                                      // 0x017C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_3;        // 0x0180(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FFloor_ReturnValue;                       // 0x0188(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_18C[0x4];                                      // 0x018C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Conv_IntToDouble_ReturnValue_1;           // 0x0190(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0198(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_199[0x7];                                      // 0x0199(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_4;        // 0x01A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_IntFloat_ReturnValue;            // 0x01A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTransform                             CallFunc_GetTransformAtDistanceAlongSpline_ReturnValue; // 0x01B0(0x0060)(IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakTransform_Location;                  // 0x0210(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               CallFunc_BreakTransform_Rotation;                  // 0x0228(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FVector                                CallFunc_BreakTransform_Scale;                     // 0x0240(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               CallFunc_ComposeRotators_ReturnValue;              // 0x0258(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FTransform                             CallFunc_MakeTransform_ReturnValue;                // 0x0270(0x0060)(IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTextRenderComponent*                   CallFunc_AddComponent_ReturnValue;                 // 0x02D0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x02D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2D9[0x3];                                      // 0x02D9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_HSVToRGB_H_ImplicitCast;                  // 0x02DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_HSVToRGB_H_ImplicitCast_1;                // 0x02E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetWorldSize_Value_ImplicitCast;          // 0x02E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_A_ImplicitCast;       // 0x02E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_A_ImplicitCast_1;     // 0x02F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetTransformAtDistanceAlongSpline_Distance_ImplicitCast; // 0x02F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels) == 0x000010, "Wrong alignment on Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels");
static_assert(sizeof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels) == 0x000300, "Wrong size on Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, Text_Component) == 0x000000, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::Text_Component' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, Label) == 0x000008, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::Label' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000018, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Add_DoubleDouble_ReturnValue) == 0x000020, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Add_DoubleDouble_ReturnValue_1) == 0x000028, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Add_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Add_DoubleDouble_ReturnValue_2) == 0x000030, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Add_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000038, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Add_DoubleDouble_ReturnValue_3) == 0x000040, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Add_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_HSVToRGB_ReturnValue) == 0x000048, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_HSVToRGB_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000058, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_SelectColor_ReturnValue) == 0x000060, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_SelectColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_HSVToRGB_ReturnValue_1) == 0x000070, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_HSVToRGB_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_SelectColor_ReturnValue_1) == 0x000080, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_SelectColor_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Conv_LinearColorToColor_ReturnValue) == 0x000090, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Conv_LinearColorToColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000094, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_SelectColor_ReturnValue_2) == 0x000098, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_SelectColor_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, Temp_int_Variable) == 0x0000A8, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_SelectColor_ReturnValue_3) == 0x0000AC, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_SelectColor_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Add_IntInt_ReturnValue) == 0x0000BC, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_EqualEqual_ByteByte_ReturnValue_2) == 0x0000C0, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_EqualEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Len_ReturnValue) == 0x0000C4, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Len_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Conv_IntToDouble_ReturnValue) == 0x0000C8, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_FMax_ReturnValue) == 0x0000D0, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_FMax_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x0000D8, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Conv_StringToText_ReturnValue) == 0x0000E0, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_GetActorScale3D_ReturnValue) == 0x0000F8, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_GetActorScale3D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Divide_VectorVector_ReturnValue) == 0x000110, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Divide_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000128, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_GetDisplayName_ReturnValue) == 0x000130, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_GetDisplayName_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Replace_ReturnValue) == 0x000140, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Replace_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_SelectString_ReturnValue) == 0x000150, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_SelectString_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Divide_DoubleDouble_ReturnValue_2) == 0x000160, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Divide_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_SelectString_ReturnValue_1) == 0x000168, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_SelectString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_GetSplineLength_ReturnValue) == 0x000178, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_GetSplineLength_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Divide_DoubleDouble_ReturnValue_3) == 0x000180, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Divide_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_FFloor_ReturnValue) == 0x000188, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_FFloor_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Conv_IntToDouble_ReturnValue_1) == 0x000190, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Conv_IntToDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000198, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Divide_DoubleDouble_ReturnValue_4) == 0x0001A0, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Divide_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Multiply_IntFloat_ReturnValue) == 0x0001A8, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_GetTransformAtDistanceAlongSpline_ReturnValue) == 0x0001B0, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_GetTransformAtDistanceAlongSpline_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_BreakTransform_Location) == 0x000210, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_BreakTransform_Location' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_BreakTransform_Rotation) == 0x000228, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_BreakTransform_Rotation' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_BreakTransform_Scale) == 0x000240, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_BreakTransform_Scale' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_ComposeRotators_ReturnValue) == 0x000258, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_ComposeRotators_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_MakeTransform_ReturnValue) == 0x000270, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_MakeTransform_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_AddComponent_ReturnValue) == 0x0002D0, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_AddComponent_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_IsValid_ReturnValue) == 0x0002D8, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_HSVToRGB_H_ImplicitCast) == 0x0002DC, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_HSVToRGB_H_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_HSVToRGB_H_ImplicitCast_1) == 0x0002E0, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_HSVToRGB_H_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_SetWorldSize_Value_ImplicitCast) == 0x0002E4, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_SetWorldSize_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Divide_DoubleDouble_A_ImplicitCast) == 0x0002E8, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Divide_DoubleDouble_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_Divide_DoubleDouble_A_ImplicitCast_1) == 0x0002F0, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_Divide_DoubleDouble_A_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels, CallFunc_GetTransformAtDistanceAlongSpline_Distance_ImplicitCast) == 0x0002F8, "Member 'Weather_Override_Volume_C_Construct_Editor_Only_Weather_Labels::CallFunc_GetTransformAtDistanceAlongSpline_Distance_ImplicitCast' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Construct Weather State Objects
// 0x0020 (0x0020 - 0x0000)
struct Weather_Override_Volume_C_Construct_Weather_State_Objects final
{
public:
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Weather_C*               CallFunc_GetActorOfClass_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                CallFunc_SpawnObject_ReturnValue;                  // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Construct_Weather_State_Objects) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Construct_Weather_State_Objects");
static_assert(sizeof(Weather_Override_Volume_C_Construct_Weather_State_Objects) == 0x000020, "Wrong size on Weather_Override_Volume_C_Construct_Weather_State_Objects");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Weather_State_Objects, CallFunc_IsValid_ReturnValue) == 0x000000, "Member 'Weather_Override_Volume_C_Construct_Weather_State_Objects::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Weather_State_Objects, CallFunc_GetActorOfClass_ReturnValue) == 0x000008, "Member 'Weather_Override_Volume_C_Construct_Weather_State_Objects::CallFunc_GetActorOfClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Weather_State_Objects, CallFunc_IsValid_ReturnValue_1) == 0x000010, "Member 'Weather_Override_Volume_C_Construct_Weather_State_Objects::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Construct_Weather_State_Objects, CallFunc_SpawnObject_ReturnValue) == 0x000018, "Member 'Weather_Override_Volume_C_Construct_Weather_State_Objects::CallFunc_SpawnObject_ReturnValue' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Create Canvas Space Triangles
// 0x01C8 (0x01C8 - 0x0000)
struct Weather_Override_Volume_C_Create_Canvas_Space_Triangles final
{
public:
	struct FVector2D                              Corner_Position;                                   // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Width;                                             // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Resolution;                                        // 0x0018(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue;        // 0x0028(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_MakeVector_ReturnValue;                   // 0x0048(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Multiply_IntInt_ReturnValue;              // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x006C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item;                           // 0x0070(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8C[0x4];                                       // 0x008C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Pos; // 0x0090(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Color; // 0x00A0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_1;                         // 0x00B0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_2;                         // 0x00C8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Pos_1; // 0x00E0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Color_1; // 0x00F0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Pos_2; // 0x0100(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Color_2; // 0x0110(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FCanvasUVTri                           K2Node_MakeStruct_CanvasUVTri;                     // 0x0120(0x0090)(ZeroConstructor, NoDestructor)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x01B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x01B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Divide_IntInt_ReturnValue;                // 0x01B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x01BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x01C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Create_Canvas_Space_Triangles");
static_assert(sizeof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles) == 0x0001C8, "Wrong size on Weather_Override_Volume_C_Create_Canvas_Space_Triangles");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, Corner_Position) == 0x000000, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::Corner_Position' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, Width) == 0x000010, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::Width' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, Resolution) == 0x000018, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::Resolution' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000020, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Conv_Vector2DToVector_ReturnValue) == 0x000028, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Conv_Vector2DToVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000040, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_MakeVector_ReturnValue) == 0x000048, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_MakeVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, Temp_int_Variable) == 0x000060, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Multiply_IntInt_ReturnValue) == 0x000064, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Multiply_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Add_IntInt_ReturnValue) == 0x000068, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Add_IntInt_ReturnValue_1) == 0x00006C, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Array_Get_Item) == 0x000070, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Add_IntInt_ReturnValue_2) == 0x000088, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Pos) == 0x000090, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Pos' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Color) == 0x0000A0, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Color' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Array_Get_Item_1) == 0x0000B0, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Array_Get_Item_2) == 0x0000C8, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Pos_1) == 0x0000E0, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Pos_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Color_1) == 0x0000F0, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Color_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Pos_2) == 0x000100, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Pos_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Color_2) == 0x000110, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Scale_And_Place_Vertex_in_Canvas_Space_Color_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, K2Node_MakeStruct_CanvasUVTri) == 0x000120, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::K2Node_MakeStruct_CanvasUVTri' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Array_Length_ReturnValue) == 0x0001B0, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Array_Add_ReturnValue) == 0x0001B4, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Divide_IntInt_ReturnValue) == 0x0001B8, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Divide_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_Subtract_IntInt_ReturnValue) == 0x0001BC, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_Canvas_Space_Triangles, CallFunc_LessEqual_IntInt_ReturnValue) == 0x0001C0, "Member 'Weather_Override_Volume_C_Create_Canvas_Space_Triangles::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Create World Space Drawing Geometry
// 0x0390 (0x0390 - 0x0000)
struct Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry final
{
public:
	TArray<struct FVector2D>                      TwoD_Inner_Loop;                                   // 0x0000(0x0010)(Edit, BlueprintVisible)
	TArray<struct FEdgeID>                        Edge_IDs;                                          // 0x0010(0x0010)(Edit, BlueprintVisible)
	TArray<struct FVertexInstanceID>              Vertex_Instance_IDs;                               // 0x0020(0x0010)(Edit, BlueprintVisible)
	TArray<struct FVertexID>                      Vertex_IDs;                                        // 0x0030(0x0010)(Edit, BlueprintVisible)
	int32                                         Next_Vert;                                         // 0x0040(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Current_Vert;                                      // 0x0044(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Point_B_Distance;                                  // 0x0048(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Point_A_Distance;                                  // 0x004C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FVector>                        Inner_Loop_Verts;                                  // 0x0050(0x0010)(Edit, BlueprintVisible)
	TArray<struct FVector>                        Outer_Loop_Verts;                                  // 0x0060(0x0010)(Edit, BlueprintVisible)
	TArray<struct FVector>                        Middle_Loop_Verts;                                 // 0x0070(0x0010)(Edit, BlueprintVisible)
	int32                                         Loop_Vertices;                                     // 0x0080(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x008C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A1[0x3];                                       // 0x00A1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x00A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x00A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x00AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_3;                 // 0x00B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x00B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_4;                 // 0x00BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x00C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C4[0x4];                                       // 0x00C4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Array_Get_Item;                           // 0x00C8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E4[0x4];                                       // 0x00E4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue;        // 0x00E8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F9[0x3];                                       // 0x00F9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x00FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_1;                         // 0x0100(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_2;                         // 0x0118(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_3;                         // 0x0130(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_4;                         // 0x0148(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_5;                         // 0x0160(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_6;                         // 0x0178(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_7;                         // 0x0190(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item_8;                         // 0x01A8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x01C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x01C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetSplineLength_ReturnValue;              // 0x01C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x01CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1CD[0x3];                                      // 0x01CD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Lerp_ReturnValue;                         // 0x01D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_5;                 // 0x01D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue;               // 0x01DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetNumberOfSplinePoints_ReturnValue;      // 0x01E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_1;             // 0x01E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x01E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1E9[0x3];                                      // 0x01E9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_GetDistanceAlongSplineAtSplinePoint_ReturnValue; // 0x01EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetDistanceAlongSplineAtSplinePoint_ReturnValue_1; // 0x01F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1F4[0x4];                                      // 0x01F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_SelectFloat_ReturnValue;                  // 0x01F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetNumberOfSplinePoints_ReturnValue_1;    // 0x0200(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0204(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x0208(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_209[0x7];                                      // 0x0209(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0210(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_GetRightVectorAtDistanceAlongSpline_ReturnValue; // 0x0218(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0230(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_DoubleToVector_ReturnValue;          // 0x0238(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_DoubleToVector_ReturnValue_1;        // 0x0250(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Multiply_VectorVector_ReturnValue;        // 0x0268(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Multiply_VectorVector_ReturnValue_1;      // 0x0280(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue_1;      // 0x0298(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue_2;      // 0x02A8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_GetLocationAtDistanceAlongSpline_ReturnValue; // 0x02B8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue_3;      // 0x02D0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue;        // 0x02E0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Add_Vector2DVector2D_ReturnValue;         // 0x02F8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_1;                  // 0x0308(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_30C[0x4];                                      // 0x030C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue_1;      // 0x0310(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Add_Vector2DVector2D_ReturnValue_1;       // 0x0328(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_2;                  // 0x0338(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_33C[0x4];                                      // 0x033C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue_2;      // 0x0340(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_3;                  // 0x0358(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_35C[0x4];                                      // 0x035C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_SelectFloat_A_ImplicitCast;               // 0x0360(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Lerp_B_ImplicitCast;                      // 0x0368(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Lerp_A_ImplicitCast;                      // 0x0370(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetLocationAtDistanceAlongSpline_Distance_ImplicitCast; // 0x0378(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetRightVectorAtDistanceAlongSpline_Distance_ImplicitCast; // 0x037C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_SelectFloat_B_ImplicitCast;               // 0x0380(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_VariableSet_Point_B_Distance_ImplicitCast;  // 0x0388(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry");
static_assert(sizeof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry) == 0x000390, "Wrong size on Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, TwoD_Inner_Loop) == 0x000000, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::TwoD_Inner_Loop' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Edge_IDs) == 0x000010, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Edge_IDs' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Vertex_Instance_IDs) == 0x000020, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Vertex_Instance_IDs' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Vertex_IDs) == 0x000030, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Vertex_IDs' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Next_Vert) == 0x000040, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Next_Vert' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Current_Vert) == 0x000044, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Current_Vert' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Point_B_Distance) == 0x000048, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Point_B_Distance' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Point_A_Distance) == 0x00004C, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Point_A_Distance' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Inner_Loop_Verts) == 0x000050, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Inner_Loop_Verts' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Outer_Loop_Verts) == 0x000060, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Outer_Loop_Verts' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Middle_Loop_Verts) == 0x000070, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Middle_Loop_Verts' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Loop_Vertices) == 0x000080, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Loop_Vertices' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Temp_int_Loop_Counter_Variable) == 0x000084, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Temp_int_Variable) == 0x000088, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Add_IntInt_ReturnValue) == 0x00008C, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000090, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000098, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_LessEqual_IntInt_ReturnValue) == 0x0000A0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Add_IntInt_ReturnValue_1) == 0x0000A4, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Temp_int_Variable_1) == 0x0000A8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Add_IntInt_ReturnValue_2) == 0x0000AC, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Add_IntInt_ReturnValue_3) == 0x0000B0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Add_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Temp_int_Array_Index_Variable) == 0x0000B4, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Temp_int_Loop_Counter_Variable_1) == 0x0000B8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Add_IntInt_ReturnValue_4) == 0x0000BC, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Add_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, Temp_int_Array_Index_Variable_1) == 0x0000C0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Get_Item) == 0x0000C8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Length_ReturnValue) == 0x0000E0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_VectorToVector2D_ReturnValue) == 0x0000E8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_VectorToVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Less_IntInt_ReturnValue) == 0x0000F8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Add_ReturnValue) == 0x0000FC, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Get_Item_1) == 0x000100, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Get_Item_2) == 0x000118, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Get_Item_3) == 0x000130, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Get_Item_4) == 0x000148, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Get_Item_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Get_Item_5) == 0x000160, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Get_Item_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Get_Item_6) == 0x000178, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Get_Item_6' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Get_Item_7) == 0x000190, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Get_Item_7' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Get_Item_8) == 0x0001A8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Get_Item_8' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Length_ReturnValue_1) == 0x0001C0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Length_ReturnValue_2) == 0x0001C4, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_GetSplineLength_ReturnValue) == 0x0001C8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_GetSplineLength_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Less_IntInt_ReturnValue_1) == 0x0001CC, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Lerp_ReturnValue) == 0x0001D0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Lerp_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Add_IntInt_ReturnValue_5) == 0x0001D8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Add_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Percent_IntInt_ReturnValue) == 0x0001DC, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Percent_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_GetNumberOfSplinePoints_ReturnValue) == 0x0001E0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_GetNumberOfSplinePoints_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Percent_IntInt_ReturnValue_1) == 0x0001E4, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Percent_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0001E8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_GetDistanceAlongSplineAtSplinePoint_ReturnValue) == 0x0001EC, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_GetDistanceAlongSplineAtSplinePoint_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_GetDistanceAlongSplineAtSplinePoint_ReturnValue_1) == 0x0001F0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_GetDistanceAlongSplineAtSplinePoint_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_SelectFloat_ReturnValue) == 0x0001F8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_SelectFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_GetNumberOfSplinePoints_ReturnValue_1) == 0x000200, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_GetNumberOfSplinePoints_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Subtract_IntInt_ReturnValue) == 0x000204, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x000208, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000210, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_GetRightVectorAtDistanceAlongSpline_ReturnValue) == 0x000218, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_GetRightVectorAtDistanceAlongSpline_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000230, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_DoubleToVector_ReturnValue) == 0x000238, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_DoubleToVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_DoubleToVector_ReturnValue_1) == 0x000250, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_DoubleToVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Multiply_VectorVector_ReturnValue) == 0x000268, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Multiply_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Multiply_VectorVector_ReturnValue_1) == 0x000280, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Multiply_VectorVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_VectorToVector2D_ReturnValue_1) == 0x000298, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_VectorToVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_VectorToVector2D_ReturnValue_2) == 0x0002A8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_VectorToVector2D_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_GetLocationAtDistanceAlongSpline_ReturnValue) == 0x0002B8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_GetLocationAtDistanceAlongSpline_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_VectorToVector2D_ReturnValue_3) == 0x0002D0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_VectorToVector2D_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_Vector2DToVector_ReturnValue) == 0x0002E0, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_Vector2DToVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Add_Vector2DVector2D_ReturnValue) == 0x0002F8, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Add_Vector2DVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Add_ReturnValue_1) == 0x000308, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Add_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_Vector2DToVector_ReturnValue_1) == 0x000310, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_Vector2DToVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Add_Vector2DVector2D_ReturnValue_1) == 0x000328, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Add_Vector2DVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Add_ReturnValue_2) == 0x000338, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Add_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Conv_Vector2DToVector_ReturnValue_2) == 0x000340, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Conv_Vector2DToVector_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Array_Add_ReturnValue_3) == 0x000358, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Array_Add_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_SelectFloat_A_ImplicitCast) == 0x000360, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_SelectFloat_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Lerp_B_ImplicitCast) == 0x000368, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Lerp_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_Lerp_A_ImplicitCast) == 0x000370, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_Lerp_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_GetLocationAtDistanceAlongSpline_Distance_ImplicitCast) == 0x000378, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_GetLocationAtDistanceAlongSpline_Distance_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_GetRightVectorAtDistanceAlongSpline_Distance_ImplicitCast) == 0x00037C, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_GetRightVectorAtDistanceAlongSpline_Distance_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, CallFunc_SelectFloat_B_ImplicitCast) == 0x000380, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::CallFunc_SelectFloat_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry, K2Node_VariableSet_Point_B_Distance_ImplicitCast) == 0x000388, "Member 'Weather_Override_Volume_C_Create_World_Space_Drawing_Geometry::K2Node_VariableSet_Point_B_Distance_ImplicitCast' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Custom Volume Behavior
// 0x0018 (0x0018 - 0x0000)
struct Weather_Override_Volume_C_Custom_Volume_Behavior final
{
public:
	double                                        Alpha;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   UDS;                                               // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               UDW_0;                                             // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Custom_Volume_Behavior) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Custom_Volume_Behavior");
static_assert(sizeof(Weather_Override_Volume_C_Custom_Volume_Behavior) == 0x000018, "Wrong size on Weather_Override_Volume_C_Custom_Volume_Behavior");
static_assert(offsetof(Weather_Override_Volume_C_Custom_Volume_Behavior, Alpha) == 0x000000, "Member 'Weather_Override_Volume_C_Custom_Volume_Behavior::Alpha' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Custom_Volume_Behavior, UDS) == 0x000008, "Member 'Weather_Override_Volume_C_Custom_Volume_Behavior::UDS' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Custom_Volume_Behavior, UDW_0) == 0x000010, "Member 'Weather_Override_Volume_C_Custom_Volume_Behavior::UDW_0' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.ExecuteUbergraph_Weather_Override_Volume
// 0x00F8 (0x00F8 - 0x0000)
struct Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Time_to_Transition_to_Random_Weather__Seconds_; // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_RandomWeatherTiming                      K2Node_CustomEvent_Random_Weather_Mode;            // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FMax_ReturnValue;                         // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasAuthority_ReturnValue;                 // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsComponentTickEnabled_ReturnValue;       // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x0022(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_23[0x5];                                       // 0x0023(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                K2Node_CustomEvent_New_Weather_Type;               // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Time_To_Transition_To_New_Weather__Seconds_; // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMax_ReturnValue_1;                       // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue;            // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_3;                // 0x0042(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_43[0x5];                                       // 0x0043(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                CallFunc_SpawnObject_ReturnValue;                  // 0x0048(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_Event_DeltaSeconds;                         // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0054(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0055(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEndPlayReason                                K2Node_Event_EndPlayReason;                        // 0x0056(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_57[0x1];                                       // 0x0057(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<EEndPlayReason>                        K2Node_MakeArray_Array;                            // 0x0058(0x0010)(ConstParm, ReferenceParm)
	bool                                          CallFunc_Array_Contains_ReturnValue;               // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0069(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6A[0x6];                                       // 0x006A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue;         // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_SafeDivide_ReturnValue;                   // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue;       // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasAuthority_ReturnValue_1;               // 0x0091(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_92[0x6];                                       // 0x0092(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                K2Node_Select_Default;                             // 0x0098(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                K2Node_Select_Default_1;                           // 0x00A0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue_1;          // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A9[0x7];                                       // 0x00A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_1;      // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_RemoveItem_ReturnValue;             // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C1[0x7];                                       // 0x00C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FMax_ReturnValue_2;                       // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               CallFunc_GetActorOfClass_ReturnValue;              // 0x00D0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x00D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D9[0x3];                                       // 0x00D9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class AActor* Actor, EEndPlayReason EndPlayReason)> K2Node_CreateDelegate_OutputDelegate; // 0x00DC(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue_2;          // 0x00EC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x00ED(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_4;                // 0x00EE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_5;                // 0x00EF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DoubleDouble_ReturnValue;      // 0x00F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume");
static_assert(sizeof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume) == 0x0000F8, "Wrong size on Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, EntryPoint) == 0x000000, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::EntryPoint' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Not_PreBool_ReturnValue) == 0x000004, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_CustomEvent_Time_to_Transition_to_Random_Weather__Seconds_) == 0x000008, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_CustomEvent_Time_to_Transition_to_Random_Weather__Seconds_' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_CustomEvent_Random_Weather_Mode) == 0x000010, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_CustomEvent_Random_Weather_Mode' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_FMax_ReturnValue) == 0x000018, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_FMax_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_HasAuthority_ReturnValue) == 0x000020, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_HasAuthority_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_IsComponentTickEnabled_ReturnValue) == 0x000021, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_IsComponentTickEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Not_PreBool_ReturnValue_1) == 0x000022, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_CustomEvent_New_Weather_Type) == 0x000028, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_CustomEvent_New_Weather_Type' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_CustomEvent_Time_To_Transition_To_New_Weather__Seconds_) == 0x000030, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_CustomEvent_Time_To_Transition_To_New_Weather__Seconds_' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_FMax_ReturnValue_1) == 0x000038, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_FMax_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_NotEqual_ByteByte_ReturnValue) == 0x000040, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_NotEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Not_PreBool_ReturnValue_2) == 0x000041, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Not_PreBool_ReturnValue_3) == 0x000042, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Not_PreBool_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_SpawnObject_ReturnValue) == 0x000048, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_SpawnObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_Event_DeltaSeconds) == 0x000050, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_Event_DeltaSeconds' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000054, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_IsValid_ReturnValue) == 0x000055, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_Event_EndPlayReason) == 0x000056, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_Event_EndPlayReason' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_MakeArray_Array) == 0x000058, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Array_Contains_ReturnValue) == 0x000068, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Array_Contains_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, Temp_bool_Variable) == 0x000069, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_GetWorldDeltaSeconds_ReturnValue) == 0x000070, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_GetWorldDeltaSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, Temp_bool_Variable_1) == 0x000078, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_SafeDivide_ReturnValue) == 0x000080, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_SafeDivide_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000088, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_LessEqual_DoubleDouble_ReturnValue) == 0x000090, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_LessEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_HasAuthority_ReturnValue_1) == 0x000091, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_HasAuthority_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_Select_Default) == 0x000098, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_Select_Default_1) == 0x0000A0, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_NotEqual_ByteByte_ReturnValue_1) == 0x0000A8, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_NotEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0000B0, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Subtract_DoubleDouble_ReturnValue_1) == 0x0000B8, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Subtract_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Array_RemoveItem_ReturnValue) == 0x0000C0, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Array_RemoveItem_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_FMax_ReturnValue_2) == 0x0000C8, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_FMax_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_GetActorOfClass_ReturnValue) == 0x0000D0, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_GetActorOfClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_IsValid_ReturnValue_1) == 0x0000D8, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, K2Node_CreateDelegate_OutputDelegate) == 0x0000DC, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_NotEqual_ByteByte_ReturnValue_2) == 0x0000EC, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_NotEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x0000ED, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Not_PreBool_ReturnValue_4) == 0x0000EE, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Not_PreBool_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_Not_PreBool_ReturnValue_5) == 0x0000EF, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_Not_PreBool_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_IsValid_ReturnValue_2) == 0x0000F0, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume, CallFunc_EqualEqual_DoubleDouble_ReturnValue) == 0x0000F1, "Member 'Weather_Override_Volume_C_ExecuteUbergraph_Weather_Override_Volume::CallFunc_EqualEqual_DoubleDouble_ReturnValue' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Get State for Saving
// 0x0230 (0x0230 - 0x0000)
struct Weather_Override_Volume_C_Get_State_for_Saving final
{
public:
	struct FUDW_WOV_State                         State;                                             // 0x0000(0x00D0)(Parm, OutParm, HasGetValueTypeHash)
	struct FUDW_WeatherState_Structure            CallFunc_Weather_State_Object_to_Structure_Structure; // 0x00D0(0x0050)(IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRandomWeatherVariation_State          CallFunc_Get_State_for_Saving_State;               // 0x0120(0x0040)(HasGetValueTypeHash)
	struct FUDW_WOV_State                         K2Node_MakeStruct_UDW_WOV_State;                   // 0x0160(0x00D0)(HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Get_State_for_Saving) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Get_State_for_Saving");
static_assert(sizeof(Weather_Override_Volume_C_Get_State_for_Saving) == 0x000230, "Wrong size on Weather_Override_Volume_C_Get_State_for_Saving");
static_assert(offsetof(Weather_Override_Volume_C_Get_State_for_Saving, State) == 0x000000, "Member 'Weather_Override_Volume_C_Get_State_for_Saving::State' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Get_State_for_Saving, CallFunc_Weather_State_Object_to_Structure_Structure) == 0x0000D0, "Member 'Weather_Override_Volume_C_Get_State_for_Saving::CallFunc_Weather_State_Object_to_Structure_Structure' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Get_State_for_Saving, CallFunc_Get_State_for_Saving_State) == 0x000120, "Member 'Weather_Override_Volume_C_Get_State_for_Saving::CallFunc_Get_State_for_Saving_State' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Get_State_for_Saving, K2Node_MakeStruct_UDW_WOV_State) == 0x000160, "Member 'Weather_Override_Volume_C_Get_State_for_Saving::K2Node_MakeStruct_UDW_WOV_State' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Increment Material State
// 0x0001 (0x0001 - 0x0000)
struct Weather_Override_Volume_C_Increment_Material_State final
{
public:
	bool                                          CallFunc_Increment_Material_State_Changed;         // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Increment_Material_State) == 0x000001, "Wrong alignment on Weather_Override_Volume_C_Increment_Material_State");
static_assert(sizeof(Weather_Override_Volume_C_Increment_Material_State) == 0x000001, "Wrong size on Weather_Override_Volume_C_Increment_Material_State");
static_assert(offsetof(Weather_Override_Volume_C_Increment_Material_State, CallFunc_Increment_Material_State_Changed) == 0x000000, "Member 'Weather_Override_Volume_C_Increment_Material_State::CallFunc_Increment_Material_State_Changed' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Initialize Spline Data
// 0x0140 (0x0140 - 0x0000)
struct Weather_Override_Volume_C_Initialize_Spline_Data final
{
public:
	TArray<struct FVector>                        Spline_Points;                                     // 0x0000(0x0010)(Edit, BlueprintVisible)
	struct FVector                                CallFunc_GetActorScale3D_ReturnValue;              // 0x0010(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector_X;                            // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_54[0x4];                                       // 0x0054(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_K2_GetActorLocation_ReturnValue;          // 0x0058(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsEmpty_ReturnValue;                // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_71[0x3];                                       // 0x0071(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0074(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0078(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x007C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7D[0x3];                                       // 0x007D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_GetVectorArrayAverage_ReturnValue;        // 0x0080(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue;        // 0x0098(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x00B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B5[0x3];                                       // 0x00B5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x00BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item;                           // 0x00C0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_GetLocationAtSplinePoint_ReturnValue;     // 0x00D8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x00F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F4[0x4];                                       // 0x00F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetNumberOfSplinePoints_ReturnValue;      // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_104[0x4];                                      // 0x0104(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_MakeVector_ReturnValue;                   // 0x0108(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0120(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x0128(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0130(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0134(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_135[0x3];                                      // 0x0135(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_SetClosedLoopAtPosition_Key_ImplicitCast; // 0x0138(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Initialize_Spline_Data) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Initialize_Spline_Data");
static_assert(sizeof(Weather_Override_Volume_C_Initialize_Spline_Data) == 0x000140, "Wrong size on Weather_Override_Volume_C_Initialize_Spline_Data");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, Spline_Points) == 0x000000, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::Spline_Points' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_GetActorScale3D_ReturnValue) == 0x000010, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_GetActorScale3D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, Temp_int_Variable) == 0x000028, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_BreakVector_X) == 0x000030, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_BreakVector_Y) == 0x000038, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_BreakVector_Z) == 0x000040, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000048, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Add_IntInt_ReturnValue) == 0x000050, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_K2_GetActorLocation_ReturnValue) == 0x000058, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_K2_GetActorLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Array_IsEmpty_ReturnValue) == 0x000070, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Array_IsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Array_Length_ReturnValue) == 0x000074, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Array_Length_ReturnValue_1) == 0x000078, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Greater_IntInt_ReturnValue) == 0x00007C, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_GetVectorArrayAverage_ReturnValue) == 0x000080, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_GetVectorArrayAverage_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Subtract_VectorVector_ReturnValue) == 0x000098, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Subtract_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, Temp_int_Loop_Counter_Variable) == 0x0000B0, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Less_IntInt_ReturnValue) == 0x0000B4, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Add_IntInt_ReturnValue_1) == 0x0000B8, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, Temp_int_Array_Index_Variable) == 0x0000BC, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Array_Get_Item) == 0x0000C0, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_GetLocationAtSplinePoint_ReturnValue) == 0x0000D8, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_GetLocationAtSplinePoint_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Array_Add_ReturnValue) == 0x0000F0, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x0000F8, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_GetNumberOfSplinePoints_ReturnValue) == 0x000100, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_GetNumberOfSplinePoints_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_MakeVector_ReturnValue) == 0x000108, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_MakeVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000120, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Add_DoubleDouble_ReturnValue) == 0x000128, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_Subtract_IntInt_ReturnValue) == 0x000130, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000134, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Initialize_Spline_Data, CallFunc_SetClosedLoopAtPosition_Key_ImplicitCast) == 0x000138, "Member 'Weather_Override_Volume_C_Initialize_Spline_Data::CallFunc_SetClosedLoopAtPosition_Key_ImplicitCast' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Is Point In Triangle
// 0x0198 (0x0198 - 0x0000)
struct Weather_Override_Volume_C_Is_Point_In_Triangle final
{
public:
	struct FVector2D                              Point;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              v1;                                                // 0x0010(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              v2;                                                // 0x0020(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              V3;                                                // 0x0030(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Yes;                                               // 0x0040(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_1;                        // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_1;                        // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_2;                        // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_2;                        // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_1;      // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_2;      // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_3;      // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_4;      // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_3;                        // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_3;                        // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C1[0x7];                                       // 0x00C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X_4;                        // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_4;                        // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_5;                        // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_5;                        // 0x00E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_5;      // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_6;      // 0x00F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_7;      // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_8;      // 0x0100(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x0108(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_3;      // 0x0110(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_9;      // 0x0118(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_6;                        // 0x0120(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_6;                        // 0x0128(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_1;       // 0x0130(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_BoolBool_ReturnValue;            // 0x0131(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_132[0x6];                                      // 0x0132(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X_7;                        // 0x0138(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_7;                        // 0x0140(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_10;     // 0x0148(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_8;                        // 0x0150(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_8;                        // 0x0158(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_11;     // 0x0160(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_12;     // 0x0168(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_13;     // 0x0170(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_4;      // 0x0178(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_5;      // 0x0180(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_14;     // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_2;       // 0x0190(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_BoolBool_ReturnValue_1;          // 0x0191(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_BoolBool_ReturnValue_2;          // 0x0192(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0193(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x0194(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0195(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Is_Point_In_Triangle) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Is_Point_In_Triangle");
static_assert(sizeof(Weather_Override_Volume_C_Is_Point_In_Triangle) == 0x000198, "Wrong size on Weather_Override_Volume_C_Is_Point_In_Triangle");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, Point) == 0x000000, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::Point' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, v1) == 0x000010, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::v1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, v2) == 0x000020, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::v2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, V3) == 0x000030, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::V3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, Yes) == 0x000040, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::Yes' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_X) == 0x000048, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_Y) == 0x000050, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_X_1) == 0x000058, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_Y_1) == 0x000060, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000068, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_X_2) == 0x000070, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_X_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_Y_2) == 0x000078, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_Y_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_1) == 0x000080, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_2) == 0x000088, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_3) == 0x000090, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000098, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x0000A0, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_4) == 0x0000A8, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_X_3) == 0x0000B0, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_X_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_Y_3) == 0x0000B8, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_Y_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x0000C0, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_X_4) == 0x0000C8, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_X_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_Y_4) == 0x0000D0, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_Y_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_X_5) == 0x0000D8, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_X_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_Y_5) == 0x0000E0, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_Y_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_5) == 0x0000E8, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_6) == 0x0000F0, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_7) == 0x0000F8, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_8) == 0x000100, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x000108, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Multiply_DoubleDouble_ReturnValue_3) == 0x000110, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Multiply_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_9) == 0x000118, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_X_6) == 0x000120, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_X_6' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_Y_6) == 0x000128, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_Y_6' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Greater_DoubleDouble_ReturnValue_1) == 0x000130, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Greater_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_NotEqual_BoolBool_ReturnValue) == 0x000131, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_NotEqual_BoolBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_X_7) == 0x000138, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_X_7' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_Y_7) == 0x000140, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_Y_7' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_10) == 0x000148, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_X_8) == 0x000150, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_X_8' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BreakVector2D_Y_8) == 0x000158, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BreakVector2D_Y_8' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_11) == 0x000160, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_12) == 0x000168, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_13) == 0x000170, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Multiply_DoubleDouble_ReturnValue_4) == 0x000178, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Multiply_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Multiply_DoubleDouble_ReturnValue_5) == 0x000180, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Multiply_DoubleDouble_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Subtract_DoubleDouble_ReturnValue_14) == 0x000188, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Subtract_DoubleDouble_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Greater_DoubleDouble_ReturnValue_2) == 0x000190, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Greater_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_NotEqual_BoolBool_ReturnValue_1) == 0x000191, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_NotEqual_BoolBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_NotEqual_BoolBool_ReturnValue_2) == 0x000192, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_NotEqual_BoolBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BooleanOR_ReturnValue) == 0x000193, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_BooleanOR_ReturnValue_1) == 0x000194, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Is_Point_In_Triangle, CallFunc_Not_PreBool_ReturnValue) == 0x000195, "Member 'Weather_Override_Volume_C_Is_Point_In_Triangle::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.ReceiveEndPlay
// 0x0001 (0x0001 - 0x0000)
struct Weather_Override_Volume_C_ReceiveEndPlay final
{
public:
	EEndPlayReason                                EndPlayReason;                                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_ReceiveEndPlay) == 0x000001, "Wrong alignment on Weather_Override_Volume_C_ReceiveEndPlay");
static_assert(sizeof(Weather_Override_Volume_C_ReceiveEndPlay) == 0x000001, "Wrong size on Weather_Override_Volume_C_ReceiveEndPlay");
static_assert(offsetof(Weather_Override_Volume_C_ReceiveEndPlay, EndPlayReason) == 0x000000, "Member 'Weather_Override_Volume_C_ReceiveEndPlay::EndPlayReason' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.ReceiveTick
// 0x0004 (0x0004 - 0x0000)
struct Weather_Override_Volume_C_ReceiveTick final
{
public:
	float                                         DeltaSeconds;                                      // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_ReceiveTick) == 0x000004, "Wrong alignment on Weather_Override_Volume_C_ReceiveTick");
static_assert(sizeof(Weather_Override_Volume_C_ReceiveTick) == 0x000004, "Wrong size on Weather_Override_Volume_C_ReceiveTick");
static_assert(offsetof(Weather_Override_Volume_C_ReceiveTick, DeltaSeconds) == 0x000000, "Member 'Weather_Override_Volume_C_ReceiveTick::DeltaSeconds' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Sample Point for Current Alpha
// 0x0148 (0x0148 - 0x0000)
struct Weather_Override_Volume_C_Sample_Point_for_Current_Alpha final
{
public:
	struct FVector                                Location;                                          // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Alpha;                                             // 0x0018(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X;                            // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue;      // 0x0048(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X_1;                          // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y_1;                          // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z_1;                          // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_MakeVector_ReturnValue;                   // 0x0078(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_FindLocationClosestToWorldLocation_ReturnValue; // 0x0090(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_FindRightVectorClosestToWorldLocation_ReturnValue; // 0x00A8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue;        // 0x00C0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue;        // 0x00D8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue_1;      // 0x00E8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_VSize_ReturnValue;                        // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Normal2D_ReturnValue;                     // 0x0100(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_DotProduct2D_ReturnValue;                 // 0x0110(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0118(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_119[0x7];                                      // 0x0119(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_SelectFloat_ReturnValue;                  // 0x0120(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0128(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0130(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Ease_ReturnValue;                         // 0x0138(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x0140(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Sample_Point_for_Current_Alpha");
static_assert(sizeof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha) == 0x000148, "Wrong size on Weather_Override_Volume_C_Sample_Point_for_Current_Alpha");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, Location) == 0x000000, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::Location' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, Alpha) == 0x000018, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::Alpha' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000020, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000028, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_BreakVector_X) == 0x000030, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_BreakVector_Y) == 0x000038, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_BreakVector_Z) == 0x000040, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_K2_GetComponentLocation_ReturnValue) == 0x000048, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_K2_GetComponentLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_BreakVector_X_1) == 0x000060, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_BreakVector_X_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_BreakVector_Y_1) == 0x000068, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_BreakVector_Y_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_BreakVector_Z_1) == 0x000070, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_BreakVector_Z_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_MakeVector_ReturnValue) == 0x000078, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_MakeVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_FindLocationClosestToWorldLocation_ReturnValue) == 0x000090, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_FindLocationClosestToWorldLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_FindRightVectorClosestToWorldLocation_ReturnValue) == 0x0000A8, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_FindRightVectorClosestToWorldLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Subtract_VectorVector_ReturnValue) == 0x0000C0, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Subtract_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Conv_VectorToVector2D_ReturnValue) == 0x0000D8, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Conv_VectorToVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Conv_VectorToVector2D_ReturnValue_1) == 0x0000E8, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Conv_VectorToVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_VSize_ReturnValue) == 0x0000F8, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_VSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Normal2D_ReturnValue) == 0x000100, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Normal2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_DotProduct2D_ReturnValue) == 0x000110, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_DotProduct2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000118, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_SelectFloat_ReturnValue) == 0x000120, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_SelectFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000128, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_MapRangeClamped_ReturnValue) == 0x000130, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Ease_ReturnValue) == 0x000138, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Ease_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Sample_Point_for_Current_Alpha, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x000140, "Member 'Weather_Override_Volume_C_Sample_Point_for_Current_Alpha::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Scale And Place Vertex in Canvas Space
// 0x00A8 (0x00A8 - 0x0000)
struct Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space final
{
public:
	struct FVector                                In;                                                // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Pos;                                               // 0x0018(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Color;                                             // 0x0028(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue;        // 0x0038(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Divide_VectorVector_ReturnValue;          // 0x0050(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue;        // 0x0068(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X;                            // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x0090(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast;                  // 0x00A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space");
static_assert(sizeof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space) == 0x0000A8, "Wrong size on Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, In) == 0x000000, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::In' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, Pos) == 0x000018, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::Pos' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, Color) == 0x000028, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::Color' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, CallFunc_Subtract_VectorVector_ReturnValue) == 0x000038, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::CallFunc_Subtract_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, CallFunc_Divide_VectorVector_ReturnValue) == 0x000050, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::CallFunc_Divide_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, CallFunc_Conv_VectorToVector2D_ReturnValue) == 0x000068, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::CallFunc_Conv_VectorToVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, CallFunc_BreakVector_X) == 0x000078, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, CallFunc_BreakVector_Y) == 0x000080, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, CallFunc_BreakVector_Z) == 0x000088, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, K2Node_MakeStruct_LinearColor) == 0x000090, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space, K2Node_MakeStruct_A_ImplicitCast) == 0x0000A0, "Member 'Weather_Override_Volume_C_Scale_And_Place_Vertex_in_Canvas_Space::K2Node_MakeStruct_A_ImplicitCast' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Start Up WOV
// 0x0003 (0x0003 - 0x0000)
struct Weather_Override_Volume_C_Start_Up_WOV final
{
public:
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Start_Up_WOV) == 0x000001, "Wrong alignment on Weather_Override_Volume_C_Start_Up_WOV");
static_assert(sizeof(Weather_Override_Volume_C_Start_Up_WOV) == 0x000003, "Wrong size on Weather_Override_Volume_C_Start_Up_WOV");
static_assert(offsetof(Weather_Override_Volume_C_Start_Up_WOV, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000000, "Member 'Weather_Override_Volume_C_Start_Up_WOV::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Start_Up_WOV, CallFunc_Not_PreBool_ReturnValue) == 0x000001, "Member 'Weather_Override_Volume_C_Start_Up_WOV::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Start_Up_WOV, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000002, "Member 'Weather_Override_Volume_C_Start_Up_WOV::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Triangulate Polygon
// 0x02A0 (0x02A0 - 0x0000)
struct Weather_Override_Volume_C_Triangulate_Polygon final
{
public:
	TArray<struct FVector2D>                      Vertices__Clockwise_;                              // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	int32                                         Iterations;                                        // 0x0010(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Max_Iterations;                                    // 0x0014(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Triangle_Contains_Vertex;                          // 0x0018(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<int32>                                 Triangle_Indexes;                                  // 0x0020(0x0010)(Edit, BlueprintVisible)
	struct FVector2D                              Next_Vertex;                                       // 0x0030(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Last_Vertex;                                       // 0x0040(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Current_Vertex;                                    // 0x0050(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Current_Index;                                     // 0x0060(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_64[0x4];                                       // 0x0064(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FVector2D>                      Remaining_Verts;                                   // 0x0068(0x0010)(Edit, BlueprintVisible)
	bool                                          Temp_bool_True_if_break_was_hit_Variable;          // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79[0x3];                                       // 0x0079(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x007C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0080(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_81[0x3];                                       // 0x0081(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x008C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0090(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x0094(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_3;                 // 0x0098(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x009C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x00A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A9[0x3];                                       // 0x00A9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Multiply_IntInt_ReturnValue;              // 0x00AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x00B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x00B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B5[0x3];                                       // 0x00B5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_Array_Get_Item;                           // 0x00B8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Array_Get_Item_1;                         // 0x00C8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue;        // 0x00D8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue_1;      // 0x00F0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Array_Get_Item_2;                         // 0x0108(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_4;                 // 0x0118(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11C[0x4];                                      // 0x011C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue_2;      // 0x0120(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue_3;      // 0x0138(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue_4;      // 0x0150(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_Vector2DToVector_ReturnValue_5;      // 0x0168(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x0180(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_1;            // 0x0184(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_5;                 // 0x0188(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue;               // 0x018C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x0190(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_Contains_ReturnValue;               // 0x0191(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x0192(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_193[0x5];                                      // 0x0193(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_Array_Get_Item_3;                         // 0x0198(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Is_Point_In_Triangle_Yes;                 // 0x01A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A9[0x3];                                      // 0x01A9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_3;               // 0x01AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x01B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x01B1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1B2[0x2];                                      // 0x01B2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_4;               // 0x01B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_6;                 // 0x01B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1BC[0x4];                                      // 0x01BC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x01C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x01C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_1;             // 0x01D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D4[0x4];                                      // 0x01D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X_1;                        // 0x01D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_1;                        // 0x01E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x01E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X_2;                        // 0x01F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y_2;                        // 0x01F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_1;      // 0x0200(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_2;      // 0x0208(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_3;      // 0x0210(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0218(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0220(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_4;      // 0x0228(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Get_Item_4;                         // 0x0230(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0234(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_235[0x3];                                      // 0x0235(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_Array_Get_Item_5;                         // 0x0238(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Get_Item_6;                         // 0x0248(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Get_Item_7;                         // 0x024C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_5;               // 0x0250(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_2;            // 0x0254(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Array_Get_Item_8;                         // 0x0258(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_7;                 // 0x0268(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_2;             // 0x026C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_8;                 // 0x0270(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_3;             // 0x0274(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Array_Get_Item_9;                         // 0x0278(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<int32>                                 K2Node_MakeArray_Array;                            // 0x0288(0x0010)(ReferenceParm)
	int32                                         CallFunc_Array_Length_ReturnValue_6;               // 0x0298(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x029C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x029D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Triangulate_Polygon) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Triangulate_Polygon");
static_assert(sizeof(Weather_Override_Volume_C_Triangulate_Polygon) == 0x0002A0, "Wrong size on Weather_Override_Volume_C_Triangulate_Polygon");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Vertices__Clockwise_) == 0x000000, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Vertices__Clockwise_' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Iterations) == 0x000010, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Iterations' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Max_Iterations) == 0x000014, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Max_Iterations' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Triangle_Contains_Vertex) == 0x000018, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Triangle_Contains_Vertex' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Triangle_Indexes) == 0x000020, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Triangle_Indexes' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Next_Vertex) == 0x000030, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Next_Vertex' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Last_Vertex) == 0x000040, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Last_Vertex' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Current_Vertex) == 0x000050, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Current_Vertex' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Current_Index) == 0x000060, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Current_Index' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Remaining_Verts) == 0x000068, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Remaining_Verts' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Temp_bool_True_if_break_was_hit_Variable) == 0x000078, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Temp_bool_True_if_break_was_hit_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Temp_int_Array_Index_Variable) == 0x00007C, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Not_PreBool_ReturnValue) == 0x000080, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Temp_int_Variable) == 0x000084, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Temp_int_Loop_Counter_Variable) == 0x000088, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Add_IntInt_ReturnValue) == 0x00008C, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Add_IntInt_ReturnValue_1) == 0x000090, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Add_IntInt_ReturnValue_2) == 0x000094, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Add_IntInt_ReturnValue_3) == 0x000098, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Add_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Length_ReturnValue) == 0x00009C, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Subtract_IntInt_ReturnValue) == 0x0000A0, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Length_ReturnValue_1) == 0x0000A4, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_LessEqual_IntInt_ReturnValue) == 0x0000A8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Multiply_IntInt_ReturnValue) == 0x0000AC, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Multiply_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, Temp_int_Variable_1) == 0x0000B0, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x0000B4, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item) == 0x0000B8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item_1) == 0x0000C8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Conv_Vector2DToVector_ReturnValue) == 0x0000D8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Conv_Vector2DToVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Conv_Vector2DToVector_ReturnValue_1) == 0x0000F0, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Conv_Vector2DToVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item_2) == 0x000108, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Add_IntInt_ReturnValue_4) == 0x000118, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Add_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Conv_Vector2DToVector_ReturnValue_2) == 0x000120, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Conv_Vector2DToVector_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Conv_Vector2DToVector_ReturnValue_3) == 0x000138, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Conv_Vector2DToVector_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Conv_Vector2DToVector_ReturnValue_4) == 0x000150, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Conv_Vector2DToVector_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Conv_Vector2DToVector_ReturnValue_5) == 0x000168, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Conv_Vector2DToVector_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Length_ReturnValue_2) == 0x000180, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Subtract_IntInt_ReturnValue_1) == 0x000184, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Subtract_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Add_IntInt_ReturnValue_5) == 0x000188, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Add_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Percent_IntInt_ReturnValue) == 0x00018C, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Percent_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Not_PreBool_ReturnValue_1) == 0x000190, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Contains_ReturnValue) == 0x000191, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Contains_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Not_PreBool_ReturnValue_2) == 0x000192, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item_3) == 0x000198, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Is_Point_In_Triangle_Yes) == 0x0001A8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Is_Point_In_Triangle_Yes' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Length_ReturnValue_3) == 0x0001AC, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Length_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Less_IntInt_ReturnValue) == 0x0001B0, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_BooleanAND_ReturnValue) == 0x0001B1, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Length_ReturnValue_4) == 0x0001B4, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Length_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Add_IntInt_ReturnValue_6) == 0x0001B8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Add_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_BreakVector2D_X) == 0x0001C0, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_BreakVector2D_Y) == 0x0001C8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Percent_IntInt_ReturnValue_1) == 0x0001D0, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Percent_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_BreakVector2D_X_1) == 0x0001D8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_BreakVector2D_Y_1) == 0x0001E0, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x0001E8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_BreakVector2D_X_2) == 0x0001F0, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_BreakVector2D_X_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_BreakVector2D_Y_2) == 0x0001F8, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_BreakVector2D_Y_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Subtract_DoubleDouble_ReturnValue_1) == 0x000200, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Subtract_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Subtract_DoubleDouble_ReturnValue_2) == 0x000208, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Subtract_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Subtract_DoubleDouble_ReturnValue_3) == 0x000210, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Subtract_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000218, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000220, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Subtract_DoubleDouble_ReturnValue_4) == 0x000228, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Subtract_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item_4) == 0x000230, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item_4' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000234, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item_5) == 0x000238, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item_6) == 0x000248, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item_6' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item_7) == 0x00024C, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item_7' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Length_ReturnValue_5) == 0x000250, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Length_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Subtract_IntInt_ReturnValue_2) == 0x000254, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Subtract_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item_8) == 0x000258, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item_8' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Add_IntInt_ReturnValue_7) == 0x000268, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Add_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Percent_IntInt_ReturnValue_2) == 0x00026C, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Percent_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Add_IntInt_ReturnValue_8) == 0x000270, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Add_IntInt_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Percent_IntInt_ReturnValue_3) == 0x000274, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Percent_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Get_Item_9) == 0x000278, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Get_Item_9' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, K2Node_MakeArray_Array) == 0x000288, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Array_Length_ReturnValue_6) == 0x000298, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Array_Length_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_Greater_IntInt_ReturnValue) == 0x00029C, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Triangulate_Polygon, CallFunc_BooleanAND_ReturnValue_1) == 0x00029D, "Member 'Weather_Override_Volume_C_Triangulate_Polygon::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.UDW End Play
// 0x0010 (0x0010 - 0x0000)
struct Weather_Override_Volume_C_UDW_End_Play final
{
public:
	class AActor*                                 Actor;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	EEndPlayReason                                EndPlayReason;                                     // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_UDW_End_Play) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_UDW_End_Play");
static_assert(sizeof(Weather_Override_Volume_C_UDW_End_Play) == 0x000010, "Wrong size on Weather_Override_Volume_C_UDW_End_Play");
static_assert(offsetof(Weather_Override_Volume_C_UDW_End_Play, Actor) == 0x000000, "Member 'Weather_Override_Volume_C_UDW_End_Play::Actor' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_UDW_End_Play, EndPlayReason) == 0x000008, "Member 'Weather_Override_Volume_C_UDW_End_Play::EndPlayReason' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Update Material State Buffer
// 0x0018 (0x0018 - 0x0000)
struct Weather_Override_Volume_C_Update_Material_State_Buffer final
{
public:
	struct FVector                                CallFunc_MakeVector_ReturnValue;                   // 0x0000(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Update_Material_State_Buffer) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Update_Material_State_Buffer");
static_assert(sizeof(Weather_Override_Volume_C_Update_Material_State_Buffer) == 0x000018, "Wrong size on Weather_Override_Volume_C_Update_Material_State_Buffer");
static_assert(offsetof(Weather_Override_Volume_C_Update_Material_State_Buffer, CallFunc_MakeVector_ReturnValue) == 0x000000, "Member 'Weather_Override_Volume_C_Update_Material_State_Buffer::CallFunc_MakeVector_ReturnValue' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.Update Volume Color
// 0x0030 (0x0030 - 0x0000)
struct Weather_Override_Volume_C_Update_Volume_Color final
{
public:
	class AUltra_Dynamic_Weather_C*               UDW_0;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A[0x6];                                        // 0x000A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                K2Node_Select_Default;                             // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                K2Node_Select_Default_1;                           // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Material_Effect_Draw_Color_from_State_Color; // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_Update_Volume_Color) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_Update_Volume_Color");
static_assert(sizeof(Weather_Override_Volume_C_Update_Volume_Color) == 0x000030, "Wrong size on Weather_Override_Volume_C_Update_Volume_Color");
static_assert(offsetof(Weather_Override_Volume_C_Update_Volume_Color, UDW_0) == 0x000000, "Member 'Weather_Override_Volume_C_Update_Volume_Color::UDW_0' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Update_Volume_Color, Temp_bool_Variable) == 0x000008, "Member 'Weather_Override_Volume_C_Update_Volume_Color::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Update_Volume_Color, Temp_bool_Variable_1) == 0x000009, "Member 'Weather_Override_Volume_C_Update_Volume_Color::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Update_Volume_Color, K2Node_Select_Default) == 0x000010, "Member 'Weather_Override_Volume_C_Update_Volume_Color::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Update_Volume_Color, K2Node_Select_Default_1) == 0x000018, "Member 'Weather_Override_Volume_C_Update_Volume_Color::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_Update_Volume_Color, CallFunc_Material_Effect_Draw_Color_from_State_Color) == 0x000020, "Member 'Weather_Override_Volume_C_Update_Volume_Color::CallFunc_Material_Effect_Draw_Color_from_State_Color' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.UserConstructionScript
// 0x0020 (0x0020 - 0x0000)
struct Weather_Override_Volume_C_UserConstructionScript final
{
public:
	class UTextRenderComponent*                   Text_Component;                                    // 0x0000(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FString                                 Label;                                             // 0x0008(0x0010)(Edit, BlueprintVisible, ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsPackagedForDistribution_ReturnValue;    // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C_UserConstructionScript) == 0x000008, "Wrong alignment on Weather_Override_Volume_C_UserConstructionScript");
static_assert(sizeof(Weather_Override_Volume_C_UserConstructionScript) == 0x000020, "Wrong size on Weather_Override_Volume_C_UserConstructionScript");
static_assert(offsetof(Weather_Override_Volume_C_UserConstructionScript, Text_Component) == 0x000000, "Member 'Weather_Override_Volume_C_UserConstructionScript::Text_Component' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_UserConstructionScript, Label) == 0x000008, "Member 'Weather_Override_Volume_C_UserConstructionScript::Label' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_UserConstructionScript, CallFunc_IsPackagedForDistribution_ReturnValue) == 0x000018, "Member 'Weather_Override_Volume_C_UserConstructionScript::CallFunc_IsPackagedForDistribution_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C_UserConstructionScript, CallFunc_Not_PreBool_ReturnValue) == 0x000019, "Member 'Weather_Override_Volume_C_UserConstructionScript::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");

// Function Weather_Override_Volume.Weather_Override_Volume_C.📘 Weather Override Volumes
// 0x0010 (0x0010 - 0x0000)
struct Weather_Override_Volume_C___Weather_Override_Volumes final
{
public:
	class AUltra_Dynamic_Weather_C*               CallFunc_GetActorOfClass_ReturnValue;              // 0x0000(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Weather_Override_Volume_C___Weather_Override_Volumes) == 0x000008, "Wrong alignment on Weather_Override_Volume_C___Weather_Override_Volumes");
static_assert(sizeof(Weather_Override_Volume_C___Weather_Override_Volumes) == 0x000010, "Wrong size on Weather_Override_Volume_C___Weather_Override_Volumes");
static_assert(offsetof(Weather_Override_Volume_C___Weather_Override_Volumes, CallFunc_GetActorOfClass_ReturnValue) == 0x000000, "Member 'Weather_Override_Volume_C___Weather_Override_Volumes::CallFunc_GetActorOfClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(Weather_Override_Volume_C___Weather_Override_Volumes, CallFunc_IsValid_ReturnValue) == 0x000008, "Member 'Weather_Override_Volume_C___Weather_Override_Volumes::CallFunc_IsValid_ReturnValue' has a wrong offset!");

}

