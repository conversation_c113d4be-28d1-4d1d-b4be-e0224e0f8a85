﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDW_WOV_State

#include "Basic.hpp"

#include "RandomWeatherVariation_State_structs.hpp"
#include "UDW_WeatherState_Structure_structs.hpp"


namespace SDK
{

// UserDefinedStruct UDW_WOV_State.UDW_WOV_State
// 0x00D0 (0x00D0 - 0x0000)
struct FUDW_WOV_State final
{
public:
	struct FRandomWeatherVariation_State          RandomWeatherState_33_83DFCBE54A683EDAC05BE790FB5F3EA0; // 0x0000(0x0040)(Edit, BlueprintVisible, HasGetValueTypeHash)
	struct FUDW_WeatherState_Structure            WeatherState_34_4FFDBC3242825222AAC2499AB18E9F1F;  // 0x0040(0x0050)(Edit, BlueprintVisible, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          TransitioningWeather_35_30791493496F900274FC4E992E495ED2; // 0x0090(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_91[0x7];                                       // 0x0091(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        TransitionTimeRemaining_36_D121C14845888F83FB2D79B02E8E7C1F; // 0x0098(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        TransitionTimerLength_37_EA0E1D744D16061342667B81CE2960BF; // 0x00A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ChangingFromRandomWeather_38_81A770004EE629A4300BE98B923413E2; // 0x00A8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ChangingToRandomWeather_39_88E2A2CD468A5A629EB814ADE20B933A; // 0x00A9(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AA[0x6];                                       // 0x00AA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                Weather_25_CD874C774ED188AF4D11E58D7A34175A;       // 0x00B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                TransitionWeatherA_40_258D58714970D7F4E69BB8BD5521E9B5; // 0x00B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                TransitionWeatherB_41_51640EB94AA5AC815DC5569687237618; // 0x00C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        VolumeAlpha_42_4C5D2EB945C4BC5FFA0D688B3137FD43;   // 0x00C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(FUDW_WOV_State) == 0x000008, "Wrong alignment on FUDW_WOV_State");
static_assert(sizeof(FUDW_WOV_State) == 0x0000D0, "Wrong size on FUDW_WOV_State");
static_assert(offsetof(FUDW_WOV_State, RandomWeatherState_33_83DFCBE54A683EDAC05BE790FB5F3EA0) == 0x000000, "Member 'FUDW_WOV_State::RandomWeatherState_33_83DFCBE54A683EDAC05BE790FB5F3EA0' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, WeatherState_34_4FFDBC3242825222AAC2499AB18E9F1F) == 0x000040, "Member 'FUDW_WOV_State::WeatherState_34_4FFDBC3242825222AAC2499AB18E9F1F' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, TransitioningWeather_35_30791493496F900274FC4E992E495ED2) == 0x000090, "Member 'FUDW_WOV_State::TransitioningWeather_35_30791493496F900274FC4E992E495ED2' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, TransitionTimeRemaining_36_D121C14845888F83FB2D79B02E8E7C1F) == 0x000098, "Member 'FUDW_WOV_State::TransitionTimeRemaining_36_D121C14845888F83FB2D79B02E8E7C1F' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, TransitionTimerLength_37_EA0E1D744D16061342667B81CE2960BF) == 0x0000A0, "Member 'FUDW_WOV_State::TransitionTimerLength_37_EA0E1D744D16061342667B81CE2960BF' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, ChangingFromRandomWeather_38_81A770004EE629A4300BE98B923413E2) == 0x0000A8, "Member 'FUDW_WOV_State::ChangingFromRandomWeather_38_81A770004EE629A4300BE98B923413E2' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, ChangingToRandomWeather_39_88E2A2CD468A5A629EB814ADE20B933A) == 0x0000A9, "Member 'FUDW_WOV_State::ChangingToRandomWeather_39_88E2A2CD468A5A629EB814ADE20B933A' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, Weather_25_CD874C774ED188AF4D11E58D7A34175A) == 0x0000B0, "Member 'FUDW_WOV_State::Weather_25_CD874C774ED188AF4D11E58D7A34175A' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, TransitionWeatherA_40_258D58714970D7F4E69BB8BD5521E9B5) == 0x0000B8, "Member 'FUDW_WOV_State::TransitionWeatherA_40_258D58714970D7F4E69BB8BD5521E9B5' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, TransitionWeatherB_41_51640EB94AA5AC815DC5569687237618) == 0x0000C0, "Member 'FUDW_WOV_State::TransitionWeatherB_41_51640EB94AA5AC815DC5569687237618' has a wrong offset!");
static_assert(offsetof(FUDW_WOV_State, VolumeAlpha_42_4C5D2EB945C4BC5FFA0D688B3137FD43) == 0x0000C8, "Member 'FUDW_WOV_State::VolumeAlpha_42_4C5D2EB945C4BC5FFA0D688B3137FD43' has a wrong offset!");

}

