﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeWidgetStack

#include "Basic.hpp"

#include "CommonUI_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass WBP_NarrativeWidgetStack.WBP_NarrativeWidgetStack_C
// 0x0000 (0x02B0 - 0x02B0)
class UWBP_NarrativeWidgetStack_C final : public UCommonActivatableWidgetStack
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WBP_NarrativeWidgetStack_C">();
	}
	static class UWBP_NarrativeWidgetStack_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWBP_NarrativeWidgetStack_C>();
	}
};
static_assert(alignof(UWBP_NarrativeWidgetStack_C) == 0x000008, "Wrong alignment on UWBP_NarrativeWidgetStack_C");
static_assert(sizeof(UWBP_NarrativeWidgetStack_C) == 0x0002B0, "Wrong size on UWBP_NarrativeWidgetStack_C");

}

