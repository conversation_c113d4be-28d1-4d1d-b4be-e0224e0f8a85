﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_QuestMarker

#include "Basic.hpp"

#include "UMG_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function W_QuestMarker.W_QuestMarker_C.Construct Marker
// 0x0005 (0x0005 - 0x0000)
struct W_QuestMarker_C_Construct_Marker final
{
public:
	bool                                          Temp_bool_Variable;                                // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable;                                // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable_1;                              // 0x0002(0x0001)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              K2Node_Select_Default;                             // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_QuestMarker_C_Construct_Marker) == 0x000001, "Wrong alignment on W_QuestMarker_C_Construct_Marker");
static_assert(sizeof(W_QuestMarker_C_Construct_Marker) == 0x000005, "Wrong size on W_QuestMarker_C_Construct_Marker");
static_assert(offsetof(W_QuestMarker_C_Construct_Marker, Temp_bool_Variable) == 0x000000, "Member 'W_QuestMarker_C_Construct_Marker::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Construct_Marker, Temp_byte_Variable) == 0x000001, "Member 'W_QuestMarker_C_Construct_Marker::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Construct_Marker, Temp_byte_Variable_1) == 0x000002, "Member 'W_QuestMarker_C_Construct_Marker::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Construct_Marker, K2Node_Select_Default) == 0x000003, "Member 'W_QuestMarker_C_Construct_Marker::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Construct_Marker, CallFunc_IsValid_ReturnValue) == 0x000004, "Member 'W_QuestMarker_C_Construct_Marker::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function W_QuestMarker.W_QuestMarker_C.ExecuteUbergraph_W_QuestMarker
// 0x0148 (0x0148 - 0x0000)
struct W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UW_WorldMarkerTooltip_C*                CallFunc_Create_ReturnValue;                       // 0x0008(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0010(0x0038)(IsPlainOldData, NoDestructor)
	struct FPointerEvent                          K2Node_Event_MouseEvent_1;                         // 0x0048(0x0078)(ConstParm)
	struct FPointerEvent                          K2Node_Event_MouseEvent;                           // 0x00C0(0x0078)(ConstParm)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0138(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0140(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker) == 0x000008, "Wrong alignment on W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker");
static_assert(sizeof(W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker) == 0x000148, "Wrong size on W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker");
static_assert(offsetof(W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker, EntryPoint) == 0x000000, "Member 'W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker, CallFunc_Create_ReturnValue) == 0x000008, "Member 'W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker, K2Node_Event_MyGeometry) == 0x000010, "Member 'W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker, K2Node_Event_MouseEvent_1) == 0x000048, "Member 'W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker::K2Node_Event_MouseEvent_1' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker, K2Node_Event_MouseEvent) == 0x0000C0, "Member 'W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker::K2Node_Event_MouseEvent' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker, CallFunc_PlayAnimationForward_ReturnValue) == 0x000138, "Member 'W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000140, "Member 'W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");

// Function W_QuestMarker.W_QuestMarker_C.Get_Marker_Box_ToolTipWidget
// 0x0010 (0x0010 - 0x0000)
struct W_QuestMarker_C_Get_Marker_Box_ToolTipWidget final
{
public:
	class UWidget*                                ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable;                                // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable_1;                              // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x000B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_TextIsEmpty_ReturnValue;                  // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              K2Node_Select_Default;                             // 0x000D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_QuestMarker_C_Get_Marker_Box_ToolTipWidget) == 0x000008, "Wrong alignment on W_QuestMarker_C_Get_Marker_Box_ToolTipWidget");
static_assert(sizeof(W_QuestMarker_C_Get_Marker_Box_ToolTipWidget) == 0x000010, "Wrong size on W_QuestMarker_C_Get_Marker_Box_ToolTipWidget");
static_assert(offsetof(W_QuestMarker_C_Get_Marker_Box_ToolTipWidget, ReturnValue) == 0x000000, "Member 'W_QuestMarker_C_Get_Marker_Box_ToolTipWidget::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Get_Marker_Box_ToolTipWidget, Temp_bool_Variable) == 0x000008, "Member 'W_QuestMarker_C_Get_Marker_Box_ToolTipWidget::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Get_Marker_Box_ToolTipWidget, Temp_byte_Variable) == 0x000009, "Member 'W_QuestMarker_C_Get_Marker_Box_ToolTipWidget::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Get_Marker_Box_ToolTipWidget, Temp_byte_Variable_1) == 0x00000A, "Member 'W_QuestMarker_C_Get_Marker_Box_ToolTipWidget::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Get_Marker_Box_ToolTipWidget, CallFunc_IsValid_ReturnValue) == 0x00000B, "Member 'W_QuestMarker_C_Get_Marker_Box_ToolTipWidget::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Get_Marker_Box_ToolTipWidget, CallFunc_TextIsEmpty_ReturnValue) == 0x00000C, "Member 'W_QuestMarker_C_Get_Marker_Box_ToolTipWidget::CallFunc_TextIsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_Get_Marker_Box_ToolTipWidget, K2Node_Select_Default) == 0x00000D, "Member 'W_QuestMarker_C_Get_Marker_Box_ToolTipWidget::K2Node_Select_Default' has a wrong offset!");

// Function W_QuestMarker.W_QuestMarker_C.OnMouseEnter
// 0x00B0 (0x00B0 - 0x0000)
struct W_QuestMarker_C_OnMouseEnter final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(W_QuestMarker_C_OnMouseEnter) == 0x000008, "Wrong alignment on W_QuestMarker_C_OnMouseEnter");
static_assert(sizeof(W_QuestMarker_C_OnMouseEnter) == 0x0000B0, "Wrong size on W_QuestMarker_C_OnMouseEnter");
static_assert(offsetof(W_QuestMarker_C_OnMouseEnter, MyGeometry) == 0x000000, "Member 'W_QuestMarker_C_OnMouseEnter::MyGeometry' has a wrong offset!");
static_assert(offsetof(W_QuestMarker_C_OnMouseEnter, MouseEvent) == 0x000038, "Member 'W_QuestMarker_C_OnMouseEnter::MouseEvent' has a wrong offset!");

// Function W_QuestMarker.W_QuestMarker_C.OnMouseLeave
// 0x0078 (0x0078 - 0x0000)
struct W_QuestMarker_C_OnMouseLeave final
{
public:
	struct FPointerEvent                          MouseEvent;                                        // 0x0000(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(W_QuestMarker_C_OnMouseLeave) == 0x000008, "Wrong alignment on W_QuestMarker_C_OnMouseLeave");
static_assert(sizeof(W_QuestMarker_C_OnMouseLeave) == 0x000078, "Wrong size on W_QuestMarker_C_OnMouseLeave");
static_assert(offsetof(W_QuestMarker_C_OnMouseLeave, MouseEvent) == 0x000000, "Member 'W_QuestMarker_C_OnMouseLeave::MouseEvent' has a wrong offset!");

}

