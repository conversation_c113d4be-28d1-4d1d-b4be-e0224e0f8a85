﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: ZombieBoss_Attack

#include "Basic.hpp"


namespace SDK::Params
{

// Function ZombieBoss_Attack.ZombieBoss_Attack_C.ExecuteUbergraph_ZombieBoss_Attack
// 0x0028 (0x0028 - 0x0000)
struct ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AAIController*                          K2Node_Event_OwnerController;                      // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  K2Node_Event_ControlledPawn;                       // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class ABP_ZombieBoss_C*                       K2Node_DynamicCast_AsBP_Zombie_Boss;               // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack) == 0x000008, "Wrong alignment on ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack");
static_assert(sizeof(ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack) == 0x000028, "Wrong size on ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack");
static_assert(offsetof(ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack, EntryPoint) == 0x000000, "Member 'ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack::EntryPoint' has a wrong offset!");
static_assert(offsetof(ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack, K2Node_Event_OwnerController) == 0x000008, "Member 'ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack::K2Node_Event_OwnerController' has a wrong offset!");
static_assert(offsetof(ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack, K2Node_Event_ControlledPawn) == 0x000010, "Member 'ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack::K2Node_Event_ControlledPawn' has a wrong offset!");
static_assert(offsetof(ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack, K2Node_DynamicCast_AsBP_Zombie_Boss) == 0x000018, "Member 'ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack::K2Node_DynamicCast_AsBP_Zombie_Boss' has a wrong offset!");
static_assert(offsetof(ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack, K2Node_DynamicCast_bSuccess) == 0x000020, "Member 'ZombieBoss_Attack_C_ExecuteUbergraph_ZombieBoss_Attack::K2Node_DynamicCast_bSuccess' has a wrong offset!");

// Function ZombieBoss_Attack.ZombieBoss_Attack_C.ReceiveExecuteAI
// 0x0010 (0x0010 - 0x0000)
struct ZombieBoss_Attack_C_ReceiveExecuteAI final
{
public:
	class AAIController*                          OwnerController;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  ControlledPawn;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(ZombieBoss_Attack_C_ReceiveExecuteAI) == 0x000008, "Wrong alignment on ZombieBoss_Attack_C_ReceiveExecuteAI");
static_assert(sizeof(ZombieBoss_Attack_C_ReceiveExecuteAI) == 0x000010, "Wrong size on ZombieBoss_Attack_C_ReceiveExecuteAI");
static_assert(offsetof(ZombieBoss_Attack_C_ReceiveExecuteAI, OwnerController) == 0x000000, "Member 'ZombieBoss_Attack_C_ReceiveExecuteAI::OwnerController' has a wrong offset!");
static_assert(offsetof(ZombieBoss_Attack_C_ReceiveExecuteAI, ControlledPawn) == 0x000008, "Member 'ZombieBoss_Attack_C_ReceiveExecuteAI::ControlledPawn' has a wrong offset!");

}

