﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UltraDynamicWeather_Functions

#include "Basic.hpp"

#include "UltraDynamicWeather_Functions_classes.hpp"
#include "UltraDynamicWeather_Functions_parameters.hpp"


namespace SDK
{

// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Change to Random Weather Variation · 𝖴𝖣𝖶
// (Static, Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Time_to_Transition_to_Random_Weather__Seconds_         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶(double Time_to_Transition_to_Random_Weather__Seconds_, class UObject* __WorldContext)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Change to Random Weather Variation · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Change_to_Random_Weather_Variation_·_𝖴𝖣𝖶 Parms{};

	Parms.Time_to_Transition_to_Random_Weather__Seconds_ = Time_to_Transition_to_Random_Weather__Seconds_;
	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Change Weather · 𝖴𝖣𝖶
// (Static, Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*          New_Weather_Type                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Time_To_Transition_To_New_Weather__Seconds_            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Change_Weather_·_𝖴𝖣𝖶(class UUDS_Weather_Settings_C* New_Weather_Type, double Time_To_Transition_To_New_Weather__Seconds_, class UObject* __WorldContext)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Change Weather · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Change_Weather_·_𝖴𝖣𝖶 Parms{};

	Parms.New_Weather_Type = New_Weather_Type;
	Parms.Time_To_Transition_To_New_Weather__Seconds_ = Time_To_Transition_To_New_Weather__Seconds_;
	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Change Wind Direction · 𝖴𝖣𝖶
// (Static, Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  New_Wind_Direction                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Change_Duration                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Change_Wind_Direction_·_𝖴𝖣𝖶(double New_Wind_Direction, double Change_Duration, class UObject* __WorldContext)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Change Wind Direction · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Change_Wind_Direction_·_𝖴𝖣𝖶 Parms{};

	Parms.New_Wind_Direction = New_Wind_Direction;
	Parms.Change_Duration = Change_Duration;
	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Flash Lightning · 𝖴𝖣𝖶
// (Static, Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Use_Custom_Lightning_Location                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Custom_Lightning_Location                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Custom_Target_Location                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Lightning_Bolt_Seed                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Flash_Lightning_·_𝖴𝖣𝖶(bool Use_Custom_Lightning_Location, const struct FVector& Custom_Lightning_Location, const struct FVector& Custom_Target_Location, int32 Lightning_Bolt_Seed, class UObject* __WorldContext)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Flash Lightning · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Flash_Lightning_·_𝖴𝖣𝖶 Parms{};

	Parms.Use_Custom_Lightning_Location = Use_Custom_Lightning_Location;
	Parms.Custom_Lightning_Location = std::move(Custom_Lightning_Location);
	Parms.Custom_Target_Location = std::move(Custom_Target_Location);
	Parms.Lightning_Bolt_Seed = Lightning_Bolt_Seed;
	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Cloud Coverage · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Cloud_Coverage                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Cloud_Coverage_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Cloud_Coverage)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Cloud Coverage · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Cloud_Coverage_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Cloud_Coverage != nullptr)
		*Cloud_Coverage = Parms.Cloud_Coverage;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Current Temperature · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// EUDS_Temperature_Sample_Location        Sample_Location                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Custom_Sample_Location                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EUDS_TemperatureType                    Scale                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Temperature                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Current_Temperature_·_𝖴𝖣𝖶(EUDS_Temperature_Sample_Location Sample_Location, const struct FVector& Custom_Sample_Location, EUDS_TemperatureType Scale, class UObject* __WorldContext, double* Temperature)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Current Temperature · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Current_Temperature_·_𝖴𝖣𝖶 Parms{};

	Parms.Sample_Location = Sample_Location;
	Parms.Custom_Sample_Location = std::move(Custom_Sample_Location);
	Parms.Scale = Scale;
	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Temperature != nullptr)
		*Temperature = Parms.Temperature;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Display Name for Current Weather · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class FString*                          As_String                                              (Parm, OutParm, ZeroConstructor, HasGetValueTypeHash)
// EUDS_Weather_Display_Names*             As_Enumerator                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶(class UObject* __WorldContext, class FString* As_String, EUDS_Weather_Display_Names* As_Enumerator)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Display Name for Current Weather · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Display_Name_for_Current_Weather_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (As_String != nullptr)
		*As_String = std::move(Parms.As_String);

	if (As_Enumerator != nullptr)
		*As_Enumerator = Parms.As_Enumerator;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Sand Amount · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Dust                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Sand_Amount_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Dust)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Sand Amount · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Sand_Amount_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Dust != nullptr)
		*Dust = Parms.Dust;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Fog · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Fog                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Fog_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Fog)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Fog · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Fog_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Fog != nullptr)
		*Fog = Parms.Fog;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Local Weather State · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class UUDS_Weather_Settings_C**         Local_Weather_State                                    (Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Local_Weather_State_·_𝖴𝖣𝖶(class UObject* __WorldContext, class UUDS_Weather_Settings_C** Local_Weather_State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Local Weather State · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Local_Weather_State_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Local_Weather_State != nullptr)
		*Local_Weather_State = Parms.Local_Weather_State;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Material Dust Coverage · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Material_Dust_Coverage                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Material_Dust_Coverage_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Material_Dust_Coverage)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Material Dust Coverage · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Material_Dust_Coverage_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Material_Dust_Coverage != nullptr)
		*Material_Dust_Coverage = Parms.Material_Dust_Coverage;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Material Snow Coverage · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Material_Snow_Coverage                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Material_Snow_Coverage_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Material_Snow_Coverage)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Material Snow Coverage · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Material_Snow_Coverage_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Material_Snow_Coverage != nullptr)
		*Material_Snow_Coverage = Parms.Material_Snow_Coverage;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Material Wetness · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Material_Wetness                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Material_Wetness_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Material_Wetness)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Material Wetness · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Material_Wetness_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Material_Wetness != nullptr)
		*Material_Wetness = Parms.Material_Wetness;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Rain Amount · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Rain                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Rain_Amount_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Rain)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Rain Amount · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Rain_Amount_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Rain != nullptr)
		*Rain = Parms.Rain;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Season · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Season                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EUDS_Season*                            Season_Enum                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Season_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Season, EUDS_Season* Season_Enum)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Season · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Season_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Season != nullptr)
		*Season = Parms.Season;

	if (Season_Enum != nullptr)
		*Season_Enum = Parms.Season_Enum;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Snow Amount · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Snow                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Snow_Amount_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Snow)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Snow Amount · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Snow_Amount_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Snow != nullptr)
		*Snow = Parms.Snow;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Lightning · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Lightning                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Lightning_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Lightning)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Lightning · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Lightning_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Lightning != nullptr)
		*Lightning = Parms.Lightning;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Ultra Dynamic Weather
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class AUltra_Dynamic_Weather_C**        UDW                                                    (Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// bool*                                   Valid                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Ultra_Dynamic_Weather(class UObject* __WorldContext, class AUltra_Dynamic_Weather_C** UDW, bool* Valid)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Ultra Dynamic Weather");

	Params::UltraDynamicWeather_Functions_C_Get_Ultra_Dynamic_Weather Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (UDW != nullptr)
		*UDW = Parms.UDW;

	if (Valid != nullptr)
		*Valid = Parms.Valid;
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Wind Direction Vector · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// struct FVector*                         Wind_Direction                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Wind_Direction_Vector_·_𝖴𝖣𝖶(class UObject* __WorldContext, struct FVector* Wind_Direction)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Wind Direction Vector · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Wind_Direction_Vector_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Wind_Direction != nullptr)
		*Wind_Direction = std::move(Parms.Wind_Direction);
}


// Function UltraDynamicWeather_Functions.UltraDynamicWeather_Functions_C.Get Wind Intensity · 𝖴𝖣𝖶
// (Static, Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UObject*                          __WorldContext                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Wind_Intensity                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUltraDynamicWeather_Functions_C::Get_Wind_Intensity_·_𝖴𝖣𝖶(class UObject* __WorldContext, double* Wind_Intensity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = StaticClass()->GetFunction("UltraDynamicWeather_Functions_C", "Get Wind Intensity · 𝖴𝖣𝖶");

	Params::UltraDynamicWeather_Functions_C_Get_Wind_Intensity_·_𝖴𝖣𝖶 Parms{};

	Parms.__WorldContext = __WorldContext;

	GetDefaultObj()->ProcessEvent(Func, &Parms);

	if (Wind_Intensity != nullptr)
		*Wind_Intensity = Parms.Wind_Intensity;
}

}

