﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_DeadPlayerLootUI

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_DeadPlayerLootUI.W_DeadPlayerLootUI_C
// 0x0110 (0x03D0 - 0x02C0)
class UW_DeadPlayerLootUI_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                B_Backpack;                                        // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                B_Bag;                                             // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                B_BodyArmor;                                       // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                B_Legs;                                            // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                B_Other;                                           // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                B_Torso;                                           // 0x02F0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CAccessories;                                      // 0x02F8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CArmor;                                            // 0x0300(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CBackpack;                                         // 0x0308(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CBinoculars;                                       // 0x0310(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CCompass;                                          // 0x0318(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CContainer;                                        // 0x0320(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CEyewear;                                          // 0x0328(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CFaceWear;                                         // 0x0330(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CFeet;                                             // 0x0338(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CFishingRod;                                       // 0x0340(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CFlashlight;                                       // 0x0348(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CGloves;                                           // 0x0350(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CGPS;                                              // 0x0358(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CHeadWear;                                         // 0x0360(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CLegs;                                             // 0x0368(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CMelee;                                            // 0x0370(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CPrimary;                                          // 0x0378(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CSecondary;                                        // 0x0380(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CSidearm;                                          // 0x0388(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CThrowable;                                        // 0x0390(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        CTorso;                                            // 0x0398(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UEquipmentSlotTitle_C*                  EquipmentSlotTitle_162;                            // 0x03A0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           InventoryVB;                                       // 0x03A8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        JSIContainer_Pockets;                              // 0x03B0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           OtherContent;                                      // 0x03B8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TArray<class UJSIContainer_C*>                AllContainers;                                     // 0x03C0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)

public:
	void AddContentToPanel(class UUserWidget* Content, class UJSIContainer_C* ToContainer);
	void DropInfo_OnItemEquippedChange_Event(class UJSIContainer_C* FromContainer, class UJSIContainer_C* ToContainer, class UJSI_Slot_C* SlotRef, bool Equipped_);
	void ExecuteUbergraph_W_DeadPlayerLootUI(int32 EntryPoint);
	void ForceInitSpecialcontainer();
	void GetAllAttachments(TArray<class FName>* Attachments);
	void GetContainerByAttachmentType(const struct FGameplayTag& Type, class UJSIContainer_C** JigContainer, int32* ContainerIndex);
	void GetDropWidget(class UDropItemBackGwidget_C** DropWRef);
	void GetJSIContainerByPlayerSlots(const struct FGameplayTag& Slot_0, class UJSIContainer_C** Container, class UJSI_Slot_C** EquippedItem, bool* IsPending_);
	void GetListOfNonAddContainers(TArray<class UJSIContainer_C*>* Containers);
	void GetLootContent(class UUserWidget** Widget);
	void GetValidReloadContainers(TArray<class UJSIContainer_C*>* Containers);
	void JigSetLootContent(class UUserWidget* Widget, const class FText& Name_0);
	void JSICheckStatus();
	void JSIOnWeightUpdated(double NewWeight);
	void OnCreatedFromUtility();
	void OnInitialized();
	void SetActionbarFollower(class UJSI_Slot_C* JigRef, bool* Return);
	void SetActorOwner(class AActor* ActorRef);
	void SetInspectorRef(class UBP_InspectorWindowWidget_C* Inspector);
	void SetItemReference(class UJSI_Slot_C* ItemRef);

	void GetListOfContainers(TArray<class UJSIContainer_C*>* Containers) const;

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_DeadPlayerLootUI_C">();
	}
	static class UW_DeadPlayerLootUI_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_DeadPlayerLootUI_C>();
	}
};
static_assert(alignof(UW_DeadPlayerLootUI_C) == 0x000008, "Wrong alignment on UW_DeadPlayerLootUI_C");
static_assert(sizeof(UW_DeadPlayerLootUI_C) == 0x0003D0, "Wrong size on UW_DeadPlayerLootUI_C");
static_assert(offsetof(UW_DeadPlayerLootUI_C, UberGraphFrame) == 0x0002C0, "Member 'UW_DeadPlayerLootUI_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, B_Backpack) == 0x0002C8, "Member 'UW_DeadPlayerLootUI_C::B_Backpack' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, B_Bag) == 0x0002D0, "Member 'UW_DeadPlayerLootUI_C::B_Bag' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, B_BodyArmor) == 0x0002D8, "Member 'UW_DeadPlayerLootUI_C::B_BodyArmor' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, B_Legs) == 0x0002E0, "Member 'UW_DeadPlayerLootUI_C::B_Legs' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, B_Other) == 0x0002E8, "Member 'UW_DeadPlayerLootUI_C::B_Other' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, B_Torso) == 0x0002F0, "Member 'UW_DeadPlayerLootUI_C::B_Torso' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CAccessories) == 0x0002F8, "Member 'UW_DeadPlayerLootUI_C::CAccessories' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CArmor) == 0x000300, "Member 'UW_DeadPlayerLootUI_C::CArmor' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CBackpack) == 0x000308, "Member 'UW_DeadPlayerLootUI_C::CBackpack' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CBinoculars) == 0x000310, "Member 'UW_DeadPlayerLootUI_C::CBinoculars' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CCompass) == 0x000318, "Member 'UW_DeadPlayerLootUI_C::CCompass' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CContainer) == 0x000320, "Member 'UW_DeadPlayerLootUI_C::CContainer' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CEyewear) == 0x000328, "Member 'UW_DeadPlayerLootUI_C::CEyewear' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CFaceWear) == 0x000330, "Member 'UW_DeadPlayerLootUI_C::CFaceWear' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CFeet) == 0x000338, "Member 'UW_DeadPlayerLootUI_C::CFeet' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CFishingRod) == 0x000340, "Member 'UW_DeadPlayerLootUI_C::CFishingRod' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CFlashlight) == 0x000348, "Member 'UW_DeadPlayerLootUI_C::CFlashlight' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CGloves) == 0x000350, "Member 'UW_DeadPlayerLootUI_C::CGloves' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CGPS) == 0x000358, "Member 'UW_DeadPlayerLootUI_C::CGPS' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CHeadWear) == 0x000360, "Member 'UW_DeadPlayerLootUI_C::CHeadWear' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CLegs) == 0x000368, "Member 'UW_DeadPlayerLootUI_C::CLegs' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CMelee) == 0x000370, "Member 'UW_DeadPlayerLootUI_C::CMelee' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CPrimary) == 0x000378, "Member 'UW_DeadPlayerLootUI_C::CPrimary' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CSecondary) == 0x000380, "Member 'UW_DeadPlayerLootUI_C::CSecondary' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CSidearm) == 0x000388, "Member 'UW_DeadPlayerLootUI_C::CSidearm' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CThrowable) == 0x000390, "Member 'UW_DeadPlayerLootUI_C::CThrowable' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, CTorso) == 0x000398, "Member 'UW_DeadPlayerLootUI_C::CTorso' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, EquipmentSlotTitle_162) == 0x0003A0, "Member 'UW_DeadPlayerLootUI_C::EquipmentSlotTitle_162' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, InventoryVB) == 0x0003A8, "Member 'UW_DeadPlayerLootUI_C::InventoryVB' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, JSIContainer_Pockets) == 0x0003B0, "Member 'UW_DeadPlayerLootUI_C::JSIContainer_Pockets' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, OtherContent) == 0x0003B8, "Member 'UW_DeadPlayerLootUI_C::OtherContent' has a wrong offset!");
static_assert(offsetof(UW_DeadPlayerLootUI_C, AllContainers) == 0x0003C0, "Member 'UW_DeadPlayerLootUI_C::AllContainers' has a wrong offset!");

}

