﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_SwatVan

#include "Basic.hpp"

#include "Vehicle_SwatVan_classes.hpp"
#include "Vehicle_SwatVan_parameters.hpp"


namespace SDK
{

// Function Vehicle_SwatVan.Vehicle_SwatVan_C.ExecuteUbergraph_Vehicle_SwatVan
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AVehicle_SwatVan_C::ExecuteUbergraph_Vehicle_SwatVan(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_SwatVan_C", "ExecuteUbergraph_Vehicle_SwatVan");

	Params::Vehicle_SwatVan_C_ExecuteUbergraph_Vehicle_SwatV<PERSON> Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Vehicle_SwatVan.Vehicle_SwatVan_C.UserConstructionScript
// (Event, Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AVehicle_SwatVan_C::UserConstructionScript()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_SwatVan_C", "UserConstructionScript");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Vehicle_SwatVan.Vehicle_SwatVan_C.ReceiveBeginPlay
// (Event, Protected, BlueprintEvent)

void AVehicle_SwatVan_C::ReceiveBeginPlay()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Vehicle_SwatVan_C", "ReceiveBeginPlay");

	UObject::ProcessEvent(Func, nullptr);
}

}

