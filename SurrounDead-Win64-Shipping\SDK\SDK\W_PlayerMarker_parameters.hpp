﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_PlayerMarker

#include "Basic.hpp"

#include "UMG_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function W_PlayerMarker.W_PlayerMarker_C.Construct Marker
// 0x0001 (0x0001 - 0x0000)
struct W_PlayerMarker_C_Construct_Marker final
{
public:
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_PlayerMarker_C_Construct_Marker) == 0x000001, "Wrong alignment on W_PlayerMarker_C_Construct_Marker");
static_assert(sizeof(W_PlayerMarker_C_Construct_Marker) == 0x000001, "Wrong size on W_PlayerMarker_C_Construct_Marker");
static_assert(offsetof(W_PlayerMarker_C_Construct_Marker, CallFunc_IsValid_ReturnValue) == 0x000000, "Member 'W_PlayerMarker_C_Construct_Marker::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function W_PlayerMarker.W_PlayerMarker_C.ExecuteUbergraph_W_PlayerMarker
// 0x0140 (0x0140 - 0x0000)
struct W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0018(0x0038)(IsPlainOldData, NoDestructor)
	struct FPointerEvent                          K2Node_Event_MouseEvent_1;                         // 0x0050(0x0078)(ConstParm)
	struct FPointerEvent                          K2Node_Event_MouseEvent;                           // 0x00C8(0x0078)(ConstParm)
};
static_assert(alignof(W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker) == 0x000008, "Wrong alignment on W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker");
static_assert(sizeof(W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker) == 0x000140, "Wrong size on W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker");
static_assert(offsetof(W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker, EntryPoint) == 0x000000, "Member 'W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000008, "Member 'W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker, CallFunc_PlayAnimationForward_ReturnValue) == 0x000010, "Member 'W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker, K2Node_Event_MyGeometry) == 0x000018, "Member 'W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker, K2Node_Event_MouseEvent_1) == 0x000050, "Member 'W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker::K2Node_Event_MouseEvent_1' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker, K2Node_Event_MouseEvent) == 0x0000C8, "Member 'W_PlayerMarker_C_ExecuteUbergraph_W_PlayerMarker::K2Node_Event_MouseEvent' has a wrong offset!");

// Function W_PlayerMarker.W_PlayerMarker_C.OnMouseButtonDoubleClick
// 0x0230 (0x0230 - 0x0000)
struct W_PlayerMarker_C_OnMouseButtonDoubleClick final
{
public:
	struct FGeometry                              InMyGeometry;                                      // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          InMouseEvent;                                      // 0x0038(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00B0(0x00B8)(Parm, OutParm, ReturnParm)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0168(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_169[0x7];                                      // 0x0169(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0170(0x00B8)()
	class UBPC_MinimapSystem_C*                   CallFunc_Get_Minimap_Component_ReturnValue;        // 0x0228(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_PlayerMarker_C_OnMouseButtonDoubleClick) == 0x000008, "Wrong alignment on W_PlayerMarker_C_OnMouseButtonDoubleClick");
static_assert(sizeof(W_PlayerMarker_C_OnMouseButtonDoubleClick) == 0x000230, "Wrong size on W_PlayerMarker_C_OnMouseButtonDoubleClick");
static_assert(offsetof(W_PlayerMarker_C_OnMouseButtonDoubleClick, InMyGeometry) == 0x000000, "Member 'W_PlayerMarker_C_OnMouseButtonDoubleClick::InMyGeometry' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_OnMouseButtonDoubleClick, InMouseEvent) == 0x000038, "Member 'W_PlayerMarker_C_OnMouseButtonDoubleClick::InMouseEvent' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_OnMouseButtonDoubleClick, ReturnValue) == 0x0000B0, "Member 'W_PlayerMarker_C_OnMouseButtonDoubleClick::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_OnMouseButtonDoubleClick, CallFunc_IsValid_ReturnValue) == 0x000168, "Member 'W_PlayerMarker_C_OnMouseButtonDoubleClick::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_OnMouseButtonDoubleClick, CallFunc_Handled_ReturnValue) == 0x000170, "Member 'W_PlayerMarker_C_OnMouseButtonDoubleClick::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_OnMouseButtonDoubleClick, CallFunc_Get_Minimap_Component_ReturnValue) == 0x000228, "Member 'W_PlayerMarker_C_OnMouseButtonDoubleClick::CallFunc_Get_Minimap_Component_ReturnValue' has a wrong offset!");

// Function W_PlayerMarker.W_PlayerMarker_C.OnMouseEnter
// 0x00B0 (0x00B0 - 0x0000)
struct W_PlayerMarker_C_OnMouseEnter final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(W_PlayerMarker_C_OnMouseEnter) == 0x000008, "Wrong alignment on W_PlayerMarker_C_OnMouseEnter");
static_assert(sizeof(W_PlayerMarker_C_OnMouseEnter) == 0x0000B0, "Wrong size on W_PlayerMarker_C_OnMouseEnter");
static_assert(offsetof(W_PlayerMarker_C_OnMouseEnter, MyGeometry) == 0x000000, "Member 'W_PlayerMarker_C_OnMouseEnter::MyGeometry' has a wrong offset!");
static_assert(offsetof(W_PlayerMarker_C_OnMouseEnter, MouseEvent) == 0x000038, "Member 'W_PlayerMarker_C_OnMouseEnter::MouseEvent' has a wrong offset!");

// Function W_PlayerMarker.W_PlayerMarker_C.OnMouseLeave
// 0x0078 (0x0078 - 0x0000)
struct W_PlayerMarker_C_OnMouseLeave final
{
public:
	struct FPointerEvent                          MouseEvent;                                        // 0x0000(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(W_PlayerMarker_C_OnMouseLeave) == 0x000008, "Wrong alignment on W_PlayerMarker_C_OnMouseLeave");
static_assert(sizeof(W_PlayerMarker_C_OnMouseLeave) == 0x000078, "Wrong size on W_PlayerMarker_C_OnMouseLeave");
static_assert(offsetof(W_PlayerMarker_C_OnMouseLeave, MouseEvent) == 0x000000, "Member 'W_PlayerMarker_C_OnMouseLeave::MouseEvent' has a wrong offset!");

}

