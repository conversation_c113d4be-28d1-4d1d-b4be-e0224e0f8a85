﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_Space_Planet

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "UDS_Space_Parent_structs.hpp"
#include "UDS_Planet_Lightsource_structs.hpp"


namespace SDK
{

// UserDefinedStruct UDS_Space_Planet.UDS_Space_Planet
// 0x0250 (0x0250 - 0x0000)
struct FUDS_Space_Planet final
{
public:
	EUDS_Space_Parent                             Parent_2_0E1A37CE4AF01519A9BCE6ACA2CD1137;         // 0x0000(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FRotator                               RelativeRotation_5_6AB46E784AAA14769695A882EB23223E; // 0x0008(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	double                                        Scale_9_D8C257DF48AA1AA6FF92B0BADA269EDB;          // 0x0020(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              ColorTexture_30_2BBB70D24D2BDD9AD355609B4FF9B21B;  // 0x0028(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	struct FLinearColor                           ColorTextureTint_123_F088C5B64DFD1D20EEC23CBC27966B9C; // 0x0050(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              NormalMap_33_A9266AE648B84654F7C9B9849CFA3E7C;     // 0x0060(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	double                                        NormalMapStrength_36_6F822CA84FCDEF589CED9595F668D77A; // 0x0088(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           DarkSideTint_40_83860D2D4EEEF7086AB3AFB09CF1DCCC;  // 0x0090(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           LightSideTint_42_0EF08CD645E27CABBE7DBD8E7471F04B; // 0x00A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        TerminatorThickness_58_E6DC59F2417BA23BB1BAA5BF464BF621; // 0x00B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        TerminatorPower_60_249814CB4B2F0A60E458FEA89132D36E; // 0x00B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        TerminatorOffset_62_E26446874807C179E62DC3A3D511938F; // 0x00C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           TerminatorTint_44_8A6F729743E618D4CBE093BDCA99CD01; // 0x00C8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_Planet_Lightsource                       LightVector_105_71AFA5654C1A2915EF2F6F9636985F79;  // 0x00D8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D9[0x7];                                       // 0x00D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CustomLightVector_108_A1E02E374DF47067FFF4BB83AD035BC4; // 0x00E0(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Glow_116_6BBE205A4CB7B5A1555103A09A90E0F7;         // 0x00F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           GlowColor_117_D644808A49C9F262468CD988D1087F09;    // 0x0100(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        GlowScale_120_20ADA5B346CD968A0904D284F1D968B0;    // 0x0110(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          RenderAtmosphere_47_8FA38C394C66F95149E4269D6A13E9DE; // 0x0118(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_119[0x7];                                      // 0x0119(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        AtmosphereStrength_50_0D33B16248764FF3CEBC98B70F026998; // 0x0120(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        AtmosphereThickness_52_0C2D526D4C6E998A929F2D9A2B3FE30B; // 0x0128(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           AtmosphereColor_55_39F94C30482BE08919CDB1BE025A7DC8; // 0x0130(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              EmissiveTexture_66_5312AAA94E7A901D5F46D6AC848F943F; // 0x0140(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	struct FLinearColor                           EmissiveLightSideTint_73_969799654B9C1754BDE425B229EA063E; // 0x0168(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           EmissiveDarkSideTint_74_EBFDD1104CC20ADAE943EC913D1977B2; // 0x0178(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          RenderRing_78_1DDEF5604EC914832AB31CA1327D2947;    // 0x0188(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_189[0x7];                                      // 0x0189(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        RingPitch_80_56F5E8E14589B8624F3C7B9951D4C8D1;     // 0x0190(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        RingRoll_82_091721764596A286D8F56C92EFB17435;      // 0x0198(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        RingDistance_84_8E87B9394476970597D7A8B81A055C39;  // 0x01A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        RingWidth_86_324627CE43F44BEFA03D019FC1E0FF8F;     // 0x01A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              RingTexture_92_B962AC934EE0E4617A29BE9F710F32EF;   // 0x01B0(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	struct FLinearColor                           RingTintColor_95_4F6B04E54C74C020B0C009971EB24538; // 0x01D8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        RingOpacity_98_6F398376468F51CF8289D9AA39638D49;   // 0x01E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        RingShadowBrightness_100_82A5108141E68184387C0CBDA39F37D6; // 0x01F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        RingShadowSoftness_102_B53027E540B8F2E6C7744D90958C1B85; // 0x01F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              MeshDeformTexture_126_B7DC215A433430E4AD89459B331BC0CE; // 0x0200(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	double                                        MeshDeformStrength_129_CBDEB487415F39784E41DBB95BE8F4B1; // 0x0228(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        MeshDeformCenterLevel_135_D94322934BFA5C635C0D2BA54E37A086; // 0x0230(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                MeshDeformTextureOffset_132_F11E46C5458E2C4E5B3ABB8E3C2E6B45; // 0x0238(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(FUDS_Space_Planet) == 0x000008, "Wrong alignment on FUDS_Space_Planet");
static_assert(sizeof(FUDS_Space_Planet) == 0x000250, "Wrong size on FUDS_Space_Planet");
static_assert(offsetof(FUDS_Space_Planet, Parent_2_0E1A37CE4AF01519A9BCE6ACA2CD1137) == 0x000000, "Member 'FUDS_Space_Planet::Parent_2_0E1A37CE4AF01519A9BCE6ACA2CD1137' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RelativeRotation_5_6AB46E784AAA14769695A882EB23223E) == 0x000008, "Member 'FUDS_Space_Planet::RelativeRotation_5_6AB46E784AAA14769695A882EB23223E' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, Scale_9_D8C257DF48AA1AA6FF92B0BADA269EDB) == 0x000020, "Member 'FUDS_Space_Planet::Scale_9_D8C257DF48AA1AA6FF92B0BADA269EDB' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, ColorTexture_30_2BBB70D24D2BDD9AD355609B4FF9B21B) == 0x000028, "Member 'FUDS_Space_Planet::ColorTexture_30_2BBB70D24D2BDD9AD355609B4FF9B21B' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, ColorTextureTint_123_F088C5B64DFD1D20EEC23CBC27966B9C) == 0x000050, "Member 'FUDS_Space_Planet::ColorTextureTint_123_F088C5B64DFD1D20EEC23CBC27966B9C' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, NormalMap_33_A9266AE648B84654F7C9B9849CFA3E7C) == 0x000060, "Member 'FUDS_Space_Planet::NormalMap_33_A9266AE648B84654F7C9B9849CFA3E7C' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, NormalMapStrength_36_6F822CA84FCDEF589CED9595F668D77A) == 0x000088, "Member 'FUDS_Space_Planet::NormalMapStrength_36_6F822CA84FCDEF589CED9595F668D77A' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, DarkSideTint_40_83860D2D4EEEF7086AB3AFB09CF1DCCC) == 0x000090, "Member 'FUDS_Space_Planet::DarkSideTint_40_83860D2D4EEEF7086AB3AFB09CF1DCCC' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, LightSideTint_42_0EF08CD645E27CABBE7DBD8E7471F04B) == 0x0000A0, "Member 'FUDS_Space_Planet::LightSideTint_42_0EF08CD645E27CABBE7DBD8E7471F04B' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, TerminatorThickness_58_E6DC59F2417BA23BB1BAA5BF464BF621) == 0x0000B0, "Member 'FUDS_Space_Planet::TerminatorThickness_58_E6DC59F2417BA23BB1BAA5BF464BF621' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, TerminatorPower_60_249814CB4B2F0A60E458FEA89132D36E) == 0x0000B8, "Member 'FUDS_Space_Planet::TerminatorPower_60_249814CB4B2F0A60E458FEA89132D36E' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, TerminatorOffset_62_E26446874807C179E62DC3A3D511938F) == 0x0000C0, "Member 'FUDS_Space_Planet::TerminatorOffset_62_E26446874807C179E62DC3A3D511938F' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, TerminatorTint_44_8A6F729743E618D4CBE093BDCA99CD01) == 0x0000C8, "Member 'FUDS_Space_Planet::TerminatorTint_44_8A6F729743E618D4CBE093BDCA99CD01' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, LightVector_105_71AFA5654C1A2915EF2F6F9636985F79) == 0x0000D8, "Member 'FUDS_Space_Planet::LightVector_105_71AFA5654C1A2915EF2F6F9636985F79' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, CustomLightVector_108_A1E02E374DF47067FFF4BB83AD035BC4) == 0x0000E0, "Member 'FUDS_Space_Planet::CustomLightVector_108_A1E02E374DF47067FFF4BB83AD035BC4' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, Glow_116_6BBE205A4CB7B5A1555103A09A90E0F7) == 0x0000F8, "Member 'FUDS_Space_Planet::Glow_116_6BBE205A4CB7B5A1555103A09A90E0F7' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, GlowColor_117_D644808A49C9F262468CD988D1087F09) == 0x000100, "Member 'FUDS_Space_Planet::GlowColor_117_D644808A49C9F262468CD988D1087F09' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, GlowScale_120_20ADA5B346CD968A0904D284F1D968B0) == 0x000110, "Member 'FUDS_Space_Planet::GlowScale_120_20ADA5B346CD968A0904D284F1D968B0' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RenderAtmosphere_47_8FA38C394C66F95149E4269D6A13E9DE) == 0x000118, "Member 'FUDS_Space_Planet::RenderAtmosphere_47_8FA38C394C66F95149E4269D6A13E9DE' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, AtmosphereStrength_50_0D33B16248764FF3CEBC98B70F026998) == 0x000120, "Member 'FUDS_Space_Planet::AtmosphereStrength_50_0D33B16248764FF3CEBC98B70F026998' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, AtmosphereThickness_52_0C2D526D4C6E998A929F2D9A2B3FE30B) == 0x000128, "Member 'FUDS_Space_Planet::AtmosphereThickness_52_0C2D526D4C6E998A929F2D9A2B3FE30B' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, AtmosphereColor_55_39F94C30482BE08919CDB1BE025A7DC8) == 0x000130, "Member 'FUDS_Space_Planet::AtmosphereColor_55_39F94C30482BE08919CDB1BE025A7DC8' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, EmissiveTexture_66_5312AAA94E7A901D5F46D6AC848F943F) == 0x000140, "Member 'FUDS_Space_Planet::EmissiveTexture_66_5312AAA94E7A901D5F46D6AC848F943F' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, EmissiveLightSideTint_73_969799654B9C1754BDE425B229EA063E) == 0x000168, "Member 'FUDS_Space_Planet::EmissiveLightSideTint_73_969799654B9C1754BDE425B229EA063E' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, EmissiveDarkSideTint_74_EBFDD1104CC20ADAE943EC913D1977B2) == 0x000178, "Member 'FUDS_Space_Planet::EmissiveDarkSideTint_74_EBFDD1104CC20ADAE943EC913D1977B2' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RenderRing_78_1DDEF5604EC914832AB31CA1327D2947) == 0x000188, "Member 'FUDS_Space_Planet::RenderRing_78_1DDEF5604EC914832AB31CA1327D2947' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RingPitch_80_56F5E8E14589B8624F3C7B9951D4C8D1) == 0x000190, "Member 'FUDS_Space_Planet::RingPitch_80_56F5E8E14589B8624F3C7B9951D4C8D1' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RingRoll_82_091721764596A286D8F56C92EFB17435) == 0x000198, "Member 'FUDS_Space_Planet::RingRoll_82_091721764596A286D8F56C92EFB17435' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RingDistance_84_8E87B9394476970597D7A8B81A055C39) == 0x0001A0, "Member 'FUDS_Space_Planet::RingDistance_84_8E87B9394476970597D7A8B81A055C39' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RingWidth_86_324627CE43F44BEFA03D019FC1E0FF8F) == 0x0001A8, "Member 'FUDS_Space_Planet::RingWidth_86_324627CE43F44BEFA03D019FC1E0FF8F' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RingTexture_92_B962AC934EE0E4617A29BE9F710F32EF) == 0x0001B0, "Member 'FUDS_Space_Planet::RingTexture_92_B962AC934EE0E4617A29BE9F710F32EF' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RingTintColor_95_4F6B04E54C74C020B0C009971EB24538) == 0x0001D8, "Member 'FUDS_Space_Planet::RingTintColor_95_4F6B04E54C74C020B0C009971EB24538' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RingOpacity_98_6F398376468F51CF8289D9AA39638D49) == 0x0001E8, "Member 'FUDS_Space_Planet::RingOpacity_98_6F398376468F51CF8289D9AA39638D49' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RingShadowBrightness_100_82A5108141E68184387C0CBDA39F37D6) == 0x0001F0, "Member 'FUDS_Space_Planet::RingShadowBrightness_100_82A5108141E68184387C0CBDA39F37D6' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, RingShadowSoftness_102_B53027E540B8F2E6C7744D90958C1B85) == 0x0001F8, "Member 'FUDS_Space_Planet::RingShadowSoftness_102_B53027E540B8F2E6C7744D90958C1B85' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, MeshDeformTexture_126_B7DC215A433430E4AD89459B331BC0CE) == 0x000200, "Member 'FUDS_Space_Planet::MeshDeformTexture_126_B7DC215A433430E4AD89459B331BC0CE' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, MeshDeformStrength_129_CBDEB487415F39784E41DBB95BE8F4B1) == 0x000228, "Member 'FUDS_Space_Planet::MeshDeformStrength_129_CBDEB487415F39784E41DBB95BE8F4B1' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, MeshDeformCenterLevel_135_D94322934BFA5C635C0D2BA54E37A086) == 0x000230, "Member 'FUDS_Space_Planet::MeshDeformCenterLevel_135_D94322934BFA5C635C0D2BA54E37A086' has a wrong offset!");
static_assert(offsetof(FUDS_Space_Planet, MeshDeformTextureOffset_132_F11E46C5458E2C4E5B3ABB8E3C2E6B45) == 0x000238, "Member 'FUDS_Space_Planet::MeshDeformTextureOffset_132_F11E46C5458E2C4E5B3ABB8E3C2E6B45' has a wrong offset!");

}

