﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDW_Temperature_Manager

#include "Basic.hpp"

#include "UDW_Temperature_Manager_classes.hpp"
#include "UDW_Temperature_Manager_parameters.hpp"


namespace SDK
{

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Calculate Temperature
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Weather_Settings_C*          Settings                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double*                                 Temperature                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDW_Temperature_Manager_C::Calculate_Temperature(class UUDS_Weather_Settings_C* Settings, double* Temperature)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDW_Temperature_Manager_C", "Calculate Temperature");

	Params::UDW_Temperature_Manager_C_Calculate_Temperature Parms{};

	Parms.Settings = Settings;

	UObject::ProcessEvent(Func, &Parms);

	if (Temperature != nullptr)
		*Temperature = Parms.Temperature;
}


// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.ExecuteUbergraph_UDW_Temperature_Manager
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDW_Temperature_Manager_C::ExecuteUbergraph_UDW_Temperature_Manager(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDW_Temperature_Manager_C", "ExecuteUbergraph_UDW_Temperature_Manager");

	Params::UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Get Current Min and Max Temperature
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 Spring_Min_and_Max                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Summer_Min_and_Max                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Autumn_Min_and_Max                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Winter_Min_and_Max                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector2D*                       Range                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDW_Temperature_Manager_C::Get_Current_Min_and_Max_Temperature(const struct FVector2D& Spring_Min_and_Max, const struct FVector2D& Summer_Min_and_Max, const struct FVector2D& Autumn_Min_and_Max, const struct FVector2D& Winter_Min_and_Max, struct FVector2D* Range)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDW_Temperature_Manager_C", "Get Current Min and Max Temperature");

	Params::UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature Parms{};

	Parms.Spring_Min_and_Max = std::move(Spring_Min_and_Max);
	Parms.Summer_Min_and_Max = std::move(Summer_Min_and_Max);
	Parms.Autumn_Min_and_Max = std::move(Autumn_Min_and_Max);
	Parms.Winter_Min_and_Max = std::move(Winter_Min_and_Max);

	UObject::ProcessEvent(Func, &Parms);

	if (Range != nullptr)
		*Range = std::move(Parms.Range);
}


// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Get Temperature
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDW_Temperature_Manager_C::Get_Temperature(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDW_Temperature_Manager_C", "Get Temperature");

	Params::UDW_Temperature_Manager_C_Get_Temperature Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.ReceiveEndPlay
// (Event, Public, BlueprintEvent)
// Parameters:
// EEndPlayReason                          EndPlayReason                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDW_Temperature_Manager_C::ReceiveEndPlay(EEndPlayReason EndPlayReason)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDW_Temperature_Manager_C", "ReceiveEndPlay");

	Params::UDW_Temperature_Manager_C_ReceiveEndPlay Parms{};

	Parms.EndPlayReason = EndPlayReason;

	UObject::ProcessEvent(Func, &Parms);
}


// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Runtime Start Temperature Manager
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UUDW_Temperature_Manager_C::Runtime_Start_Temperature_Manager()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDW_Temperature_Manager_C", "Runtime Start Temperature Manager");

	UObject::ProcessEvent(Func, nullptr);
}


// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Set Up Temperature Manager
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AUltra_Dynamic_Weather_C*         UDW_0                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class UUDS_Weather_Settings_C*          Weather_State_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UUDW_Temperature_Manager_C::Set_Up_Temperature_Manager(class AUltra_Dynamic_Weather_C* UDW_0, class UUDS_Weather_Settings_C* Weather_State_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDW_Temperature_Manager_C", "Set Up Temperature Manager");

	Params::UDW_Temperature_Manager_C_Set_Up_Temperature_Manager Parms{};

	Parms.UDW_0 = UDW_0;
	Parms.Weather_State_0 = Weather_State_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Update Target Temperature
// (Protected, BlueprintCallable, BlueprintEvent)

void UUDW_Temperature_Manager_C::Update_Target_Temperature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDW_Temperature_Manager_C", "Update Target Temperature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Update Temperature Range
// (Protected, BlueprintCallable, BlueprintEvent)

void UUDW_Temperature_Manager_C::Update_Temperature_Range()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDW_Temperature_Manager_C", "Update Temperature Range");

	UObject::ProcessEvent(Func, nullptr);
}

}

