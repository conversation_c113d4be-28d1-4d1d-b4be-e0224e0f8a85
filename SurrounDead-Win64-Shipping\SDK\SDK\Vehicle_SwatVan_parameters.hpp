﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_SwatVan

#include "Basic.hpp"


namespace SDK::Params
{

// Function Vehicle_SwatVan.Vehicle_SwatVan_C.ExecuteUbergraph_Vehicle_SwatVan
// 0x0004 (0x0004 - 0x0000)
struct Vehicle_SwatVan_C_ExecuteUbergraph_Vehicle_SwatVan final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Vehicle_SwatVan_C_ExecuteUbergraph_Vehicle_SwatVan) == 0x000004, "Wrong alignment on Vehicle_SwatVan_C_ExecuteUbergraph_Vehicle_SwatVan");
static_assert(sizeof(Vehicle_SwatVan_C_ExecuteUbergraph_Vehicle_SwatVan) == 0x000004, "Wrong size on Vehicle_SwatVan_C_ExecuteUbergraph_Vehicle_SwatVan");
static_assert(offsetof(Vehicle_SwatVan_C_ExecuteUbergraph_Vehicle_SwatVan, EntryPoint) == 0x000000, "Member 'Vehicle_SwatVan_C_ExecuteUbergraph_Vehicle_SwatVan::EntryPoint' has a wrong offset!");

// Function Vehicle_SwatVan.Vehicle_SwatVan_C.UserConstructionScript
// 0x0010 (0x0010 - 0x0000)
struct Vehicle_SwatVan_C_UserConstructionScript final
{
public:
	TArray<class USpotLightComponent*>            K2Node_MakeArray_Array;                            // 0x0000(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(Vehicle_SwatVan_C_UserConstructionScript) == 0x000008, "Wrong alignment on Vehicle_SwatVan_C_UserConstructionScript");
static_assert(sizeof(Vehicle_SwatVan_C_UserConstructionScript) == 0x000010, "Wrong size on Vehicle_SwatVan_C_UserConstructionScript");
static_assert(offsetof(Vehicle_SwatVan_C_UserConstructionScript, K2Node_MakeArray_Array) == 0x000000, "Member 'Vehicle_SwatVan_C_UserConstructionScript::K2Node_MakeArray_Array' has a wrong offset!");

}

