﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeActivatableWidget

#include "Basic.hpp"


namespace SDK::Params
{

// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.BP_OnHandleBackAction
// 0x0001 (0x0001 - 0x0000)
struct WBP_NarrativeActivatableWidget_C_BP_OnHandleBackAction final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeActivatableWidget_C_BP_OnHandleBackAction) == 0x000001, "Wrong alignment on WBP_NarrativeActivatableWidget_C_BP_OnHandleBackAction");
static_assert(sizeof(WBP_NarrativeActivatableWidget_C_BP_OnHandleBackAction) == 0x000001, "Wrong size on WBP_NarrativeActivatableWidget_C_BP_OnHandleBackAction");
static_assert(offsetof(WBP_NarrativeActivatableWidget_C_BP_OnHandleBackAction, ReturnValue) == 0x000000, "Member 'WBP_NarrativeActivatableWidget_C_BP_OnHandleBackAction::ReturnValue' has a wrong offset!");

// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.ExecuteUbergraph_WBP_NarrativeActivatableWidget
// 0x0004 (0x0004 - 0x0000)
struct WBP_NarrativeActivatableWidget_C_ExecuteUbergraph_WBP_NarrativeActivatableWidget final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeActivatableWidget_C_ExecuteUbergraph_WBP_NarrativeActivatableWidget) == 0x000004, "Wrong alignment on WBP_NarrativeActivatableWidget_C_ExecuteUbergraph_WBP_NarrativeActivatableWidget");
static_assert(sizeof(WBP_NarrativeActivatableWidget_C_ExecuteUbergraph_WBP_NarrativeActivatableWidget) == 0x000004, "Wrong size on WBP_NarrativeActivatableWidget_C_ExecuteUbergraph_WBP_NarrativeActivatableWidget");
static_assert(offsetof(WBP_NarrativeActivatableWidget_C_ExecuteUbergraph_WBP_NarrativeActivatableWidget, EntryPoint) == 0x000000, "Member 'WBP_NarrativeActivatableWidget_C_ExecuteUbergraph_WBP_NarrativeActivatableWidget::EntryPoint' has a wrong offset!");

// Function WBP_NarrativeActivatableWidget.WBP_NarrativeActivatableWidget_C.HandleFocus
// 0x0010 (0x0010 - 0x0000)
struct WBP_NarrativeActivatableWidget_C_HandleFocus final
{
public:
	class UWidget*                                CallFunc_GetDesiredFocusTarget_ReturnValue;        // 0x0000(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeActivatableWidget_C_HandleFocus) == 0x000008, "Wrong alignment on WBP_NarrativeActivatableWidget_C_HandleFocus");
static_assert(sizeof(WBP_NarrativeActivatableWidget_C_HandleFocus) == 0x000010, "Wrong size on WBP_NarrativeActivatableWidget_C_HandleFocus");
static_assert(offsetof(WBP_NarrativeActivatableWidget_C_HandleFocus, CallFunc_GetDesiredFocusTarget_ReturnValue) == 0x000000, "Member 'WBP_NarrativeActivatableWidget_C_HandleFocus::CallFunc_GetDesiredFocusTarget_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeActivatableWidget_C_HandleFocus, CallFunc_IsValid_ReturnValue) == 0x000008, "Member 'WBP_NarrativeActivatableWidget_C_HandleFocus::CallFunc_IsValid_ReturnValue' has a wrong offset!");

}

