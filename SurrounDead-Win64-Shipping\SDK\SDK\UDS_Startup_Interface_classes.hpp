﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_Startup_Interface

#include "Basic.hpp"

#include "CoreUObject_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass UDS_Startup_Interface.UDS_Startup_Interface_C
// 0x0000 (0x0000 - 0x0000)
class IUDS_Startup_Interface_C final
{
public:
	void UDS_Ending_Play();
	void UDS_Starting_Up(class AUltra_Dynamic_Sky_C* UDS);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"UDS_Startup_Interface_C">();
	}
	static class IUDS_Startup_Interface_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<IUDS_Startup_Interface_C>();
	}

	class UObject* AsUObject()
	{
		return reinterpret_cast<UObject*>(this);
	}
	const class UObject* AsUObject() const
	{
		return reinterpret_cast<const UObject*>(this);
	}
};
static_assert(alignof(IUDS_Startup_Interface_C) == 0x000001, "Wrong alignment on IUDS_Startup_Interface_C");
static_assert(sizeof(IUDS_Startup_Interface_C) == 0x000001, "Wrong size on IUDS_Startup_Interface_C");

}

