﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_QuantitySelector

#include "Basic.hpp"

#include "NarrativeCommonUI_structs.hpp"
#include "Engine_structs.hpp"
#include "WBP_NarrativeMenu_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WBP_QuantitySelector.WBP_QuantitySelector_C
// 0x0080 (0x04C8 - 0x0448)
class UWBP_QuantitySelector_C final : public UWBP_NarrativeMenu_C
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame_WBP_QuantitySelector_C;             // 0x0448(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UCommonTextBlock*                       CommonTextBlock_Instruction;                       // 0x0450(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCommonTextBlock*                       CommonTextBlock_Max;                               // 0x0458(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCommonTextBlock*                       CommonTextBlock_Min;                               // 0x0460(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCommonTextBlock*                       CommonTextBlock_Quantity;                          // 0x0468(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UAnalogSlider*                          Slider_Quantity;                                   // 0x0470(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	int32                                         MinAmount;                                         // 0x0478(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	int32                                         MaxAmount;                                         // 0x047C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	struct FDataTableRowHandle                    ConfirmBinding;                                    // 0x0480(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	struct FInputActionBindingHandle              ConfirmHandle;                                     // 0x0490(0x0004)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	uint8                                         Pad_494[0x4];                                      // 0x0494(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(int32 Quantity)> OnConfirmed;                                      // 0x0498(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	class FText                                   InstructionText;                                   // 0x04A8(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	int32                                         SelectedAmount;                                    // 0x04C0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void BndEvt__WBP_QuantitySelector_Slider_Quantity_K2Node_ComponentBoundEvent_0_OnFloatValueChangedEvent__DelegateSignature(float Value);
	void Destruct();
	void ExecuteUbergraph_WBP_QuantitySelector(int32 EntryPoint);
	void Initialize(int32 MinAmount_0, int32 MaxAmount_0, const class FText& InstructionText_0);
	void OnConfirm(class FName ActionName);
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void Update_Drop_Amount(double A);
	void RegisterActions();

	class UWidget* BP_GetDesiredFocusTarget() const;

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WBP_QuantitySelector_C">();
	}
	static class UWBP_QuantitySelector_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWBP_QuantitySelector_C>();
	}
};
static_assert(alignof(UWBP_QuantitySelector_C) == 0x000008, "Wrong alignment on UWBP_QuantitySelector_C");
static_assert(sizeof(UWBP_QuantitySelector_C) == 0x0004C8, "Wrong size on UWBP_QuantitySelector_C");
static_assert(offsetof(UWBP_QuantitySelector_C, UberGraphFrame_WBP_QuantitySelector_C) == 0x000448, "Member 'UWBP_QuantitySelector_C::UberGraphFrame_WBP_QuantitySelector_C' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, CommonTextBlock_Instruction) == 0x000450, "Member 'UWBP_QuantitySelector_C::CommonTextBlock_Instruction' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, CommonTextBlock_Max) == 0x000458, "Member 'UWBP_QuantitySelector_C::CommonTextBlock_Max' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, CommonTextBlock_Min) == 0x000460, "Member 'UWBP_QuantitySelector_C::CommonTextBlock_Min' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, CommonTextBlock_Quantity) == 0x000468, "Member 'UWBP_QuantitySelector_C::CommonTextBlock_Quantity' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, Slider_Quantity) == 0x000470, "Member 'UWBP_QuantitySelector_C::Slider_Quantity' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, MinAmount) == 0x000478, "Member 'UWBP_QuantitySelector_C::MinAmount' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, MaxAmount) == 0x00047C, "Member 'UWBP_QuantitySelector_C::MaxAmount' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, ConfirmBinding) == 0x000480, "Member 'UWBP_QuantitySelector_C::ConfirmBinding' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, ConfirmHandle) == 0x000490, "Member 'UWBP_QuantitySelector_C::ConfirmHandle' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, OnConfirmed) == 0x000498, "Member 'UWBP_QuantitySelector_C::OnConfirmed' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, InstructionText) == 0x0004A8, "Member 'UWBP_QuantitySelector_C::InstructionText' has a wrong offset!");
static_assert(offsetof(UWBP_QuantitySelector_C, SelectedAmount) == 0x0004C0, "Member 'UWBP_QuantitySelector_C::SelectedAmount' has a wrong offset!");

}

