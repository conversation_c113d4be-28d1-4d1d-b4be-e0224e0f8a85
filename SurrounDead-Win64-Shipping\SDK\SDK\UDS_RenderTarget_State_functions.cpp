﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_RenderTarget_State

#include "Basic.hpp"

#include "UDS_RenderTarget_State_classes.hpp"
#include "UDS_RenderTarget_State_parameters.hpp"


namespace SDK
{

// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Canvas Brush Location
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// const struct FVector2D&                 In                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector2D*                       Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDS_RenderTarget_State_C::Canvas_Brush_Location(const struct FVector2D& In, struct FVector2D* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDS_RenderTarget_State_C", "Canvas Brush Location");

	Params::UDS_RenderTarget_State_C_Canvas_Brush_Location Parms{};

	Parms.In = std::move(In);

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Canvas Brush Size
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// const struct FVector2D&                 In                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector2D*                       Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDS_RenderTarget_State_C::Canvas_Brush_Size(const struct FVector2D& In, struct FVector2D* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDS_RenderTarget_State_C", "Canvas Brush Size");

	Params::UDS_RenderTarget_State_C_Canvas_Brush_Size Parms{};

	Parms.In = std::move(In);

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Set Location
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Center_Location_0                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Mapping_Vector4                                        (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDS_RenderTarget_State_C::Set_Location(const struct FVector& Center_Location_0, struct FLinearColor* Mapping_Vector4)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDS_RenderTarget_State_C", "Set Location");

	Params::UDS_RenderTarget_State_C_Set_Location Parms{};

	Parms.Center_Location_0 = std::move(Center_Location_0);

	UObject::ProcessEvent(Func, &Parms);

	if (Mapping_Vector4 != nullptr)
		*Mapping_Vector4 = std::move(Parms.Mapping_Vector4);
}


// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Set Render Target
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTextureRenderTarget2D*           Render_Target_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class UTextureRenderTarget2D*           Buffer_Target_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UUDS_RenderTarget_State_C::Set_Render_Target(class UTextureRenderTarget2D* Render_Target_0, class UTextureRenderTarget2D* Buffer_Target_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDS_RenderTarget_State_C", "Set Render Target");

	Params::UDS_RenderTarget_State_C_Set_Render_Target Parms{};

	Parms.Render_Target_0 = Render_Target_0;
	Parms.Buffer_Target_0 = Buffer_Target_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Set Size
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Size_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDS_RenderTarget_State_C::Set_Size(double Size_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDS_RenderTarget_State_C", "Set Size");

	Params::UDS_RenderTarget_State_C_Set_Size Parms{};

	Parms.Size_0 = Size_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Target Extent 2D
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector2D*                       Extent                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDS_RenderTarget_State_C::Target_Extent_2D(struct FVector2D* Extent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDS_RenderTarget_State_C", "Target Extent 2D");

	Params::UDS_RenderTarget_State_C_Target_Extent_2D Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Extent != nullptr)
		*Extent = std::move(Parms.Extent);
}


// Function UDS_RenderTarget_State.UDS_RenderTarget_State_C.Target Needs Recenter
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Control_Location                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Axis_Mask                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Yes                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UUDS_RenderTarget_State_C::Target_Needs_Recenter(const struct FVector& Control_Location, const struct FVector& Axis_Mask, bool* Yes)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("UDS_RenderTarget_State_C", "Target Needs Recenter");

	Params::UDS_RenderTarget_State_C_Target_Needs_Recenter Parms{};

	Parms.Control_Location = std::move(Control_Location);
	Parms.Axis_Mask = std::move(Axis_Mask);

	UObject::ProcessEvent(Func, &Parms);

	if (Yes != nullptr)
		*Yes = Parms.Yes;
}

}

