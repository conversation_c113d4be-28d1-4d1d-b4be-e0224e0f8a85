﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_PlayerMarker

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "S_MarkerData_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_PlayerMarker.W_PlayerMarker_C
// 0x00B8 (0x0378 - 0x02C0)
class UW_PlayerMarker_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Hover;                                             // 0x02C8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 Fade;                                              // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Icon;                                              // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               Marker_Box;                                        // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           VerticalBox_2;                                     // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	bool                                          Is_Hidden_;                                        // 0x02F0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2F1[0x7];                                      // 0x02F1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ABP_PlayerMarker_C*                     Target_Actor;                                      // 0x02F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	struct FS_MarkerData                          Marker_Data;                                       // 0x0300(0x0070)(Edit, BlueprintVisible, ExposeOnSpawn, HasGetValueTypeHash)
	int32                                         Index_0;                                           // 0x0370(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)

public:
	void Construct();
	void Construct_Marker();
	void ExecuteUbergraph_W_PlayerMarker(int32 EntryPoint);
	struct FEventReply OnMouseButtonDoubleClick(const struct FGeometry& InMyGeometry, const struct FPointerEvent& InMouseEvent);
	void OnMouseEnter(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	void OnMouseLeave(const struct FPointerEvent& MouseEvent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_PlayerMarker_C">();
	}
	static class UW_PlayerMarker_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_PlayerMarker_C>();
	}
};
static_assert(alignof(UW_PlayerMarker_C) == 0x000008, "Wrong alignment on UW_PlayerMarker_C");
static_assert(sizeof(UW_PlayerMarker_C) == 0x000378, "Wrong size on UW_PlayerMarker_C");
static_assert(offsetof(UW_PlayerMarker_C, UberGraphFrame) == 0x0002C0, "Member 'UW_PlayerMarker_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_PlayerMarker_C, Hover) == 0x0002C8, "Member 'UW_PlayerMarker_C::Hover' has a wrong offset!");
static_assert(offsetof(UW_PlayerMarker_C, Fade) == 0x0002D0, "Member 'UW_PlayerMarker_C::Fade' has a wrong offset!");
static_assert(offsetof(UW_PlayerMarker_C, Icon) == 0x0002D8, "Member 'UW_PlayerMarker_C::Icon' has a wrong offset!");
static_assert(offsetof(UW_PlayerMarker_C, Marker_Box) == 0x0002E0, "Member 'UW_PlayerMarker_C::Marker_Box' has a wrong offset!");
static_assert(offsetof(UW_PlayerMarker_C, VerticalBox_2) == 0x0002E8, "Member 'UW_PlayerMarker_C::VerticalBox_2' has a wrong offset!");
static_assert(offsetof(UW_PlayerMarker_C, Is_Hidden_) == 0x0002F0, "Member 'UW_PlayerMarker_C::Is_Hidden_' has a wrong offset!");
static_assert(offsetof(UW_PlayerMarker_C, Target_Actor) == 0x0002F8, "Member 'UW_PlayerMarker_C::Target_Actor' has a wrong offset!");
static_assert(offsetof(UW_PlayerMarker_C, Marker_Data) == 0x000300, "Member 'UW_PlayerMarker_C::Marker_Data' has a wrong offset!");
static_assert(offsetof(UW_PlayerMarker_C, Index_0) == 0x000370, "Member 'UW_PlayerMarker_C::Index_0' has a wrong offset!");

}

