﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_CampfireUI

#include "Basic.hpp"

#include "GameplayTags_structs.hpp"
#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "S_JigCrafting_structs.hpp"
#include "RepItemInfo_structs.hpp"
#include "InputCore_structs.hpp"


namespace SDK::Params
{

// Function W_CampfireUI.W_CampfireUI_C.AddRequiredItems
// 0x0180 (0x0180 - 0x0000)
struct W_CampfireUI_C_AddRequiredItems final
{
public:
	struct FRepItemInfo                           ItemInfo;                                          // 0x0000(0x0078)(Edit, BlueprintVisible, HasGetValueTypeHash)
	bool                                          CanCraft_;                                         // 0x0078(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79[0x3];                                       // 0x0079(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x007C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x008C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0090(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_94[0x4];                                       // 0x0094(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSI_Slot_C*>                    CallFunc_CheckCraftingIngredients_ItemFound_Element; // 0x0098(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UJSI_Slot_C*                            CallFunc_Array_Get_Item;                           // 0x00A8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGuid                                  CallFunc_GetUniqueID_UniqueServerID;               // 0x00B4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00C4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C5[0x3];                                       // 0x00C5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x00C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsValidIndex_ReturnValue;           // 0x00CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_CD[0x3];                                       // 0x00CD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Map_Find_Value;                           // 0x00D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x00D4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D5[0x3];                                       // 0x00D5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_DC[0x4];                                       // 0x00DC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FRepItemInfo                           CallFunc_Array_Get_Item_1;                         // 0x00E0(0x0078)(HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_159[0x3];                                      // 0x0159(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Multiply_IntInt_ReturnValue;              // 0x015C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Added;                // 0x0160(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_161[0x3];                                      // 0x0161(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_AddNewInventoryItem_SlotIndex;            // 0x0164(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UJigsawItem_DataAsset_C*                CallFunc_AddNewInventoryItem_ItemInfo;             // 0x0168(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            CallFunc_AddNewInventoryItem_SlotItemRef;          // 0x0170(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Stacked_;             // 0x0178(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_AddRequiredItems) == 0x000008, "Wrong alignment on W_CampfireUI_C_AddRequiredItems");
static_assert(sizeof(W_CampfireUI_C_AddRequiredItems) == 0x000180, "Wrong size on W_CampfireUI_C_AddRequiredItems");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, ItemInfo) == 0x000000, "Member 'W_CampfireUI_C_AddRequiredItems::ItemInfo' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CanCraft_) == 0x000078, "Member 'W_CampfireUI_C_AddRequiredItems::CanCraft_' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, Temp_int_Array_Index_Variable) == 0x00007C, "Member 'W_CampfireUI_C_AddRequiredItems::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, Temp_int_Loop_Counter_Variable) == 0x000080, "Member 'W_CampfireUI_C_AddRequiredItems::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Add_IntInt_ReturnValue) == 0x000084, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, Temp_int_Array_Index_Variable_1) == 0x000088, "Member 'W_CampfireUI_C_AddRequiredItems::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, Temp_int_Loop_Counter_Variable_1) == 0x00008C, "Member 'W_CampfireUI_C_AddRequiredItems::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Add_IntInt_ReturnValue_1) == 0x000090, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_CheckCraftingIngredients_ItemFound_Element) == 0x000098, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_CheckCraftingIngredients_ItemFound_Element' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Array_Get_Item) == 0x0000A8, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Array_Length_ReturnValue) == 0x0000B0, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_GetUniqueID_UniqueServerID) == 0x0000B4, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_GetUniqueID_UniqueServerID' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Less_IntInt_ReturnValue) == 0x0000C4, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Array_Add_ReturnValue) == 0x0000C8, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Array_IsValidIndex_ReturnValue) == 0x0000CC, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Array_IsValidIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Map_Find_Value) == 0x0000D0, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Map_Find_ReturnValue) == 0x0000D4, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Array_Length_ReturnValue_1) == 0x0000D8, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Array_Get_Item_1) == 0x0000E0, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Less_IntInt_ReturnValue_1) == 0x000158, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_Multiply_IntInt_ReturnValue) == 0x00015C, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_Multiply_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_Added) == 0x000160, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_Added' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_SlotIndex) == 0x000164, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_SlotIndex' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_ItemInfo) == 0x000168, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_ItemInfo' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_SlotItemRef) == 0x000170, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_SlotItemRef' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_Stacked_) == 0x000178, "Member 'W_CampfireUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_Stacked_' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.CheckIngredientsAvailability
// 0x0158 (0x0158 - 0x0000)
struct W_CampfireUI_C_CheckIngredientsAvailability final
{
public:
	bool                                          Proceed;                                           // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FRepItemInfo                           ItemInfo;                                          // 0x0008(0x0078)(Edit, BlueprintVisible, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x008C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0090(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0094(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class UJSI_Slot_C*>                    CallFunc_CheckCraftingIngredients_ItemFound_Element; // 0x0098(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UJSI_Slot_C*                            CallFunc_Array_Get_Item;                           // 0x00A8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGuid                                  CallFunc_GetUniqueID_UniqueServerID;               // 0x00B4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00C4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C5[0x3];                                       // 0x00C5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x00C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsValidIndex_ReturnValue;           // 0x00CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_CD[0x3];                                       // 0x00CD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D4[0x4];                                       // 0x00D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FRepItemInfo                           CallFunc_Array_Get_Item_1;                         // 0x00D8(0x0078)(HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0150(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_CheckIngredientsAvailability) == 0x000008, "Wrong alignment on W_CampfireUI_C_CheckIngredientsAvailability");
static_assert(sizeof(W_CampfireUI_C_CheckIngredientsAvailability) == 0x000158, "Wrong size on W_CampfireUI_C_CheckIngredientsAvailability");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, Proceed) == 0x000000, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::Proceed' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, ItemInfo) == 0x000008, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::ItemInfo' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, Temp_int_Loop_Counter_Variable) == 0x000080, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Add_IntInt_ReturnValue) == 0x000084, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, Temp_int_Array_Index_Variable) == 0x000088, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, Temp_int_Array_Index_Variable_1) == 0x00008C, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, Temp_int_Loop_Counter_Variable_1) == 0x000090, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Add_IntInt_ReturnValue_1) == 0x000094, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_CheckCraftingIngredients_ItemFound_Element) == 0x000098, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_CheckCraftingIngredients_ItemFound_Element' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Array_Get_Item) == 0x0000A8, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Array_Length_ReturnValue) == 0x0000B0, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_GetUniqueID_UniqueServerID) == 0x0000B4, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_GetUniqueID_UniqueServerID' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Less_IntInt_ReturnValue) == 0x0000C4, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Array_Add_ReturnValue) == 0x0000C8, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Array_IsValidIndex_ReturnValue) == 0x0000CC, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Array_IsValidIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Array_Length_ReturnValue_1) == 0x0000D0, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Array_Get_Item_1) == 0x0000D8, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_CheckIngredientsAvailability, CallFunc_Less_IntInt_ReturnValue_1) == 0x000150, "Member 'W_CampfireUI_C_CheckIngredientsAvailability::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.EventOnMouseButtonDown
// 0x0028 (0x0028 - 0x0000)
struct W_CampfireUI_C_EventOnMouseButtonDown final
{
public:
	class UJSIContainer_C*                        Container;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            SlotRef;                                           // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FKey                                   Button;                                            // 0x0010(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_EventOnMouseButtonDown) == 0x000008, "Wrong alignment on W_CampfireUI_C_EventOnMouseButtonDown");
static_assert(sizeof(W_CampfireUI_C_EventOnMouseButtonDown) == 0x000028, "Wrong size on W_CampfireUI_C_EventOnMouseButtonDown");
static_assert(offsetof(W_CampfireUI_C_EventOnMouseButtonDown, Container) == 0x000000, "Member 'W_CampfireUI_C_EventOnMouseButtonDown::Container' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_EventOnMouseButtonDown, SlotRef) == 0x000008, "Member 'W_CampfireUI_C_EventOnMouseButtonDown::SlotRef' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_EventOnMouseButtonDown, Button) == 0x000010, "Member 'W_CampfireUI_C_EventOnMouseButtonDown::Button' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.ExecuteUbergraph_W_CampfireUI
// 0x0228 (0x0228 - 0x0000)
struct W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UJSIContainer_C* Container, class UJSI_Slot_C* SlotRef, const struct FKey& Button)> K2Node_CreateDelegate_OutputDelegate; // 0x0004(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGuid                                  Temp_struct_Variable;                              // 0x0018(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x3];                                       // 0x0029(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_1;                               // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            K2Node_Event_ItemRef;                              // 0x0038(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 K2Node_Event_ActorRef;                             // 0x0040(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_NewWeight;                            // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUserWidget*                            K2Node_Event_Widget;                               // 0x0050(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   K2Node_Event_Name;                                 // 0x0058(0x0018)()
	class UBP_InspectorWindowWidget_C*            K2Node_Event_Inspector;                            // 0x0070(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        K2Node_CustomEvent_Container;                      // 0x0078(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            K2Node_CustomEvent_SlotRef;                        // 0x0080(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FKey                                   K2Node_CustomEvent_Button;                         // 0x0088(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A1[0x7];                                       // 0x00A1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetItemName_Name;                         // 0x00A8(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x00C0(0x0050)(HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0110(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0120(0x0018)()
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0138(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0139(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13A[0x2];                                      // 0x013A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x013C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_141[0x3];                                      // 0x0141(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0144(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetIsEnabled_ReturnValue;                 // 0x0148(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x0149(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CheckIngredientsAvailability_Proceed;     // 0x014A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14B[0x1];                                      // 0x014B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_2;                               // 0x014C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            CallFunc_JigTryAddGetPendingRef_OutputPin;         // 0x0150(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JigTryAddGetPendingRef_Stack_;            // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_isRotated__ReturnValue;                   // 0x0159(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15A[0x2];                                      // 0x015A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGuid                                  CallFunc_GetUniqueID_UniqueServerID;               // 0x015C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x016C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_16D[0x3];                                      // 0x016D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGuid                                  K2Node_Select_Default;                             // 0x0170(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CheckIngredientsAvailability_Proceed_1;   // 0x0180(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0181(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_182[0x6];                                      // 0x0182(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class ULevellingComponent_C*                  CallFunc_GetLevellingComponent_LevellingComponent; // 0x0188(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_IntFloat_ReturnValue;            // 0x0190(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_AddXP_XPOutput;                           // 0x0198(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue;                       // 0x01A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A4[0x4];                                      // 0x01A4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	int64                                         CallFunc_Conv_IntToInt64_ReturnValue;              // 0x01A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x01B0(0x0050)(HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array_1;                          // 0x0200(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue_1;                     // 0x0210(0x0018)()
};
static_assert(alignof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI) == 0x000008, "Wrong alignment on W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI");
static_assert(sizeof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI) == 0x000228, "Wrong size on W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, EntryPoint) == 0x000000, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_CreateDelegate_OutputDelegate) == 0x000004, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, Temp_int_Variable) == 0x000014, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, Temp_struct_Variable) == 0x000018, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::Temp_struct_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, Temp_bool_Variable) == 0x000028, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, Temp_int_Variable_1) == 0x00002C, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_Add_IntInt_ReturnValue) == 0x000030, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_Event_ItemRef) == 0x000038, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_Event_ItemRef' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_Event_ActorRef) == 0x000040, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_Event_ActorRef' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_Event_NewWeight) == 0x000048, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_Event_NewWeight' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_Event_Widget) == 0x000050, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_Event_Widget' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_Event_Name) == 0x000058, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_Event_Name' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_Event_Inspector) == 0x000070, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_Event_Inspector' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_CustomEvent_Container) == 0x000078, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_CustomEvent_Container' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_CustomEvent_SlotRef) == 0x000080, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_CustomEvent_SlotRef' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_CustomEvent_Button) == 0x000088, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_CustomEvent_Button' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x0000A0, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_GetItemName_Name) == 0x0000A8, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_GetItemName_Name' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_MakeStruct_FormatArgumentData) == 0x0000C0, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_MakeArray_Array) == 0x000110, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_Format_ReturnValue) == 0x000120, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_IsValid_ReturnValue) == 0x000138, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_Greater_IntInt_ReturnValue) == 0x000139, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_Add_IntInt_ReturnValue_1) == 0x00013C, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, Temp_bool_Has_Been_Initd_Variable) == 0x000140, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_Subtract_IntInt_ReturnValue) == 0x000144, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_GetIsEnabled_ReturnValue) == 0x000148, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_GetIsEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, Temp_bool_IsClosed_Variable) == 0x000149, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_CheckIngredientsAvailability_Proceed) == 0x00014A, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_CheckIngredientsAvailability_Proceed' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, Temp_int_Variable_2) == 0x00014C, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::Temp_int_Variable_2' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_JigTryAddGetPendingRef_OutputPin) == 0x000150, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_JigTryAddGetPendingRef_OutputPin' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_JigTryAddGetPendingRef_Stack_) == 0x000158, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_JigTryAddGetPendingRef_Stack_' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_isRotated__ReturnValue) == 0x000159, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_isRotated__ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_GetUniqueID_UniqueServerID) == 0x00015C, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_GetUniqueID_UniqueServerID' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_IsValid_ReturnValue_1) == 0x00016C, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_Select_Default) == 0x000170, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_CheckIngredientsAvailability_Proceed_1) == 0x000180, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_CheckIngredientsAvailability_Proceed_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000181, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_GetLevellingComponent_LevellingComponent) == 0x000188, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_GetLevellingComponent_LevellingComponent' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_Multiply_IntFloat_ReturnValue) == 0x000190, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_AddXP_XPOutput) == 0x000198, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_AddXP_XPOutput' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_FTrunc_ReturnValue) == 0x0001A0, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_FTrunc_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_Conv_IntToInt64_ReturnValue) == 0x0001A8, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_Conv_IntToInt64_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_MakeStruct_FormatArgumentData_1) == 0x0001B0, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, K2Node_MakeArray_Array_1) == 0x000200, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::K2Node_MakeArray_Array_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI, CallFunc_Format_ReturnValue_1) == 0x000210, "Member 'W_CampfireUI_C_ExecuteUbergraph_W_CampfireUI::CallFunc_Format_ReturnValue_1' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.GetAllAttachments
// 0x0010 (0x0010 - 0x0000)
struct W_CampfireUI_C_GetAllAttachments final
{
public:
	TArray<class FName>                           Attachments;                                       // 0x0000(0x0010)(Parm, OutParm)
};
static_assert(alignof(W_CampfireUI_C_GetAllAttachments) == 0x000008, "Wrong alignment on W_CampfireUI_C_GetAllAttachments");
static_assert(sizeof(W_CampfireUI_C_GetAllAttachments) == 0x000010, "Wrong size on W_CampfireUI_C_GetAllAttachments");
static_assert(offsetof(W_CampfireUI_C_GetAllAttachments, Attachments) == 0x000000, "Member 'W_CampfireUI_C_GetAllAttachments::Attachments' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.GetContainerByAttachmentType
// 0x0018 (0x0018 - 0x0000)
struct W_CampfireUI_C_GetContainerByAttachmentType final
{
public:
	struct FGameplayTag                           Type;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        JigContainer;                                      // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         ContainerIndex;                                    // 0x0010(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_GetContainerByAttachmentType) == 0x000008, "Wrong alignment on W_CampfireUI_C_GetContainerByAttachmentType");
static_assert(sizeof(W_CampfireUI_C_GetContainerByAttachmentType) == 0x000018, "Wrong size on W_CampfireUI_C_GetContainerByAttachmentType");
static_assert(offsetof(W_CampfireUI_C_GetContainerByAttachmentType, Type) == 0x000000, "Member 'W_CampfireUI_C_GetContainerByAttachmentType::Type' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_GetContainerByAttachmentType, JigContainer) == 0x000008, "Member 'W_CampfireUI_C_GetContainerByAttachmentType::JigContainer' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_GetContainerByAttachmentType, ContainerIndex) == 0x000010, "Member 'W_CampfireUI_C_GetContainerByAttachmentType::ContainerIndex' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.GetDropWidget
// 0x0008 (0x0008 - 0x0000)
struct W_CampfireUI_C_GetDropWidget final
{
public:
	class UDropItemBackGwidget_C*                 DropWRef;                                          // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_GetDropWidget) == 0x000008, "Wrong alignment on W_CampfireUI_C_GetDropWidget");
static_assert(sizeof(W_CampfireUI_C_GetDropWidget) == 0x000008, "Wrong size on W_CampfireUI_C_GetDropWidget");
static_assert(offsetof(W_CampfireUI_C_GetDropWidget, DropWRef) == 0x000000, "Member 'W_CampfireUI_C_GetDropWidget::DropWRef' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.GetJSIContainerByPlayerSlots
// 0x0020 (0x0020 - 0x0000)
struct W_CampfireUI_C_GetJSIContainerByPlayerSlots final
{
public:
	struct FGameplayTag                           Slot_0;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        Container;                                         // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            EquippedItem;                                      // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          IsPending_;                                        // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_GetJSIContainerByPlayerSlots) == 0x000008, "Wrong alignment on W_CampfireUI_C_GetJSIContainerByPlayerSlots");
static_assert(sizeof(W_CampfireUI_C_GetJSIContainerByPlayerSlots) == 0x000020, "Wrong size on W_CampfireUI_C_GetJSIContainerByPlayerSlots");
static_assert(offsetof(W_CampfireUI_C_GetJSIContainerByPlayerSlots, Slot_0) == 0x000000, "Member 'W_CampfireUI_C_GetJSIContainerByPlayerSlots::Slot_0' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_GetJSIContainerByPlayerSlots, Container) == 0x000008, "Member 'W_CampfireUI_C_GetJSIContainerByPlayerSlots::Container' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_GetJSIContainerByPlayerSlots, EquippedItem) == 0x000010, "Member 'W_CampfireUI_C_GetJSIContainerByPlayerSlots::EquippedItem' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_GetJSIContainerByPlayerSlots, IsPending_) == 0x000018, "Member 'W_CampfireUI_C_GetJSIContainerByPlayerSlots::IsPending_' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.GetListOfNonAddContainers
// 0x0010 (0x0010 - 0x0000)
struct W_CampfireUI_C_GetListOfNonAddContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_CampfireUI_C_GetListOfNonAddContainers) == 0x000008, "Wrong alignment on W_CampfireUI_C_GetListOfNonAddContainers");
static_assert(sizeof(W_CampfireUI_C_GetListOfNonAddContainers) == 0x000010, "Wrong size on W_CampfireUI_C_GetListOfNonAddContainers");
static_assert(offsetof(W_CampfireUI_C_GetListOfNonAddContainers, Containers) == 0x000000, "Member 'W_CampfireUI_C_GetListOfNonAddContainers::Containers' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.GetLootContent
// 0x0008 (0x0008 - 0x0000)
struct W_CampfireUI_C_GetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_GetLootContent) == 0x000008, "Wrong alignment on W_CampfireUI_C_GetLootContent");
static_assert(sizeof(W_CampfireUI_C_GetLootContent) == 0x000008, "Wrong size on W_CampfireUI_C_GetLootContent");
static_assert(offsetof(W_CampfireUI_C_GetLootContent, Widget) == 0x000000, "Member 'W_CampfireUI_C_GetLootContent::Widget' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.GetText
// 0x0060 (0x0060 - 0x0000)
struct W_CampfireUI_C_GetText final
{
public:
	class FText                                   ReturnValue;                                       // 0x0000(0x0018)(Parm, OutParm, ReturnParm)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0018(0x0018)()
	class FText                                   CallFunc_Append_Text_Return_Value;                 // 0x0030(0x0018)()
	class FText                                   CallFunc_Append_Text_Return_Value_1;               // 0x0048(0x0018)()
};
static_assert(alignof(W_CampfireUI_C_GetText) == 0x000008, "Wrong alignment on W_CampfireUI_C_GetText");
static_assert(sizeof(W_CampfireUI_C_GetText) == 0x000060, "Wrong size on W_CampfireUI_C_GetText");
static_assert(offsetof(W_CampfireUI_C_GetText, ReturnValue) == 0x000000, "Member 'W_CampfireUI_C_GetText::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_GetText, CallFunc_Conv_IntToText_ReturnValue) == 0x000018, "Member 'W_CampfireUI_C_GetText::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_GetText, CallFunc_Append_Text_Return_Value) == 0x000030, "Member 'W_CampfireUI_C_GetText::CallFunc_Append_Text_Return_Value' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_GetText, CallFunc_Append_Text_Return_Value_1) == 0x000048, "Member 'W_CampfireUI_C_GetText::CallFunc_Append_Text_Return_Value_1' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.GetValidReloadContainers
// 0x0010 (0x0010 - 0x0000)
struct W_CampfireUI_C_GetValidReloadContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_CampfireUI_C_GetValidReloadContainers) == 0x000008, "Wrong alignment on W_CampfireUI_C_GetValidReloadContainers");
static_assert(sizeof(W_CampfireUI_C_GetValidReloadContainers) == 0x000010, "Wrong size on W_CampfireUI_C_GetValidReloadContainers");
static_assert(offsetof(W_CampfireUI_C_GetValidReloadContainers, Containers) == 0x000000, "Member 'W_CampfireUI_C_GetValidReloadContainers::Containers' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.InitializeInventory
// 0x0108 (0x0108 - 0x0000)
struct W_CampfireUI_C_InitializeInventory final
{
public:
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FS_JigCrafting                         CallFunc_Array_Get_Item;                           // 0x0008(0x00B8)(HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C4[0x4];                                       // 0x00C4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class APawn*                                  CallFunc_GetPlayerPawn_ReturnValue;                // 0x00C8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_JigMultiplayer_C*                   CallFunc_GetComponentByClass_ReturnValue;          // 0x00D0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UBP_JigMultiplayer_C*                   CallFunc_GetComponentByClass_ReturnValue_1;        // 0x00D8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x00E1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E2[0x2];                                       // 0x00E2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x00E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Added;                // 0x00E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E9[0x3];                                       // 0x00E9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_AddNewInventoryItem_SlotIndex;            // 0x00EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UJigsawItem_DataAsset_C*                CallFunc_AddNewInventoryItem_ItemInfo;             // 0x00F0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            CallFunc_AddNewInventoryItem_SlotItemRef;          // 0x00F8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Stacked_;             // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0101(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_102[0x2];                                      // 0x0102(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0104(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_InitializeInventory) == 0x000008, "Wrong alignment on W_CampfireUI_C_InitializeInventory");
static_assert(sizeof(W_CampfireUI_C_InitializeInventory) == 0x000108, "Wrong size on W_CampfireUI_C_InitializeInventory");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, Temp_int_Array_Index_Variable) == 0x000000, "Member 'W_CampfireUI_C_InitializeInventory::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_IsValid_ReturnValue) == 0x000004, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_Array_Get_Item) == 0x000008, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_Array_Length_ReturnValue) == 0x0000C0, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_GetPlayerPawn_ReturnValue) == 0x0000C8, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_GetPlayerPawn_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_GetComponentByClass_ReturnValue) == 0x0000D0, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_GetComponentByClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_GetComponentByClass_ReturnValue_1) == 0x0000D8, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_GetComponentByClass_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_IsVisible_ReturnValue) == 0x0000E0, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_Not_PreBool_ReturnValue) == 0x0000E1, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, Temp_int_Loop_Counter_Variable) == 0x0000E4, "Member 'W_CampfireUI_C_InitializeInventory::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_Added) == 0x0000E8, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_Added' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_SlotIndex) == 0x0000EC, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_SlotIndex' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_ItemInfo) == 0x0000F0, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_ItemInfo' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_SlotItemRef) == 0x0000F8, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_SlotItemRef' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_Stacked_) == 0x000100, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_Stacked_' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_Less_IntInt_ReturnValue) == 0x000101, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_InitializeInventory, CallFunc_Add_IntInt_ReturnValue) == 0x000104, "Member 'W_CampfireUI_C_InitializeInventory::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.JigSetLootContent
// 0x0020 (0x0020 - 0x0000)
struct W_CampfireUI_C_JigSetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   Name_0;                                            // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(W_CampfireUI_C_JigSetLootContent) == 0x000008, "Wrong alignment on W_CampfireUI_C_JigSetLootContent");
static_assert(sizeof(W_CampfireUI_C_JigSetLootContent) == 0x000020, "Wrong size on W_CampfireUI_C_JigSetLootContent");
static_assert(offsetof(W_CampfireUI_C_JigSetLootContent, Widget) == 0x000000, "Member 'W_CampfireUI_C_JigSetLootContent::Widget' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_JigSetLootContent, Name_0) == 0x000008, "Member 'W_CampfireUI_C_JigSetLootContent::Name_0' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.JSIOnWeightUpdated
// 0x0008 (0x0008 - 0x0000)
struct W_CampfireUI_C_JSIOnWeightUpdated final
{
public:
	double                                        NewWeight;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong alignment on W_CampfireUI_C_JSIOnWeightUpdated");
static_assert(sizeof(W_CampfireUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong size on W_CampfireUI_C_JSIOnWeightUpdated");
static_assert(offsetof(W_CampfireUI_C_JSIOnWeightUpdated, NewWeight) == 0x000000, "Member 'W_CampfireUI_C_JSIOnWeightUpdated::NewWeight' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.SetActionbarFollower
// 0x0010 (0x0010 - 0x0000)
struct W_CampfireUI_C_SetActionbarFollower final
{
public:
	class UJSI_Slot_C*                            JigRef;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Return;                                            // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_SetActionbarFollower) == 0x000008, "Wrong alignment on W_CampfireUI_C_SetActionbarFollower");
static_assert(sizeof(W_CampfireUI_C_SetActionbarFollower) == 0x000010, "Wrong size on W_CampfireUI_C_SetActionbarFollower");
static_assert(offsetof(W_CampfireUI_C_SetActionbarFollower, JigRef) == 0x000000, "Member 'W_CampfireUI_C_SetActionbarFollower::JigRef' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetActionbarFollower, Return) == 0x000008, "Member 'W_CampfireUI_C_SetActionbarFollower::Return' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.SetActorOwner
// 0x0008 (0x0008 - 0x0000)
struct W_CampfireUI_C_SetActorOwner final
{
public:
	class AActor*                                 ActorRef;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_SetActorOwner) == 0x000008, "Wrong alignment on W_CampfireUI_C_SetActorOwner");
static_assert(sizeof(W_CampfireUI_C_SetActorOwner) == 0x000008, "Wrong size on W_CampfireUI_C_SetActorOwner");
static_assert(offsetof(W_CampfireUI_C_SetActorOwner, ActorRef) == 0x000000, "Member 'W_CampfireUI_C_SetActorOwner::ActorRef' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.SetCraftableItems
// 0x01B0 (0x01B0 - 0x0000)
struct W_CampfireUI_C_SetCraftableItems final
{
public:
	bool                                          InvalidItem;                                       // 0x0000(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSI_Slot_C*>                    CallFunc_Map_Keys_Keys;                            // 0x0020(0x0010)(ReferenceParm, ContainsInstancedReference)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            CallFunc_Array_Get_Item;                           // 0x0038(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FGuid>                          K2Node_MakeArray_Array;                            // 0x0048(0x0010)(ReferenceParm)
	struct FS_JigCrafting                         CallFunc_Array_Get_Item_1;                         // 0x0058(0x00B8)(HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0110(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0114(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_115[0x3];                                      // 0x0115(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FRepItemInfo                           CallFunc_Array_Get_Item_2;                         // 0x0118(0x0078)(HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0190(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0194(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_195[0x3];                                      // 0x0195(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSI_Slot_C*>                    CallFunc_CheckCraftingIngredients_ItemFound_Element; // 0x0198(0x0010)(ReferenceParm, ContainsInstancedReference)
	bool                                          CallFunc_Array_IsValidIndex_ReturnValue;           // 0x01A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_SetCraftableItems) == 0x000008, "Wrong alignment on W_CampfireUI_C_SetCraftableItems");
static_assert(sizeof(W_CampfireUI_C_SetCraftableItems) == 0x0001B0, "Wrong size on W_CampfireUI_C_SetCraftableItems");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, InvalidItem) == 0x000000, "Member 'W_CampfireUI_C_SetCraftableItems::InvalidItem' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, Temp_int_Array_Index_Variable) == 0x000004, "Member 'W_CampfireUI_C_SetCraftableItems::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, Temp_int_Loop_Counter_Variable) == 0x000008, "Member 'W_CampfireUI_C_SetCraftableItems::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, Temp_int_Loop_Counter_Variable_1) == 0x00000C, "Member 'W_CampfireUI_C_SetCraftableItems::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Add_IntInt_ReturnValue) == 0x000010, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Add_IntInt_ReturnValue_1) == 0x000014, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, Temp_int_Array_Index_Variable_1) == 0x000018, "Member 'W_CampfireUI_C_SetCraftableItems::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Map_Keys_Keys) == 0x000020, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Map_Keys_Keys' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_IsValid_ReturnValue) == 0x000030, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Array_Get_Item) == 0x000038, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_IsValid_ReturnValue_1) == 0x000040, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, K2Node_MakeArray_Array) == 0x000048, "Member 'W_CampfireUI_C_SetCraftableItems::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Array_Get_Item_1) == 0x000058, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Array_Length_ReturnValue) == 0x000110, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Less_IntInt_ReturnValue) == 0x000114, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Array_Get_Item_2) == 0x000118, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Array_Length_ReturnValue_1) == 0x000190, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Less_IntInt_ReturnValue_1) == 0x000194, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_CheckCraftingIngredients_ItemFound_Element) == 0x000198, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_CheckCraftingIngredients_ItemFound_Element' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_SetCraftableItems, CallFunc_Array_IsValidIndex_ReturnValue) == 0x0001A8, "Member 'W_CampfireUI_C_SetCraftableItems::CallFunc_Array_IsValidIndex_ReturnValue' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.SetInspectorRef
// 0x0008 (0x0008 - 0x0000)
struct W_CampfireUI_C_SetInspectorRef final
{
public:
	class UBP_InspectorWindowWidget_C*            Inspector;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_SetInspectorRef) == 0x000008, "Wrong alignment on W_CampfireUI_C_SetInspectorRef");
static_assert(sizeof(W_CampfireUI_C_SetInspectorRef) == 0x000008, "Wrong size on W_CampfireUI_C_SetInspectorRef");
static_assert(offsetof(W_CampfireUI_C_SetInspectorRef, Inspector) == 0x000000, "Member 'W_CampfireUI_C_SetInspectorRef::Inspector' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.SetItemReference
// 0x0008 (0x0008 - 0x0000)
struct W_CampfireUI_C_SetItemReference final
{
public:
	class UJSI_Slot_C*                            ItemRef;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_CampfireUI_C_SetItemReference) == 0x000008, "Wrong alignment on W_CampfireUI_C_SetItemReference");
static_assert(sizeof(W_CampfireUI_C_SetItemReference) == 0x000008, "Wrong size on W_CampfireUI_C_SetItemReference");
static_assert(offsetof(W_CampfireUI_C_SetItemReference, ItemRef) == 0x000000, "Member 'W_CampfireUI_C_SetItemReference::ItemRef' has a wrong offset!");

// Function W_CampfireUI.W_CampfireUI_C.GetListOfContainers
// 0x0020 (0x0020 - 0x0000)
struct W_CampfireUI_C_GetListOfContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
	TArray<class UJSIContainer_C*>                K2Node_MakeArray_Array;                            // 0x0010(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(W_CampfireUI_C_GetListOfContainers) == 0x000008, "Wrong alignment on W_CampfireUI_C_GetListOfContainers");
static_assert(sizeof(W_CampfireUI_C_GetListOfContainers) == 0x000020, "Wrong size on W_CampfireUI_C_GetListOfContainers");
static_assert(offsetof(W_CampfireUI_C_GetListOfContainers, Containers) == 0x000000, "Member 'W_CampfireUI_C_GetListOfContainers::Containers' has a wrong offset!");
static_assert(offsetof(W_CampfireUI_C_GetListOfContainers, K2Node_MakeArray_Array) == 0x000010, "Member 'W_CampfireUI_C_GetListOfContainers::K2Node_MakeArray_Array' has a wrong offset!");

}

