﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeButton

#include "Basic.hpp"


namespace SDK::Params
{

// Function WBP_NarrativeButton.WBP_NarrativeButton_C.ExecuteUbergraph_WBP_NarrativeButton
// 0x0050 (0x0050 - 0x0000)
struct WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TSubclassOf<class UCommonTextStyle>           CallFunc_GetCurrentTextStyleClass_ReturnValue;     // 0x0008(0x0008)(ZeroConstructor, NoD<PERSON>ru<PERSON>, UObjectWrapper, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12[0x6];                                       // 0x0012(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_CustomEvent_Text;                           // 0x0018(0x0018)()
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0038(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UHorizontalBoxSlot*                     CallFunc_SlotAsHorizontalBoxSlot_ReturnValue;      // 0x0040(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton) == 0x000008, "Wrong alignment on WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton");
static_assert(sizeof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton) == 0x000050, "Wrong size on WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton");
static_assert(offsetof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton, EntryPoint) == 0x000000, "Member 'WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton::EntryPoint' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton, CallFunc_GetCurrentTextStyleClass_ReturnValue) == 0x000008, "Member 'WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton::CallFunc_GetCurrentTextStyleClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton, CallFunc_IsValid_ReturnValue) == 0x000010, "Member 'WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton, K2Node_Event_IsDesignTime) == 0x000011, "Member 'WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton, K2Node_CustomEvent_Text) == 0x000018, "Member 'WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton::K2Node_CustomEvent_Text' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton, CallFunc_IsValid_ReturnValue_1) == 0x000030, "Member 'WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton, CallFunc_PlayAnimation_ReturnValue) == 0x000038, "Member 'WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton, CallFunc_SlotAsHorizontalBoxSlot_ReturnValue) == 0x000040, "Member 'WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton::CallFunc_SlotAsHorizontalBoxSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton, CallFunc_IsValid_ReturnValue_2) == 0x000048, "Member 'WBP_NarrativeButton_C_ExecuteUbergraph_WBP_NarrativeButton::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");

// Function WBP_NarrativeButton.WBP_NarrativeButton_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WBP_NarrativeButton_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeButton_C_PreConstruct) == 0x000001, "Wrong alignment on WBP_NarrativeButton_C_PreConstruct");
static_assert(sizeof(WBP_NarrativeButton_C_PreConstruct) == 0x000001, "Wrong size on WBP_NarrativeButton_C_PreConstruct");
static_assert(offsetof(WBP_NarrativeButton_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WBP_NarrativeButton_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WBP_NarrativeButton.WBP_NarrativeButton_C.Set Button Text
// 0x0018 (0x0018 - 0x0000)
struct WBP_NarrativeButton_C_Set_Button_Text final
{
public:
	class FText                                   Text_0;                                            // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WBP_NarrativeButton_C_Set_Button_Text) == 0x000008, "Wrong alignment on WBP_NarrativeButton_C_Set_Button_Text");
static_assert(sizeof(WBP_NarrativeButton_C_Set_Button_Text) == 0x000018, "Wrong size on WBP_NarrativeButton_C_Set_Button_Text");
static_assert(offsetof(WBP_NarrativeButton_C_Set_Button_Text, Text_0) == 0x000000, "Member 'WBP_NarrativeButton_C_Set_Button_Text::Text_0' has a wrong offset!");

}

