﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WorldObjectComponent

#include "Basic.hpp"

#include "Engine_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass WorldObjectComponent.WorldObjectComponent_C
// 0x0030 (0x00D0 - 0x00A0)
class UWorldObjectComponent_C final : public UActorComponent
{
public:
	TMulticastInlineDelegate<void()>              ObjectDestroyed;                                   // 0x00A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(double Dmg)>    ObjectDamaged;                                     // 0x00B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              ObjectInteracted;                                  // 0x00C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WorldObjectComponent_C">();
	}
	static class UWorldObjectComponent_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWorldObjectComponent_C>();
	}
};
static_assert(alignof(UWorldObjectComponent_C) == 0x000008, "Wrong alignment on UWorldObjectComponent_C");
static_assert(sizeof(UWorldObjectComponent_C) == 0x0000D0, "Wrong size on UWorldObjectComponent_C");
static_assert(offsetof(UWorldObjectComponent_C, ObjectDestroyed) == 0x0000A0, "Member 'UWorldObjectComponent_C::ObjectDestroyed' has a wrong offset!");
static_assert(offsetof(UWorldObjectComponent_C, ObjectDamaged) == 0x0000B0, "Member 'UWorldObjectComponent_C::ObjectDamaged' has a wrong offset!");
static_assert(offsetof(UWorldObjectComponent_C, ObjectInteracted) == 0x0000C0, "Member 'UWorldObjectComponent_C::ObjectInteracted' has a wrong offset!");

}

