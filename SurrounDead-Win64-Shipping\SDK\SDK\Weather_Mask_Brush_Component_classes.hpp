﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Weather_Mask_Brush_Component

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "WeatherMask_classes.hpp"
#include "UDS_Weather_Mask_Brush_structs.hpp"


namespace SDK
{

// BlueprintGeneratedClass Weather_Mask_Brush_Component.Weather_Mask_Brush_Component_C
// 0x0010 (0x06B0 - 0x06A0)
class UWeather_Mask_Brush_Component_C final : public UWeatherMask_C
{
public:
	EUDS_Weather_Mask_Brush                       Brush;                                             // 0x0692(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	bool                                          Cancel_Masks_Above;                                // 0x0693(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_694[0x4];                                      // 0x0694(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             Brush_Texture;                                     // 0x0698(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Brush_Color;                                       // 0x06A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Calculate_Masking_At_Location(const struct FVector& Location, struct FVector2D* Mask, bool* Cancel_All_Masks);
	void Prepare_for_Drawing(bool Runtime, class AUltra_Dynamic_Weather_C* UDW_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Weather_Mask_Brush_Component_C">();
	}
	static class UWeather_Mask_Brush_Component_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWeather_Mask_Brush_Component_C>();
	}
};
static_assert(alignof(UWeather_Mask_Brush_Component_C) == 0x000010, "Wrong alignment on UWeather_Mask_Brush_Component_C");
static_assert(sizeof(UWeather_Mask_Brush_Component_C) == 0x0006B0, "Wrong size on UWeather_Mask_Brush_Component_C");
static_assert(offsetof(UWeather_Mask_Brush_Component_C, Brush) == 0x000692, "Member 'UWeather_Mask_Brush_Component_C::Brush' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Brush_Component_C, Cancel_Masks_Above) == 0x000693, "Member 'UWeather_Mask_Brush_Component_C::Cancel_Masks_Above' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Brush_Component_C, Brush_Texture) == 0x000698, "Member 'UWeather_Mask_Brush_Component_C::Brush_Texture' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Brush_Component_C, Brush_Color) == 0x0006A0, "Member 'UWeather_Mask_Brush_Component_C::Brush_Color' has a wrong offset!");

}

