﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_WorldMarkerTooltip

#include "Basic.hpp"


namespace SDK::Params
{

// Function W_WorldMarkerTooltip.W_WorldMarkerTooltip_C.ExecuteUbergraph_W_WorldMarkerTooltip
// 0x0020 (0x0020 - 0x0000)
struct W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_TextToUpper_ReturnValue;                  // 0x0008(0x0018)()
};
static_assert(alignof(W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip) == 0x000008, "Wrong alignment on W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip");
static_assert(sizeof(W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip) == 0x000020, "Wrong size on W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip");
static_assert(offsetof(W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip, EntryPoint) == 0x000000, "Member 'W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip, CallFunc_TextToUpper_ReturnValue) == 0x000008, "Member 'W_WorldMarkerTooltip_C_ExecuteUbergraph_W_WorldMarkerTooltip::CallFunc_TextToUpper_ReturnValue' has a wrong offset!");

}

