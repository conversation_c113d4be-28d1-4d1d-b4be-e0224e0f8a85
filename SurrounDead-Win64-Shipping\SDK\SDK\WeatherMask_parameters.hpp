﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WeatherMask

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WeatherMask.WeatherMask_C.Calculate Masking At Location
// 0x0030 (0x0030 - 0x0000)
struct WeatherMask_C_Calculate_Masking_At_Location final
{
public:
	struct FVector                                Location;                                          // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Mask;                                              // 0x0018(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cancel_All_Masks;                                  // 0x0028(0x0001)(Parm, OutParm, ZeroConstructor, Is<PERSON>lainOldData, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Calculate_Masking_At_Location) == 0x000008, "Wrong alignment on WeatherMask_C_Calculate_Masking_At_Location");
static_assert(sizeof(WeatherMask_C_Calculate_Masking_At_Location) == 0x000030, "Wrong size on WeatherMask_C_Calculate_Masking_At_Location");
static_assert(offsetof(WeatherMask_C_Calculate_Masking_At_Location, Location) == 0x000000, "Member 'WeatherMask_C_Calculate_Masking_At_Location::Location' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Calculate_Masking_At_Location, Mask) == 0x000018, "Member 'WeatherMask_C_Calculate_Masking_At_Location::Mask' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Calculate_Masking_At_Location, Cancel_All_Masks) == 0x000028, "Member 'WeatherMask_C_Calculate_Masking_At_Location::Cancel_All_Masks' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.Component Generally In Range
// 0x0038 (0x0038 - 0x0000)
struct WeatherMask_C_Component_Generally_In_Range final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue;      // 0x0008(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Vector_Distance2D_ReturnValue;            // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Component_Generally_In_Range) == 0x000008, "Wrong alignment on WeatherMask_C_Component_Generally_In_Range");
static_assert(sizeof(WeatherMask_C_Component_Generally_In_Range) == 0x000038, "Wrong size on WeatherMask_C_Component_Generally_In_Range");
static_assert(offsetof(WeatherMask_C_Component_Generally_In_Range, ReturnValue) == 0x000000, "Member 'WeatherMask_C_Component_Generally_In_Range::ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Component_Generally_In_Range, CallFunc_K2_GetComponentLocation_ReturnValue) == 0x000008, "Member 'WeatherMask_C_Component_Generally_In_Range::CallFunc_K2_GetComponentLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Component_Generally_In_Range, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000020, "Member 'WeatherMask_C_Component_Generally_In_Range::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Component_Generally_In_Range, CallFunc_Vector_Distance2D_ReturnValue) == 0x000028, "Member 'WeatherMask_C_Component_Generally_In_Range::CallFunc_Vector_Distance2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Component_Generally_In_Range, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000030, "Member 'WeatherMask_C_Component_Generally_In_Range::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.Editor Update
// 0x0018 (0x0018 - 0x0000)
struct WeatherMask_C_Editor_Update final
{
public:
	bool                                          CallFunc_Component_Generally_In_Range_ReturnValue; // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsPackagedForDistribution_ReturnValue;    // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2[0x6];                                        // 0x0002(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Weather_C*               CallFunc_GetActorOfClass_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Runtime_Or_Initializing_ReturnValue;      // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x0013(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Editor_Update) == 0x000008, "Wrong alignment on WeatherMask_C_Editor_Update");
static_assert(sizeof(WeatherMask_C_Editor_Update) == 0x000018, "Wrong size on WeatherMask_C_Editor_Update");
static_assert(offsetof(WeatherMask_C_Editor_Update, CallFunc_Component_Generally_In_Range_ReturnValue) == 0x000000, "Member 'WeatherMask_C_Editor_Update::CallFunc_Component_Generally_In_Range_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Editor_Update, CallFunc_IsPackagedForDistribution_ReturnValue) == 0x000001, "Member 'WeatherMask_C_Editor_Update::CallFunc_IsPackagedForDistribution_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Editor_Update, CallFunc_GetActorOfClass_ReturnValue) == 0x000008, "Member 'WeatherMask_C_Editor_Update::CallFunc_GetActorOfClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Editor_Update, CallFunc_Not_PreBool_ReturnValue) == 0x000010, "Member 'WeatherMask_C_Editor_Update::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Editor_Update, CallFunc_IsValid_ReturnValue) == 0x000011, "Member 'WeatherMask_C_Editor_Update::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Editor_Update, CallFunc_Runtime_Or_Initializing_ReturnValue) == 0x000012, "Member 'WeatherMask_C_Editor_Update::CallFunc_Runtime_Or_Initializing_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Editor_Update, CallFunc_Not_PreBool_ReturnValue_1) == 0x000013, "Member 'WeatherMask_C_Editor_Update::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Editor_Update, CallFunc_BooleanAND_ReturnValue) == 0x000014, "Member 'WeatherMask_C_Editor_Update::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.ExecuteUbergraph_WeatherMask
// 0x0040 (0x0040 - 0x0000)
struct WeatherMask_C_ExecuteUbergraph_WeatherMask final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Weather_C*               CallFunc_GetActorOfClass_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEndPlayReason                                K2Node_Event_EndPlayReason;                        // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Component_Generally_In_Range_ReturnValue; // 0x0013(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<EEndPlayReason>                        K2Node_MakeArray_Array;                            // 0x0018(0x0010)(ConstParm, ReferenceParm)
	bool                                          CallFunc_Array_Contains_ReturnValue;               // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Weather_C*               CallFunc_GetActorOfClass_ReturnValue_1;            // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_Contains_ReturnValue_1;             // 0x0039(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_ExecuteUbergraph_WeatherMask) == 0x000008, "Wrong alignment on WeatherMask_C_ExecuteUbergraph_WeatherMask");
static_assert(sizeof(WeatherMask_C_ExecuteUbergraph_WeatherMask) == 0x000040, "Wrong size on WeatherMask_C_ExecuteUbergraph_WeatherMask");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, EntryPoint) == 0x000000, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::EntryPoint' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, CallFunc_GetActorOfClass_ReturnValue) == 0x000008, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::CallFunc_GetActorOfClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, CallFunc_IsValid_ReturnValue) == 0x000010, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, K2Node_Event_EndPlayReason) == 0x000011, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::K2Node_Event_EndPlayReason' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000012, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, CallFunc_Component_Generally_In_Range_ReturnValue) == 0x000013, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::CallFunc_Component_Generally_In_Range_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, CallFunc_BooleanAND_ReturnValue) == 0x000014, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, K2Node_MakeArray_Array) == 0x000018, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, CallFunc_Array_Contains_ReturnValue) == 0x000028, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::CallFunc_Array_Contains_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, CallFunc_GetActorOfClass_ReturnValue_1) == 0x000030, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::CallFunc_GetActorOfClass_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, CallFunc_IsValid_ReturnValue_1) == 0x000038, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_ExecuteUbergraph_WeatherMask, CallFunc_Array_Contains_ReturnValue_1) == 0x000039, "Member 'WeatherMask_C_ExecuteUbergraph_WeatherMask::CallFunc_Array_Contains_ReturnValue_1' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.Force Update
// 0x0010 (0x0010 - 0x0000)
struct WeatherMask_C_Force_Update final
{
public:
	class AUltra_Dynamic_Weather_C*               CallFunc_Get_Ultra_Dynamic_Weather_UDW;            // 0x0000(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Get_Ultra_Dynamic_Weather_Valid;          // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Force_Update) == 0x000008, "Wrong alignment on WeatherMask_C_Force_Update");
static_assert(sizeof(WeatherMask_C_Force_Update) == 0x000010, "Wrong size on WeatherMask_C_Force_Update");
static_assert(offsetof(WeatherMask_C_Force_Update, CallFunc_Get_Ultra_Dynamic_Weather_UDW) == 0x000000, "Member 'WeatherMask_C_Force_Update::CallFunc_Get_Ultra_Dynamic_Weather_UDW' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Force_Update, CallFunc_Get_Ultra_Dynamic_Weather_Valid) == 0x000008, "Member 'WeatherMask_C_Force_Update::CallFunc_Get_Ultra_Dynamic_Weather_Valid' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.Get Brush Location
// 0x0030 (0x0030 - 0x0000)
struct WeatherMask_C_Get_Brush_Location final
{
public:
	struct FVector2D                              Out;                                               // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Divide_Vector2DFloat_ReturnValue;         // 0x0010(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Subtract_Vector2DVector2D_ReturnValue;    // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Get_Brush_Location) == 0x000008, "Wrong alignment on WeatherMask_C_Get_Brush_Location");
static_assert(sizeof(WeatherMask_C_Get_Brush_Location) == 0x000030, "Wrong size on WeatherMask_C_Get_Brush_Location");
static_assert(offsetof(WeatherMask_C_Get_Brush_Location, Out) == 0x000000, "Member 'WeatherMask_C_Get_Brush_Location::Out' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Location, CallFunc_Divide_Vector2DFloat_ReturnValue) == 0x000010, "Member 'WeatherMask_C_Get_Brush_Location::CallFunc_Divide_Vector2DFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Location, CallFunc_Subtract_Vector2DVector2D_ReturnValue) == 0x000020, "Member 'WeatherMask_C_Get_Brush_Location::CallFunc_Subtract_Vector2DVector2D_ReturnValue' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.Get Brush Scale
// 0x0048 (0x0048 - 0x0000)
struct WeatherMask_C_Get_Brush_Scale final
{
public:
	struct FVector2D                              Out;                                               // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetComponentScale_ReturnValue;         // 0x0010(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue;        // 0x0028(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Multiply_Vector2DVector2D_ReturnValue;    // 0x0038(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Get_Brush_Scale) == 0x000008, "Wrong alignment on WeatherMask_C_Get_Brush_Scale");
static_assert(sizeof(WeatherMask_C_Get_Brush_Scale) == 0x000048, "Wrong size on WeatherMask_C_Get_Brush_Scale");
static_assert(offsetof(WeatherMask_C_Get_Brush_Scale, Out) == 0x000000, "Member 'WeatherMask_C_Get_Brush_Scale::Out' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Scale, CallFunc_K2_GetComponentScale_ReturnValue) == 0x000010, "Member 'WeatherMask_C_Get_Brush_Scale::CallFunc_K2_GetComponentScale_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Scale, CallFunc_Conv_VectorToVector2D_ReturnValue) == 0x000028, "Member 'WeatherMask_C_Get_Brush_Scale::CallFunc_Conv_VectorToVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Scale, CallFunc_Multiply_Vector2DVector2D_ReturnValue) == 0x000038, "Member 'WeatherMask_C_Get_Brush_Scale::CallFunc_Multiply_Vector2DVector2D_ReturnValue' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.Get Brush Yaw
// 0x0038 (0x0038 - 0x0000)
struct WeatherMask_C_Get_Brush_Yaw final
{
public:
	double                                        Out;                                               // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               CallFunc_K2_GetComponentRotation_ReturnValue;      // 0x0008(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor)
	float                                         CallFunc_BreakRotator_Roll;                        // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakRotator_Pitch;                       // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakRotator_Yaw;                         // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_FunctionResult_Out_ImplicitCast;            // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Get_Brush_Yaw) == 0x000008, "Wrong alignment on WeatherMask_C_Get_Brush_Yaw");
static_assert(sizeof(WeatherMask_C_Get_Brush_Yaw) == 0x000038, "Wrong size on WeatherMask_C_Get_Brush_Yaw");
static_assert(offsetof(WeatherMask_C_Get_Brush_Yaw, Out) == 0x000000, "Member 'WeatherMask_C_Get_Brush_Yaw::Out' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Yaw, CallFunc_K2_GetComponentRotation_ReturnValue) == 0x000008, "Member 'WeatherMask_C_Get_Brush_Yaw::CallFunc_K2_GetComponentRotation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Yaw, CallFunc_BreakRotator_Roll) == 0x000020, "Member 'WeatherMask_C_Get_Brush_Yaw::CallFunc_BreakRotator_Roll' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Yaw, CallFunc_BreakRotator_Pitch) == 0x000024, "Member 'WeatherMask_C_Get_Brush_Yaw::CallFunc_BreakRotator_Pitch' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Yaw, CallFunc_BreakRotator_Yaw) == 0x000028, "Member 'WeatherMask_C_Get_Brush_Yaw::CallFunc_BreakRotator_Yaw' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Brush_Yaw, K2Node_FunctionResult_Out_ImplicitCast) == 0x000030, "Member 'WeatherMask_C_Get_Brush_Yaw::K2Node_FunctionResult_Out_ImplicitCast' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.Get Center Location
// 0x0038 (0x0038 - 0x0000)
struct WeatherMask_C_Get_Center_Location final
{
public:
	struct FVector2D                              Out;                                               // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue;      // 0x0010(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Conv_VectorToVector2D_ReturnValue;        // 0x0028(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Get_Center_Location) == 0x000008, "Wrong alignment on WeatherMask_C_Get_Center_Location");
static_assert(sizeof(WeatherMask_C_Get_Center_Location) == 0x000038, "Wrong size on WeatherMask_C_Get_Center_Location");
static_assert(offsetof(WeatherMask_C_Get_Center_Location, Out) == 0x000000, "Member 'WeatherMask_C_Get_Center_Location::Out' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Center_Location, CallFunc_K2_GetComponentLocation_ReturnValue) == 0x000010, "Member 'WeatherMask_C_Get_Center_Location::CallFunc_K2_GetComponentLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Center_Location, CallFunc_Conv_VectorToVector2D_ReturnValue) == 0x000028, "Member 'WeatherMask_C_Get_Center_Location::CallFunc_Conv_VectorToVector2D_ReturnValue' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.Get Max Distance
// 0x0020 (0x0020 - 0x0000)
struct WeatherMask_C_Get_Max_Distance final
{
public:
	double                                        Out;                                               // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetAbsMax2D_ReturnValue;                  // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Hypotenuse_ReturnValue;                   // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Get_Max_Distance) == 0x000008, "Wrong alignment on WeatherMask_C_Get_Max_Distance");
static_assert(sizeof(WeatherMask_C_Get_Max_Distance) == 0x000020, "Wrong size on WeatherMask_C_Get_Max_Distance");
static_assert(offsetof(WeatherMask_C_Get_Max_Distance, Out) == 0x000000, "Member 'WeatherMask_C_Get_Max_Distance::Out' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Max_Distance, CallFunc_GetAbsMax2D_ReturnValue) == 0x000008, "Member 'WeatherMask_C_Get_Max_Distance::CallFunc_GetAbsMax2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Max_Distance, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000010, "Member 'WeatherMask_C_Get_Max_Distance::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Get_Max_Distance, CallFunc_Hypotenuse_ReturnValue) == 0x000018, "Member 'WeatherMask_C_Get_Max_Distance::CallFunc_Hypotenuse_ReturnValue' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.Prepare for Drawing
// 0x0050 (0x0050 - 0x0000)
struct WeatherMask_C_Prepare_for_Drawing final
{
public:
	bool                                          Runtime;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AUltra_Dynamic_Weather_C*               UDW_0;                                             // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Get_Max_Distance_Out;                     // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Get_Brush_Yaw_Out;                        // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Get_Center_Location_Out;                  // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Get_Brush_Location_Out;                   // 0x0030(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Get_Brush_Scale_Out;                      // 0x0040(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_Prepare_for_Drawing) == 0x000008, "Wrong alignment on WeatherMask_C_Prepare_for_Drawing");
static_assert(sizeof(WeatherMask_C_Prepare_for_Drawing) == 0x000050, "Wrong size on WeatherMask_C_Prepare_for_Drawing");
static_assert(offsetof(WeatherMask_C_Prepare_for_Drawing, Runtime) == 0x000000, "Member 'WeatherMask_C_Prepare_for_Drawing::Runtime' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Prepare_for_Drawing, UDW_0) == 0x000008, "Member 'WeatherMask_C_Prepare_for_Drawing::UDW_0' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Prepare_for_Drawing, CallFunc_Get_Max_Distance_Out) == 0x000010, "Member 'WeatherMask_C_Prepare_for_Drawing::CallFunc_Get_Max_Distance_Out' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Prepare_for_Drawing, CallFunc_Get_Brush_Yaw_Out) == 0x000018, "Member 'WeatherMask_C_Prepare_for_Drawing::CallFunc_Get_Brush_Yaw_Out' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Prepare_for_Drawing, CallFunc_Get_Center_Location_Out) == 0x000020, "Member 'WeatherMask_C_Prepare_for_Drawing::CallFunc_Get_Center_Location_Out' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Prepare_for_Drawing, CallFunc_Get_Brush_Location_Out) == 0x000030, "Member 'WeatherMask_C_Prepare_for_Drawing::CallFunc_Get_Brush_Location_Out' has a wrong offset!");
static_assert(offsetof(WeatherMask_C_Prepare_for_Drawing, CallFunc_Get_Brush_Scale_Out) == 0x000040, "Member 'WeatherMask_C_Prepare_for_Drawing::CallFunc_Get_Brush_Scale_Out' has a wrong offset!");

// Function WeatherMask.WeatherMask_C.ReceiveEndPlay
// 0x0001 (0x0001 - 0x0000)
struct WeatherMask_C_ReceiveEndPlay final
{
public:
	EEndPlayReason                                EndPlayReason;                                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WeatherMask_C_ReceiveEndPlay) == 0x000001, "Wrong alignment on WeatherMask_C_ReceiveEndPlay");
static_assert(sizeof(WeatherMask_C_ReceiveEndPlay) == 0x000001, "Wrong size on WeatherMask_C_ReceiveEndPlay");
static_assert(offsetof(WeatherMask_C_ReceiveEndPlay, EndPlayReason) == 0x000000, "Member 'WeatherMask_C_ReceiveEndPlay::EndPlayReason' has a wrong offset!");

}

