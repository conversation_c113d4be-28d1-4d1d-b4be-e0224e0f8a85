﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Base

#include "Basic.hpp"

#include "S_Effects_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "EProgressMethod_structs.hpp"
#include "EGradientTypes_structs.hpp"
#include "EMarqueeMask_structs.hpp"
#include "Slate_structs.hpp"


namespace SDK::Params
{

// Function WB_Base.WB_Base_C.ExecuteUbergraph_WB_Base
// 0x0128 (0x0128 - 0x0000)
struct WB_Base_C_ExecuteUbergraph_WB_Base final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Event_Index;                                // 0x0004(0x0004)(ZeroConstructor, Is<PERSON>lainOldData, <PERSON><PERSON><PERSON>ru<PERSON>, HasGetValueTypeHash)
	struct FVector2D                              K2Node_Event_Size;                                 // 0x0008(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_Value;                                // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_Event_Color_3;                              // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_TargetPercent;                        // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_Thickness;                            // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_UseTargetPercent;                     // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressMethod                               K2Node_Event_EProgressMethod;                      // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_42[0x6];                                       // 0x0042(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Event_CurrentInterpTime;                    // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_TargetInterpTime;                     // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_Event_Color_2;                              // 0x0058(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_Event_Color_1;                              // 0x0068(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_UseGradientFillColor;                 // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79[0x3];                                       // 0x0079(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_Event_Color;                                // 0x007C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8C[0x4];                                       // 0x008C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Event_GradientPower;                        // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_Event_Tiling_1;                             // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_99[0x7];                                       // 0x0099(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                K2Node_Event_Mask_1;                               // 0x00A0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             K2Node_Event_Mask;                                 // 0x00A8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_Event_Tiling;                               // 0x00B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B1[0x7];                                       // 0x00B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Event_BlurStrength;                         // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EGradientTypes                                K2Node_Event_GradientType;                         // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C1[0x7];                                       // 0x00C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_Event_BlendMask;                            // 0x00C8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          K2Node_Event_FillType;                             // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D1[0x7];                                       // 0x00D1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Event_Spacing_1;                            // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsMarquee_1;                          // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsMarquee;                            // 0x00E1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E2[0x6];                                       // 0x00E2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_Event_Image_1;                              // 0x00E8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             K2Node_Event_Image;                                // 0x00F0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	EMarqueeMask                                  K2Node_Event_MaskType;                             // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F9[0x3];                                       // 0x00F9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Event_Steps;                                // 0x00FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_Spacing;                              // 0x0100(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_AbsoluteFillMethod;                   // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_109[0x7];                                      // 0x0109(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FS_Effects>                     K2Node_Event_Effects;                              // 0x0110(0x0010)(ConstParm, ReferenceParm)
	int32                                         K2Node_Event_Index_1;                              // 0x0120(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsEnabled_1;                          // 0x0124(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsEnabled;                            // 0x0125(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_ExecuteUbergraph_WB_Base) == 0x000008, "Wrong alignment on WB_Base_C_ExecuteUbergraph_WB_Base");
static_assert(sizeof(WB_Base_C_ExecuteUbergraph_WB_Base) == 0x000128, "Wrong size on WB_Base_C_ExecuteUbergraph_WB_Base");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, EntryPoint) == 0x000000, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Index) == 0x000004, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Index' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Size) == 0x000008, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Size' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Value) == 0x000018, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Value' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Color_3) == 0x000020, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Color_3' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_TargetPercent) == 0x000030, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_TargetPercent' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Thickness) == 0x000038, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Thickness' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_UseTargetPercent) == 0x000040, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_UseTargetPercent' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_EProgressMethod) == 0x000041, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_EProgressMethod' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_CurrentInterpTime) == 0x000048, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_CurrentInterpTime' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_TargetInterpTime) == 0x000050, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_TargetInterpTime' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Color_2) == 0x000058, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Color_2' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Color_1) == 0x000068, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Color_1' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_UseGradientFillColor) == 0x000078, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_UseGradientFillColor' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Color) == 0x00007C, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Color' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_GradientPower) == 0x000090, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_GradientPower' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Tiling_1) == 0x000098, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Tiling_1' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Mask_1) == 0x0000A0, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Mask_1' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Mask) == 0x0000A8, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Mask' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Tiling) == 0x0000B0, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Tiling' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_BlurStrength) == 0x0000B8, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_BlurStrength' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_GradientType) == 0x0000C0, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_GradientType' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_BlendMask) == 0x0000C8, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_BlendMask' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_FillType) == 0x0000D0, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_FillType' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Spacing_1) == 0x0000D8, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Spacing_1' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_IsMarquee_1) == 0x0000E0, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_IsMarquee_1' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_IsMarquee) == 0x0000E1, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_IsMarquee' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Image_1) == 0x0000E8, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Image_1' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Image) == 0x0000F0, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Image' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_MaskType) == 0x0000F8, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_MaskType' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Steps) == 0x0000FC, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Steps' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Spacing) == 0x000100, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Spacing' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_AbsoluteFillMethod) == 0x000108, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_AbsoluteFillMethod' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Effects) == 0x000110, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Effects' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_Index_1) == 0x000120, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_Index_1' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_IsEnabled_1) == 0x000124, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_IsEnabled_1' has a wrong offset!");
static_assert(offsetof(WB_Base_C_ExecuteUbergraph_WB_Base, K2Node_Event_IsEnabled) == 0x000125, "Member 'WB_Base_C_ExecuteUbergraph_WB_Base::K2Node_Event_IsEnabled' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_AddEffect
// 0x0090 (0x0090 - 0x0000)
struct WB_Base_C_PB_AddEffect final
{
public:
	struct FS_Effects                             Effect;                                            // 0x0000(0x0088)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	int32                                         Index_0;                                           // 0x0088(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_AddEffect) == 0x000008, "Wrong alignment on WB_Base_C_PB_AddEffect");
static_assert(sizeof(WB_Base_C_PB_AddEffect) == 0x000090, "Wrong size on WB_Base_C_PB_AddEffect");
static_assert(offsetof(WB_Base_C_PB_AddEffect, Effect) == 0x000000, "Member 'WB_Base_C_PB_AddEffect::Effect' has a wrong offset!");
static_assert(offsetof(WB_Base_C_PB_AddEffect, Index_0) == 0x000088, "Member 'WB_Base_C_PB_AddEffect::Index_0' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetBackgroundColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_GetBackgroundColor final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetBackgroundColor) == 0x000004, "Wrong alignment on WB_Base_C_PB_GetBackgroundColor");
static_assert(sizeof(WB_Base_C_PB_GetBackgroundColor) == 0x000010, "Wrong size on WB_Base_C_PB_GetBackgroundColor");
static_assert(offsetof(WB_Base_C_PB_GetBackgroundColor, Color) == 0x000000, "Member 'WB_Base_C_PB_GetBackgroundColor::Color' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetEffects
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_GetEffects final
{
public:
	TArray<struct FS_Effects>                     Effects;                                           // 0x0000(0x0010)(Parm, OutParm)
};
static_assert(alignof(WB_Base_C_PB_GetEffects) == 0x000008, "Wrong alignment on WB_Base_C_PB_GetEffects");
static_assert(sizeof(WB_Base_C_PB_GetEffects) == 0x000010, "Wrong size on WB_Base_C_PB_GetEffects");
static_assert(offsetof(WB_Base_C_PB_GetEffects, Effects) == 0x000000, "Member 'WB_Base_C_PB_GetEffects::Effects' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetFillColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_GetFillColor final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetFillColor) == 0x000004, "Wrong alignment on WB_Base_C_PB_GetFillColor");
static_assert(sizeof(WB_Base_C_PB_GetFillColor) == 0x000010, "Wrong size on WB_Base_C_PB_GetFillColor");
static_assert(offsetof(WB_Base_C_PB_GetFillColor, Color) == 0x000000, "Member 'WB_Base_C_PB_GetFillColor::Color' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetInterpTimeCurrent
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_GetInterpTimeCurrent final
{
public:
	double                                        CurrentInterpTime;                                 // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetInterpTimeCurrent) == 0x000008, "Wrong alignment on WB_Base_C_PB_GetInterpTimeCurrent");
static_assert(sizeof(WB_Base_C_PB_GetInterpTimeCurrent) == 0x000008, "Wrong size on WB_Base_C_PB_GetInterpTimeCurrent");
static_assert(offsetof(WB_Base_C_PB_GetInterpTimeCurrent, CurrentInterpTime) == 0x000000, "Member 'WB_Base_C_PB_GetInterpTimeCurrent::CurrentInterpTime' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetInterpTimeTarget
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_GetInterpTimeTarget final
{
public:
	double                                        TargetInterpTime;                                  // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetInterpTimeTarget) == 0x000008, "Wrong alignment on WB_Base_C_PB_GetInterpTimeTarget");
static_assert(sizeof(WB_Base_C_PB_GetInterpTimeTarget) == 0x000008, "Wrong size on WB_Base_C_PB_GetInterpTimeTarget");
static_assert(offsetof(WB_Base_C_PB_GetInterpTimeTarget, TargetInterpTime) == 0x000000, "Member 'WB_Base_C_PB_GetInterpTimeTarget::TargetInterpTime' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetIsCustomMarquee
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_GetIsCustomMarquee final
{
public:
	bool                                          IsMarquee;                                         // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetIsCustomMarquee) == 0x000001, "Wrong alignment on WB_Base_C_PB_GetIsCustomMarquee");
static_assert(sizeof(WB_Base_C_PB_GetIsCustomMarquee) == 0x000001, "Wrong size on WB_Base_C_PB_GetIsCustomMarquee");
static_assert(offsetof(WB_Base_C_PB_GetIsCustomMarquee, IsMarquee) == 0x000000, "Member 'WB_Base_C_PB_GetIsCustomMarquee::IsMarquee' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetIsMarquee
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_GetIsMarquee final
{
public:
	bool                                          IsMarquee;                                         // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetIsMarquee) == 0x000001, "Wrong alignment on WB_Base_C_PB_GetIsMarquee");
static_assert(sizeof(WB_Base_C_PB_GetIsMarquee) == 0x000001, "Wrong size on WB_Base_C_PB_GetIsMarquee");
static_assert(offsetof(WB_Base_C_PB_GetIsMarquee, IsMarquee) == 0x000000, "Member 'WB_Base_C_PB_GetIsMarquee::IsMarquee' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_GetPercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetPercent) == 0x000008, "Wrong alignment on WB_Base_C_PB_GetPercent");
static_assert(sizeof(WB_Base_C_PB_GetPercent) == 0x000008, "Wrong size on WB_Base_C_PB_GetPercent");
static_assert(offsetof(WB_Base_C_PB_GetPercent, Percent) == 0x000000, "Member 'WB_Base_C_PB_GetPercent::Percent' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetProgressMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_GetProgressMethod final
{
public:
	EProgressMethod                               EProgressMethod;                                   // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetProgressMethod) == 0x000001, "Wrong alignment on WB_Base_C_PB_GetProgressMethod");
static_assert(sizeof(WB_Base_C_PB_GetProgressMethod) == 0x000001, "Wrong size on WB_Base_C_PB_GetProgressMethod");
static_assert(offsetof(WB_Base_C_PB_GetProgressMethod, EProgressMethod) == 0x000000, "Member 'WB_Base_C_PB_GetProgressMethod::EProgressMethod' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetSeparationSteps
// 0x0004 (0x0004 - 0x0000)
struct WB_Base_C_PB_GetSeparationSteps final
{
public:
	int32                                         Steps;                                             // 0x0000(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetSeparationSteps) == 0x000004, "Wrong alignment on WB_Base_C_PB_GetSeparationSteps");
static_assert(sizeof(WB_Base_C_PB_GetSeparationSteps) == 0x000004, "Wrong size on WB_Base_C_PB_GetSeparationSteps");
static_assert(offsetof(WB_Base_C_PB_GetSeparationSteps, Steps) == 0x000000, "Member 'WB_Base_C_PB_GetSeparationSteps::Steps' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetSize
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_GetSize final
{
public:
	struct FVector2D                              Size;                                              // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetSize) == 0x000008, "Wrong alignment on WB_Base_C_PB_GetSize");
static_assert(sizeof(WB_Base_C_PB_GetSize) == 0x000010, "Wrong size on WB_Base_C_PB_GetSize");
static_assert(offsetof(WB_Base_C_PB_GetSize, Size) == 0x000000, "Member 'WB_Base_C_PB_GetSize::Size' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetTargetFillColor_Negative
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_GetTargetFillColor_Negative final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetTargetFillColor_Negative) == 0x000004, "Wrong alignment on WB_Base_C_PB_GetTargetFillColor_Negative");
static_assert(sizeof(WB_Base_C_PB_GetTargetFillColor_Negative) == 0x000010, "Wrong size on WB_Base_C_PB_GetTargetFillColor_Negative");
static_assert(offsetof(WB_Base_C_PB_GetTargetFillColor_Negative, Color) == 0x000000, "Member 'WB_Base_C_PB_GetTargetFillColor_Negative::Color' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetTargetFillColor_Positive
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_GetTargetFillColor_Positive final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetTargetFillColor_Positive) == 0x000004, "Wrong alignment on WB_Base_C_PB_GetTargetFillColor_Positive");
static_assert(sizeof(WB_Base_C_PB_GetTargetFillColor_Positive) == 0x000010, "Wrong size on WB_Base_C_PB_GetTargetFillColor_Positive");
static_assert(offsetof(WB_Base_C_PB_GetTargetFillColor_Positive, Color) == 0x000000, "Member 'WB_Base_C_PB_GetTargetFillColor_Positive::Color' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetTargetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_GetTargetPercent final
{
public:
	double                                        TargetPercent;                                     // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetTargetPercent) == 0x000008, "Wrong alignment on WB_Base_C_PB_GetTargetPercent");
static_assert(sizeof(WB_Base_C_PB_GetTargetPercent) == 0x000008, "Wrong size on WB_Base_C_PB_GetTargetPercent");
static_assert(offsetof(WB_Base_C_PB_GetTargetPercent, TargetPercent) == 0x000000, "Member 'WB_Base_C_PB_GetTargetPercent::TargetPercent' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetThickness
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_GetThickness final
{
public:
	double                                        Thickness;                                         // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetThickness) == 0x000008, "Wrong alignment on WB_Base_C_PB_GetThickness");
static_assert(sizeof(WB_Base_C_PB_GetThickness) == 0x000008, "Wrong size on WB_Base_C_PB_GetThickness");
static_assert(offsetof(WB_Base_C_PB_GetThickness, Thickness) == 0x000000, "Member 'WB_Base_C_PB_GetThickness::Thickness' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetUseGradientFillColor
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_GetUseGradientFillColor final
{
public:
	bool                                          UseGradientFillColor;                              // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetUseGradientFillColor) == 0x000001, "Wrong alignment on WB_Base_C_PB_GetUseGradientFillColor");
static_assert(sizeof(WB_Base_C_PB_GetUseGradientFillColor) == 0x000001, "Wrong size on WB_Base_C_PB_GetUseGradientFillColor");
static_assert(offsetof(WB_Base_C_PB_GetUseGradientFillColor, UseGradientFillColor) == 0x000000, "Member 'WB_Base_C_PB_GetUseGradientFillColor::UseGradientFillColor' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_GetUseTargetPercent
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_GetUseTargetPercent final
{
public:
	bool                                          UseTargetPercent;                                  // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_GetUseTargetPercent) == 0x000001, "Wrong alignment on WB_Base_C_PB_GetUseTargetPercent");
static_assert(sizeof(WB_Base_C_PB_GetUseTargetPercent) == 0x000001, "Wrong size on WB_Base_C_PB_GetUseTargetPercent");
static_assert(offsetof(WB_Base_C_PB_GetUseTargetPercent, UseTargetPercent) == 0x000000, "Member 'WB_Base_C_PB_GetUseTargetPercent::UseTargetPercent' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_RemoveEffect
// 0x0004 (0x0004 - 0x0000)
struct WB_Base_C_PB_RemoveEffect final
{
public:
	int32                                         Index_0;                                           // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_RemoveEffect) == 0x000004, "Wrong alignment on WB_Base_C_PB_RemoveEffect");
static_assert(sizeof(WB_Base_C_PB_RemoveEffect) == 0x000004, "Wrong size on WB_Base_C_PB_RemoveEffect");
static_assert(offsetof(WB_Base_C_PB_RemoveEffect, Index_0) == 0x000000, "Member 'WB_Base_C_PB_RemoveEffect::Index_0' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetAllEffectsEnabled
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetAllEffectsEnabled final
{
public:
	bool                                          IsEnabled;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetAllEffectsEnabled) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetAllEffectsEnabled");
static_assert(sizeof(WB_Base_C_PB_SetAllEffectsEnabled) == 0x000001, "Wrong size on WB_Base_C_PB_SetAllEffectsEnabled");
static_assert(offsetof(WB_Base_C_PB_SetAllEffectsEnabled, IsEnabled) == 0x000000, "Member 'WB_Base_C_PB_SetAllEffectsEnabled::IsEnabled' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetBackgroundBlurStrength
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetBackgroundBlurStrength final
{
public:
	double                                        BlurStrength;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetBackgroundBlurStrength) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetBackgroundBlurStrength");
static_assert(sizeof(WB_Base_C_PB_SetBackgroundBlurStrength) == 0x000008, "Wrong size on WB_Base_C_PB_SetBackgroundBlurStrength");
static_assert(offsetof(WB_Base_C_PB_SetBackgroundBlurStrength, BlurStrength) == 0x000000, "Member 'WB_Base_C_PB_SetBackgroundBlurStrength::BlurStrength' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetBackgroundBrushTiling
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetBackgroundBrushTiling final
{
public:
	ESlateBrushTileType                           Tiling;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetBackgroundBrushTiling) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetBackgroundBrushTiling");
static_assert(sizeof(WB_Base_C_PB_SetBackgroundBrushTiling) == 0x000001, "Wrong size on WB_Base_C_PB_SetBackgroundBrushTiling");
static_assert(offsetof(WB_Base_C_PB_SetBackgroundBrushTiling, Tiling) == 0x000000, "Member 'WB_Base_C_PB_SetBackgroundBrushTiling::Tiling' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetBackgroundColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_SetBackgroundColor final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetBackgroundColor) == 0x000004, "Wrong alignment on WB_Base_C_PB_SetBackgroundColor");
static_assert(sizeof(WB_Base_C_PB_SetBackgroundColor) == 0x000010, "Wrong size on WB_Base_C_PB_SetBackgroundColor");
static_assert(offsetof(WB_Base_C_PB_SetBackgroundColor, Color) == 0x000000, "Member 'WB_Base_C_PB_SetBackgroundColor::Color' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetBackgroundColorMask
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetBackgroundColorMask final
{
public:
	class UTexture2D*                             Mask;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetBackgroundColorMask) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetBackgroundColorMask");
static_assert(sizeof(WB_Base_C_PB_SetBackgroundColorMask) == 0x000008, "Wrong size on WB_Base_C_PB_SetBackgroundColorMask");
static_assert(offsetof(WB_Base_C_PB_SetBackgroundColorMask, Mask) == 0x000000, "Member 'WB_Base_C_PB_SetBackgroundColorMask::Mask' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetBlendMask
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetBlendMask final
{
public:
	class UTexture2D*                             BlendMask;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetBlendMask) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetBlendMask");
static_assert(sizeof(WB_Base_C_PB_SetBlendMask) == 0x000008, "Wrong size on WB_Base_C_PB_SetBlendMask");
static_assert(offsetof(WB_Base_C_PB_SetBlendMask, BlendMask) == 0x000000, "Member 'WB_Base_C_PB_SetBlendMask::BlendMask' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetCustomMarqueeImage
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetCustomMarqueeImage final
{
public:
	class UTexture2D*                             Image;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetCustomMarqueeImage) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetCustomMarqueeImage");
static_assert(sizeof(WB_Base_C_PB_SetCustomMarqueeImage) == 0x000008, "Wrong size on WB_Base_C_PB_SetCustomMarqueeImage");
static_assert(offsetof(WB_Base_C_PB_SetCustomMarqueeImage, Image) == 0x000000, "Member 'WB_Base_C_PB_SetCustomMarqueeImage::Image' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetCustomMarqueeMaskType
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetCustomMarqueeMaskType final
{
public:
	EMarqueeMask                                  MaskType;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetCustomMarqueeMaskType) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetCustomMarqueeMaskType");
static_assert(sizeof(WB_Base_C_PB_SetCustomMarqueeMaskType) == 0x000001, "Wrong size on WB_Base_C_PB_SetCustomMarqueeMaskType");
static_assert(offsetof(WB_Base_C_PB_SetCustomMarqueeMaskType, MaskType) == 0x000000, "Member 'WB_Base_C_PB_SetCustomMarqueeMaskType::MaskType' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetEffectEnabled
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetEffectEnabled final
{
public:
	int32                                         Index_0;                                           // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsEnabled;                                         // 0x0004(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetEffectEnabled) == 0x000004, "Wrong alignment on WB_Base_C_PB_SetEffectEnabled");
static_assert(sizeof(WB_Base_C_PB_SetEffectEnabled) == 0x000008, "Wrong size on WB_Base_C_PB_SetEffectEnabled");
static_assert(offsetof(WB_Base_C_PB_SetEffectEnabled, Index_0) == 0x000000, "Member 'WB_Base_C_PB_SetEffectEnabled::Index_0' has a wrong offset!");
static_assert(offsetof(WB_Base_C_PB_SetEffectEnabled, IsEnabled) == 0x000004, "Member 'WB_Base_C_PB_SetEffectEnabled::IsEnabled' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetEffects
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_SetEffects final
{
public:
	TArray<struct FS_Effects>                     Effects;                                           // 0x0000(0x0010)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WB_Base_C_PB_SetEffects) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetEffects");
static_assert(sizeof(WB_Base_C_PB_SetEffects) == 0x000010, "Wrong size on WB_Base_C_PB_SetEffects");
static_assert(offsetof(WB_Base_C_PB_SetEffects, Effects) == 0x000000, "Member 'WB_Base_C_PB_SetEffects::Effects' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetFillColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_SetFillColor final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetFillColor) == 0x000004, "Wrong alignment on WB_Base_C_PB_SetFillColor");
static_assert(sizeof(WB_Base_C_PB_SetFillColor) == 0x000010, "Wrong size on WB_Base_C_PB_SetFillColor");
static_assert(offsetof(WB_Base_C_PB_SetFillColor, Color) == 0x000000, "Member 'WB_Base_C_PB_SetFillColor::Color' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetFillColorBrushTiling
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetFillColorBrushTiling final
{
public:
	ESlateBrushTileType                           Tiling;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetFillColorBrushTiling) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetFillColorBrushTiling");
static_assert(sizeof(WB_Base_C_PB_SetFillColorBrushTiling) == 0x000001, "Wrong size on WB_Base_C_PB_SetFillColorBrushTiling");
static_assert(offsetof(WB_Base_C_PB_SetFillColorBrushTiling, Tiling) == 0x000000, "Member 'WB_Base_C_PB_SetFillColorBrushTiling::Tiling' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetFillColorGradientPower
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetFillColorGradientPower final
{
public:
	double                                        GradientPower;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetFillColorGradientPower) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetFillColorGradientPower");
static_assert(sizeof(WB_Base_C_PB_SetFillColorGradientPower) == 0x000008, "Wrong size on WB_Base_C_PB_SetFillColorGradientPower");
static_assert(offsetof(WB_Base_C_PB_SetFillColorGradientPower, GradientPower) == 0x000000, "Member 'WB_Base_C_PB_SetFillColorGradientPower::GradientPower' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetFillColorGradientType
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetFillColorGradientType final
{
public:
	EGradientTypes                                GradientType;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetFillColorGradientType) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetFillColorGradientType");
static_assert(sizeof(WB_Base_C_PB_SetFillColorGradientType) == 0x000001, "Wrong size on WB_Base_C_PB_SetFillColorGradientType");
static_assert(offsetof(WB_Base_C_PB_SetFillColorGradientType, GradientType) == 0x000000, "Member 'WB_Base_C_PB_SetFillColorGradientType::GradientType' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetFillColorMask
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetFillColorMask final
{
public:
	class UObject*                                Mask;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetFillColorMask) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetFillColorMask");
static_assert(sizeof(WB_Base_C_PB_SetFillColorMask) == 0x000008, "Wrong size on WB_Base_C_PB_SetFillColorMask");
static_assert(offsetof(WB_Base_C_PB_SetFillColorMask, Mask) == 0x000000, "Member 'WB_Base_C_PB_SetFillColorMask::Mask' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetFillFromCenterSpacing
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetFillFromCenterSpacing final
{
public:
	double                                        Spacing;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetFillFromCenterSpacing) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetFillFromCenterSpacing");
static_assert(sizeof(WB_Base_C_PB_SetFillFromCenterSpacing) == 0x000008, "Wrong size on WB_Base_C_PB_SetFillFromCenterSpacing");
static_assert(offsetof(WB_Base_C_PB_SetFillFromCenterSpacing, Spacing) == 0x000000, "Member 'WB_Base_C_PB_SetFillFromCenterSpacing::Spacing' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetFillType
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetFillType final
{
public:
	EProgressBarFillType                          FillType;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetFillType) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetFillType");
static_assert(sizeof(WB_Base_C_PB_SetFillType) == 0x000001, "Wrong size on WB_Base_C_PB_SetFillType");
static_assert(offsetof(WB_Base_C_PB_SetFillType, FillType) == 0x000000, "Member 'WB_Base_C_PB_SetFillType::FillType' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetInterpTimeCurrent
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetInterpTimeCurrent final
{
public:
	double                                        CurrentInterpTime;                                 // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetInterpTimeCurrent) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetInterpTimeCurrent");
static_assert(sizeof(WB_Base_C_PB_SetInterpTimeCurrent) == 0x000008, "Wrong size on WB_Base_C_PB_SetInterpTimeCurrent");
static_assert(offsetof(WB_Base_C_PB_SetInterpTimeCurrent, CurrentInterpTime) == 0x000000, "Member 'WB_Base_C_PB_SetInterpTimeCurrent::CurrentInterpTime' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetInterpTimeTarget
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetInterpTimeTarget final
{
public:
	double                                        TargetInterpTime;                                  // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetInterpTimeTarget) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetInterpTimeTarget");
static_assert(sizeof(WB_Base_C_PB_SetInterpTimeTarget) == 0x000008, "Wrong size on WB_Base_C_PB_SetInterpTimeTarget");
static_assert(offsetof(WB_Base_C_PB_SetInterpTimeTarget, TargetInterpTime) == 0x000000, "Member 'WB_Base_C_PB_SetInterpTimeTarget::TargetInterpTime' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetIsCustomMarquee
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetIsCustomMarquee final
{
public:
	bool                                          IsMarquee;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetIsCustomMarquee) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetIsCustomMarquee");
static_assert(sizeof(WB_Base_C_PB_SetIsCustomMarquee) == 0x000001, "Wrong size on WB_Base_C_PB_SetIsCustomMarquee");
static_assert(offsetof(WB_Base_C_PB_SetIsCustomMarquee, IsMarquee) == 0x000000, "Member 'WB_Base_C_PB_SetIsCustomMarquee::IsMarquee' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetIsMarquee
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetIsMarquee final
{
public:
	bool                                          IsMarquee;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetIsMarquee) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetIsMarquee");
static_assert(sizeof(WB_Base_C_PB_SetIsMarquee) == 0x000001, "Wrong size on WB_Base_C_PB_SetIsMarquee");
static_assert(offsetof(WB_Base_C_PB_SetIsMarquee, IsMarquee) == 0x000000, "Member 'WB_Base_C_PB_SetIsMarquee::IsMarquee' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetMarqueeImage
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetMarqueeImage final
{
public:
	class UTexture2D*                             Image;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetMarqueeImage) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetMarqueeImage");
static_assert(sizeof(WB_Base_C_PB_SetMarqueeImage) == 0x000008, "Wrong size on WB_Base_C_PB_SetMarqueeImage");
static_assert(offsetof(WB_Base_C_PB_SetMarqueeImage, Image) == 0x000000, "Member 'WB_Base_C_PB_SetMarqueeImage::Image' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetPercent final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetPercent) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetPercent");
static_assert(sizeof(WB_Base_C_PB_SetPercent) == 0x000008, "Wrong size on WB_Base_C_PB_SetPercent");
static_assert(offsetof(WB_Base_C_PB_SetPercent, Value) == 0x000000, "Member 'WB_Base_C_PB_SetPercent::Value' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetProgressMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetProgressMethod final
{
public:
	EProgressMethod                               EProgressMethod;                                   // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetProgressMethod) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetProgressMethod");
static_assert(sizeof(WB_Base_C_PB_SetProgressMethod) == 0x000001, "Wrong size on WB_Base_C_PB_SetProgressMethod");
static_assert(offsetof(WB_Base_C_PB_SetProgressMethod, EProgressMethod) == 0x000000, "Member 'WB_Base_C_PB_SetProgressMethod::EProgressMethod' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetSeparationAbsoluteFill
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetSeparationAbsoluteFill final
{
public:
	bool                                          AbsoluteFillMethod;                                // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetSeparationAbsoluteFill) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetSeparationAbsoluteFill");
static_assert(sizeof(WB_Base_C_PB_SetSeparationAbsoluteFill) == 0x000001, "Wrong size on WB_Base_C_PB_SetSeparationAbsoluteFill");
static_assert(offsetof(WB_Base_C_PB_SetSeparationAbsoluteFill, AbsoluteFillMethod) == 0x000000, "Member 'WB_Base_C_PB_SetSeparationAbsoluteFill::AbsoluteFillMethod' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetSeparationSteps
// 0x0004 (0x0004 - 0x0000)
struct WB_Base_C_PB_SetSeparationSteps final
{
public:
	int32                                         Steps;                                             // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetSeparationSteps) == 0x000004, "Wrong alignment on WB_Base_C_PB_SetSeparationSteps");
static_assert(sizeof(WB_Base_C_PB_SetSeparationSteps) == 0x000004, "Wrong size on WB_Base_C_PB_SetSeparationSteps");
static_assert(offsetof(WB_Base_C_PB_SetSeparationSteps, Steps) == 0x000000, "Member 'WB_Base_C_PB_SetSeparationSteps::Steps' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetSeparationStepsSpacing
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetSeparationStepsSpacing final
{
public:
	double                                        Spacing;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetSeparationStepsSpacing) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetSeparationStepsSpacing");
static_assert(sizeof(WB_Base_C_PB_SetSeparationStepsSpacing) == 0x000008, "Wrong size on WB_Base_C_PB_SetSeparationStepsSpacing");
static_assert(offsetof(WB_Base_C_PB_SetSeparationStepsSpacing, Spacing) == 0x000000, "Member 'WB_Base_C_PB_SetSeparationStepsSpacing::Spacing' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetSize
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_SetSize final
{
public:
	struct FVector2D                              Size;                                              // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetSize) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetSize");
static_assert(sizeof(WB_Base_C_PB_SetSize) == 0x000010, "Wrong size on WB_Base_C_PB_SetSize");
static_assert(offsetof(WB_Base_C_PB_SetSize, Size) == 0x000000, "Member 'WB_Base_C_PB_SetSize::Size' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetTargetFillColor_Negative
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_SetTargetFillColor_Negative final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetTargetFillColor_Negative) == 0x000004, "Wrong alignment on WB_Base_C_PB_SetTargetFillColor_Negative");
static_assert(sizeof(WB_Base_C_PB_SetTargetFillColor_Negative) == 0x000010, "Wrong size on WB_Base_C_PB_SetTargetFillColor_Negative");
static_assert(offsetof(WB_Base_C_PB_SetTargetFillColor_Negative, Color) == 0x000000, "Member 'WB_Base_C_PB_SetTargetFillColor_Negative::Color' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetTargetFillColor_Positive
// 0x0010 (0x0010 - 0x0000)
struct WB_Base_C_PB_SetTargetFillColor_Positive final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetTargetFillColor_Positive) == 0x000004, "Wrong alignment on WB_Base_C_PB_SetTargetFillColor_Positive");
static_assert(sizeof(WB_Base_C_PB_SetTargetFillColor_Positive) == 0x000010, "Wrong size on WB_Base_C_PB_SetTargetFillColor_Positive");
static_assert(offsetof(WB_Base_C_PB_SetTargetFillColor_Positive, Color) == 0x000000, "Member 'WB_Base_C_PB_SetTargetFillColor_Positive::Color' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetTargetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetTargetPercent final
{
public:
	double                                        TargetPercent;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetTargetPercent) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetTargetPercent");
static_assert(sizeof(WB_Base_C_PB_SetTargetPercent) == 0x000008, "Wrong size on WB_Base_C_PB_SetTargetPercent");
static_assert(offsetof(WB_Base_C_PB_SetTargetPercent, TargetPercent) == 0x000000, "Member 'WB_Base_C_PB_SetTargetPercent::TargetPercent' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetThickness
// 0x0008 (0x0008 - 0x0000)
struct WB_Base_C_PB_SetThickness final
{
public:
	double                                        Thickness;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetThickness) == 0x000008, "Wrong alignment on WB_Base_C_PB_SetThickness");
static_assert(sizeof(WB_Base_C_PB_SetThickness) == 0x000008, "Wrong size on WB_Base_C_PB_SetThickness");
static_assert(offsetof(WB_Base_C_PB_SetThickness, Thickness) == 0x000000, "Member 'WB_Base_C_PB_SetThickness::Thickness' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetUseGradientFillColor
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetUseGradientFillColor final
{
public:
	bool                                          UseGradientFillColor;                              // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetUseGradientFillColor) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetUseGradientFillColor");
static_assert(sizeof(WB_Base_C_PB_SetUseGradientFillColor) == 0x000001, "Wrong size on WB_Base_C_PB_SetUseGradientFillColor");
static_assert(offsetof(WB_Base_C_PB_SetUseGradientFillColor, UseGradientFillColor) == 0x000000, "Member 'WB_Base_C_PB_SetUseGradientFillColor::UseGradientFillColor' has a wrong offset!");

// Function WB_Base.WB_Base_C.PB_SetUseTargetPercent
// 0x0001 (0x0001 - 0x0000)
struct WB_Base_C_PB_SetUseTargetPercent final
{
public:
	bool                                          UseTargetPercent;                                  // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Base_C_PB_SetUseTargetPercent) == 0x000001, "Wrong alignment on WB_Base_C_PB_SetUseTargetPercent");
static_assert(sizeof(WB_Base_C_PB_SetUseTargetPercent) == 0x000001, "Wrong size on WB_Base_C_PB_SetUseTargetPercent");
static_assert(offsetof(WB_Base_C_PB_SetUseTargetPercent, UseTargetPercent) == 0x000000, "Member 'WB_Base_C_PB_SetUseTargetPercent::UseTargetPercent' has a wrong offset!");

}

