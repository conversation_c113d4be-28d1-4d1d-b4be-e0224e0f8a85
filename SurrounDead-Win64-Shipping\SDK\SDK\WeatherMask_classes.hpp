﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WeatherMask

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "Engine_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass WeatherMask.WeatherMask_C
// 0x0070 (0x06A0 - 0x0630)
#pragma pack(push, 0x1)
class alignas(0x10) UWeatherMask_C : public UStaticMeshComponent
{
public:
	uint8                                         Pad_628[0x8];                                      // 0x0628(0x0008)(Fixing Size After Last Property [ Dumper-7 ])
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0630(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class AUltra_Dynamic_Weather_C*               UDW;                                               // 0x0638(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, Transient, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Dust;                                              // 0x0640(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	double                                        Mask_Wetness;                                      // 0x0648(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	struct FVector2D                              Brush_Location;                                    // 0x0650(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Brush_Scale;                                       // 0x0660(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Brush_Yaw;                                         // 0x0670(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Center_Location;                                   // 0x0678(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Distance;                                      // 0x0688(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Ready_for_Drawing;                                 // 0x0690(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Spawned_At_Runtime;                                // 0x0691(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Calculate_Masking_At_Location(const struct FVector& Location, struct FVector2D* Mask, bool* Cancel_All_Masks);
	bool Component_Generally_In_Range();
	void Configure_Collision();
	void Disable();
	void Editor_Update();
	void Enable();
	void ExecuteUbergraph_WeatherMask(int32 EntryPoint);
	void Force_Update();
	void Get_Brush_Location(struct FVector2D* Out);
	void Get_Brush_Scale(struct FVector2D* Out);
	void Get_Brush_Yaw(double* Out);
	void Get_Center_Location(struct FVector2D* Out);
	void Get_Max_Distance(double* Out);
	void Prepare_for_Drawing(bool Runtime, class AUltra_Dynamic_Weather_C* UDW_0);
	void ReceiveBeginPlay();
	void ReceiveEndPlay(EEndPlayReason EndPlayReason);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WeatherMask_C">();
	}
	static class UWeatherMask_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWeatherMask_C>();
	}
};
#pragma pack(pop)
static_assert(alignof(UWeatherMask_C) == 0x000010, "Wrong alignment on UWeatherMask_C");
static_assert(sizeof(UWeatherMask_C) == 0x0006A0, "Wrong size on UWeatherMask_C");
static_assert(offsetof(UWeatherMask_C, UberGraphFrame) == 0x000630, "Member 'UWeatherMask_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, UDW) == 0x000638, "Member 'UWeatherMask_C::UDW' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, Dust) == 0x000640, "Member 'UWeatherMask_C::Dust' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, Mask_Wetness) == 0x000648, "Member 'UWeatherMask_C::Mask_Wetness' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, Brush_Location) == 0x000650, "Member 'UWeatherMask_C::Brush_Location' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, Brush_Scale) == 0x000660, "Member 'UWeatherMask_C::Brush_Scale' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, Brush_Yaw) == 0x000670, "Member 'UWeatherMask_C::Brush_Yaw' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, Center_Location) == 0x000678, "Member 'UWeatherMask_C::Center_Location' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, Max_Distance) == 0x000688, "Member 'UWeatherMask_C::Max_Distance' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, Ready_for_Drawing) == 0x000690, "Member 'UWeatherMask_C::Ready_for_Drawing' has a wrong offset!");
static_assert(offsetof(UWeatherMask_C, Spawned_At_Runtime) == 0x000691, "Member 'UWeatherMask_C::Spawned_At_Runtime' has a wrong offset!");

}

