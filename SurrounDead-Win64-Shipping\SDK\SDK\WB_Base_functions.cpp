﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Base

#include "Basic.hpp"

#include "WB_Base_classes.hpp"
#include "WB_Base_parameters.hpp"


namespace SDK
{

// Function WB_Base.WB_Base_C.ExecuteUbergraph_WB_Base
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::ExecuteUbergraph_WB_Base(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "ExecuteUbergraph_WB_Base");

	Params::WB_Base_C_ExecuteUbergraph_WB_Base Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_AddEffect
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FS_Effects&                Effect                                                 (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
// int32*                                  Index_0                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_AddEffect(const struct FS_Effects& Effect, int32* Index_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_AddEffect");

	Params::WB_Base_C_PB_AddEffect Parms{};

	Parms.Effect = std::move(Effect);

	UObject::ProcessEvent(Func, &Parms);

	if (Index_0 != nullptr)
		*Index_0 = Parms.Index_0;
}


// Function WB_Base.WB_Base_C.PB_GetBackgroundColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FLinearColor*                    Color                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetBackgroundColor(struct FLinearColor* Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetBackgroundColor");

	Params::WB_Base_C_PB_GetBackgroundColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Color != nullptr)
		*Color = std::move(Parms.Color);
}


// Function WB_Base.WB_Base_C.PB_GetEffects
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<struct FS_Effects>*              Effects                                                (Parm, OutParm)

void UWB_Base_C::PB_GetEffects(TArray<struct FS_Effects>* Effects)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetEffects");

	Params::WB_Base_C_PB_GetEffects Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Effects != nullptr)
		*Effects = std::move(Parms.Effects);
}


// Function WB_Base.WB_Base_C.PB_GetFillColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FLinearColor*                    Color                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetFillColor(struct FLinearColor* Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetFillColor");

	Params::WB_Base_C_PB_GetFillColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Color != nullptr)
		*Color = std::move(Parms.Color);
}


// Function WB_Base.WB_Base_C.PB_GetInterpTimeCurrent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 CurrentInterpTime                                      (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetInterpTimeCurrent(double* CurrentInterpTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetInterpTimeCurrent");

	Params::WB_Base_C_PB_GetInterpTimeCurrent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (CurrentInterpTime != nullptr)
		*CurrentInterpTime = Parms.CurrentInterpTime;
}


// Function WB_Base.WB_Base_C.PB_GetInterpTimeTarget
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 TargetInterpTime                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetInterpTimeTarget(double* TargetInterpTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetInterpTimeTarget");

	Params::WB_Base_C_PB_GetInterpTimeTarget Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (TargetInterpTime != nullptr)
		*TargetInterpTime = Parms.TargetInterpTime;
}


// Function WB_Base.WB_Base_C.PB_GetIsCustomMarquee
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   IsMarquee                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetIsCustomMarquee(bool* IsMarquee)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetIsCustomMarquee");

	Params::WB_Base_C_PB_GetIsCustomMarquee Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (IsMarquee != nullptr)
		*IsMarquee = Parms.IsMarquee;
}


// Function WB_Base.WB_Base_C.PB_GetIsMarquee
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   IsMarquee                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetIsMarquee(bool* IsMarquee)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetIsMarquee");

	Params::WB_Base_C_PB_GetIsMarquee Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (IsMarquee != nullptr)
		*IsMarquee = Parms.IsMarquee;
}


// Function WB_Base.WB_Base_C.PB_GetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 Percent                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetPercent(double* Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetPercent");

	Params::WB_Base_C_PB_GetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Percent != nullptr)
		*Percent = Parms.Percent;
}


// Function WB_Base.WB_Base_C.PB_GetProgressMethod
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressMethod*                        EProgressMethod                                        (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetProgressMethod(EProgressMethod* EProgressMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetProgressMethod");

	Params::WB_Base_C_PB_GetProgressMethod Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (EProgressMethod != nullptr)
		*EProgressMethod = Parms.EProgressMethod;
}


// Function WB_Base.WB_Base_C.PB_GetSeparationSteps
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32*                                  Steps                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetSeparationSteps(int32* Steps)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetSeparationSteps");

	Params::WB_Base_C_PB_GetSeparationSteps Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Steps != nullptr)
		*Steps = Parms.Steps;
}


// Function WB_Base.WB_Base_C.PB_GetSize
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FVector2D*                       Size                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetSize(struct FVector2D* Size)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetSize");

	Params::WB_Base_C_PB_GetSize Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Size != nullptr)
		*Size = std::move(Parms.Size);
}


// Function WB_Base.WB_Base_C.PB_GetTargetFillColor_Negative
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FLinearColor*                    Color                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetTargetFillColor_Negative(struct FLinearColor* Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetTargetFillColor_Negative");

	Params::WB_Base_C_PB_GetTargetFillColor_Negative Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Color != nullptr)
		*Color = std::move(Parms.Color);
}


// Function WB_Base.WB_Base_C.PB_GetTargetFillColor_Positive
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FLinearColor*                    Color                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetTargetFillColor_Positive(struct FLinearColor* Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetTargetFillColor_Positive");

	Params::WB_Base_C_PB_GetTargetFillColor_Positive Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Color != nullptr)
		*Color = std::move(Parms.Color);
}


// Function WB_Base.WB_Base_C.PB_GetTargetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 TargetPercent                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetTargetPercent(double* TargetPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetTargetPercent");

	Params::WB_Base_C_PB_GetTargetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (TargetPercent != nullptr)
		*TargetPercent = Parms.TargetPercent;
}


// Function WB_Base.WB_Base_C.PB_GetThickness
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 Thickness                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetThickness(double* Thickness)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetThickness");

	Params::WB_Base_C_PB_GetThickness Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Thickness != nullptr)
		*Thickness = Parms.Thickness;
}


// Function WB_Base.WB_Base_C.PB_GetUseGradientFillColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   UseGradientFillColor                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetUseGradientFillColor(bool* UseGradientFillColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetUseGradientFillColor");

	Params::WB_Base_C_PB_GetUseGradientFillColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (UseGradientFillColor != nullptr)
		*UseGradientFillColor = Parms.UseGradientFillColor;
}


// Function WB_Base.WB_Base_C.PB_GetUseTargetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   UseTargetPercent                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_GetUseTargetPercent(bool* UseTargetPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_GetUseTargetPercent");

	Params::WB_Base_C_PB_GetUseTargetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (UseTargetPercent != nullptr)
		*UseTargetPercent = Parms.UseTargetPercent;
}


// Function WB_Base.WB_Base_C.PB_RemoveEffect
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Index_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_RemoveEffect(int32 Index_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_RemoveEffect");

	Params::WB_Base_C_PB_RemoveEffect Parms{};

	Parms.Index_0 = Index_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetAllEffectsEnabled
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsEnabled                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetAllEffectsEnabled(bool IsEnabled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetAllEffectsEnabled");

	Params::WB_Base_C_PB_SetAllEffectsEnabled Parms{};

	Parms.IsEnabled = IsEnabled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetBackgroundBlurStrength
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  BlurStrength                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetBackgroundBlurStrength(double BlurStrength)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetBackgroundBlurStrength");

	Params::WB_Base_C_PB_SetBackgroundBlurStrength Parms{};

	Parms.BlurStrength = BlurStrength;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetBackgroundBrushTiling
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetBackgroundBrushTiling(ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetBackgroundBrushTiling");

	Params::WB_Base_C_PB_SetBackgroundBrushTiling Parms{};

	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetBackgroundColor
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetBackgroundColor(const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetBackgroundColor");

	Params::WB_Base_C_PB_SetBackgroundColor Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetBackgroundColorMask
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture2D*                       Mask                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetBackgroundColorMask(class UTexture2D* Mask)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetBackgroundColorMask");

	Params::WB_Base_C_PB_SetBackgroundColorMask Parms{};

	Parms.Mask = Mask;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetBlendMask
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture2D*                       BlendMask                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetBlendMask(class UTexture2D* BlendMask)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetBlendMask");

	Params::WB_Base_C_PB_SetBlendMask Parms{};

	Parms.BlendMask = BlendMask;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetCustomMarqueeImage
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture2D*                       Image                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetCustomMarqueeImage(class UTexture2D* Image)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetCustomMarqueeImage");

	Params::WB_Base_C_PB_SetCustomMarqueeImage Parms{};

	Parms.Image = Image;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetCustomMarqueeMaskType
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EMarqueeMask                            MaskType                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetCustomMarqueeMaskType(EMarqueeMask MaskType)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetCustomMarqueeMaskType");

	Params::WB_Base_C_PB_SetCustomMarqueeMaskType Parms{};

	Parms.MaskType = MaskType;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetEffectEnabled
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Index_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    IsEnabled                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetEffectEnabled(int32 Index_0, bool IsEnabled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetEffectEnabled");

	Params::WB_Base_C_PB_SetEffectEnabled Parms{};

	Parms.Index_0 = Index_0;
	Parms.IsEnabled = IsEnabled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetEffects
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const TArray<struct FS_Effects>&        Effects                                                (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWB_Base_C::PB_SetEffects(const TArray<struct FS_Effects>& Effects)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetEffects");

	Params::WB_Base_C_PB_SetEffects Parms{};

	Parms.Effects = std::move(Effects);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetFillColor
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetFillColor(const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetFillColor");

	Params::WB_Base_C_PB_SetFillColor Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetFillColorBrushTiling
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetFillColorBrushTiling(ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetFillColorBrushTiling");

	Params::WB_Base_C_PB_SetFillColorBrushTiling Parms{};

	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetFillColorGradientPower
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  GradientPower                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetFillColorGradientPower(double GradientPower)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetFillColorGradientPower");

	Params::WB_Base_C_PB_SetFillColorGradientPower Parms{};

	Parms.GradientPower = GradientPower;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetFillColorGradientType
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EGradientTypes                          GradientType                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetFillColorGradientType(EGradientTypes GradientType)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetFillColorGradientType");

	Params::WB_Base_C_PB_SetFillColorGradientType Parms{};

	Parms.GradientType = GradientType;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetFillColorMask
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          Mask                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetFillColorMask(class UObject* Mask)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetFillColorMask");

	Params::WB_Base_C_PB_SetFillColorMask Parms{};

	Parms.Mask = Mask;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetFillFromCenterSpacing
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Spacing                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetFillFromCenterSpacing(double Spacing)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetFillFromCenterSpacing");

	Params::WB_Base_C_PB_SetFillFromCenterSpacing Parms{};

	Parms.Spacing = Spacing;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetFillType
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressBarFillType                    FillType                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetFillType(EProgressBarFillType FillType)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetFillType");

	Params::WB_Base_C_PB_SetFillType Parms{};

	Parms.FillType = FillType;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetInterpTimeCurrent
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  CurrentInterpTime                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetInterpTimeCurrent(double CurrentInterpTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetInterpTimeCurrent");

	Params::WB_Base_C_PB_SetInterpTimeCurrent Parms{};

	Parms.CurrentInterpTime = CurrentInterpTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetInterpTimeTarget
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  TargetInterpTime                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetInterpTimeTarget(double TargetInterpTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetInterpTimeTarget");

	Params::WB_Base_C_PB_SetInterpTimeTarget Parms{};

	Parms.TargetInterpTime = TargetInterpTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetIsCustomMarquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsMarquee                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetIsCustomMarquee(bool IsMarquee)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetIsCustomMarquee");

	Params::WB_Base_C_PB_SetIsCustomMarquee Parms{};

	Parms.IsMarquee = IsMarquee;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetIsMarquee
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsMarquee                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetIsMarquee(bool IsMarquee)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetIsMarquee");

	Params::WB_Base_C_PB_SetIsMarquee Parms{};

	Parms.IsMarquee = IsMarquee;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetMarqueeImage
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture2D*                       Image                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetMarqueeImage(class UTexture2D* Image)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetMarqueeImage");

	Params::WB_Base_C_PB_SetMarqueeImage Parms{};

	Parms.Image = Image;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetPercent
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetPercent(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetPercent");

	Params::WB_Base_C_PB_SetPercent Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetProgressMethod
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressMethod                         EProgressMethod                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetProgressMethod(EProgressMethod EProgressMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetProgressMethod");

	Params::WB_Base_C_PB_SetProgressMethod Parms{};

	Parms.EProgressMethod = EProgressMethod;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetSeparationAbsoluteFill
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    AbsoluteFillMethod                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetSeparationAbsoluteFill(bool AbsoluteFillMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetSeparationAbsoluteFill");

	Params::WB_Base_C_PB_SetSeparationAbsoluteFill Parms{};

	Parms.AbsoluteFillMethod = AbsoluteFillMethod;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetSeparationSteps
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Steps                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetSeparationSteps(int32 Steps)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetSeparationSteps");

	Params::WB_Base_C_PB_SetSeparationSteps Parms{};

	Parms.Steps = Steps;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetSeparationStepsSpacing
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Spacing                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetSeparationStepsSpacing(double Spacing)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetSeparationStepsSpacing");

	Params::WB_Base_C_PB_SetSeparationStepsSpacing Parms{};

	Parms.Spacing = Spacing;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetSize
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 Size                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetSize(const struct FVector2D& Size)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetSize");

	Params::WB_Base_C_PB_SetSize Parms{};

	Parms.Size = std::move(Size);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetTargetFillColor_Negative
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetTargetFillColor_Negative(const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetTargetFillColor_Negative");

	Params::WB_Base_C_PB_SetTargetFillColor_Negative Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetTargetFillColor_Positive
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetTargetFillColor_Positive(const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetTargetFillColor_Positive");

	Params::WB_Base_C_PB_SetTargetFillColor_Positive Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetTargetPercent
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  TargetPercent                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetTargetPercent(double TargetPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetTargetPercent");

	Params::WB_Base_C_PB_SetTargetPercent Parms{};

	Parms.TargetPercent = TargetPercent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetThickness
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Thickness                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetThickness(double Thickness)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetThickness");

	Params::WB_Base_C_PB_SetThickness Parms{};

	Parms.Thickness = Thickness;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetUseGradientFillColor
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    UseGradientFillColor                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetUseGradientFillColor(bool UseGradientFillColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetUseGradientFillColor");

	Params::WB_Base_C_PB_SetUseGradientFillColor Parms{};

	Parms.UseGradientFillColor = UseGradientFillColor;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Base.WB_Base_C.PB_SetUseTargetPercent
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    UseTargetPercent                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Base_C::PB_SetUseTargetPercent(bool UseTargetPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Base_C", "PB_SetUseTargetPercent");

	Params::WB_Base_C_PB_SetUseTargetPercent Parms{};

	Parms.UseTargetPercent = UseTargetPercent;

	UObject::ProcessEvent(Func, &Parms);
}

}

