﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_BaseProgressBar

#include "Basic.hpp"

#include "WB_BaseProgressBar_classes.hpp"
#include "WB_BaseProgressBar_parameters.hpp"


namespace SDK
{

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.ExecuteUbergraph_WB_BaseProgressBar
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::ExecuteUbergraph_WB_BaseProgressBar(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "ExecuteUbergraph_WB_BaseProgressBar");

	Params::WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.GetCurrentProgressBar
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UProgressBar**                    AsProgress_Bar                                         (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::GetCurrentProgressBar(class UProgressBar** AsProgress_Bar)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "GetCurrentProgressBar");

	Params::WB_BaseProgressBar_C_GetCurrentProgressBar Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (AsProgress_Bar != nullptr)
		*AsProgress_Bar = Parms.AsProgress_Bar;
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.GetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Percent_0                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::GetPercent(double* Percent_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "GetPercent");

	Params::WB_BaseProgressBar_C_GetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Percent_0 != nullptr)
		*Percent_0 = Parms.Percent_0;
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetBackgroundTint
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Tint                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetBackgroundTint(const struct FLinearColor& Tint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetBackgroundTint");

	Params::WB_BaseProgressBar_C_SetBackgroundTint Parms{};

	Parms.Tint = std::move(Tint);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetBarFillType
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressBarFillType                    BarFillType                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bUseShader_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetBarFillType(EProgressBarFillType BarFillType, bool bUseShader_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetBarFillType");

	Params::WB_BaseProgressBar_C_SetBarFillType Parms{};

	Parms.BarFillType = BarFillType;
	Parms.bUseShader_0 = bUseShader_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillColorAndOpacity
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColor                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetFillColorAndOpacity(const struct FLinearColor& InColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetFillColorAndOpacity");

	Params::WB_BaseProgressBar_C_SetFillColorAndOpacity Parms{};

	Parms.InColor = std::move(InColor);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImage
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          FillImage                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetFillImage(class UObject* FillImage)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetFillImage");

	Params::WB_BaseProgressBar_C_SetFillImage Parms{};

	Parms.FillImage = FillImage;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImageDrawAs
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushDrawType                     Draw_As                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetFillImageDrawAs(ESlateBrushDrawType Draw_As)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetFillImageDrawAs");

	Params::WB_BaseProgressBar_C_SetFillImageDrawAs Parms{};

	Parms.Draw_As = Draw_As;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImageMargin
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Margin                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetFillImageMargin(double Margin)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetFillImageMargin");

	Params::WB_BaseProgressBar_C_SetFillImageMargin Parms{};

	Parms.Margin = Margin;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImageSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 FillImageSize                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetFillImageSize(const struct FVector2D& FillImageSize)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetFillImageSize");

	Params::WB_BaseProgressBar_C_SetFillImageSize Parms{};

	Parms.FillImageSize = std::move(FillImageSize);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImageTiling
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetFillImageTiling(ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetFillImageTiling");

	Params::WB_BaseProgressBar_C_SetFillImageTiling Parms{};

	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetImageBlendMask
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          BlendMask                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetImageBlendMask(class UObject* BlendMask)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetImageBlendMask");

	Params::WB_BaseProgressBar_C_SetImageBlendMask Parms{};

	Parms.BlendMask = BlendMask;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetImagePercent
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Percent_0                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetImagePercent(double Percent_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetImagePercent");

	Params::WB_BaseProgressBar_C_SetImagePercent Parms{};

	Parms.Percent_0 = Percent_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetImgsEnabled
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bInIsEnabled                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetImgsEnabled(bool bInIsEnabled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetImgsEnabled");

	Params::WB_BaseProgressBar_C_SetImgsEnabled Parms{};

	Parms.bInIsEnabled = bInIsEnabled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetInstanceBlendMask
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           Img                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UTexture*                         BlendMask                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetInstanceBlendMask(class UImage* Img, class UTexture* BlendMask)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetInstanceBlendMask");

	Params::WB_BaseProgressBar_C_SetInstanceBlendMask Parms{};

	Parms.Img = Img;
	Parms.BlendMask = BlendMask;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetInstancePercent
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           Img                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// double                                  Percent_0                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetInstancePercent(class UImage* Img, double Percent_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetInstancePercent");

	Params::WB_BaseProgressBar_C_SetInstancePercent Parms{};

	Parms.Img = Img;
	Parms.Percent_0 = Percent_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetIsMarquee
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    InbIsMarquee                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetIsMarquee(bool InbIsMarquee)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetIsMarquee");

	Params::WB_BaseProgressBar_C_SetIsMarquee Parms{};

	Parms.InbIsMarquee = InbIsMarquee;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeDrawAs
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushDrawType                     Draw_As                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetMarqueeDrawAs(ESlateBrushDrawType Draw_As)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetMarqueeDrawAs");

	Params::WB_BaseProgressBar_C_SetMarqueeDrawAs Parms{};

	Parms.Draw_As = Draw_As;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeImage
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          Image                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetMarqueeImage(class UObject* Image)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetMarqueeImage");

	Params::WB_BaseProgressBar_C_SetMarqueeImage Parms{};

	Parms.Image = Image;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeImageSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 Image_Size                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetMarqueeImageSize(const struct FVector2D& Image_Size)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetMarqueeImageSize");

	Params::WB_BaseProgressBar_C_SetMarqueeImageSize Parms{};

	Parms.Image_Size = std::move(Image_Size);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeTiling
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetMarqueeTiling(ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetMarqueeTiling");

	Params::WB_BaseProgressBar_C_SetMarqueeTiling Parms{};

	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeTint
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetMarqueeTint(const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetMarqueeTint");

	Params::WB_BaseProgressBar_C_SetMarqueeTint Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetPBsEnabled
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bInIsEnabled                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetPBsEnabled(bool bInIsEnabled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetPBsEnabled");

	Params::WB_BaseProgressBar_C_SetPBsEnabled Parms{};

	Parms.bInIsEnabled = bInIsEnabled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  InPercent                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetPercent(double InPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetPercent");

	Params::WB_BaseProgressBar_C_SetPercent Parms{};

	Parms.InPercent = InPercent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetScalarParameter
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           Img                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class FName                             ParameterName                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetScalarParameter(class UImage* Img, class FName ParameterName, double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetScalarParameter");

	Params::WB_BaseProgressBar_C_SetScalarParameter Parms{};

	Parms.Img = Img;
	Parms.ParameterName = ParameterName;
	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetTextureParameter
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           Img                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class FName                             ParameterName                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UTexture*                         Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_BaseProgressBar_C::SetTextureParameter(class UImage* Img, class FName ParameterName, class UTexture* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_BaseProgressBar_C", "SetTextureParameter");

	Params::WB_BaseProgressBar_C_SetTextureParameter Parms{};

	Parms.Img = Img;
	Parms.ParameterName = ParameterName;
	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}

}

