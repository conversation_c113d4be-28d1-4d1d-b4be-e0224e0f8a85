﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_WorldMarkerTooltip

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_WorldMarkerTooltip.W_WorldMarkerTooltip_C
// 0x0078 (0x0338 - 0x02C0)
class UW_WorldMarkerTooltip_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                DescriptionBorder;                                 // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             POIDescription;                                    // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 POIImage;                                          // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             POIName;                                           // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                TitleBorder;                                       // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   Name_0;                                            // 0x02F0(0x0018)(Edit, BlueprintVisible)
	class FText                                   Description;                                       // 0x0308(0x0018)(Edit, BlueprintVisible)
	class UObject*                                Image;                                             // 0x0320(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Color;                                             // 0x0328(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Construct();
	void ExecuteUbergraph_W_WorldMarkerTooltip(int32 EntryPoint);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_WorldMarkerTooltip_C">();
	}
	static class UW_WorldMarkerTooltip_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_WorldMarkerTooltip_C>();
	}
};
static_assert(alignof(UW_WorldMarkerTooltip_C) == 0x000008, "Wrong alignment on UW_WorldMarkerTooltip_C");
static_assert(sizeof(UW_WorldMarkerTooltip_C) == 0x000338, "Wrong size on UW_WorldMarkerTooltip_C");
static_assert(offsetof(UW_WorldMarkerTooltip_C, UberGraphFrame) == 0x0002C0, "Member 'UW_WorldMarkerTooltip_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_WorldMarkerTooltip_C, DescriptionBorder) == 0x0002C8, "Member 'UW_WorldMarkerTooltip_C::DescriptionBorder' has a wrong offset!");
static_assert(offsetof(UW_WorldMarkerTooltip_C, POIDescription) == 0x0002D0, "Member 'UW_WorldMarkerTooltip_C::POIDescription' has a wrong offset!");
static_assert(offsetof(UW_WorldMarkerTooltip_C, POIImage) == 0x0002D8, "Member 'UW_WorldMarkerTooltip_C::POIImage' has a wrong offset!");
static_assert(offsetof(UW_WorldMarkerTooltip_C, POIName) == 0x0002E0, "Member 'UW_WorldMarkerTooltip_C::POIName' has a wrong offset!");
static_assert(offsetof(UW_WorldMarkerTooltip_C, TitleBorder) == 0x0002E8, "Member 'UW_WorldMarkerTooltip_C::TitleBorder' has a wrong offset!");
static_assert(offsetof(UW_WorldMarkerTooltip_C, Name_0) == 0x0002F0, "Member 'UW_WorldMarkerTooltip_C::Name_0' has a wrong offset!");
static_assert(offsetof(UW_WorldMarkerTooltip_C, Description) == 0x000308, "Member 'UW_WorldMarkerTooltip_C::Description' has a wrong offset!");
static_assert(offsetof(UW_WorldMarkerTooltip_C, Image) == 0x000320, "Member 'UW_WorldMarkerTooltip_C::Image' has a wrong offset!");
static_assert(offsetof(UW_WorldMarkerTooltip_C, Color) == 0x000328, "Member 'UW_WorldMarkerTooltip_C::Color' has a wrong offset!");

}

