﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_POIMarkerTooltip

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "Struct_POI_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_POIMarkerTooltip.W_POIMarkerTooltip_C
// 0x0118 (0x03D8 - 0x02C0)
class UW_POIMarkerTooltip_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UHorizontalBox*                         ContaminationBox;                                  // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 ContaminationImg;                                  // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             ContaminationLevelText;                            // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_92;                                          // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           LargePOIInfoBox;                                   // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             POIBossAmount;                                     // 0x02F0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             POIContainer;                                      // 0x02F8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             POIDescription;                                    // 0x0300(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 POIImage;                                          // 0x0308(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             POILoot;                                           // 0x0310(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             POIName;                                           // 0x0318(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                POITitleBorder;                                    // 0x0320(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FString                                 Name_0;                                            // 0x0328(0x0010)(Edit, BlueprintVisible, ZeroConstructor, HasGetValueTypeHash)
	class FString                                 Description;                                       // 0x0338(0x0010)(Edit, BlueprintVisible, ZeroConstructor, HasGetValueTypeHash)
	class FString                                 ItemsToSpawn;                                      // 0x0348(0x0010)(Edit, BlueprintVisible, ZeroConstructor, HasGetValueTypeHash)
	struct FStruct_POI                            POI;                                               // 0x0358(0x0070)(Edit, BlueprintVisible, HasGetValueTypeHash)
	struct FLinearColor                           TitleBorderColor;                                  // 0x03C8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)

public:
	void Construct();
	void ExecuteUbergraph_W_POIMarkerTooltip(int32 EntryPoint);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_POIMarkerTooltip_C">();
	}
	static class UW_POIMarkerTooltip_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_POIMarkerTooltip_C>();
	}
};
static_assert(alignof(UW_POIMarkerTooltip_C) == 0x000008, "Wrong alignment on UW_POIMarkerTooltip_C");
static_assert(sizeof(UW_POIMarkerTooltip_C) == 0x0003D8, "Wrong size on UW_POIMarkerTooltip_C");
static_assert(offsetof(UW_POIMarkerTooltip_C, UberGraphFrame) == 0x0002C0, "Member 'UW_POIMarkerTooltip_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, ContaminationBox) == 0x0002C8, "Member 'UW_POIMarkerTooltip_C::ContaminationBox' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, ContaminationImg) == 0x0002D0, "Member 'UW_POIMarkerTooltip_C::ContaminationImg' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, ContaminationLevelText) == 0x0002D8, "Member 'UW_POIMarkerTooltip_C::ContaminationLevelText' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, Image_92) == 0x0002E0, "Member 'UW_POIMarkerTooltip_C::Image_92' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, LargePOIInfoBox) == 0x0002E8, "Member 'UW_POIMarkerTooltip_C::LargePOIInfoBox' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, POIBossAmount) == 0x0002F0, "Member 'UW_POIMarkerTooltip_C::POIBossAmount' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, POIContainer) == 0x0002F8, "Member 'UW_POIMarkerTooltip_C::POIContainer' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, POIDescription) == 0x000300, "Member 'UW_POIMarkerTooltip_C::POIDescription' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, POIImage) == 0x000308, "Member 'UW_POIMarkerTooltip_C::POIImage' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, POILoot) == 0x000310, "Member 'UW_POIMarkerTooltip_C::POILoot' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, POIName) == 0x000318, "Member 'UW_POIMarkerTooltip_C::POIName' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, POITitleBorder) == 0x000320, "Member 'UW_POIMarkerTooltip_C::POITitleBorder' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, Name_0) == 0x000328, "Member 'UW_POIMarkerTooltip_C::Name_0' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, Description) == 0x000338, "Member 'UW_POIMarkerTooltip_C::Description' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, ItemsToSpawn) == 0x000348, "Member 'UW_POIMarkerTooltip_C::ItemsToSpawn' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, POI) == 0x000358, "Member 'UW_POIMarkerTooltip_C::POI' has a wrong offset!");
static_assert(offsetof(UW_POIMarkerTooltip_C, TitleBorderColor) == 0x0003C8, "Member 'UW_POIMarkerTooltip_C::TitleBorderColor' has a wrong offset!");

}

