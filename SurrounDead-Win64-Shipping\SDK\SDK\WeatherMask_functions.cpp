﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WeatherMask

#include "Basic.hpp"

#include "WeatherMask_classes.hpp"
#include "WeatherMask_parameters.hpp"


namespace SDK
{

// Function WeatherMask.WeatherMask_C.Calculate Masking At Location
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector2D*                       Mask                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Cancel_All_Masks                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeatherMask_C::Calculate_Masking_At_Location(const struct FVector& Location, struct FVector2D* Mask, bool* Cancel_All_Masks)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Calculate Masking At Location");

	Params::WeatherMask_C_Calculate_Masking_At_Location Parms{};

	Parms.Location = std::move(Location);

	UObject::ProcessEvent(Func, &Parms);

	if (Mask != nullptr)
		*Mask = std::move(Parms.Mask);

	if (Cancel_All_Masks != nullptr)
		*Cancel_All_Masks = Parms.Cancel_All_Masks;
}


// Function WeatherMask.WeatherMask_C.Component Generally In Range
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool UWeatherMask_C::Component_Generally_In_Range()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Component Generally In Range");

	Params::WeatherMask_C_Component_Generally_In_Range Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WeatherMask.WeatherMask_C.Configure Collision
// (Protected, BlueprintCallable, BlueprintEvent)

void UWeatherMask_C::Configure_Collision()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Configure Collision");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WeatherMask.WeatherMask_C.Disable
// (Public, BlueprintCallable, BlueprintEvent)

void UWeatherMask_C::Disable()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Disable");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WeatherMask.WeatherMask_C.Editor Update
// (Public, BlueprintCallable, BlueprintEvent)

void UWeatherMask_C::Editor_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Editor Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WeatherMask.WeatherMask_C.Enable
// (Public, BlueprintCallable, BlueprintEvent)

void UWeatherMask_C::Enable()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Enable");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WeatherMask.WeatherMask_C.ExecuteUbergraph_WeatherMask
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeatherMask_C::ExecuteUbergraph_WeatherMask(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "ExecuteUbergraph_WeatherMask");

	Params::WeatherMask_C_ExecuteUbergraph_WeatherMask Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WeatherMask.WeatherMask_C.Force Update
// (Public, BlueprintCallable, BlueprintEvent)

void UWeatherMask_C::Force_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Force Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WeatherMask.WeatherMask_C.Get Brush Location
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector2D*                       Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeatherMask_C::Get_Brush_Location(struct FVector2D* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Get Brush Location");

	Params::WeatherMask_C_Get_Brush_Location Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function WeatherMask.WeatherMask_C.Get Brush Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector2D*                       Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeatherMask_C::Get_Brush_Scale(struct FVector2D* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Get Brush Scale");

	Params::WeatherMask_C_Get_Brush_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function WeatherMask.WeatherMask_C.Get Brush Yaw
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeatherMask_C::Get_Brush_Yaw(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Get Brush Yaw");

	Params::WeatherMask_C_Get_Brush_Yaw Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function WeatherMask.WeatherMask_C.Get Center Location
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector2D*                       Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeatherMask_C::Get_Center_Location(struct FVector2D* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Get Center Location");

	Params::WeatherMask_C_Get_Center_Location Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function WeatherMask.WeatherMask_C.Get Max Distance
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeatherMask_C::Get_Max_Distance(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Get Max Distance");

	Params::WeatherMask_C_Get_Max_Distance Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function WeatherMask.WeatherMask_C.Prepare for Drawing
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Runtime                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class AUltra_Dynamic_Weather_C*         UDW_0                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWeatherMask_C::Prepare_for_Drawing(bool Runtime, class AUltra_Dynamic_Weather_C* UDW_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "Prepare for Drawing");

	Params::WeatherMask_C_Prepare_for_Drawing Parms{};

	Parms.Runtime = Runtime;
	Parms.UDW_0 = UDW_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WeatherMask.WeatherMask_C.ReceiveBeginPlay
// (Event, Public, BlueprintEvent)

void UWeatherMask_C::ReceiveBeginPlay()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "ReceiveBeginPlay");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WeatherMask.WeatherMask_C.ReceiveEndPlay
// (Event, Public, BlueprintEvent)
// Parameters:
// EEndPlayReason                          EndPlayReason                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeatherMask_C::ReceiveEndPlay(EEndPlayReason EndPlayReason)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WeatherMask_C", "ReceiveEndPlay");

	Params::WeatherMask_C_ReceiveEndPlay Parms{};

	Parms.EndPlayReason = EndPlayReason;

	UObject::ProcessEvent(Func, &Parms);
}

}

