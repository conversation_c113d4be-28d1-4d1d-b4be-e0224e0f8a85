﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_NarrativeMenu_QuestJournal

#include "Basic.hpp"

#include "W_NarrativeMenu_QuestJournal_classes.hpp"
#include "W_NarrativeMenu_QuestJournal_parameters.hpp"


namespace SDK
{

// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.BndEvt__BP_QuestJournal_Button_Exit_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UW_NarrativeMenu_QuestJournal_C::BndEvt__BP_QuestJournal_Button_Exit_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "BndEvt__BP_QuestJournal_Button_Exit_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.BndEvt__BP_QuestJournal_UseShared?_K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// bool                                    bIsChecked                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_NarrativeMenu_QuestJournal_C::BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature(bool bIsChecked)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "BndEvt__BP_QuestJournal_UseShared?_K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature");

	Params::W_NarrativeMenu_QuestJournal_C_BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature Parms{};

	Parms.bIsChecked = bIsChecked;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.Clear Quest
// (BlueprintCallable, BlueprintEvent)

void UW_NarrativeMenu_QuestJournal_C::Clear_Quest()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "Clear Quest");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.CreateQuestWidgetButton
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UQuest*                           Quest                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class UBP_QuestJournalQuest_C*          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)

class UBP_QuestJournalQuest_C* UW_NarrativeMenu_QuestJournal_C::CreateQuestWidgetButton(class UQuest* Quest)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "CreateQuestWidgetButton");

	Params::W_NarrativeMenu_QuestJournal_C_CreateQuestWidgetButton Parms{};

	Parms.Quest = Quest;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.ExecuteUbergraph_W_NarrativeMenu_QuestJournal
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_NarrativeMenu_QuestJournal_C::ExecuteUbergraph_W_NarrativeMenu_QuestJournal(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "ExecuteUbergraph_W_NarrativeMenu_QuestJournal");

	Params::W_NarrativeMenu_QuestJournal_C_ExecuteUbergraph_W_NarrativeMenu_QuestJournal Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.Initialize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UNarrativeComponent*              Narrative                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_NarrativeMenu_QuestJournal_C::Initialize(class UNarrativeComponent* Narrative)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "Initialize");

	Params::W_NarrativeMenu_QuestJournal_C_Initialize Parms{};

	Parms.Narrative = Narrative;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.OnTogglePartyQuestsAction
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class FName                             ActionName                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_NarrativeMenu_QuestJournal_C::OnTogglePartyQuestsAction(class FName ActionName)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "OnTogglePartyQuestsAction");

	Params::W_NarrativeMenu_QuestJournal_C_OnTogglePartyQuestsAction Parms{};

	Parms.ActionName = ActionName;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.Party Quests Toggled
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    PartyQuests                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_NarrativeMenu_QuestJournal_C::Party_Quests_Toggled(bool PartyQuests)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "Party Quests Toggled");

	Params::W_NarrativeMenu_QuestJournal_C_Party_Quests_Toggled Parms{};

	Parms.PartyQuests = PartyQuests;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.RegisterActions
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UW_NarrativeMenu_QuestJournal_C::RegisterActions()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "RegisterActions");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.Show Quest
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UQuest*                           Quest                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class UBP_QuestJournalQuest_C*          JournalButton                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_NarrativeMenu_QuestJournal_C::Show_Quest(class UQuest* Quest, class UBP_QuestJournalQuest_C* JournalButton)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "Show Quest");

	Params::W_NarrativeMenu_QuestJournal_C_Show_Quest Parms{};

	Parms.Quest = Quest;
	Parms.JournalButton = JournalButton;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_NarrativeMenu_QuestJournal_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C.BP_GetDesiredFocusTarget
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent, Const)
// Parameters:
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)

class UWidget* UW_NarrativeMenu_QuestJournal_C::BP_GetDesiredFocusTarget() const
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_NarrativeMenu_QuestJournal_C", "BP_GetDesiredFocusTarget");

	Params::W_NarrativeMenu_QuestJournal_C_BP_GetDesiredFocusTarget Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

