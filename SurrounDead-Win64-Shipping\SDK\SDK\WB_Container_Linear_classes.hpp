﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Container_Linear

#include "Basic.hpp"

#include "EMarqueeMask_structs.hpp"
#include "Slate_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "EMarqueeMethod_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"
#include "EProgressMethod_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_Container_Linear.WB_Container_Linear_C
// 0x00F0 (0x03B0 - 0x02C0)
class UWB_Container_Linear_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWB_PB_Linear_Base_C*                   BaseProgressBar;                                   // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWB_Marquee_Linear_C*                   BaseProgressBar_Marquee;                           // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWB_Marquee_Linear_C*                   OnTop_Marquee;                                     // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UOverlay*                               OV_BaseProgressBar_Marquee;                        // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UOverlay*                               OV_OnTop_Marquee;                                  // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UOverlay*                               OV_TargetProgressBar;                              // 0x02F0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UOverlay*                               OV_TargetProgressBar_Marquee;                      // 0x02F8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UProgressBar*                           PB_DefaultMarquee;                                 // 0x0300(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        Switcher;                                          // 0x0308(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWB_PB_Linear_Target_C*                 TargetProgressBar;                                 // 0x0310(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWB_Marquee_Linear_C*                   TargetProgressBar_Marquee;                         // 0x0318(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWB_LinearProgress_Separated_C*         WB_LinearProgress_Separated;                       // 0x0320(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FVector2D                              Size;                                              // 0x0328(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CurrentPercent;                                    // 0x0338(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           TargetFillColor_Positive;                          // 0x0340(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           TargetFillColor_Negative;                          // 0x0350(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        TargetPercent;                                     // 0x0360(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsNegativeFillValue;                              // 0x0368(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_369[0x7];                                      // 0x0369(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        DeltaTime;                                         // 0x0370(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           Timer;                                             // 0x0378(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseTargetPercent;                                 // 0x0380(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsDesignTime;                                     // 0x0381(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressMethod                               ProgressMethod;                                    // 0x0382(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseMarquee;                                       // 0x0383(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMarqueeMethod                                MarqueeMethod;                                     // 0x0384(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseSeparation;                                    // 0x0385(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_386[0x2];                                      // 0x0386(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           FillColor;                                         // 0x0388(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           ProgressChangeColor;                               // 0x0398(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseProgressChangeColor;                           // 0x03A8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsProgressChanging;                               // 0x03A9(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          BarFillType;                                       // 0x03AA(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void AddBackground(const struct FLinearColor& Color, bool bUseBackgroundBlur, double BlurStrength);
	void AddSegments(int32 NumSegments, double Spacing, const struct FVector2D& Size_0, const struct FLinearColor& FillColor_0, EProgressBarFillType FillType, bool bIsFillFromCenter, bool bUseShader);
	void ExecuteUbergraph_WB_Container_Linear(int32 EntryPoint);
	double FindHighestPercentValue();
	class UWB_Marquee_Linear_C* FindMarquee();
	struct FLinearColor FindTargetFillColor();
	double FindTargetPercentValue();
	void FindTargetProgressBarPosition(double Percent);
	double GetAbsoluteTargetPercent();
	void GetPercent(double* Percent);
	void GetPercent_Separated(double* Percent);
	void GetSizeX(double* Current_Size_X);
	void GetSizeY(double* Current_Size_Y);
	void GetTargetPercent(double* Percent);
	void Handle_SetUseSeparation(bool bUseSeparation_0);
	void Hide_AllMarquees();
	void Interp_BasePB_Color(const struct FLinearColor& Target, double InterpSpeed, bool bIsChanging);
	bool IsMarqueeMethod(EMarqueeMethod Method);
	bool IsNegativeFillValue();
	bool IsProgressMethodInterpolate();
	bool IsProgressMethodStatic();
	void PreConstruct(bool IsDesignTime);
	void Set_DefaultMarquee_Visibility(bool bVisible);
	void Set_MarqueeMask(EMarqueeMask MaskType, class UTexture2D* CustomMask);
	void Set_Mirror_OV_BasePB_Marquee(bool Mirror);
	void Set_Mirror_OV_OnTopMarquee(bool Mirror);
	void Set_Mirror_OV_TargetPB_Marquee(bool Mirror);
	void SetBarFillType(EProgressBarFillType FillType, bool bUseShader);
	void SetCustomMarqueeAppearance(bool bIsMarquee, EMarqueeMethod MarqueeMethod_0);
	void SetCustomMarqueeColor(const struct FLinearColor& InColorAndOpacity);
	void SetCustomMarqueeMask(EMarqueeMask MaskType, class UTexture2D* CustomMask, ESlateBrushTileType MaskTiling);
	void SetCustomMarqueePercent_Current();
	void SetCustomMarqueePercent_CurrentAndTarget();
	void SetCustomMarqueePercent_Target(double Percent);
	void SetCustomMarqueeSpeed(double Speed);
	void SetDefaultMarquee_DrawAs(ESlateBrushDrawType Draw_As);
	void SetDefaultMarquee_Image(class UTexture2D* Image);
	void SetDefaultMarquee_ImageSize(const struct FVector2D& Image_Size);
	void SetDefaultMarquee_IsMarquee(bool IsMarquee);
	void SetDefaultMarquee_Tiling(ESlateBrushTileType Tiling);
	void SetDefaultMarquee_Tint(const struct FLinearColor& Color);
	void SetFillColor(const struct FLinearColor& InColor, double GradientPower, ESlateBrushTileType Tiling);
	void SetFillColorMask(class UObject* Value);
	void SetGradientMask(class UTexture2D* GradientTexture);
	void SetMirrorX_OV_Target(bool MirrorX);
	void SetMirrorY_OV_Target(bool MirrorY);
	void SetPercent(double InPercent);
	void SetProgressMethod(EProgressMethod ProgressMethod_0);
	void SetSize(const struct FVector2D& Size_0);
	void SetTargetFillColorNegative(const struct FLinearColor& Color);
	void SetTargetFillColorPositive(const struct FLinearColor& Color);
	void SetTargetPercent(double TargetPercent_0);
	void SetUseAbsoluteFillMethod(bool bAbsoluteFill);
	void SetUseGradient(bool UseGradient);
	void SetUseSeparation(bool bUseSeparation_0);
	void SetUseTargetPercent(bool UseTargetPercent);
	void StartTriggerProgressChangeColor(const struct FLinearColor& ProgressChangeColor_0);
	void StopTriggerProgressChangeColor();
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void UpdateMarqueeFillType();
	void UpdatePercent();
	void UpdateSeparationPercent(double Percent);
	void UpdateStaticPercent();
	void UpdateTargetPercent();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_Container_Linear_C">();
	}
	static class UWB_Container_Linear_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_Container_Linear_C>();
	}
};
static_assert(alignof(UWB_Container_Linear_C) == 0x000008, "Wrong alignment on UWB_Container_Linear_C");
static_assert(sizeof(UWB_Container_Linear_C) == 0x0003B0, "Wrong size on UWB_Container_Linear_C");
static_assert(offsetof(UWB_Container_Linear_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_Container_Linear_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, BaseProgressBar) == 0x0002C8, "Member 'UWB_Container_Linear_C::BaseProgressBar' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, BaseProgressBar_Marquee) == 0x0002D0, "Member 'UWB_Container_Linear_C::BaseProgressBar_Marquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, OnTop_Marquee) == 0x0002D8, "Member 'UWB_Container_Linear_C::OnTop_Marquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, OV_BaseProgressBar_Marquee) == 0x0002E0, "Member 'UWB_Container_Linear_C::OV_BaseProgressBar_Marquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, OV_OnTop_Marquee) == 0x0002E8, "Member 'UWB_Container_Linear_C::OV_OnTop_Marquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, OV_TargetProgressBar) == 0x0002F0, "Member 'UWB_Container_Linear_C::OV_TargetProgressBar' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, OV_TargetProgressBar_Marquee) == 0x0002F8, "Member 'UWB_Container_Linear_C::OV_TargetProgressBar_Marquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, PB_DefaultMarquee) == 0x000300, "Member 'UWB_Container_Linear_C::PB_DefaultMarquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, Switcher) == 0x000308, "Member 'UWB_Container_Linear_C::Switcher' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, TargetProgressBar) == 0x000310, "Member 'UWB_Container_Linear_C::TargetProgressBar' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, TargetProgressBar_Marquee) == 0x000318, "Member 'UWB_Container_Linear_C::TargetProgressBar_Marquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, WB_LinearProgress_Separated) == 0x000320, "Member 'UWB_Container_Linear_C::WB_LinearProgress_Separated' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, Size) == 0x000328, "Member 'UWB_Container_Linear_C::Size' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, CurrentPercent) == 0x000338, "Member 'UWB_Container_Linear_C::CurrentPercent' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, TargetFillColor_Positive) == 0x000340, "Member 'UWB_Container_Linear_C::TargetFillColor_Positive' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, TargetFillColor_Negative) == 0x000350, "Member 'UWB_Container_Linear_C::TargetFillColor_Negative' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, TargetPercent) == 0x000360, "Member 'UWB_Container_Linear_C::TargetPercent' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, bIsNegativeFillValue) == 0x000368, "Member 'UWB_Container_Linear_C::bIsNegativeFillValue' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, DeltaTime) == 0x000370, "Member 'UWB_Container_Linear_C::DeltaTime' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, Timer) == 0x000378, "Member 'UWB_Container_Linear_C::Timer' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, bUseTargetPercent) == 0x000380, "Member 'UWB_Container_Linear_C::bUseTargetPercent' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, bIsDesignTime) == 0x000381, "Member 'UWB_Container_Linear_C::bIsDesignTime' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, ProgressMethod) == 0x000382, "Member 'UWB_Container_Linear_C::ProgressMethod' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, bUseMarquee) == 0x000383, "Member 'UWB_Container_Linear_C::bUseMarquee' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, MarqueeMethod) == 0x000384, "Member 'UWB_Container_Linear_C::MarqueeMethod' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, bUseSeparation) == 0x000385, "Member 'UWB_Container_Linear_C::bUseSeparation' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, FillColor) == 0x000388, "Member 'UWB_Container_Linear_C::FillColor' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, ProgressChangeColor) == 0x000398, "Member 'UWB_Container_Linear_C::ProgressChangeColor' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, bUseProgressChangeColor) == 0x0003A8, "Member 'UWB_Container_Linear_C::bUseProgressChangeColor' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, bIsProgressChanging) == 0x0003A9, "Member 'UWB_Container_Linear_C::bIsProgressChanging' has a wrong offset!");
static_assert(offsetof(UWB_Container_Linear_C, BarFillType) == 0x0003AA, "Member 'UWB_Container_Linear_C::BarFillType' has a wrong offset!");

}

