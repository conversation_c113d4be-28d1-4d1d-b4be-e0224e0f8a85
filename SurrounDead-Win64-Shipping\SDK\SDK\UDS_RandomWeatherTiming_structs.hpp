﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_RandomWeatherTiming

#include "Basic.hpp"


namespace SDK
{

// UserDefinedEnum UDS_RandomWeatherTiming.UDS_RandomWeatherTiming
// NumValues: 0x0005
enum class EUDS_RandomWeatherTiming : uint8
{
	NewEnumerator3                           = 0,
	NewEnumerator0                           = 1,
	NewEnumerator1                           = 2,
	NewEnumerator2                           = 3,
	UDS_MAX                                  = 4,
};

}

