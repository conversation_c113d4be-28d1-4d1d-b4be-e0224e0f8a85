﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDW_WeatherControlledActor_Interface

#include "Basic.hpp"

#include "CoreUObject_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass UDW_WeatherControlledActor_Interface.UDW_WeatherControlledActor_Interface_C
// 0x0000 (0x0000 - 0x0000)
class IUDW_WeatherControlledActor_Interface_C final
{
public:
	void UDW_Editor_Update();
	void UDW_Instant_Update();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"UDW_WeatherControlledActor_Interface_C">();
	}
	static class IUDW_WeatherControlledActor_Interface_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<IUDW_WeatherControlledActor_Interface_C>();
	}

	class UObject* AsUObject()
	{
		return reinterpret_cast<UObject*>(this);
	}
	const class UObject* AsUObject() const
	{
		return reinterpret_cast<const UObject*>(this);
	}
};
static_assert(alignof(IUDW_WeatherControlledActor_Interface_C) == 0x000001, "Wrong alignment on IUDW_WeatherControlledActor_Interface_C");
static_assert(sizeof(IUDW_WeatherControlledActor_Interface_C) == 0x000001, "Wrong size on IUDW_WeatherControlledActor_Interface_C");

}

