﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeHUD

#include "Basic.hpp"

#include "NarrativeCommonUI_classes.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WBP_NarrativeHUD.WBP_NarrativeHUD_C
// 0x0040 (0x0328 - 0x02E8)
class UWBP_NarrativeHUD_C final : public UNarrativeCommonHUD
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02E8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UCommonBoundActionBar*                  CommonBoundActionBar;                              // 0x02F0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWBP_NarrativeWidgetStack_C*            MenuStack;                                         // 0x02F8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCommonActivatableWidgetStack*          PromptStack;                                       // 0x0300(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWBP_NotificationBox_C*                 WBP_NotificationBox;                               // 0x0308(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              OnMenuAdded;                                       // 0x0310(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	int32                                         MaxAllowedNotifications;                           // 0x0320(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WBP_NarrativeHUD(int32 EntryPoint);
	void Open_Quantity_Selector(int32 MinAmount, int32 MaxAmount, const class FText& InstructionText, class UWBP_QuantitySelector_C** Quantity_Selector);
	class UWBP_NarrativeMenu_C* OpenMenu(TSubclassOf<class UWBP_NarrativeMenu_C> ActivatableWidgetClass);
	void ShowNotification(const class FText& NotificationText, const float Duration);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WBP_NarrativeHUD_C">();
	}
	static class UWBP_NarrativeHUD_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWBP_NarrativeHUD_C>();
	}
};
static_assert(alignof(UWBP_NarrativeHUD_C) == 0x000008, "Wrong alignment on UWBP_NarrativeHUD_C");
static_assert(sizeof(UWBP_NarrativeHUD_C) == 0x000328, "Wrong size on UWBP_NarrativeHUD_C");
static_assert(offsetof(UWBP_NarrativeHUD_C, UberGraphFrame) == 0x0002E8, "Member 'UWBP_NarrativeHUD_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUD_C, CommonBoundActionBar) == 0x0002F0, "Member 'UWBP_NarrativeHUD_C::CommonBoundActionBar' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUD_C, MenuStack) == 0x0002F8, "Member 'UWBP_NarrativeHUD_C::MenuStack' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUD_C, PromptStack) == 0x000300, "Member 'UWBP_NarrativeHUD_C::PromptStack' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUD_C, WBP_NotificationBox) == 0x000308, "Member 'UWBP_NarrativeHUD_C::WBP_NotificationBox' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUD_C, OnMenuAdded) == 0x000310, "Member 'UWBP_NarrativeHUD_C::OnMenuAdded' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUD_C, MaxAllowedNotifications) == 0x000320, "Member 'UWBP_NarrativeHUD_C::MaxAllowedNotifications' has a wrong offset!");

}

