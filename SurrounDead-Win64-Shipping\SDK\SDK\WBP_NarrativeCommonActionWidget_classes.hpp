﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeCommonActionWidget

#include "Basic.hpp"

#include "CommonUI_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass WBP_NarrativeCommonActionWidget.WBP_NarrativeCommonActionWidget_C
// 0x0000 (0x0490 - 0x0490)
class UWBP_NarrativeCommonActionWidget_C final : public UCommonActionWidget
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WBP_NarrativeCommonActionWidget_C">();
	}
	static class UWBP_NarrativeCommonActionWidget_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWBP_NarrativeCommonActionWidget_C>();
	}
};
static_assert(alignof(UWBP_NarrativeCommonActionWidget_C) == 0x000010, "Wrong alignment on UWBP_NarrativeCommonActionWidget_C");
static_assert(sizeof(UWBP_NarrativeCommonActionWidget_C) == 0x000490, "Wrong size on UWBP_NarrativeCommonActionWidget_C");

}

