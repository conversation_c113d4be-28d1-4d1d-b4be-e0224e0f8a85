cmake_minimum_required(VERSION 3.16)
project(COD_Controller_Mod)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type to Release by default
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /D_DEBUG")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -D_DEBUG")
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/SurrounDead-Win64-Shipping/SDK)

# Source files
set(SOURCES
    COD_Controller_Main.cpp
    COD_Controller_Mod.hpp
)

# Create the DLL
add_library(COD_Controller_Mod SHARED ${SOURCES})

# Link libraries
target_link_libraries(COD_Controller_Mod
    xinput
    user32
    kernel32
)

# Set output directory
set_target_properties(COD_Controller_Mod PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# Copy to game directory (optional)
if(DEFINED GAME_DIR)
    add_custom_command(TARGET COD_Controller_Mod POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:COD_Controller_Mod> ${GAME_DIR}
        COMMENT "Copying DLL to game directory"
    )
endif()

# Create installer script
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/install_mod.bat.in
    ${CMAKE_BINARY_DIR}/install_mod.bat
    @ONLY
)

# Print build information
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
