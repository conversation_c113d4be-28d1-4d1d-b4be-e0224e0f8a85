﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_PB_Linear_Base

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "Slate_structs.hpp"


namespace SDK::Params
{

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.ExecuteUbergraph_WB_PB_Linear_Base
// 0x0198 (0x0198 - 0x0000)
struct WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x0004(0x0001)(ZeroConstructor, IsPlainOldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                K2Node_CustomEvent_Value;                          // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_FindFillSize_ReturnValue;                 // 0x0010(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_FindFillSize_ReturnValue_1;               // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_UseGradient;                    // 0x0031(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_32[0x6];                                       // 0x0032(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               CallFunc_GetEffectMaterial_ReturnValue;            // 0x0038(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Value_1;                        // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              K2Node_CustomEvent_Size;                           // 0x0050(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_InColor;                        // 0x0060(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_GradientPower;                  // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_CustomEvent_Tiling;                         // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable;                                // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_91[0x7];                                       // 0x0091(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_InPercent;                      // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_1;               // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_2;               // 0x00A1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_1;                     // 0x00A2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A3[0x5];                                       // 0x00A3(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_CustomEvent_GradientTexture;                // 0x00A8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_NewColor;                       // 0x00B0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_InterpSpeed;                    // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_IsChanging;                     // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C9[0x7];                                       // 0x00C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue;         // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_CInterpTo_ReturnValue;                    // 0x00D8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          K2Node_CustomEvent_FillType;                       // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseShader;                     // 0x00F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x00F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_3;               // 0x00F3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x00F4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x00F5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_2;                     // 0x00F6(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F7[0x1];                                       // 0x00F7(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_2;                              // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0100(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0108(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0110(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_1;            // 0x0118(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0120(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_121[0x7];                                      // 0x0121(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0128(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0138(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_SubtractBrightnessValue_ReturnValue;      // 0x0140(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetEffectMaterial_ReturnValue_1;          // 0x0150(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_3;                     // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_159[0x3];                                      // 0x0159(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_GetRenderOpacity_ReturnValue;             // 0x015C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue_1;       // 0x0160(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_1;                           // 0x0168(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FInterpTo_ReturnValue;                    // 0x0170(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0178(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_CInterpTo_InterpSpeed_ImplicitCast;       // 0x017C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_CInterpTo_DeltaTime_ImplicitCast;         // 0x0180(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_184[0x4];                                      // 0x0184(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FInterpTo_Current_ImplicitCast;           // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetRenderOpacity_InOpacity_ImplicitCast;  // 0x0190(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base");
static_assert(sizeof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base) == 0x000198, "Wrong size on WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, EntryPoint) == 0x000000, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_IsClosed_Variable) == 0x000004, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_Value) == 0x000008, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_Value' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_FindFillSize_ReturnValue) == 0x000010, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_FindFillSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_FindFillSize_ReturnValue_1) == 0x000020, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_FindFillSize_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_IsValid_ReturnValue) == 0x000030, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_UseGradient) == 0x000031, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_UseGradient' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_GetEffectMaterial_ReturnValue) == 0x000038, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_GetEffectMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_IsValid_ReturnValue_1) == 0x000040, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_Value_1) == 0x000048, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_Value_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_Size) == 0x000050, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_Size' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_InColor) == 0x000060, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_InColor' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_GradientPower) == 0x000070, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_GradientPower' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_Tiling) == 0x000078, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_Tiling' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_MapRangeClamped_ReturnValue) == 0x000080, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_real_Variable) == 0x000088, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_Has_Been_Initd_Variable) == 0x000090, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_InPercent) == 0x000098, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_InPercent' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_Has_Been_Initd_Variable_1) == 0x0000A0, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_Has_Been_Initd_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_Has_Been_Initd_Variable_2) == 0x0000A1, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_Has_Been_Initd_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_IsClosed_Variable_1) == 0x0000A2, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_IsClosed_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_GradientTexture) == 0x0000A8, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_GradientTexture' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_NewColor) == 0x0000B0, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_NewColor' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_InterpSpeed) == 0x0000C0, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_InterpSpeed' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_IsChanging) == 0x0000C8, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_IsChanging' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_GetWorldDeltaSeconds_ReturnValue) == 0x0000D0, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_GetWorldDeltaSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_CInterpTo_ReturnValue) == 0x0000D8, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_CInterpTo_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_real_Variable_1) == 0x0000E8, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_FillType) == 0x0000F0, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_FillType' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_CustomEvent_bUseShader) == 0x0000F1, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_CustomEvent_bUseShader' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_SwitchEnum_CmpSuccess) == 0x0000F2, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_Has_Been_Initd_Variable_3) == 0x0000F3, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_Has_Been_Initd_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_Variable) == 0x0000F4, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_Event_IsDesignTime) == 0x0000F5, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_IsClosed_Variable_2) == 0x0000F6, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_IsClosed_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_real_Variable_2) == 0x0000F8, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_real_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_BreakVector2D_X) == 0x000100, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_BreakVector2D_Y) == 0x000108, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000110, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_MapRangeClamped_ReturnValue_1) == 0x000118, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_MapRangeClamped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_Variable_1) == 0x000120, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_MakeVector2D_ReturnValue) == 0x000128, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_Select_Default) == 0x000138, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_SubtractBrightnessValue_ReturnValue) == 0x000140, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_SubtractBrightnessValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_GetEffectMaterial_ReturnValue_1) == 0x000150, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_GetEffectMaterial_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, Temp_bool_IsClosed_Variable_3) == 0x000158, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::Temp_bool_IsClosed_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_GetRenderOpacity_ReturnValue) == 0x00015C, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_GetRenderOpacity_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_GetWorldDeltaSeconds_ReturnValue_1) == 0x000160, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_GetWorldDeltaSeconds_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, K2Node_Select_Default_1) == 0x000168, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_FInterpTo_ReturnValue) == 0x000170, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_FInterpTo_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000178, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_CInterpTo_InterpSpeed_ImplicitCast) == 0x00017C, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_CInterpTo_InterpSpeed_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_CInterpTo_DeltaTime_ImplicitCast) == 0x000180, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_CInterpTo_DeltaTime_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_FInterpTo_Current_ImplicitCast) == 0x000188, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_FInterpTo_Current_ImplicitCast' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base, CallFunc_SetRenderOpacity_InOpacity_ImplicitCast) == 0x000190, "Member 'WB_PB_Linear_Base_C_ExecuteUbergraph_WB_PB_Linear_Base::CallFunc_SetRenderOpacity_InOpacity_ImplicitCast' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.FindFillSize
// 0x0068 (0x0068 - 0x0000)
struct WB_PB_Linear_Base_C_FindFillSize final
{
public:
	class UObject*                                Object;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              ReturnValue;                                       // 0x0008(0x0010)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             K2Node_DynamicCast_AsTexture_2D;                   // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_GetTexture2DSizeClamped_ReturnValue;      // 0x0028(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstance*                      K2Node_DynamicCast_AsMaterial_Instance;            // 0x0038(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsValidIndex_ReturnValue;           // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_42[0x6];                                       // 0x0042(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_DynamicCast_AsTexture_2D_1;                 // 0x0048(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51[0x7];                                       // 0x0051(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_GetTexture2DSizeClamped_ReturnValue_1;    // 0x0058(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_FindFillSize) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_FindFillSize");
static_assert(sizeof(WB_PB_Linear_Base_C_FindFillSize) == 0x000068, "Wrong size on WB_PB_Linear_Base_C_FindFillSize");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, Object) == 0x000000, "Member 'WB_PB_Linear_Base_C_FindFillSize::Object' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, ReturnValue) == 0x000008, "Member 'WB_PB_Linear_Base_C_FindFillSize::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, K2Node_DynamicCast_AsTexture_2D) == 0x000018, "Member 'WB_PB_Linear_Base_C_FindFillSize::K2Node_DynamicCast_AsTexture_2D' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, K2Node_DynamicCast_bSuccess) == 0x000020, "Member 'WB_PB_Linear_Base_C_FindFillSize::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, CallFunc_GetTexture2DSizeClamped_ReturnValue) == 0x000028, "Member 'WB_PB_Linear_Base_C_FindFillSize::CallFunc_GetTexture2DSizeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, K2Node_DynamicCast_AsMaterial_Instance) == 0x000038, "Member 'WB_PB_Linear_Base_C_FindFillSize::K2Node_DynamicCast_AsMaterial_Instance' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, K2Node_DynamicCast_bSuccess_1) == 0x000040, "Member 'WB_PB_Linear_Base_C_FindFillSize::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, CallFunc_Array_IsValidIndex_ReturnValue) == 0x000041, "Member 'WB_PB_Linear_Base_C_FindFillSize::CallFunc_Array_IsValidIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, K2Node_DynamicCast_AsTexture_2D_1) == 0x000048, "Member 'WB_PB_Linear_Base_C_FindFillSize::K2Node_DynamicCast_AsTexture_2D_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, K2Node_DynamicCast_bSuccess_2) == 0x000050, "Member 'WB_PB_Linear_Base_C_FindFillSize::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_FindFillSize, CallFunc_GetTexture2DSizeClamped_ReturnValue_1) == 0x000058, "Member 'WB_PB_Linear_Base_C_FindFillSize::CallFunc_GetTexture2DSizeClamped_ReturnValue_1' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.GetPercent
// 0x0010 (0x0010 - 0x0000)
struct WB_PB_Linear_Base_C_GetPercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetPercent_Percent;                       // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_GetPercent) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_GetPercent");
static_assert(sizeof(WB_PB_Linear_Base_C_GetPercent) == 0x000010, "Wrong size on WB_PB_Linear_Base_C_GetPercent");
static_assert(offsetof(WB_PB_Linear_Base_C_GetPercent, Percent) == 0x000000, "Member 'WB_PB_Linear_Base_C_GetPercent::Percent' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_GetPercent, CallFunc_GetPercent_Percent) == 0x000008, "Member 'WB_PB_Linear_Base_C_GetPercent::CallFunc_GetPercent_Percent' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.Mirror_DesignTimeGradient
// 0x0078 (0x0078 - 0x0000)
struct WB_PB_Linear_Base_C_Mirror_DesignTimeGradient final
{
public:
	bool                                          Mirror;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               CallFunc_GetEffectMaterial_ReturnValue;            // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_2;                              // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_3;                              // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_1;                           // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0060(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0070(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_Mirror_DesignTimeGradient");
static_assert(sizeof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient) == 0x000078, "Wrong size on WB_PB_Linear_Base_C_Mirror_DesignTimeGradient");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, Mirror) == 0x000000, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::Mirror' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, Temp_real_Variable) == 0x000008, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, Temp_bool_Variable) == 0x000010, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, CallFunc_GetEffectMaterial_ReturnValue) == 0x000018, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::CallFunc_GetEffectMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, Temp_real_Variable_1) == 0x000020, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, Temp_bool_Variable_1) == 0x000028, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, Temp_real_Variable_2) == 0x000030, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::Temp_real_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, K2Node_Select_Default) == 0x000038, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, Temp_real_Variable_3) == 0x000040, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::Temp_real_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, K2Node_Select_Default_1) == 0x000048, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, CallFunc_BreakVector2D_X) == 0x000050, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, CallFunc_BreakVector2D_Y) == 0x000058, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, CallFunc_MakeVector2D_ReturnValue) == 0x000060, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Mirror_DesignTimeGradient, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000070, "Member 'WB_PB_Linear_Base_C_Mirror_DesignTimeGradient::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WB_PB_Linear_Base_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_PreConstruct) == 0x000001, "Wrong alignment on WB_PB_Linear_Base_C_PreConstruct");
static_assert(sizeof(WB_PB_Linear_Base_C_PreConstruct) == 0x000001, "Wrong size on WB_PB_Linear_Base_C_PreConstruct");
static_assert(offsetof(WB_PB_Linear_Base_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WB_PB_Linear_Base_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.Rotate_DesignRetainerGradient
// 0x0020 (0x0020 - 0x0000)
struct WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient final
{
public:
	double                                        Rotation;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetEffectMaterial_ReturnValue;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient");
static_assert(sizeof(WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient) == 0x000020, "Wrong size on WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient");
static_assert(offsetof(WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient, Rotation) == 0x000000, "Member 'WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient::Rotation' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000008, "Member 'WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient, CallFunc_GetEffectMaterial_ReturnValue) == 0x000010, "Member 'WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient::CallFunc_GetEffectMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000018, "Member 'WB_PB_Linear_Base_C_Rotate_DesignRetainerGradient::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.Rotate_DesignTimeGradient
// 0x0010 (0x0010 - 0x0000)
struct WB_PB_Linear_Base_C_Rotate_DesignTimeGradient final
{
public:
	double                                        Angle;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetRenderTransformAngle_Angle_ImplicitCast; // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_Rotate_DesignTimeGradient) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_Rotate_DesignTimeGradient");
static_assert(sizeof(WB_PB_Linear_Base_C_Rotate_DesignTimeGradient) == 0x000010, "Wrong size on WB_PB_Linear_Base_C_Rotate_DesignTimeGradient");
static_assert(offsetof(WB_PB_Linear_Base_C_Rotate_DesignTimeGradient, Angle) == 0x000000, "Member 'WB_PB_Linear_Base_C_Rotate_DesignTimeGradient::Angle' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_Rotate_DesignTimeGradient, CallFunc_SetRenderTransformAngle_Angle_ImplicitCast) == 0x000008, "Member 'WB_PB_Linear_Base_C_Rotate_DesignTimeGradient::CallFunc_SetRenderTransformAngle_Angle_ImplicitCast' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.SetBarFillType
// 0x0002 (0x0002 - 0x0000)
struct WB_PB_Linear_Base_C_SetBarFillType final
{
public:
	EProgressBarFillType                          FillType;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseShader;                                        // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_SetBarFillType) == 0x000001, "Wrong alignment on WB_PB_Linear_Base_C_SetBarFillType");
static_assert(sizeof(WB_PB_Linear_Base_C_SetBarFillType) == 0x000002, "Wrong size on WB_PB_Linear_Base_C_SetBarFillType");
static_assert(offsetof(WB_PB_Linear_Base_C_SetBarFillType, FillType) == 0x000000, "Member 'WB_PB_Linear_Base_C_SetBarFillType::FillType' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_SetBarFillType, bUseShader) == 0x000001, "Member 'WB_PB_Linear_Base_C_SetBarFillType::bUseShader' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.SetFillColor
// 0x0020 (0x0020 - 0x0000)
struct WB_PB_Linear_Base_C_SetFillColor final
{
public:
	struct FLinearColor                           InColor;                                           // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        GradientPower;                                     // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           Tiling;                                            // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_SetFillColor) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_SetFillColor");
static_assert(sizeof(WB_PB_Linear_Base_C_SetFillColor) == 0x000020, "Wrong size on WB_PB_Linear_Base_C_SetFillColor");
static_assert(offsetof(WB_PB_Linear_Base_C_SetFillColor, InColor) == 0x000000, "Member 'WB_PB_Linear_Base_C_SetFillColor::InColor' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_SetFillColor, GradientPower) == 0x000010, "Member 'WB_PB_Linear_Base_C_SetFillColor::GradientPower' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_SetFillColor, Tiling) == 0x000018, "Member 'WB_PB_Linear_Base_C_SetFillColor::Tiling' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.SetFillColorMask
// 0x0008 (0x0008 - 0x0000)
struct WB_PB_Linear_Base_C_SetFillColorMask final
{
public:
	class UObject*                                Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_SetFillColorMask) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_SetFillColorMask");
static_assert(sizeof(WB_PB_Linear_Base_C_SetFillColorMask) == 0x000008, "Wrong size on WB_PB_Linear_Base_C_SetFillColorMask");
static_assert(offsetof(WB_PB_Linear_Base_C_SetFillColorMask, Value) == 0x000000, "Member 'WB_PB_Linear_Base_C_SetFillColorMask::Value' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.SetGradientMask
// 0x0008 (0x0008 - 0x0000)
struct WB_PB_Linear_Base_C_SetGradientMask final
{
public:
	class UTexture2D*                             GradientTexture;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_SetGradientMask) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_SetGradientMask");
static_assert(sizeof(WB_PB_Linear_Base_C_SetGradientMask) == 0x000008, "Wrong size on WB_PB_Linear_Base_C_SetGradientMask");
static_assert(offsetof(WB_PB_Linear_Base_C_SetGradientMask, GradientTexture) == 0x000000, "Member 'WB_PB_Linear_Base_C_SetGradientMask::GradientTexture' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.SetGradientPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_PB_Linear_Base_C_SetGradientPercent final
{
public:
	double                                        Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_SetGradientPercent) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_SetGradientPercent");
static_assert(sizeof(WB_PB_Linear_Base_C_SetGradientPercent) == 0x000008, "Wrong size on WB_PB_Linear_Base_C_SetGradientPercent");
static_assert(offsetof(WB_PB_Linear_Base_C_SetGradientPercent, Value) == 0x000000, "Member 'WB_PB_Linear_Base_C_SetGradientPercent::Value' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.SetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_PB_Linear_Base_C_SetPercent final
{
public:
	double                                        InPercent;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_SetPercent) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_SetPercent");
static_assert(sizeof(WB_PB_Linear_Base_C_SetPercent) == 0x000008, "Wrong size on WB_PB_Linear_Base_C_SetPercent");
static_assert(offsetof(WB_PB_Linear_Base_C_SetPercent, InPercent) == 0x000000, "Member 'WB_PB_Linear_Base_C_SetPercent::InPercent' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.SetSize
// 0x0010 (0x0010 - 0x0000)
struct WB_PB_Linear_Base_C_SetSize final
{
public:
	struct FVector2D                              Size_0;                                            // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_SetSize) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_SetSize");
static_assert(sizeof(WB_PB_Linear_Base_C_SetSize) == 0x000010, "Wrong size on WB_PB_Linear_Base_C_SetSize");
static_assert(offsetof(WB_PB_Linear_Base_C_SetSize, Size_0) == 0x000000, "Member 'WB_PB_Linear_Base_C_SetSize::Size_0' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.SetUseGradient
// 0x0001 (0x0001 - 0x0000)
struct WB_PB_Linear_Base_C_SetUseGradient final
{
public:
	bool                                          UseGradient;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_SetUseGradient) == 0x000001, "Wrong alignment on WB_PB_Linear_Base_C_SetUseGradient");
static_assert(sizeof(WB_PB_Linear_Base_C_SetUseGradient) == 0x000001, "Wrong size on WB_PB_Linear_Base_C_SetUseGradient");
static_assert(offsetof(WB_PB_Linear_Base_C_SetUseGradient, UseGradient) == 0x000000, "Member 'WB_PB_Linear_Base_C_SetUseGradient::UseGradient' has a wrong offset!");

// Function WB_PB_Linear_Base.WB_PB_Linear_Base_C.UpdateProgressChangeColor
// 0x0020 (0x0020 - 0x0000)
struct WB_PB_Linear_Base_C_UpdateProgressChangeColor final
{
public:
	struct FLinearColor                           NewColor;                                          // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        InterpSpeed;                                       // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsChanging;                                        // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Linear_Base_C_UpdateProgressChangeColor) == 0x000008, "Wrong alignment on WB_PB_Linear_Base_C_UpdateProgressChangeColor");
static_assert(sizeof(WB_PB_Linear_Base_C_UpdateProgressChangeColor) == 0x000020, "Wrong size on WB_PB_Linear_Base_C_UpdateProgressChangeColor");
static_assert(offsetof(WB_PB_Linear_Base_C_UpdateProgressChangeColor, NewColor) == 0x000000, "Member 'WB_PB_Linear_Base_C_UpdateProgressChangeColor::NewColor' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_UpdateProgressChangeColor, InterpSpeed) == 0x000010, "Member 'WB_PB_Linear_Base_C_UpdateProgressChangeColor::InterpSpeed' has a wrong offset!");
static_assert(offsetof(WB_PB_Linear_Base_C_UpdateProgressChangeColor, IsChanging) == 0x000018, "Member 'WB_PB_Linear_Base_C_UpdateProgressChangeColor::IsChanging' has a wrong offset!");

}

