﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: TypedElementFramework

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"


namespace SDK
{

// ScriptStruct TypedElementFramework.ScriptTypedElementHandle
// 0x0008 (0x0008 - 0x0000)
struct alignas(0x08) FScriptTypedElementHandle final
{
public:
	uint8                                         Pad_0[0x8];                                        // 0x0000(0x0008)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FScriptTypedElementHandle) == 0x000008, "Wrong alignment on FScriptTypedElementHandle");
static_assert(sizeof(FScriptTypedElementHandle) == 0x000008, "Wrong size on FScriptTypedElementHandle");

// ScriptStruct TypedElementFramework.TypedElementDataStorageColumn
// 0x0000 (0x0000 - 0x0000)
#pragma pack(push, 0x1)
struct alignas(0x01) FTypedElementDataStorageColumn
{
};
#pragma pack(pop)
static_assert(alignof(FTypedElementDataStorageColumn) == 0x000001, "Wrong alignment on FTypedElementDataStorageColumn");
static_assert(sizeof(FTypedElementDataStorageColumn) == 0x000001, "Wrong size on FTypedElementDataStorageColumn");

// ScriptStruct TypedElementFramework.TypedElementUObjectColumn
// 0x0008 (0x0008 - 0x0000)
struct alignas(0x04) FTypedElementUObjectColumn final : public FTypedElementDataStorageColumn
{
public:
	uint8                                         Pad_0[0x8];                                        // 0x0000(0x0008)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementUObjectColumn) == 0x000004, "Wrong alignment on FTypedElementUObjectColumn");
static_assert(sizeof(FTypedElementUObjectColumn) == 0x000008, "Wrong size on FTypedElementUObjectColumn");

// ScriptStruct TypedElementFramework.TypedElementExternalObjectColumn
// 0x0008 (0x0008 - 0x0000)
struct alignas(0x08) FTypedElementExternalObjectColumn final : public FTypedElementDataStorageColumn
{
public:
	uint8                                         Pad_0[0x8];                                        // 0x0000(0x0008)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementExternalObjectColumn) == 0x000008, "Wrong alignment on FTypedElementExternalObjectColumn");
static_assert(sizeof(FTypedElementExternalObjectColumn) == 0x000008, "Wrong size on FTypedElementExternalObjectColumn");

// ScriptStruct TypedElementFramework.TypedElementDataStorageTag
// 0x0001 (0x0001 - 0x0000)
struct FTypedElementDataStorageTag
{
public:
	uint8                                         Pad_0[0x1];                                        // 0x0000(0x0001)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementDataStorageTag) == 0x000001, "Wrong alignment on FTypedElementDataStorageTag");
static_assert(sizeof(FTypedElementDataStorageTag) == 0x000001, "Wrong size on FTypedElementDataStorageTag");

// ScriptStruct TypedElementFramework.TypedElementWidgetConstructor
// 0x0010 (0x0010 - 0x0000)
struct alignas(0x08) FTypedElementWidgetConstructor final
{
public:
	uint8                                         Pad_0[0x10];                                       // 0x0000(0x0010)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementWidgetConstructor) == 0x000008, "Wrong alignment on FTypedElementWidgetConstructor");
static_assert(sizeof(FTypedElementWidgetConstructor) == 0x000010, "Wrong size on FTypedElementWidgetConstructor");

// ScriptStruct TypedElementFramework.TypedElementLabelColumn
// 0x0010 (0x0010 - 0x0000)
struct FTypedElementLabelColumn final : public FTypedElementDataStorageColumn
{
public:
	class FString                                 Label;                                             // 0x0000(0x0010)(ZeroConstructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementLabelColumn) == 0x000008, "Wrong alignment on FTypedElementLabelColumn");
static_assert(sizeof(FTypedElementLabelColumn) == 0x000010, "Wrong size on FTypedElementLabelColumn");
static_assert(offsetof(FTypedElementLabelColumn, Label) == 0x000000, "Member 'FTypedElementLabelColumn::Label' has a wrong offset!");

// ScriptStruct TypedElementFramework.TypedElementLabelHashColumn
// 0x0008 (0x0008 - 0x0000)
struct FTypedElementLabelHashColumn final : public FTypedElementDataStorageColumn
{
public:
	uint64                                        LabelHash;                                         // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementLabelHashColumn) == 0x000008, "Wrong alignment on FTypedElementLabelHashColumn");
static_assert(sizeof(FTypedElementLabelHashColumn) == 0x000008, "Wrong size on FTypedElementLabelHashColumn");
static_assert(offsetof(FTypedElementLabelHashColumn, LabelHash) == 0x000000, "Member 'FTypedElementLabelHashColumn::LabelHash' has a wrong offset!");

// ScriptStruct TypedElementFramework.ScriptTypedElementListProxy
// 0x0010 (0x0010 - 0x0000)
struct alignas(0x08) FScriptTypedElementListProxy final
{
public:
	uint8                                         Pad_0[0x10];                                       // 0x0000(0x0010)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FScriptTypedElementListProxy) == 0x000008, "Wrong alignment on FScriptTypedElementListProxy");
static_assert(sizeof(FScriptTypedElementListProxy) == 0x000010, "Wrong size on FScriptTypedElementListProxy");

// ScriptStruct TypedElementFramework.TypedElementSyncBackToWorldTag
// 0x0000 (0x0001 - 0x0001)
struct FTypedElementSyncBackToWorldTag final : public FTypedElementDataStorageTag
{
};
static_assert(alignof(FTypedElementSyncBackToWorldTag) == 0x000001, "Wrong alignment on FTypedElementSyncBackToWorldTag");
static_assert(sizeof(FTypedElementSyncBackToWorldTag) == 0x000001, "Wrong size on FTypedElementSyncBackToWorldTag");

// ScriptStruct TypedElementFramework.TypedElementSyncFromWorldTag
// 0x0000 (0x0001 - 0x0001)
struct FTypedElementSyncFromWorldTag final : public FTypedElementDataStorageTag
{
};
static_assert(alignof(FTypedElementSyncFromWorldTag) == 0x000001, "Wrong alignment on FTypedElementSyncFromWorldTag");
static_assert(sizeof(FTypedElementSyncFromWorldTag) == 0x000001, "Wrong size on FTypedElementSyncFromWorldTag");

// ScriptStruct TypedElementFramework.TypedElementRowReferenceColumn
// 0x0008 (0x0008 - 0x0000)
struct alignas(0x08) FTypedElementRowReferenceColumn final : public FTypedElementDataStorageColumn
{
public:
	uint8                                         Pad_0[0x8];                                        // 0x0000(0x0008)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementRowReferenceColumn) == 0x000008, "Wrong alignment on FTypedElementRowReferenceColumn");
static_assert(sizeof(FTypedElementRowReferenceColumn) == 0x000008, "Wrong size on FTypedElementRowReferenceColumn");

// ScriptStruct TypedElementFramework.TypedElementPackagePathColumn
// 0x0010 (0x0010 - 0x0000)
struct FTypedElementPackagePathColumn final : public FTypedElementDataStorageColumn
{
public:
	class FString                                 Path;                                              // 0x0000(0x0010)(ZeroConstructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementPackagePathColumn) == 0x000008, "Wrong alignment on FTypedElementPackagePathColumn");
static_assert(sizeof(FTypedElementPackagePathColumn) == 0x000010, "Wrong size on FTypedElementPackagePathColumn");
static_assert(offsetof(FTypedElementPackagePathColumn, Path) == 0x000000, "Member 'FTypedElementPackagePathColumn::Path' has a wrong offset!");

// ScriptStruct TypedElementFramework.TypedElementPackageLoadedPathColumn
// 0x000C (0x000C - 0x0000)
struct alignas(0x04) FTypedElementPackageLoadedPathColumn final : public FTypedElementDataStorageColumn
{
public:
	uint8                                         Pad_0[0xC];                                        // 0x0000(0x000C)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementPackageLoadedPathColumn) == 0x000004, "Wrong alignment on FTypedElementPackageLoadedPathColumn");
static_assert(sizeof(FTypedElementPackageLoadedPathColumn) == 0x00000C, "Wrong size on FTypedElementPackageLoadedPathColumn");

// ScriptStruct TypedElementFramework.TypedElementSelectionColumn
// 0x0001 (0x0001 - 0x0000)
struct FTypedElementSelectionColumn final : public FTypedElementDataStorageColumn
{
public:
	uint8                                         Pad_0[0x1];                                        // 0x0000(0x0001)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementSelectionColumn) == 0x000001, "Wrong alignment on FTypedElementSelectionColumn");
static_assert(sizeof(FTypedElementSelectionColumn) == 0x000001, "Wrong size on FTypedElementSelectionColumn");

// ScriptStruct TypedElementFramework.TypedElementSlateWidgetReferenceColumn
// 0x0010 (0x0010 - 0x0000)
struct alignas(0x08) FTypedElementSlateWidgetReferenceColumn final : public FTypedElementDataStorageColumn
{
public:
	uint8                                         Pad_0[0x10];                                       // 0x0000(0x0010)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementSlateWidgetReferenceColumn) == 0x000008, "Wrong alignment on FTypedElementSlateWidgetReferenceColumn");
static_assert(sizeof(FTypedElementSlateWidgetReferenceColumn) == 0x000010, "Wrong size on FTypedElementSlateWidgetReferenceColumn");

// ScriptStruct TypedElementFramework.TypedElementSlateWidgetReferenceDeletesRowTag
// 0x0000 (0x0001 - 0x0001)
struct FTypedElementSlateWidgetReferenceDeletesRowTag final : public FTypedElementDataStorageTag
{
};
static_assert(alignof(FTypedElementSlateWidgetReferenceDeletesRowTag) == 0x000001, "Wrong alignment on FTypedElementSlateWidgetReferenceDeletesRowTag");
static_assert(sizeof(FTypedElementSlateWidgetReferenceDeletesRowTag) == 0x000001, "Wrong size on FTypedElementSlateWidgetReferenceDeletesRowTag");

// ScriptStruct TypedElementFramework.TypedElementLocalTransformColumn
// 0x0060 (0x0060 - 0x0000)
struct FTypedElementLocalTransformColumn final : public FTypedElementDataStorageColumn
{
public:
	struct FTransform                             Transform;                                         // 0x0000(0x0060)(IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementLocalTransformColumn) == 0x000010, "Wrong alignment on FTypedElementLocalTransformColumn");
static_assert(sizeof(FTypedElementLocalTransformColumn) == 0x000060, "Wrong size on FTypedElementLocalTransformColumn");
static_assert(offsetof(FTypedElementLocalTransformColumn, Transform) == 0x000000, "Member 'FTypedElementLocalTransformColumn::Transform' has a wrong offset!");

// ScriptStruct TypedElementFramework.TypedElementClassTypeInfoColumn
// 0x0008 (0x0008 - 0x0000)
struct alignas(0x04) FTypedElementClassTypeInfoColumn final : public FTypedElementDataStorageColumn
{
public:
	uint8                                         Pad_0[0x8];                                        // 0x0000(0x0008)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementClassTypeInfoColumn) == 0x000004, "Wrong alignment on FTypedElementClassTypeInfoColumn");
static_assert(sizeof(FTypedElementClassTypeInfoColumn) == 0x000008, "Wrong size on FTypedElementClassTypeInfoColumn");

// ScriptStruct TypedElementFramework.TypedElementScriptStructTypeInfoColumn
// 0x0008 (0x0008 - 0x0000)
struct alignas(0x04) FTypedElementScriptStructTypeInfoColumn final : public FTypedElementDataStorageColumn
{
public:
	uint8                                         Pad_0[0x8];                                        // 0x0000(0x0008)(Fixing Struct Size After Last Property [ Dumper-7 ])
};
static_assert(alignof(FTypedElementScriptStructTypeInfoColumn) == 0x000004, "Wrong alignment on FTypedElementScriptStructTypeInfoColumn");
static_assert(sizeof(FTypedElementScriptStructTypeInfoColumn) == 0x000008, "Wrong size on FTypedElementScriptStructTypeInfoColumn");

// ScriptStruct TypedElementFramework.TypedElementU32IntValueCacheColumn
// 0x0004 (0x0004 - 0x0000)
struct FTypedElementU32IntValueCacheColumn final : public FTypedElementDataStorageColumn
{
public:
	uint32                                        Value;                                             // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementU32IntValueCacheColumn) == 0x000004, "Wrong alignment on FTypedElementU32IntValueCacheColumn");
static_assert(sizeof(FTypedElementU32IntValueCacheColumn) == 0x000004, "Wrong size on FTypedElementU32IntValueCacheColumn");
static_assert(offsetof(FTypedElementU32IntValueCacheColumn, Value) == 0x000000, "Member 'FTypedElementU32IntValueCacheColumn::Value' has a wrong offset!");

// ScriptStruct TypedElementFramework.TypedElementI32IntValueCacheColumn
// 0x0004 (0x0004 - 0x0000)
struct FTypedElementI32IntValueCacheColumn final : public FTypedElementDataStorageColumn
{
public:
	int32                                         Value;                                             // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementI32IntValueCacheColumn) == 0x000004, "Wrong alignment on FTypedElementI32IntValueCacheColumn");
static_assert(sizeof(FTypedElementI32IntValueCacheColumn) == 0x000004, "Wrong size on FTypedElementI32IntValueCacheColumn");
static_assert(offsetof(FTypedElementI32IntValueCacheColumn, Value) == 0x000000, "Member 'FTypedElementI32IntValueCacheColumn::Value' has a wrong offset!");

// ScriptStruct TypedElementFramework.TypedElementU64IntValueCacheColumn
// 0x0008 (0x0008 - 0x0000)
struct FTypedElementU64IntValueCacheColumn final : public FTypedElementDataStorageColumn
{
public:
	uint64                                        Value;                                             // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementU64IntValueCacheColumn) == 0x000008, "Wrong alignment on FTypedElementU64IntValueCacheColumn");
static_assert(sizeof(FTypedElementU64IntValueCacheColumn) == 0x000008, "Wrong size on FTypedElementU64IntValueCacheColumn");
static_assert(offsetof(FTypedElementU64IntValueCacheColumn, Value) == 0x000000, "Member 'FTypedElementU64IntValueCacheColumn::Value' has a wrong offset!");

// ScriptStruct TypedElementFramework.TypedElementI64IntValueCacheColumn
// 0x0008 (0x0008 - 0x0000)
struct FTypedElementI64IntValueCacheColumn final : public FTypedElementDataStorageColumn
{
public:
	int64                                         Value;                                             // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementI64IntValueCacheColumn) == 0x000008, "Wrong alignment on FTypedElementI64IntValueCacheColumn");
static_assert(sizeof(FTypedElementI64IntValueCacheColumn) == 0x000008, "Wrong size on FTypedElementI64IntValueCacheColumn");
static_assert(offsetof(FTypedElementI64IntValueCacheColumn, Value) == 0x000000, "Member 'FTypedElementI64IntValueCacheColumn::Value' has a wrong offset!");

// ScriptStruct TypedElementFramework.TypedElementFloatValueCacheColumn
// 0x0004 (0x0004 - 0x0000)
struct FTypedElementFloatValueCacheColumn final : public FTypedElementDataStorageColumn
{
public:
	float                                         Value;                                             // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementFloatValueCacheColumn) == 0x000004, "Wrong alignment on FTypedElementFloatValueCacheColumn");
static_assert(sizeof(FTypedElementFloatValueCacheColumn) == 0x000004, "Wrong size on FTypedElementFloatValueCacheColumn");
static_assert(offsetof(FTypedElementFloatValueCacheColumn, Value) == 0x000000, "Member 'FTypedElementFloatValueCacheColumn::Value' has a wrong offset!");

// ScriptStruct TypedElementFramework.TypedElementViewportColorColumn
// 0x0001 (0x0001 - 0x0000)
struct FTypedElementViewportColorColumn final : public FTypedElementDataStorageColumn
{
public:
	uint8                                         SelectionOutlineColorIndex;                        // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash, NativeAccessSpecifierPublic)
};
static_assert(alignof(FTypedElementViewportColorColumn) == 0x000001, "Wrong alignment on FTypedElementViewportColorColumn");
static_assert(sizeof(FTypedElementViewportColorColumn) == 0x000001, "Wrong size on FTypedElementViewportColorColumn");
static_assert(offsetof(FTypedElementViewportColorColumn, SelectionOutlineColorIndex) == 0x000000, "Member 'FTypedElementViewportColorColumn::SelectionOutlineColorIndex' has a wrong offset!");

}

