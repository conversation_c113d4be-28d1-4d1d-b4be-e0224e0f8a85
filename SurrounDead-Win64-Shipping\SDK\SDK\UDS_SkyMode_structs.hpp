﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_SkyMode

#include "Basic.hpp"


namespace SDK
{

// UserDefinedEnum UDS_SkyMode.UDS_SkyMode
// NumValues: 0x0007
enum class EUDS_SkyMode : uint8
{
	NewEnumerator0                           = 0,
	NewEnumerator6                           = 1,
	NewEnumerator1                           = 2,
	NewEnumerator4                           = 3,
	NewEnumerator8                           = 4,
	NewEnumerator10                          = 5,
	UDS_MAX                                  = 6,
};

}

