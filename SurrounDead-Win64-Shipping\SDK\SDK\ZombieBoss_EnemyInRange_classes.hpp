﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: ZombieBoss_EnemyInRange

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AIModule_structs.hpp"
#include "AIModule_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass ZombieBoss_EnemyInRange.ZombieBoss_EnemyInRange_C
// 0x0058 (0x00F0 - 0x0098)
class UZombieBoss_EnemyInRange_C final : public UBTService_BlueprintBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0098(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	struct FBlackboardKeySelector                 InRange_;                                          // 0x00A0(0x0028)(Edit, BlueprintVisible)
	struct FBlackboardKeySelector                 AttackTarget;                                      // 0x00C8(0x0028)(Edit, BlueprintVisible)

public:
	void ExecuteUbergraph_ZombieBoss_EnemyInRange(int32 EntryPoint);
	void ReceiveActivationAI(class AAIController* OwnerController, class APawn* ControlledPawn);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"ZombieBoss_EnemyInRange_C">();
	}
	static class UZombieBoss_EnemyInRange_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UZombieBoss_EnemyInRange_C>();
	}
};
static_assert(alignof(UZombieBoss_EnemyInRange_C) == 0x000008, "Wrong alignment on UZombieBoss_EnemyInRange_C");
static_assert(sizeof(UZombieBoss_EnemyInRange_C) == 0x0000F0, "Wrong size on UZombieBoss_EnemyInRange_C");
static_assert(offsetof(UZombieBoss_EnemyInRange_C, UberGraphFrame) == 0x000098, "Member 'UZombieBoss_EnemyInRange_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UZombieBoss_EnemyInRange_C, InRange_) == 0x0000A0, "Member 'UZombieBoss_EnemyInRange_C::InRange_' has a wrong offset!");
static_assert(offsetof(UZombieBoss_EnemyInRange_C, AttackTarget) == 0x0000C8, "Member 'UZombieBoss_EnemyInRange_C::AttackTarget' has a wrong offset!");

}

