﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_PickupUtilityTruck

#include "Basic.hpp"


namespace SDK::Params
{

// Function Vehicle_PickupUtilityTruck.Vehicle_PickupUtilityTruck_C.UserConstructionScript
// 0x0010 (0x0010 - 0x0000)
struct Vehicle_PickupUtilityTruck_C_UserConstructionScript final
{
public:
	TArray<class USpotLightComponent*>            K2Node_MakeArray_Array;                            // 0x0000(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(Vehicle_PickupUtilityTruck_C_UserConstructionScript) == 0x000008, "Wrong alignment on Vehicle_PickupUtilityTruck_C_UserConstructionScript");
static_assert(sizeof(Vehicle_PickupUtilityTruck_C_UserConstructionScript) == 0x000010, "Wrong size on Vehicle_PickupUtilityTruck_C_UserConstructionScript");
static_assert(offsetof(Vehicle_PickupUtilityTruck_C_UserConstructionScript, K2Node_MakeArray_Array) == 0x000000, "Member 'Vehicle_PickupUtilityTruck_C_UserConstructionScript::K2Node_MakeArray_Array' has a wrong offset!");

}

