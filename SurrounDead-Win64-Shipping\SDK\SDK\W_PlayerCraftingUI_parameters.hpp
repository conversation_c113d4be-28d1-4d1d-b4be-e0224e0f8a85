﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_PlayerCraftingUI

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "S_JigCrafting_structs.hpp"
#include "RepItemInfo_structs.hpp"
#include "InputCore_structs.hpp"


namespace SDK::Params
{

// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.AddRequiredItems
// 0x0108 (0x0108 - 0x0000)
struct W_PlayerCraftingUI_C_AddRequiredItems final
{
public:
	bool                                          CanCraft_;                                         // 0x0000(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Map_Find_Value;                           // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x3];                                       // 0x0021(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRepItemInfo                           CallFunc_Array_Get_Item;                           // 0x0028(0x0078)(HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A1[0x7];                                       // 0x00A1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSI_Slot_C*>                    CallFunc_CheckCraftingIngredients_ItemFound_Element; // 0x00A8(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UJSI_Slot_C*                            CallFunc_Array_Get_Item_1;                         // 0x00B8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGuid                                  CallFunc_GetUniqueID_UniqueServerID;               // 0x00C4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x00D4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D5[0x3];                                       // 0x00D5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsValidIndex_ReturnValue;           // 0x00DC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_DD[0x3];                                       // 0x00DD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Multiply_IntInt_ReturnValue;              // 0x00E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Added;                // 0x00E4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E5[0x3];                                       // 0x00E5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_AddNewInventoryItem_SlotIndex;            // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_EC[0x4];                                       // 0x00EC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UJigsawItem_DataAsset_C*                CallFunc_AddNewInventoryItem_ItemInfo;             // 0x00F0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            CallFunc_AddNewInventoryItem_SlotItemRef;          // 0x00F8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Stacked_;             // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_PlayerCraftingUI_C_AddRequiredItems) == 0x000008, "Wrong alignment on W_PlayerCraftingUI_C_AddRequiredItems");
static_assert(sizeof(W_PlayerCraftingUI_C_AddRequiredItems) == 0x000108, "Wrong size on W_PlayerCraftingUI_C_AddRequiredItems");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CanCraft_) == 0x000000, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CanCraft_' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, Temp_int_Array_Index_Variable) == 0x000004, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, Temp_int_Loop_Counter_Variable) == 0x000008, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Add_IntInt_ReturnValue) == 0x00000C, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, Temp_int_Array_Index_Variable_1) == 0x000010, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, Temp_int_Loop_Counter_Variable_1) == 0x000014, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Add_IntInt_ReturnValue_1) == 0x000018, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Map_Find_Value) == 0x00001C, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Map_Find_ReturnValue) == 0x000020, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Array_Length_ReturnValue) == 0x000024, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Array_Get_Item) == 0x000028, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Less_IntInt_ReturnValue) == 0x0000A0, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_CheckCraftingIngredients_ItemFound_Element) == 0x0000A8, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_CheckCraftingIngredients_ItemFound_Element' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Array_Get_Item_1) == 0x0000B8, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Array_Length_ReturnValue_1) == 0x0000C0, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_GetUniqueID_UniqueServerID) == 0x0000C4, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_GetUniqueID_UniqueServerID' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Less_IntInt_ReturnValue_1) == 0x0000D4, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Array_Add_ReturnValue) == 0x0000D8, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Array_IsValidIndex_ReturnValue) == 0x0000DC, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Array_IsValidIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_Multiply_IntInt_ReturnValue) == 0x0000E0, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_Multiply_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_Added) == 0x0000E4, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_Added' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_SlotIndex) == 0x0000E8, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_SlotIndex' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_ItemInfo) == 0x0000F0, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_ItemInfo' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_SlotItemRef) == 0x0000F8, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_SlotItemRef' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_AddRequiredItems, CallFunc_AddNewInventoryItem_Stacked_) == 0x000100, "Member 'W_PlayerCraftingUI_C_AddRequiredItems::CallFunc_AddNewInventoryItem_Stacked_' has a wrong offset!");

// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.CheckIngredientsAvailability
// 0x00D8 (0x00D8 - 0x0000)
struct W_PlayerCraftingUI_C_CheckIngredientsAvailability final
{
public:
	bool                                          Proceed;                                           // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRepItemInfo                           CallFunc_Array_Get_Item;                           // 0x0020(0x0078)(HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_99[0x7];                                       // 0x0099(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSI_Slot_C*>                    CallFunc_CheckCraftingIngredients_ItemFound_Element; // 0x00A0(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UJSI_Slot_C*                            CallFunc_Array_Get_Item_1;                         // 0x00B0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGuid                                  CallFunc_GetUniqueID_UniqueServerID;               // 0x00BC(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x00CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_CD[0x3];                                       // 0x00CD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x00D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsValidIndex_ReturnValue;           // 0x00D4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_PlayerCraftingUI_C_CheckIngredientsAvailability) == 0x000008, "Wrong alignment on W_PlayerCraftingUI_C_CheckIngredientsAvailability");
static_assert(sizeof(W_PlayerCraftingUI_C_CheckIngredientsAvailability) == 0x0000D8, "Wrong size on W_PlayerCraftingUI_C_CheckIngredientsAvailability");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, Proceed) == 0x000000, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::Proceed' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, Temp_int_Loop_Counter_Variable) == 0x000004, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Add_IntInt_ReturnValue) == 0x000008, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, Temp_int_Array_Index_Variable) == 0x00000C, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, Temp_int_Array_Index_Variable_1) == 0x000010, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, Temp_int_Loop_Counter_Variable_1) == 0x000014, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Add_IntInt_ReturnValue_1) == 0x000018, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Array_Length_ReturnValue) == 0x00001C, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Array_Get_Item) == 0x000020, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Less_IntInt_ReturnValue) == 0x000098, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_CheckCraftingIngredients_ItemFound_Element) == 0x0000A0, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_CheckCraftingIngredients_ItemFound_Element' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Array_Get_Item_1) == 0x0000B0, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Array_Length_ReturnValue_1) == 0x0000B8, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_GetUniqueID_UniqueServerID) == 0x0000BC, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_GetUniqueID_UniqueServerID' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Less_IntInt_ReturnValue_1) == 0x0000CC, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Array_Add_ReturnValue) == 0x0000D0, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_CheckIngredientsAvailability, CallFunc_Array_IsValidIndex_ReturnValue) == 0x0000D4, "Member 'W_PlayerCraftingUI_C_CheckIngredientsAvailability::CallFunc_Array_IsValidIndex_ReturnValue' has a wrong offset!");

// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.EventOnMouseButtonDown
// 0x0028 (0x0028 - 0x0000)
struct W_PlayerCraftingUI_C_EventOnMouseButtonDown final
{
public:
	class UJSIContainer_C*                        Container;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            SlotRef;                                           // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FKey                                   Button;                                            // 0x0010(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
};
static_assert(alignof(W_PlayerCraftingUI_C_EventOnMouseButtonDown) == 0x000008, "Wrong alignment on W_PlayerCraftingUI_C_EventOnMouseButtonDown");
static_assert(sizeof(W_PlayerCraftingUI_C_EventOnMouseButtonDown) == 0x000028, "Wrong size on W_PlayerCraftingUI_C_EventOnMouseButtonDown");
static_assert(offsetof(W_PlayerCraftingUI_C_EventOnMouseButtonDown, Container) == 0x000000, "Member 'W_PlayerCraftingUI_C_EventOnMouseButtonDown::Container' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_EventOnMouseButtonDown, SlotRef) == 0x000008, "Member 'W_PlayerCraftingUI_C_EventOnMouseButtonDown::SlotRef' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_EventOnMouseButtonDown, Button) == 0x000010, "Member 'W_PlayerCraftingUI_C_EventOnMouseButtonDown::Button' has a wrong offset!");

// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.ExecuteUbergraph_W_PlayerCraftingUI
// 0x01F8 (0x01F8 - 0x0000)
struct W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UJSIContainer_C* Container, class UJSI_Slot_C* SlotRef, const struct FKey& Button)> K2Node_CreateDelegate_OutputDelegate; // 0x0004(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGuid                                  Temp_struct_Variable;                              // 0x0018(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x3];                                       // 0x0029(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_1;                               // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x0034(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_35[0x3];                                       // 0x0035(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSIContainer_C*                        K2Node_CustomEvent_Container;                      // 0x0038(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            K2Node_CustomEvent_SlotRef;                        // 0x0040(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FKey                                   K2Node_CustomEvent_Button;                         // 0x0048(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetItemName_Name;                         // 0x0068(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0080(0x0050)(HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x00D0(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x00E0(0x0018)()
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x00F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CheckIngredientsAvailability_Proceed;     // 0x00FA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CheckIngredientsAvailability_Proceed_1;   // 0x00FB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x00FC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_FD[0x3];                                       // 0x00FD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_2;                               // 0x0104(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0108(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetIsEnabled_ReturnValue;                 // 0x010C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_10D[0x3];                                      // 0x010D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            CallFunc_JigTryAddGetPendingRef_OutputPin;         // 0x0110(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_JigTryAddGetPendingRef_Stack_;            // 0x0118(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_isRotated__ReturnValue;                   // 0x0119(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11A[0x2];                                      // 0x011A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGuid                                  CallFunc_GetUniqueID_UniqueServerID;               // 0x011C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x012C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12D[0x3];                                      // 0x012D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGuid                                  K2Node_Select_Default;                             // 0x0130(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_141[0x7];                                      // 0x0141(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ULevellingComponent_C*                  CallFunc_GetLevellingComponent_LevellingComponent; // 0x0148(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0150(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_IntFloat_ReturnValue;            // 0x0158(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0160(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_AddXP_XPOutput;                           // 0x0168(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue;                       // 0x0170(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_174[0x4];                                      // 0x0174(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	int64                                         CallFunc_Conv_IntToInt64_ReturnValue;              // 0x0178(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x0180(0x0050)(HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array_1;                          // 0x01D0(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue_1;                     // 0x01E0(0x0018)()
};
static_assert(alignof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI) == 0x000008, "Wrong alignment on W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI");
static_assert(sizeof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI) == 0x0001F8, "Wrong size on W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, EntryPoint) == 0x000000, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, K2Node_CreateDelegate_OutputDelegate) == 0x000004, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, Temp_int_Variable) == 0x000014, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, Temp_struct_Variable) == 0x000018, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::Temp_struct_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, Temp_bool_Variable) == 0x000028, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, Temp_int_Variable_1) == 0x00002C, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Add_IntInt_ReturnValue) == 0x000030, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, Temp_bool_Has_Been_Initd_Variable) == 0x000034, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, K2Node_CustomEvent_Container) == 0x000038, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::K2Node_CustomEvent_Container' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, K2Node_CustomEvent_SlotRef) == 0x000040, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::K2Node_CustomEvent_SlotRef' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, K2Node_CustomEvent_Button) == 0x000048, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::K2Node_CustomEvent_Button' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x000060, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_GetItemName_Name) == 0x000068, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_GetItemName_Name' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, K2Node_MakeStruct_FormatArgumentData) == 0x000080, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, K2Node_MakeArray_Array) == 0x0000D0, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Format_ReturnValue) == 0x0000E0, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, Temp_bool_IsClosed_Variable) == 0x0000F8, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_IsValid_ReturnValue) == 0x0000F9, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_CheckIngredientsAvailability_Proceed) == 0x0000FA, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_CheckIngredientsAvailability_Proceed' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_CheckIngredientsAvailability_Proceed_1) == 0x0000FB, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_CheckIngredientsAvailability_Proceed_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Greater_IntInt_ReturnValue) == 0x0000FC, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Add_IntInt_ReturnValue_1) == 0x000100, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, Temp_int_Variable_2) == 0x000104, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::Temp_int_Variable_2' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Subtract_IntInt_ReturnValue) == 0x000108, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_GetIsEnabled_ReturnValue) == 0x00010C, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_GetIsEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_JigTryAddGetPendingRef_OutputPin) == 0x000110, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_JigTryAddGetPendingRef_OutputPin' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_JigTryAddGetPendingRef_Stack_) == 0x000118, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_JigTryAddGetPendingRef_Stack_' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_isRotated__ReturnValue) == 0x000119, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_isRotated__ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_GetUniqueID_UniqueServerID) == 0x00011C, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_GetUniqueID_UniqueServerID' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_IsValid_ReturnValue_1) == 0x00012C, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, K2Node_Select_Default) == 0x000130, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000140, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_GetLevellingComponent_LevellingComponent) == 0x000148, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_GetLevellingComponent_LevellingComponent' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000150, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Multiply_IntFloat_ReturnValue) == 0x000158, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000160, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_AddXP_XPOutput) == 0x000168, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_AddXP_XPOutput' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_FTrunc_ReturnValue) == 0x000170, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_FTrunc_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Conv_IntToInt64_ReturnValue) == 0x000178, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Conv_IntToInt64_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, K2Node_MakeStruct_FormatArgumentData_1) == 0x000180, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, K2Node_MakeArray_Array_1) == 0x0001D0, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::K2Node_MakeArray_Array_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI, CallFunc_Format_ReturnValue_1) == 0x0001E0, "Member 'W_PlayerCraftingUI_C_ExecuteUbergraph_W_PlayerCraftingUI::CallFunc_Format_ReturnValue_1' has a wrong offset!");

// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.GetText
// 0x0060 (0x0060 - 0x0000)
struct W_PlayerCraftingUI_C_GetText final
{
public:
	class FText                                   ReturnValue;                                       // 0x0000(0x0018)(Parm, OutParm, ReturnParm)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0018(0x0018)()
	class FText                                   CallFunc_Append_Text_Return_Value;                 // 0x0030(0x0018)()
	class FText                                   CallFunc_Append_Text_Return_Value_1;               // 0x0048(0x0018)()
};
static_assert(alignof(W_PlayerCraftingUI_C_GetText) == 0x000008, "Wrong alignment on W_PlayerCraftingUI_C_GetText");
static_assert(sizeof(W_PlayerCraftingUI_C_GetText) == 0x000060, "Wrong size on W_PlayerCraftingUI_C_GetText");
static_assert(offsetof(W_PlayerCraftingUI_C_GetText, ReturnValue) == 0x000000, "Member 'W_PlayerCraftingUI_C_GetText::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_GetText, CallFunc_Conv_IntToText_ReturnValue) == 0x000018, "Member 'W_PlayerCraftingUI_C_GetText::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_GetText, CallFunc_Append_Text_Return_Value) == 0x000030, "Member 'W_PlayerCraftingUI_C_GetText::CallFunc_Append_Text_Return_Value' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_GetText, CallFunc_Append_Text_Return_Value_1) == 0x000048, "Member 'W_PlayerCraftingUI_C_GetText::CallFunc_Append_Text_Return_Value_1' has a wrong offset!");

// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.InitializeInventory
// 0x0100 (0x0100 - 0x0000)
struct W_PlayerCraftingUI_C_InitializeInventory final
{
public:
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FS_JigCrafting                         CallFunc_Array_Get_Item;                           // 0x0008(0x00B8)(HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x00C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Added;                // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C9[0x3];                                       // 0x00C9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_AddNewInventoryItem_SlotIndex;            // 0x00CC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UJigsawItem_DataAsset_C*                CallFunc_AddNewInventoryItem_ItemInfo;             // 0x00D0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            CallFunc_AddNewInventoryItem_SlotItemRef;          // 0x00D8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_AddNewInventoryItem_Stacked_;             // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00E1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E2[0x2];                                       // 0x00E2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x00E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  CallFunc_GetPlayerPawn_ReturnValue;                // 0x00E8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UBP_JigMultiplayer_C*                   CallFunc_GetComponentByClass_ReturnValue;          // 0x00F0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x00F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_PlayerCraftingUI_C_InitializeInventory) == 0x000008, "Wrong alignment on W_PlayerCraftingUI_C_InitializeInventory");
static_assert(sizeof(W_PlayerCraftingUI_C_InitializeInventory) == 0x000100, "Wrong size on W_PlayerCraftingUI_C_InitializeInventory");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, Temp_int_Array_Index_Variable) == 0x000000, "Member 'W_PlayerCraftingUI_C_InitializeInventory::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_IsValid_ReturnValue) == 0x000004, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_Array_Get_Item) == 0x000008, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_Array_Length_ReturnValue) == 0x0000C0, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, Temp_int_Loop_Counter_Variable) == 0x0000C4, "Member 'W_PlayerCraftingUI_C_InitializeInventory::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_Added) == 0x0000C8, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_Added' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_SlotIndex) == 0x0000CC, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_SlotIndex' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_ItemInfo) == 0x0000D0, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_ItemInfo' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_SlotItemRef) == 0x0000D8, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_SlotItemRef' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_AddNewInventoryItem_Stacked_) == 0x0000E0, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_AddNewInventoryItem_Stacked_' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_Less_IntInt_ReturnValue) == 0x0000E1, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_Add_IntInt_ReturnValue) == 0x0000E4, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_GetPlayerPawn_ReturnValue) == 0x0000E8, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_GetPlayerPawn_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_GetComponentByClass_ReturnValue) == 0x0000F0, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_GetComponentByClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_IsVisible_ReturnValue) == 0x0000F8, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_InitializeInventory, CallFunc_Not_PreBool_ReturnValue) == 0x0000F9, "Member 'W_PlayerCraftingUI_C_InitializeInventory::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");

// Function W_PlayerCraftingUI.W_PlayerCraftingUI_C.SetCraftableItems
// 0x01A8 (0x01A8 - 0x0000)
struct W_PlayerCraftingUI_C_SetCraftableItems final
{
public:
	bool                                          InvalidItem;                                       // 0x0000(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UJSI_Slot_C*>                    CallFunc_Map_Keys_Keys;                            // 0x0020(0x0010)(ReferenceParm, ContainsInstancedReference)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            CallFunc_Array_Get_Item;                           // 0x0038(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FGuid>                          K2Node_MakeArray_Array;                            // 0x0048(0x0010)(ReferenceParm)
	struct FS_JigCrafting                         CallFunc_Array_Get_Item_1;                         // 0x0058(0x00B8)(HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0110(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0114(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_115[0x3];                                      // 0x0115(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FRepItemInfo                           CallFunc_Array_Get_Item_2;                         // 0x0118(0x0078)(HasGetValueTypeHash)
	TArray<class UJSI_Slot_C*>                    CallFunc_CheckCraftingIngredients_ItemFound_Element; // 0x0190(0x0010)(ReferenceParm, ContainsInstancedReference)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x01A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsValidIndex_ReturnValue;           // 0x01A4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x01A5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_PlayerCraftingUI_C_SetCraftableItems) == 0x000008, "Wrong alignment on W_PlayerCraftingUI_C_SetCraftableItems");
static_assert(sizeof(W_PlayerCraftingUI_C_SetCraftableItems) == 0x0001A8, "Wrong size on W_PlayerCraftingUI_C_SetCraftableItems");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, InvalidItem) == 0x000000, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::InvalidItem' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, Temp_int_Array_Index_Variable) == 0x000004, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, Temp_int_Loop_Counter_Variable) == 0x000008, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, Temp_int_Loop_Counter_Variable_1) == 0x00000C, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Add_IntInt_ReturnValue) == 0x000010, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Add_IntInt_ReturnValue_1) == 0x000014, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, Temp_int_Array_Index_Variable_1) == 0x000018, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Map_Keys_Keys) == 0x000020, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Map_Keys_Keys' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_IsValid_ReturnValue) == 0x000030, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Array_Get_Item) == 0x000038, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_IsValid_ReturnValue_1) == 0x000040, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, K2Node_MakeArray_Array) == 0x000048, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Array_Get_Item_1) == 0x000058, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Array_Length_ReturnValue) == 0x000110, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Less_IntInt_ReturnValue) == 0x000114, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Array_Get_Item_2) == 0x000118, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_CheckCraftingIngredients_ItemFound_Element) == 0x000190, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_CheckCraftingIngredients_ItemFound_Element' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Array_Length_ReturnValue_1) == 0x0001A0, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Array_IsValidIndex_ReturnValue) == 0x0001A4, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Array_IsValidIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_PlayerCraftingUI_C_SetCraftableItems, CallFunc_Less_IntInt_ReturnValue_1) == 0x0001A5, "Member 'W_PlayerCraftingUI_C_SetCraftableItems::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");

}

