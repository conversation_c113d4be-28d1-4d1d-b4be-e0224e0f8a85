﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Ultra_Dynamic_Weather_Interface

#include "Basic.hpp"

#include "CoreUObject_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C
// 0x0000 (0x0000 - 0x0000)
class IUltra_Dynamic_Weather_Interface_C final
{
public:
	void Editor_Tick(const struct FVector& Editor_Camera_Location, const struct FRotator& Editor_Camera_Rotation, double Delta_Time, bool* Completed);
	void Get_Control_Point_Location(struct FVector* Location);
	void Get_Local_Weather_State_Values(double* Cloud_Coverage, double* Wind_Intensity, double* Rain, double* Snow, double* Dust, double* Fog, double* Lightning);
	void Get_UDS_Values_Controlled_by_UDW(double* Cloud_Coverage, double* Fog, double* Dust_Amount, double* Cloud_Direction, double* Wind_Speed_Multiplier, double* Fog_Vertical_Velocity);
	void Get_UDS_Weather_Override_Bool(bool* Cloud_Coverage, bool* Fog, bool* Dust);
	void Get_UDW_State_for_Saving(struct FUDS_and_UDW_State* UDW_State);
	void Initialize_Weather(class AUltra_Dynamic_Sky_C* UDS);
	void Report_Removed_Radial_Storm(class AActor* Storm, bool* Success);
	void UDS_Reconstruct(bool* Success);
	void UDS_Weather_Variable_Overrides(bool Override_Clouds, double Cloud_Coverage, bool Override_Fog, double Fog, bool Override_Dust, double Dust, bool* Success);
	void UDW_Runtime_Tick(double Delta_Time);
	void UDW_State_Apply(const struct FUDS_and_UDW_State& State, bool* Completed);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Ultra_Dynamic_Weather_Interface_C">();
	}
	static class IUltra_Dynamic_Weather_Interface_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<IUltra_Dynamic_Weather_Interface_C>();
	}

	class UObject* AsUObject()
	{
		return reinterpret_cast<UObject*>(this);
	}
	const class UObject* AsUObject() const
	{
		return reinterpret_cast<const UObject*>(this);
	}
};
static_assert(alignof(IUltra_Dynamic_Weather_Interface_C) == 0x000001, "Wrong alignment on IUltra_Dynamic_Weather_Interface_C");
static_assert(sizeof(IUltra_Dynamic_Weather_Interface_C) == 0x000001, "Wrong size on IUltra_Dynamic_Weather_Interface_C");

}

