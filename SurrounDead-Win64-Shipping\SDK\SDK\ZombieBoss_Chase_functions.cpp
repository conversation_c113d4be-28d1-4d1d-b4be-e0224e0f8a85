﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: ZombieBoss_Chase

#include "Basic.hpp"

#include "ZombieBoss_Chase_classes.hpp"
#include "ZombieBoss_Chase_parameters.hpp"


namespace SDK
{

// Function ZombieBoss_Chase.ZombieBoss_Chase_C.ExecuteUbergraph_ZombieBoss_Chase
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UZombieBoss_Chase_C::ExecuteUbergraph_ZombieBoss_Chase(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZombieBoss_Chase_C", "ExecuteUbergraph_ZombieBoss_Chase");

	Params::<PERSON><PERSON><PERSON>_Chase_C_ExecuteUbergraph_ZombieB<PERSON>_Chase Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function ZombieBoss_Chase.ZombieBoss_Chase_C.ReceiveExecuteAI
// (Event, Protected, BlueprintEvent)
// Parameters:
// class AAIController*                    OwnerController                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class APawn*                            ControlledPawn                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UZombieBoss_Chase_C::ReceiveExecuteAI(class AAIController* OwnerController, class APawn* ControlledPawn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZombieBoss_Chase_C", "ReceiveExecuteAI");

	Params::ZombieBoss_Chase_C_ReceiveExecuteAI Parms{};

	Parms.OwnerController = OwnerController;
	Parms.ControlledPawn = ControlledPawn;

	UObject::ProcessEvent(Func, &Parms);
}

}

