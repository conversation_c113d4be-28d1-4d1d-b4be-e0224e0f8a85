﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_BaseProgressBar

#include "Basic.hpp"

#include "Slate_structs.hpp"
#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_BaseProgressBar.WB_BaseProgressBar_C
// 0x00A0 (0x0360 - 0x02C0)
class UWB_BaseProgressBar_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                B_FillTypes;                                       // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSki<PERSON>, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UProgressBar*                           BottomToTop;                                       // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Center_Left;                                       // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Center_Right;                                      // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UProgressBar*                           FillFromCenter;                                    // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        FillTypes;                                         // 0x02F0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Img_BottomToTop;                                   // 0x02F8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         Img_FillFromCenter;                                // 0x0300(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        Img_FillTypes;                                     // 0x0308(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Img_LeftToRight;                                   // 0x0310(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Img_RightToLeft;                                   // 0x0318(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Img_TopToBottom;                                   // 0x0320(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UProgressBar*                           LeftToRight;                                       // 0x0328(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UProgressBar*                           RightToLeft;                                       // 0x0330(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UProgressBar*                           TopToBottom;                                       // 0x0338(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        WS_Marquee;                                        // 0x0340(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	EProgressBarFillType                          FillType;                                          // 0x0348(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_349[0x7];                                      // 0x0349(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Percent;                                           // 0x0350(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseShader;                                        // 0x0358(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WB_BaseProgressBar(int32 EntryPoint);
	void GetCurrentProgressBar(class UProgressBar** AsProgress_Bar);
	void GetPercent(double* Percent_0);
	void SetBackgroundTint(const struct FLinearColor& Tint);
	void SetBarFillType(EProgressBarFillType BarFillType, bool bUseShader_0);
	void SetFillColorAndOpacity(const struct FLinearColor& InColor);
	void SetFillImage(class UObject* FillImage);
	void SetFillImageDrawAs(ESlateBrushDrawType Draw_As);
	void SetFillImageMargin(double Margin);
	void SetFillImageSize(const struct FVector2D& FillImageSize);
	void SetFillImageTiling(ESlateBrushTileType Tiling);
	void SetImageBlendMask(class UObject* BlendMask);
	void SetImagePercent(double Percent_0);
	void SetImgsEnabled(bool bInIsEnabled);
	void SetInstanceBlendMask(class UImage* Img, class UTexture* BlendMask);
	void SetInstancePercent(class UImage* Img, double Percent_0);
	void SetIsMarquee(bool InbIsMarquee);
	void SetMarqueeDrawAs(ESlateBrushDrawType Draw_As);
	void SetMarqueeImage(class UObject* Image);
	void SetMarqueeImageSize(const struct FVector2D& Image_Size);
	void SetMarqueeTiling(ESlateBrushTileType Tiling);
	void SetMarqueeTint(const struct FLinearColor& Color);
	void SetPBsEnabled(bool bInIsEnabled);
	void SetPercent(double InPercent);
	void SetScalarParameter(class UImage* Img, class FName ParameterName, double Value);
	void SetTextureParameter(class UImage* Img, class FName ParameterName, class UTexture* Value);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_BaseProgressBar_C">();
	}
	static class UWB_BaseProgressBar_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_BaseProgressBar_C>();
	}
};
static_assert(alignof(UWB_BaseProgressBar_C) == 0x000008, "Wrong alignment on UWB_BaseProgressBar_C");
static_assert(sizeof(UWB_BaseProgressBar_C) == 0x000360, "Wrong size on UWB_BaseProgressBar_C");
static_assert(offsetof(UWB_BaseProgressBar_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_BaseProgressBar_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, B_FillTypes) == 0x0002C8, "Member 'UWB_BaseProgressBar_C::B_FillTypes' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, BottomToTop) == 0x0002D0, "Member 'UWB_BaseProgressBar_C::BottomToTop' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, Center_Left) == 0x0002D8, "Member 'UWB_BaseProgressBar_C::Center_Left' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, Center_Right) == 0x0002E0, "Member 'UWB_BaseProgressBar_C::Center_Right' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, FillFromCenter) == 0x0002E8, "Member 'UWB_BaseProgressBar_C::FillFromCenter' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, FillTypes) == 0x0002F0, "Member 'UWB_BaseProgressBar_C::FillTypes' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, Img_BottomToTop) == 0x0002F8, "Member 'UWB_BaseProgressBar_C::Img_BottomToTop' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, Img_FillFromCenter) == 0x000300, "Member 'UWB_BaseProgressBar_C::Img_FillFromCenter' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, Img_FillTypes) == 0x000308, "Member 'UWB_BaseProgressBar_C::Img_FillTypes' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, Img_LeftToRight) == 0x000310, "Member 'UWB_BaseProgressBar_C::Img_LeftToRight' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, Img_RightToLeft) == 0x000318, "Member 'UWB_BaseProgressBar_C::Img_RightToLeft' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, Img_TopToBottom) == 0x000320, "Member 'UWB_BaseProgressBar_C::Img_TopToBottom' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, LeftToRight) == 0x000328, "Member 'UWB_BaseProgressBar_C::LeftToRight' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, RightToLeft) == 0x000330, "Member 'UWB_BaseProgressBar_C::RightToLeft' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, TopToBottom) == 0x000338, "Member 'UWB_BaseProgressBar_C::TopToBottom' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, WS_Marquee) == 0x000340, "Member 'UWB_BaseProgressBar_C::WS_Marquee' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, FillType) == 0x000348, "Member 'UWB_BaseProgressBar_C::FillType' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, Percent) == 0x000350, "Member 'UWB_BaseProgressBar_C::Percent' has a wrong offset!");
static_assert(offsetof(UWB_BaseProgressBar_C, bUseShader) == 0x000358, "Member 'UWB_BaseProgressBar_C::bUseShader' has a wrong offset!");

}

