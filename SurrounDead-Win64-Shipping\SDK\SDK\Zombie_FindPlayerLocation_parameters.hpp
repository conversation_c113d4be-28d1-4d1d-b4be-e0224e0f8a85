﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_FindPlayerLocation

#include "Basic.hpp"

#include "SurrounDead_structs.hpp"


namespace SDK::Params
{

// Function Zombie_FindPlayerLocation.Zombie_FindPlayerLocation_C.ExecuteUbergraph_Zombie_FindPlayerLocation
// 0x0070 (0x0070 - 0x0000)
struct Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_True_if_break_was_hit_Variable;          // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D[0x3];                                        // 0x000D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class AAIController*                          K2Node_Event_OwnerController;                      // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  K2Node_Event_ControlledPawn;                       // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24[0x4];                                       // 0x0024(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAIPerceptionComponent*                 CallFunc_GetComponentByClass_ReturnValue;          // 0x0028(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TArray<class AActor*>                         CallFunc_GetCurrentlyPerceivedActors_OutActors;    // 0x0030(0x0010)(ReferenceParm)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_44[0x4];                                       // 0x0044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FActor_Dist>                    CallFunc_Distance_Sort_Sorted_Array;               // 0x0048(0x0010)(ReferenceParm)
	struct FActor_Dist                            CallFunc_Array_Get_Item;                           // 0x0058(0x0010)(NoDestructor)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x006C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x006D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x006E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_ActorHasTag_ReturnValue;                  // 0x006F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation) == 0x000008, "Wrong alignment on Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation");
static_assert(sizeof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation) == 0x000070, "Wrong size on Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, EntryPoint) == 0x000000, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::EntryPoint' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, Temp_bool_True_if_break_was_hit_Variable) == 0x000004, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::Temp_bool_True_if_break_was_hit_Variable' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, Temp_int_Array_Index_Variable) == 0x000008, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_Not_PreBool_ReturnValue) == 0x00000C, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, K2Node_Event_OwnerController) == 0x000010, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::K2Node_Event_OwnerController' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, K2Node_Event_ControlledPawn) == 0x000018, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::K2Node_Event_ControlledPawn' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, Temp_int_Loop_Counter_Variable) == 0x000020, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_GetComponentByClass_ReturnValue) == 0x000028, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_GetComponentByClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_GetCurrentlyPerceivedActors_OutActors) == 0x000030, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_GetCurrentlyPerceivedActors_OutActors' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_Add_IntInt_ReturnValue) == 0x000040, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_Distance_Sort_Sorted_Array) == 0x000048, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_Distance_Sort_Sorted_Array' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_Array_Get_Item) == 0x000058, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_Array_Length_ReturnValue) == 0x000068, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_Less_IntInt_ReturnValue) == 0x00006C, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_IsValid_ReturnValue) == 0x00006D, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_BooleanAND_ReturnValue) == 0x00006E, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation, CallFunc_ActorHasTag_ReturnValue) == 0x00006F, "Member 'Zombie_FindPlayerLocation_C_ExecuteUbergraph_Zombie_FindPlayerLocation::CallFunc_ActorHasTag_ReturnValue' has a wrong offset!");

// Function Zombie_FindPlayerLocation.Zombie_FindPlayerLocation_C.ReceiveExecuteAI
// 0x0010 (0x0010 - 0x0000)
struct Zombie_FindPlayerLocation_C_ReceiveExecuteAI final
{
public:
	class AAIController*                          OwnerController;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  ControlledPawn;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Zombie_FindPlayerLocation_C_ReceiveExecuteAI) == 0x000008, "Wrong alignment on Zombie_FindPlayerLocation_C_ReceiveExecuteAI");
static_assert(sizeof(Zombie_FindPlayerLocation_C_ReceiveExecuteAI) == 0x000010, "Wrong size on Zombie_FindPlayerLocation_C_ReceiveExecuteAI");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ReceiveExecuteAI, OwnerController) == 0x000000, "Member 'Zombie_FindPlayerLocation_C_ReceiveExecuteAI::OwnerController' has a wrong offset!");
static_assert(offsetof(Zombie_FindPlayerLocation_C_ReceiveExecuteAI, ControlledPawn) == 0x000008, "Member 'Zombie_FindPlayerLocation_C_ReceiveExecuteAI::ControlledPawn' has a wrong offset!");

}

