﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_NarrativeMenu_QuestJournal

#include "Basic.hpp"

#include "NarrativeCommonUI_structs.hpp"
#include "Engine_structs.hpp"
#include "WBP_NarrativeMenu_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_NarrativeMenu_QuestJournal.W_NarrativeMenu_QuestJournal_C
// 0x00B8 (0x0500 - 0x0448)
class UW_NarrativeMenu_QuestJournal_C final : public UWBP_NarrativeMenu_C
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame_W_NarrativeMenu_QuestJournal_C;     // 0x0448(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UScrollBox*                             ActiveQuestsBox;                                   // 0x0450(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BG_Objectives;                                     // 0x0458(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScrollBox*                             BranchesBox;                                       // 0x0460(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UButton*                                Button_Exit;                                       // 0x0468(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCommonTextBlock*                       CommonText_Header;                                 // 0x0470(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScrollBox*                             FinishedQuestsBox;                                 // 0x0478(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_Exit;                                        // 0x0480(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             QuestTitle;                                        // 0x0488(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class URichTextBlock*                         RichText_CurrentStateDescription;                  // 0x0490(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class URichTextBlock*                         RichText_QuestDescription;                         // 0x0498(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Text_ActiveQuestCount;                             // 0x04A0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Text_FinishedQuestCount;                           // 0x04A8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCheckBox*                              UseShared_;                                        // 0x04B0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNarrativeComponent*                    NarrativeComp;                                     // 0x04B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	class UQuest*                                 CurrentQuest;                                      // 0x04C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	TArray<class UQuestState*>                    Reached_States;                                    // 0x04C8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<class UBP_QuestJournalQuest_C*>        AllJournalButtons;                                 // 0x04D8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	struct FDataTableRowHandle                    TogglePartyQuestsAction;                           // 0x04E8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	struct FInputActionBindingHandle              TogglePartyQuestsActionHandle;                     // 0x04F8(0x0004)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)

public:
	void BndEvt__BP_QuestJournal_Button_Exit_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature();
	void BndEvt__BP_QuestJournal_UseShared__K2Node_ComponentBoundEvent_1_OnCheckBoxComponentStateChanged__DelegateSignature(bool bIsChecked);
	void Clear_Quest();
	class UBP_QuestJournalQuest_C* CreateQuestWidgetButton(class UQuest* Quest);
	void ExecuteUbergraph_W_NarrativeMenu_QuestJournal(int32 EntryPoint);
	void Initialize(class UNarrativeComponent* Narrative);
	void OnTogglePartyQuestsAction(class FName ActionName);
	void Party_Quests_Toggled(bool PartyQuests);
	void RegisterActions();
	void Show_Quest(class UQuest* Quest, class UBP_QuestJournalQuest_C* JournalButton);
	void Construct();

	class UWidget* BP_GetDesiredFocusTarget() const;

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_NarrativeMenu_QuestJournal_C">();
	}
	static class UW_NarrativeMenu_QuestJournal_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_NarrativeMenu_QuestJournal_C>();
	}
};
static_assert(alignof(UW_NarrativeMenu_QuestJournal_C) == 0x000008, "Wrong alignment on UW_NarrativeMenu_QuestJournal_C");
static_assert(sizeof(UW_NarrativeMenu_QuestJournal_C) == 0x000500, "Wrong size on UW_NarrativeMenu_QuestJournal_C");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, UberGraphFrame_W_NarrativeMenu_QuestJournal_C) == 0x000448, "Member 'UW_NarrativeMenu_QuestJournal_C::UberGraphFrame_W_NarrativeMenu_QuestJournal_C' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, ActiveQuestsBox) == 0x000450, "Member 'UW_NarrativeMenu_QuestJournal_C::ActiveQuestsBox' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, BG_Objectives) == 0x000458, "Member 'UW_NarrativeMenu_QuestJournal_C::BG_Objectives' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, BranchesBox) == 0x000460, "Member 'UW_NarrativeMenu_QuestJournal_C::BranchesBox' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, Button_Exit) == 0x000468, "Member 'UW_NarrativeMenu_QuestJournal_C::Button_Exit' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, CommonText_Header) == 0x000470, "Member 'UW_NarrativeMenu_QuestJournal_C::CommonText_Header' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, FinishedQuestsBox) == 0x000478, "Member 'UW_NarrativeMenu_QuestJournal_C::FinishedQuestsBox' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, Image_Exit) == 0x000480, "Member 'UW_NarrativeMenu_QuestJournal_C::Image_Exit' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, QuestTitle) == 0x000488, "Member 'UW_NarrativeMenu_QuestJournal_C::QuestTitle' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, RichText_CurrentStateDescription) == 0x000490, "Member 'UW_NarrativeMenu_QuestJournal_C::RichText_CurrentStateDescription' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, RichText_QuestDescription) == 0x000498, "Member 'UW_NarrativeMenu_QuestJournal_C::RichText_QuestDescription' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, Text_ActiveQuestCount) == 0x0004A0, "Member 'UW_NarrativeMenu_QuestJournal_C::Text_ActiveQuestCount' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, Text_FinishedQuestCount) == 0x0004A8, "Member 'UW_NarrativeMenu_QuestJournal_C::Text_FinishedQuestCount' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, UseShared_) == 0x0004B0, "Member 'UW_NarrativeMenu_QuestJournal_C::UseShared_' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, NarrativeComp) == 0x0004B8, "Member 'UW_NarrativeMenu_QuestJournal_C::NarrativeComp' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, CurrentQuest) == 0x0004C0, "Member 'UW_NarrativeMenu_QuestJournal_C::CurrentQuest' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, Reached_States) == 0x0004C8, "Member 'UW_NarrativeMenu_QuestJournal_C::Reached_States' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, AllJournalButtons) == 0x0004D8, "Member 'UW_NarrativeMenu_QuestJournal_C::AllJournalButtons' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, TogglePartyQuestsAction) == 0x0004E8, "Member 'UW_NarrativeMenu_QuestJournal_C::TogglePartyQuestsAction' has a wrong offset!");
static_assert(offsetof(UW_NarrativeMenu_QuestJournal_C, TogglePartyQuestsActionHandle) == 0x0004F8, "Member 'UW_NarrativeMenu_QuestJournal_C::TogglePartyQuestsActionHandle' has a wrong offset!");

}

