﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Image_Raw

#include "Basic.hpp"

#include "WB_Image_Raw_classes.hpp"
#include "WB_Image_Raw_parameters.hpp"


namespace SDK
{

// Function WB_Image_Raw.WB_Image_Raw_C.ExecuteUbergraph_WB_Image_Raw
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Image_Raw_C::ExecuteUbergraph_WB_Image_Raw(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Image_Raw_C", "ExecuteUbergraph_WB_Image_Raw");

	Params::WB_Image_Raw_C_ExecuteUbergraph_WB_Image_Raw Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Image_Raw.WB_Image_Raw_C.SetBackgroundBlur
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsEnabled                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  BlurStrength                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Image_Raw_C::SetBackgroundBlur(bool IsEnabled, double BlurStrength)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Image_Raw_C", "SetBackgroundBlur");

	Params::WB_Image_Raw_C_SetBackgroundBlur Parms{};

	Parms.IsEnabled = IsEnabled;
	Parms.BlurStrength = BlurStrength;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Image_Raw.WB_Image_Raw_C.SetColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Image_Raw_C::SetColor(const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Image_Raw_C", "SetColor");

	Params::WB_Image_Raw_C_SetColor Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}

}

