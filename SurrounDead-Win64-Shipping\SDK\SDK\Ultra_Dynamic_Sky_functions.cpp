﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Ultra_Dynamic_Sky

#include "Basic.hpp"

#include "Ultra_Dynamic_Sky_classes.hpp"
#include "Ultra_Dynamic_Sky_parameters.hpp"


namespace SDK
{

// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.2D Clouds Shading Offset Vector
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    LinearColor                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::TwoD_Clouds_Shading_Offset_Vector(struct FLinearColor* LinearColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "2D Clouds Shading Offset Vector");

	Params::Ultra_Dynamic_Sky_C_TwoD_Clouds_Shading_Offset_Vector Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (LinearColor != nullptr)
		*LinearColor = std::move(Parms.LinearColor);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.3 Color Time Blend
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// const struct FLinearColor&              Day_Color                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Dusk_Color                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Night_Color                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Three_Color_Time_Blend(const struct FLinearColor& Day_Color, const struct FLinearColor& Dusk_Color, const struct FLinearColor& Night_Color, struct FLinearColor* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "3 Color Time Blend");

	Params::Ultra_Dynamic_Sky_C_Three_Color_Time_Blend Parms{};

	Parms.Day_Color = std::move(Day_Color);
	Parms.Dusk_Color = std::move(Dusk_Color);
	Parms.Night_Color = std::move(Night_Color);

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Absent Directional Lights Brightness
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Absent_Directional_Lights_Brightness()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Absent Directional Lights Brightness");

	Params::Ultra_Dynamic_Sky_C_Absent_Directional_Lights_Brightness Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Add Constructed Components
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Add_Constructed_Components()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Add Constructed Components");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Add Modifier
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Modifier_C*                  Modifier                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Fade_In_Time                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Add_Modifier(class UUDS_Modifier_C* Modifier, double Fade_In_Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Add Modifier");

	Params::Ultra_Dynamic_Sky_C_Add_Modifier Parms{};

	Parms.Modifier = Modifier;
	Parms.Fade_In_Time = Fade_In_Time;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Add Object to Async Loading Queue
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// TSoftObjectPtr<class UObject>           Object                                                 (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
// bool                                    High_Priority                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Add_Object_to_Async_Loading_Queue(TSoftObjectPtr<class UObject> Object, bool High_Priority)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Add Object to Async Loading Queue");

	Params::Ultra_Dynamic_Sky_C_Add_Object_to_Async_Loading_Queue Parms{};

	Parms.Object = Object;
	Parms.High_Priority = High_Priority;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Add Static Post Process Materials
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Add_Static_Post_Process_Materials()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Add Static Post Process Materials");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Adjust Base Sun Light Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// const struct FVector&                   Sun_Vector                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Intensity                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Multiplier                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Adjust_Base_Sun_Light_Intensity(const struct FVector& Sun_Vector, double* Intensity, double* Multiplier)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Adjust Base Sun Light Intensity");

	Params::Ultra_Dynamic_Sky_C_Adjust_Base_Sun_Light_Intensity Parms{};

	Parms.Sun_Vector = std::move(Sun_Vector);

	UObject::ProcessEvent(Func, &Parms);

	if (Intensity != nullptr)
		*Intensity = Parms.Intensity;

	if (Multiplier != nullptr)
		*Multiplier = Parms.Multiplier;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Adjust for World Origin Rebasing
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Adjust_for_World_Origin_Rebasing()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Adjust for World Origin Rebasing");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.All Volumetric Cloud MIDs
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TArray<class UMaterialInstanceDynamic*>*MIDs                                                   (Parm, OutParm)

void AUltra_Dynamic_Sky_C::All_Volumetric_Cloud_MIDs(TArray<class UMaterialInstanceDynamic*>* MIDs)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "All Volumetric Cloud MIDs");

	Params::Ultra_Dynamic_Sky_C_All_Volumetric_Cloud_MIDs Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (MIDs != nullptr)
		*MIDs = std::move(Parms.MIDs);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Applied Cloud Speed
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Applied_Cloud_Speed(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Applied Cloud Speed");

	Params::Ultra_Dynamic_Sky_C_Applied_Cloud_Speed Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Console Variable with Check
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FString&                    CVar                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
// double                                  setting                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Type                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Apply_Console_Variable_with_Check(const class FString& CVar, double setting, int32 Type)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Console Variable with Check");

	Params::Ultra_Dynamic_Sky_C_Apply_Console_Variable_with_Check Parms{};

	Parms.CVar = std::move(CVar);
	Parms.setting = setting;
	Parms.Type = Type;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Editor Weather Override
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Apply_Editor_Weather_Override()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Editor Weather Override");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Feature Level Mode Changes
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   Made_Changes                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Apply_Feature_Level_Mode_Changes(bool* Made_Changes)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Feature Level Mode Changes");

	Params::Ultra_Dynamic_Sky_C_Apply_Feature_Level_Mode_Changes Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Made_Changes != nullptr)
		*Made_Changes = Parms.Made_Changes;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Light Shaft Settings
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UDirectionalLightComponent*       Light                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Max_Brightness                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Bloom_Threshold                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector2D&                 Bloom_Scale                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Forward_Vector                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Apply_Light_Shaft_Settings(class UDirectionalLightComponent* Light, const struct FVector2D& Max_Brightness, const struct FVector2D& Bloom_Threshold, const struct FVector2D& Bloom_Scale, const struct FVector& Forward_Vector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Light Shaft Settings");

	Params::Ultra_Dynamic_Sky_C_Apply_Light_Shaft_Settings Parms{};

	Parms.Light = Light;
	Parms.Max_Brightness = std::move(Max_Brightness);
	Parms.Bloom_Threshold = std::move(Bloom_Threshold);
	Parms.Bloom_Scale = std::move(Bloom_Scale);
	Parms.Forward_Vector = std::move(Forward_Vector);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Location Preset
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_CityPresets                        Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Apply_Location_Preset(EUDS_CityPresets Location)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Location Preset");

	Params::Ultra_Dynamic_Sky_C_Apply_Location_Preset Parms{};

	Parms.Location = Location;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Modifier Property Overrides
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Modifier_C*                  Modifier                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Alpha                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Apply_Modifier_Property_Overrides(class UUDS_Modifier_C* Modifier, double Alpha)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Modifier Property Overrides");

	Params::Ultra_Dynamic_Sky_C_Apply_Modifier_Property_Overrides Parms{};

	Parms.Modifier = Modifier;
	Parms.Alpha = Alpha;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Modifiers
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Apply_Modifiers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Modifiers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Saved UDS and UDW State
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FUDS_and_UDW_State&        State                                                  (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Apply_Saved_UDS_and_UDW_State(const struct FUDS_and_UDW_State& State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Saved UDS and UDW State");

	Params::Ultra_Dynamic_Sky_C_Apply_Saved_UDS_and_UDW_State Parms{};

	Parms.State = std::move(State);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Starting Modifiers
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Apply_Starting_Modifiers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Starting Modifiers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply System Time
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Apply_System_Time()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply System Time");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Apply Volumetric Mode
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_VolRT_Mode                         Mode                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Apply_Volumetric_Mode(EUDS_VolRT_Mode Mode)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Apply Volumetric Mode");

	Params::Ultra_Dynamic_Sky_C_Apply_Volumetric_Mode Parms{};

	Parms.Mode = Mode;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Approximate Real Sun Moon and Stars
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Time_of_Day_0                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Only_Calculate_Sun                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector*                         Sun_Vector                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector*                         Moon_Vector                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Real_Phase                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector*                         Phase_Alignment                                        (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Celestial_Yaw                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Celestial_Orbit                                        (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Approximate_Real_Sun_Moon_and_Stars(double Time_of_Day_0, bool Only_Calculate_Sun, struct FVector* Sun_Vector, struct FVector* Moon_Vector, double* Real_Phase, struct FVector* Phase_Alignment, struct FLinearColor* Celestial_Yaw, double* Celestial_Orbit)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Approximate Real Sun Moon and Stars");

	Params::Ultra_Dynamic_Sky_C_Approximate_Real_Sun_Moon_and_Stars Parms{};

	Parms.Time_of_Day_0 = Time_of_Day_0;
	Parms.Only_Calculate_Sun = Only_Calculate_Sun;

	UObject::ProcessEvent(Func, &Parms);

	if (Sun_Vector != nullptr)
		*Sun_Vector = std::move(Parms.Sun_Vector);

	if (Moon_Vector != nullptr)
		*Moon_Vector = std::move(Parms.Moon_Vector);

	if (Real_Phase != nullptr)
		*Real_Phase = Parms.Real_Phase;

	if (Phase_Alignment != nullptr)
		*Phase_Alignment = std::move(Parms.Phase_Alignment);

	if (Celestial_Yaw != nullptr)
		*Celestial_Yaw = std::move(Parms.Celestial_Yaw);

	if (Celestial_Orbit != nullptr)
		*Celestial_Orbit = Parms.Celestial_Orbit;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cache Color
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_CachedProperties                   Property                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Set_Value                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Change_Tolerance                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cache_Color(EUDS_CachedProperties Property, const struct FLinearColor& Set_Value, double Change_Tolerance)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cache Color");

	Params::Ultra_Dynamic_Sky_C_Cache_Color Parms{};

	Parms.Property = Property;
	Parms.Set_Value = std::move(Set_Value);
	Parms.Change_Tolerance = Change_Tolerance;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cache Float
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_CachedProperties                   Property                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Set_Value                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Change_Tolerance                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cache_Float(EUDS_CachedProperties Property, double Set_Value, double Change_Tolerance)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cache Float");

	Params::Ultra_Dynamic_Sky_C_Cache_Float Parms{};

	Parms.Property = Property;
	Parms.Set_Value = Set_Value;
	Parms.Change_Tolerance = Change_Tolerance;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cache Properties
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Cache_Group                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Starting_Cache_Fill                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cache_Properties(int32 Cache_Group, bool Starting_Cache_Fill)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cache Properties");

	Params::Ultra_Dynamic_Sky_C_Cache_Properties Parms{};

	Parms.Cache_Group = Cache_Group;
	Parms.Starting_Cache_Fill = Starting_Cache_Fill;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cache Sun and Moon Orientation
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Cache_Sun_and_Moon_Orientation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cache Sun and Moon Orientation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cache Timer And Update Speed
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   Hard_Cache_Reset                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cache_Timer_And_Update_Speed(bool* Hard_Cache_Reset)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cache Timer And Update Speed");

	Params::Ultra_Dynamic_Sky_C_Cache_Timer_And_Update_Speed Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Hard_Cache_Reset != nullptr)
		*Hard_Cache_Reset = Parms.Hard_Cache_Reset;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cache Vector
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_CachedProperties                   Property                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Set_Value                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Change_Tolerance                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cache_Vector(EUDS_CachedProperties Property, const struct FVector& Set_Value, double Change_Tolerance)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cache Vector");

	Params::Ultra_Dynamic_Sky_C_Cache_Vector Parms{};

	Parms.Property = Property;
	Parms.Set_Value = std::move(Set_Value);
	Parms.Change_Tolerance = Change_Tolerance;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Camera Location Dependent Updates
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Camera_Location_Dependent_Updates()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Camera Location Dependent Updates");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Change Sky Mode at Runtime
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_SkyMode                            New_Sky_Mode                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Change_Sky_Mode_at_Runtime(EUDS_SkyMode New_Sky_Mode)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Change Sky Mode at Runtime");

	Params::Ultra_Dynamic_Sky_C_Change_Sky_Mode_at_Runtime Parms{};

	Parms.New_Sky_Mode = New_Sky_Mode;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Check for Cloud Coverage Target Recenter
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Check_for_Cloud_Coverage_Target_Recenter()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Check for Cloud Coverage Target Recenter");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Check for Daylight Savings Time
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Hour                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Check_for_Daylight_Savings_Time(int32 Hour)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Check for Daylight Savings Time");

	Params::Ultra_Dynamic_Sky_C_Check_for_Daylight_Savings_Time Parms{};

	Parms.Hour = Hour;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Check for Time Event Dispatchers
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Check_for_Time_Event_Dispatchers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Check for Time Event Dispatchers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Check if Point is Exposed to Sun or Moon Light
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Distance_to_Trace                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// TArray<class AActor*>&                  Actors_to_Ignore_in_Trace                              (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// bool*                                   Exposed_to_Sun                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Exposed_to_Moon                                        (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Exposed_to_Either                                      (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Light_Intensity__Lux_                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Check_if_Point_is_Exposed_to_Sun_or_Moon_Light(const struct FVector& Location, double Distance_to_Trace, TArray<class AActor*>& Actors_to_Ignore_in_Trace, bool* Exposed_to_Sun, bool* Exposed_to_Moon, bool* Exposed_to_Either, double* Light_Intensity__Lux_)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Check if Point is Exposed to Sun or Moon Light");

	Params::Ultra_Dynamic_Sky_C_Check_if_Point_is_Exposed_to_Sun_or_Moon_Light Parms{};

	Parms.Location = std::move(Location);
	Parms.Distance_to_Trace = Distance_to_Trace;
	Parms.Actors_to_Ignore_in_Trace = std::move(Actors_to_Ignore_in_Trace);

	UObject::ProcessEvent(Func, &Parms);

	Actors_to_Ignore_in_Trace = std::move(Parms.Actors_to_Ignore_in_Trace);

	if (Exposed_to_Sun != nullptr)
		*Exposed_to_Sun = Parms.Exposed_to_Sun;

	if (Exposed_to_Moon != nullptr)
		*Exposed_to_Moon = Parms.Exposed_to_Moon;

	if (Exposed_to_Either != nullptr)
		*Exposed_to_Either = Parms.Exposed_to_Either;

	if (Light_Intensity__Lux_ != nullptr)
		*Light_Intensity__Lux_ = Parms.Light_Intensity__Lux_;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Check If Year is Leap Year
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   Year_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Leap_Year                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Check_If_Year_is_Leap_Year(int32 Year_0, bool* Leap_Year)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Check If Year is Leap Year");

	Params::Ultra_Dynamic_Sky_C_Check_If_Year_is_Leap_Year Parms{};

	Parms.Year_0 = Year_0;

	UObject::ProcessEvent(Func, &Parms);

	if (Leap_Year != nullptr)
		*Leap_Year = Parms.Leap_Year;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Check to Start Volumetric Cloud Light Rays
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Check_to_Start_Volumetric_Cloud_Light_Rays()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Check to Start Volumetric Cloud Light Rays");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cinematic Mode Startup
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Cinematic_Mode_Startup()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cinematic Mode Startup");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Clear Modifiers
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Clear_Modifiers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Clear Modifiers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Client Check Initial Replication
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Client_Check_Initial_Replication()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Client Check Initial Replication");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cloud Coverage Target Mapping
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Mapping                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cloud_Coverage_Target_Mapping(struct FVector* Mapping)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cloud Coverage Target Mapping");

	Params::Ultra_Dynamic_Sky_C_Cloud_Coverage_Target_Mapping Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Mapping != nullptr)
		*Mapping = std::move(Parms.Mapping);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cloud Layer Top and Bottom World Height
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Bottom_World_Height                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Top_World_Height                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cloud_Layer_Top_and_Bottom_World_Height(double* Bottom_World_Height, double* Top_World_Height)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cloud Layer Top and Bottom World Height");

	Params::Ultra_Dynamic_Sky_C_Cloud_Layer_Top_and_Bottom_World_Height Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Bottom_World_Height != nullptr)
		*Bottom_World_Height = Parms.Bottom_World_Height;

	if (Top_World_Height != nullptr)
		*Top_World_Height = Parms.Top_World_Height;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cloud Shadows Cloud Density
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Cloud_Shadows_Cloud_Density()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cloud Shadows Cloud Density");

	Params::Ultra_Dynamic_Sky_C_Cloud_Shadows_Cloud_Density Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cloud Shadows Light Vector And Cancel Value
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FLinearColor*                    Vector                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Cancel_Value                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cloud_Shadows_Light_Vector_And_Cancel_Value(struct FLinearColor* Vector, double* Cancel_Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cloud Shadows Light Vector And Cancel Value");

	Params::Ultra_Dynamic_Sky_C_Cloud_Shadows_Light_Vector_And_Cancel_Value Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Vector != nullptr)
		*Vector = std::move(Parms.Vector);

	if (Cancel_Value != nullptr)
		*Cancel_Value = Parms.Cancel_Value;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cloud Shadows Parent Material
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TSoftObjectPtr<class UMaterialInterface>*Mat                                                    (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cloud_Shadows_Parent_Material(TSoftObjectPtr<class UMaterialInterface>* Mat)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cloud Shadows Parent Material");

	Params::Ultra_Dynamic_Sky_C_Cloud_Shadows_Parent_Material Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Mat != nullptr)
		*Mat = Parms.Mat;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cloud Texture Pan Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Scale                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cloud_Texture_Pan_Scale(double* Scale)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cloud Texture Pan Scale");

	Params::Ultra_Dynamic_Sky_C_Cloud_Texture_Pan_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Scale != nullptr)
		*Scale = Parms.Scale;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cloud Texture Velocity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FVector AUltra_Dynamic_Sky_C::Cloud_Texture_Velocity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cloud Texture Velocity");

	Params::Ultra_Dynamic_Sky_C_Cloud_Texture_Velocity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Cloud Wisp Gradient Vector
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Cloud_Wisp_Gradient_Vector(struct FLinearColor* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Cloud Wisp Gradient Vector");

	Params::Ultra_Dynamic_Sky_C_Cloud_Wisp_Gradient_Vector Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Clouds Time of Day Factor
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Clouds_Time_of_Day_Factor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Clouds Time of Day Factor");

	Params::Ultra_Dynamic_Sky_C_Clouds_Time_of_Day_Factor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Combined Night Brightness
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Combined_Night_Brightness(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Combined Night Brightness");

	Params::Ultra_Dynamic_Sky_C_Combined_Night_Brightness Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Composite Context Vector
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Composite_Context_Vector(struct FVector* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Composite Context Vector");

	Params::Ultra_Dynamic_Sky_C_Composite_Context_Vector Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Composite Weather Vector
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Composite_Weather_Vector(struct FVector* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Composite Weather Vector");

	Params::Ultra_Dynamic_Sky_C_Composite_Weather_Vector Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Configure Directional Light with Feature Toggle
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Toggle                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UDirectionalLightComponent*&      Selected_Component_Variable                            (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, InstancedReference, ReferenceParm, NoDestructor, HasGetValueTypeHash)
// class UDirectionalLightComponent*       Built_in_Light_Component                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class ADirectionalLight*                Custom_Light_Actor                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// int32                                   Atmospheric_Index                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EComponentMobility                      Mobility                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Light_Visibility                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Configure_Directional_Light_with_Feature_Toggle(bool Toggle, class UDirectionalLightComponent*& Selected_Component_Variable, class UDirectionalLightComponent* Built_in_Light_Component, class ADirectionalLight* Custom_Light_Actor, int32 Atmospheric_Index, EComponentMobility Mobility, bool Light_Visibility)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Configure Directional Light with Feature Toggle");

	Params::Ultra_Dynamic_Sky_C_Configure_Directional_Light_with_Feature_Toggle Parms{};

	Parms.Toggle = Toggle;
	Parms.Selected_Component_Variable = Selected_Component_Variable;
	Parms.Built_in_Light_Component = Built_in_Light_Component;
	Parms.Custom_Light_Actor = Custom_Light_Actor;
	Parms.Atmospheric_Index = Atmospheric_Index;
	Parms.Mobility = Mobility;
	Parms.Light_Visibility = Light_Visibility;

	UObject::ProcessEvent(Func, &Parms);

	Selected_Component_Variable = Parms.Selected_Component_Variable;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Configure Height Fog with Feature Toggle
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Configure_Height_Fog_with_Feature_Toggle()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Configure Height Fog with Feature Toggle");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Configure Sky Light with Feature Toggle
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Configure_Sky_Light_with_Feature_Toggle()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Configure Sky Light with Feature Toggle");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construct Cloud Shadows MID
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construct_Cloud_Shadows_MID()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construct Cloud Shadows MID");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construct Inside Cloud Fog
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construct_Inside_Cloud_Fog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construct Inside Cloud Fog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construct Lens Flare
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construct_Lens_Flare()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construct Lens Flare");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construct Overcast Turbulence
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construct_Overcast_Turbulence()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construct Overcast Turbulence");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construct Path Tracer Fog
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construct_Path_Tracer_Fog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construct Path Tracer Fog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construct Sky Sphere and Material
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construct_Sky_Sphere_and_Material()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construct Sky Sphere and Material");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construct Space Layer
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construct_Space_Layer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construct Space Layer");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construct Volumetric Aurora
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construct_Volumetric_Aurora()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construct Volumetric Aurora");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construct Volumetric Clouds
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construct_Volumetric_Clouds()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construct Volumetric Clouds");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Construction Script Function
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Construction_Script_Function()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Construction Script Function");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Create Post Process Components
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Create_Post_Process_Components()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Create Post Process Components");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Create UDS and UDW State for Saving
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FUDS_and_UDW_State*              Packaged_State                                         (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Create_UDS_and_UDW_State_for_Saving(struct FUDS_and_UDW_State* Packaged_State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Create UDS and UDW State for Saving");

	Params::Ultra_Dynamic_Sky_C_Create_UDS_and_UDW_State_for_Saving Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Packaged_State != nullptr)
		*Packaged_State = std::move(Parms.Packaged_State);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current 2D Cloud Tint
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_2D_Cloud_Tint()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current 2D Cloud Tint");

	Params::Ultra_Dynamic_Sky_C_Current_2D_Cloud_Tint Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Aurora Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Aurora_Intensity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Aurora Intensity");

	Params::Ultra_Dynamic_Sky_C_Current_Aurora_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Base Clouds Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Base_Clouds_Scale()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Base Clouds Scale");

	Params::Ultra_Dynamic_Sky_C_Current_Base_Clouds_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Cloud Fog Post Process Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Cloud_Fog_Post_Process_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Cloud Fog Post Process Color");

	Params::Ultra_Dynamic_Sky_C_Current_Cloud_Fog_Post_Process_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Cloud Wisps Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Cloud_Wisps_Color(struct FLinearColor* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Cloud Wisps Color");

	Params::Ultra_Dynamic_Sky_C_Current_Cloud_Wisps_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Directional Inscattering Exponent
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Directional_Inscattering_Exponent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Directional Inscattering Exponent");

	Params::Ultra_Dynamic_Sky_C_Current_Directional_Inscattering_Exponent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Event Date
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FDateTime                        ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, NoDestructor, HasGetValueTypeHash)

struct FDateTime AUltra_Dynamic_Sky_C::Current_Event_Date()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Event Date");

	Params::Ultra_Dynamic_Sky_C_Current_Event_Date Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Exposure Bias
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Bias                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Exposure_Bias(double* Bias)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Exposure Bias");

	Params::Ultra_Dynamic_Sky_C_Current_Exposure_Bias Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Bias != nullptr)
		*Bias = Parms.Bias;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Fog Density
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Fog_Density()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Fog Density");

	Params::Ultra_Dynamic_Sky_C_Current_Fog_Density Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Fog Directional Inscattering Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Directional_Inscattering_Color                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Fog_Directional_Inscattering_Color(struct FLinearColor* Directional_Inscattering_Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Fog Directional Inscattering Color");

	Params::Ultra_Dynamic_Sky_C_Current_Fog_Directional_Inscattering_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Directional_Inscattering_Color != nullptr)
		*Directional_Inscattering_Color = std::move(Parms.Directional_Inscattering_Color);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Fog Inscattering Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Inscattering_Color                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Fog_Inscattering_Color(struct FLinearColor* Inscattering_Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Fog Inscattering Color");

	Params::Ultra_Dynamic_Sky_C_Current_Fog_Inscattering_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Inscattering_Color != nullptr)
		*Inscattering_Color = std::move(Parms.Inscattering_Color);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Hour as Integer
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32*                                  Hour                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Hour_as_Integer(int32* Hour)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Hour as Integer");

	Params::Ultra_Dynamic_Sky_C_Current_Hour_as_Integer Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Hour != nullptr)
		*Hour = Parms.Hour;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Lerp to Simplified Clouds
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Alpha                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Lerp_to_Simplified_Clouds(double* Alpha)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Lerp to Simplified Clouds");

	Params::Ultra_Dynamic_Sky_C_Current_Lerp_to_Simplified_Clouds Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Alpha != nullptr)
		*Alpha = Parms.Alpha;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Light Pollution
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Light_Pollution()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Light Pollution");

	Params::Ultra_Dynamic_Sky_C_Current_Light_Pollution Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Lit Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Lit_Intensity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Lit Intensity");

	Params::Ultra_Dynamic_Sky_C_Current_Lit_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Max Trace Distance
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Max_Trace_Distance(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Max Trace Distance");

	Params::Ultra_Dynamic_Sky_C_Current_Max_Trace_Distance Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Mie Anisotropy
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Mie_Anisotropy()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Mie Anisotropy");

	Params::Ultra_Dynamic_Sky_C_Current_Mie_Anisotropy Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Mie Scattering Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Mie_Scattering_Scale()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Mie Scattering Scale");

	Params::Ultra_Dynamic_Sky_C_Current_Mie_Scattering_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Minute as Integer
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32*                                  Minute                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Minute_as_Integer(int32* Minute)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Minute as Integer");

	Params::Ultra_Dynamic_Sky_C_Current_Minute_as_Integer Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Minute != nullptr)
		*Minute = Parms.Minute;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Month Lengths
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   Year_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// TArray<int32>*                          Lengths                                                (Parm, OutParm)

void AUltra_Dynamic_Sky_C::Current_Month_Lengths(int32 Year_0, TArray<int32>* Lengths)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Month Lengths");

	Params::Ultra_Dynamic_Sky_C_Current_Month_Lengths Parms{};

	Parms.Year_0 = Year_0;

	UObject::ProcessEvent(Func, &Parms);

	if (Lengths != nullptr)
		*Lengths = std::move(Parms.Lengths);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Moon Light Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Color                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Moon_Light_Color(struct FLinearColor* Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Moon Light Color");

	Params::Ultra_Dynamic_Sky_C_Current_Moon_Light_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Color != nullptr)
		*Color = std::move(Parms.Color);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Moon Light Material Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Moon_Light_Material_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Moon Light Material Color");

	Params::Ultra_Dynamic_Sky_C_Current_Moon_Light_Material_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Moon Lit Percent
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Moon_Lit_Percent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Moon Lit Percent");

	Params::Ultra_Dynamic_Sky_C_Current_Moon_Lit_Percent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Moon Material Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Moon_Material_Intensity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Moon Material Intensity");

	Params::Ultra_Dynamic_Sky_C_Current_Moon_Material_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Moon Phase Angle
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Moon_Phase_Angle()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Moon Phase Angle");

	Params::Ultra_Dynamic_Sky_C_Current_Moon_Phase_Angle Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Moon Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Scale                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Moon_Scale(double* Scale)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Moon Scale");

	Params::Ultra_Dynamic_Sky_C_Current_Moon_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Scale != nullptr)
		*Scale = Parms.Scale;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Moons Cloud Mask
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Moons_Cloud_Mask()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Moons Cloud Mask");

	Params::Ultra_Dynamic_Sky_C_Current_Moons_Cloud_Mask Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Night Sky Glow
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Night_Sky_Glow()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Night Sky Glow");

	Params::Ultra_Dynamic_Sky_C_Current_Night_Sky_Glow Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Overall Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Overall_Intensity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Overall Intensity");

	Params::Ultra_Dynamic_Sky_C_Current_Overall_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Overcast Turbulence Strength
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Overcast_Turbulence_Strength()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Overcast Turbulence Strength");

	Params::Ultra_Dynamic_Sky_C_Current_Overcast_Turbulence_Strength Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Rayleigh Scattering Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Rayleigh_Scattering_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Rayleigh Scattering Color");

	Params::Ultra_Dynamic_Sky_C_Current_Rayleigh_Scattering_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sky Ambient Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Sky_Ambient_Color                                      (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Sky_Ambient_Color(struct FLinearColor* Sky_Ambient_Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sky Ambient Color");

	Params::Ultra_Dynamic_Sky_C_Current_Sky_Ambient_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Sky_Ambient_Color != nullptr)
		*Sky_Ambient_Color = std::move(Parms.Sky_Ambient_Color);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sky Atmosphere Absorption Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Sky_Atmosphere_Absorption_Color(struct FLinearColor* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sky Atmosphere Absorption Color");

	Params::Ultra_Dynamic_Sky_C_Current_Sky_Atmosphere_Absorption_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sky Atmosphere Luminance
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Sky_Atmosphere_Luminance()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sky Atmosphere Luminance");

	Params::Ultra_Dynamic_Sky_C_Current_Sky_Atmosphere_Luminance Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sky Light Lower Hemisphere Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Sky_Light_Lower_Hemisphere_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sky Light Lower Hemisphere Color");

	Params::Ultra_Dynamic_Sky_C_Current_Sky_Light_Lower_Hemisphere_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Solar Eclipse Values
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Eclipse_Percent_0                                      (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Tint_Color                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Solar_Eclipse_Values(double* Eclipse_Percent_0, struct FLinearColor* Tint_Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Solar Eclipse Values");

	Params::Ultra_Dynamic_Sky_C_Current_Solar_Eclipse_Values Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Eclipse_Percent_0 != nullptr)
		*Eclipse_Percent_0 = Parms.Eclipse_Percent_0;

	if (Tint_Color != nullptr)
		*Tint_Color = std::move(Parms.Tint_Color);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Space Layer Brightness
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Space_Layer_Brightness()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Space Layer Brightness");

	Params::Ultra_Dynamic_Sky_C_Current_Space_Layer_Brightness Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Stars Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Stars_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Stars Color");

	Params::Ultra_Dynamic_Sky_C_Current_Stars_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sun Disk Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Sun_Disk_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sun Disk Color");

	Params::Ultra_Dynamic_Sky_C_Current_Sun_Disk_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sun Disk Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Sun_Disk_Intensity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sun Disk Intensity");

	Params::Ultra_Dynamic_Sky_C_Current_Sun_Disk_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sun Light Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Sun_Light_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sun Light Color");

	Params::Ultra_Dynamic_Sky_C_Current_Sun_Light_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sun Light Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Sun_Light_Intensity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sun Light Intensity");

	Params::Ultra_Dynamic_Sky_C_Current_Sun_Light_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sun Moon Cache Delta
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Sun_Moon_Cache_Delta()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sun Moon Cache Delta");

	Params::Ultra_Dynamic_Sky_C_Current_Sun_Moon_Cache_Delta Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sun Radius
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Sun_Radius()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sun Radius");

	Params::Ultra_Dynamic_Sky_C_Current_Sun_Radius Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sun Specular Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Sun_Specular_Scale()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sun Specular Scale");

	Params::Ultra_Dynamic_Sky_C_Current_Sun_Specular_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sunrise Event Time
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Sunrise_Event_Time()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sunrise Event Time");

	Params::Ultra_Dynamic_Sky_C_Current_Sunrise_Event_Time Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Sunset Event Time
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Sunset_Event_Time()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Sunset Event Time");

	Params::Ultra_Dynamic_Sky_C_Current_Sunset_Event_Time Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Sunrise Event State
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool*                                   Sun_Up                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Sunrise_Event_State(bool* Sun_Up)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Sunrise Event State");

	Params::Ultra_Dynamic_Sky_C_Sunrise_Event_State Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Sun_Up != nullptr)
		*Sun_Up = Parms.Sun_Up;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current View Sample Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_View_Sample_Scale(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current View Sample Scale");

	Params::Ultra_Dynamic_Sky_C_Current_View_Sample_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Volumetric Cloud Albedo
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Current_Volumetric_Cloud_Albedo()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Volumetric Cloud Albedo");

	Params::Ultra_Dynamic_Sky_C_Current_Volumetric_Cloud_Albedo Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Volumetric Cloud Inner Emit Limit
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Volumetric_Cloud_Inner_Emit_Limit(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Volumetric Cloud Inner Emit Limit");

	Params::Ultra_Dynamic_Sky_C_Current_Volumetric_Cloud_Inner_Emit_Limit Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Volumetric Cloud Macro Variation
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Volumetric_Cloud_Macro_Variation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Volumetric Cloud Macro Variation");

	Params::Ultra_Dynamic_Sky_C_Current_Volumetric_Cloud_Macro_Variation Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Volumetric Cloud Multiscattering Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Volumetric_Cloud_Multiscattering_Intensity(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Volumetric Cloud Multiscattering Intensity");

	Params::Ultra_Dynamic_Sky_C_Current_Volumetric_Cloud_Multiscattering_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Volumetric Cloud Multiscattering Occlusion
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Occlusion                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Volumetric_Cloud_Multiscattering_Occlusion(double* Occlusion)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Volumetric Cloud Multiscattering Occlusion");

	Params::Ultra_Dynamic_Sky_C_Current_Volumetric_Cloud_Multiscattering_Occlusion Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Occlusion != nullptr)
		*Occlusion = Parms.Occlusion;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Volumetric Cloud Shadow Tracing Distance
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Volumetric_Cloud_Shadow_Tracing_Distance(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Volumetric Cloud Shadow Tracing Distance");

	Params::Ultra_Dynamic_Sky_C_Current_Volumetric_Cloud_Shadow_Tracing_Distance Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Volumetric Cloud Sky Atmo Contribution
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Volumetric_Cloud_Sky_Atmo_Contribution()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Volumetric Cloud Sky Atmo Contribution");

	Params::Ultra_Dynamic_Sky_C_Current_Volumetric_Cloud_Sky_Atmo_Contribution Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Volumetric Clouds Density
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    Layer_1                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Current_Volumetric_Clouds_Density(bool Layer_1, double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Volumetric Clouds Density");

	Params::Ultra_Dynamic_Sky_C_Current_Volumetric_Clouds_Density Parms{};

	Parms.Layer_1 = Layer_1;

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Volumetric Multiscattering Phase 1
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Volumetric_Multiscattering_Phase_1()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Volumetric Multiscattering Phase 1");

	Params::Ultra_Dynamic_Sky_C_Current_Volumetric_Multiscattering_Phase_1 Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Current Wisps Opacity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Current_Wisps_Opacity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Current Wisps Opacity");

	Params::Ultra_Dynamic_Sky_C_Current_Wisps_Opacity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Date and Time to Year Progress
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   Month_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Day_0                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Time                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Days_from_Start_of_Year                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Date_and_Time_to_Year_Progress(int32 Month_0, int32 Day_0, double Time, double* Days_from_Start_of_Year)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Date and Time to Year Progress");

	Params::Ultra_Dynamic_Sky_C_Date_and_Time_to_Year_Progress Parms{};

	Parms.Month_0 = Month_0;
	Parms.Day_0 = Day_0;
	Parms.Time = Time;

	UObject::ProcessEvent(Func, &Parms);

	if (Days_from_Start_of_Year != nullptr)
		*Days_from_Start_of_Year = Parms.Days_from_Start_of_Year;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Day Count at the Start of a Month
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   Year_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Month_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Count                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Day_Count_at_the_Start_of_a_Month(int32 Year_0, int32 Month_0, int32* Count)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Day Count at the Start of a Month");

	Params::Ultra_Dynamic_Sky_C_Day_Count_at_the_Start_of_a_Month Parms{};

	Parms.Year_0 = Year_0;
	Parms.Month_0 = Month_0;

	UObject::ProcessEvent(Func, &Parms);

	if (Count != nullptr)
		*Count = Parms.Count;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Day Ended
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Day_Ended()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Day Ended");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Days Since J2000
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   Input_Day                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Input_Month                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Input_Year                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Days                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Days_Since_J2000(int32 Input_Day, int32 Input_Month, int32 Input_Year, int32* Days)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Days Since J2000");

	Params::Ultra_Dynamic_Sky_C_Days_Since_J2000 Parms{};

	Parms.Input_Day = Input_Day;
	Parms.Input_Month = Input_Month;
	Parms.Input_Year = Input_Year;

	UObject::ProcessEvent(Func, &Parms);

	if (Days != nullptr)
		*Days = Parms.Days;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Days Since Y1D1M1
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Input_Day                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Input_Month                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Input_Year                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Days                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Days_Since_Y1D1M1(int32 Input_Day, int32 Input_Month, int32 Input_Year, int32* Days)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Days Since Y1D1M1");

	Params::Ultra_Dynamic_Sky_C_Days_Since_Y1D1M1 Parms{};

	Parms.Input_Day = Input_Day;
	Parms.Input_Month = Input_Month;
	Parms.Input_Year = Input_Year;

	UObject::ProcessEvent(Func, &Parms);

	if (Days != nullptr)
		*Days = Parms.Days;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Dimming Directional Lights
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool AUltra_Dynamic_Sky_C::Dimming_Directional_Lights()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Dimming Directional Lights");

	Params::Ultra_Dynamic_Sky_C_Dimming_Directional_Lights Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Directional Inscattering Multiplier
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Multiplier                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Directional_Inscattering_Multiplier(double* Multiplier)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Directional Inscattering Multiplier");

	Params::Ultra_Dynamic_Sky_C_Directional_Inscattering_Multiplier Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Multiplier != nullptr)
		*Multiplier = Parms.Multiplier;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Directional Light Dimming
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Directional_Light_Dimming(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Directional Light Dimming");

	Params::Ultra_Dynamic_Sky_C_Directional_Light_Dimming Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Directional Source Angle
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Disk_Scale                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Max_Scale_Factor                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Scale_Setting                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Directional_Source_Angle(double Disk_Scale, double Max_Scale_Factor, double Scale_Setting, double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Directional Source Angle");

	Params::Ultra_Dynamic_Sky_C_Directional_Source_Angle Parms{};

	Parms.Disk_Scale = Disk_Scale;
	Parms.Max_Scale_Factor = Max_Scale_Factor;
	Parms.Scale_Setting = Scale_Setting;

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Disable Instant Exposure
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Disable_Instant_Exposure()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Disable Instant Exposure");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Editor Tick
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Editor_Camera_Location                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FRotator&                  Editor_Camera_Rotation_0                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// bool                                    Sequencer_Open                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Editor_Tick(const struct FVector& Editor_Camera_Location, const struct FRotator& Editor_Camera_Rotation_0, bool Sequencer_Open)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Editor Tick");

	Params::Ultra_Dynamic_Sky_C_Editor_Tick Parms{};

	Parms.Editor_Camera_Location = std::move(Editor_Camera_Location);
	Parms.Editor_Camera_Rotation_0 = std::move(Editor_Camera_Rotation_0);
	Parms.Sequencer_Open = Sequencer_Open;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Editor Update from Weather
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Editor_Update_from_Weather()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Editor Update from Weather");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.ExecuteUbergraph_Ultra_Dynamic_Sky
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::ExecuteUbergraph_Ultra_Dynamic_Sky(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "ExecuteUbergraph_Ultra_Dynamic_Sky");

	Params::Ultra_Dynamic_Sky_C_ExecuteUbergraph_Ultra_Dynamic_Sky Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Filtered Moon Light Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Unfiltered                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Intensity                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Filtered_Moon_Light_Intensity(double Unfiltered, double* Intensity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Filtered Moon Light Intensity");

	Params::Ultra_Dynamic_Sky_C_Filtered_Moon_Light_Intensity Parms{};

	Parms.Unfiltered = Unfiltered;

	UObject::ProcessEvent(Func, &Parms);

	if (Intensity != nullptr)
		*Intensity = Parms.Intensity;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Sunrise Times
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Sunrise_Times()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Sunrise Times");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Finish Time Transition
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Finish_Time_Transition()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Finish Time Transition");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Fire Editor Dispatchers
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Fire_Editor_Dispatchers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Fire Editor Dispatchers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Fog and Dust Shadow Value
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Fog_and_Dust_Shadow_Value()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Fog and Dust Shadow Value");

	Params::Ultra_Dynamic_Sky_C_Fog_and_Dust_Shadow_Value Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Fog Height Falloff
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Fog_Height_Falloff()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Fog Height Falloff");

	Params::Ultra_Dynamic_Sky_C_Fog_Height_Falloff Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Fog Start Distance
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Fog_Start_Distance()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Fog Start Distance");

	Params::Ultra_Dynamic_Sky_C_Fog_Start_Distance Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Force Startup
// (BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Force_Startup()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Force Startup");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Force Valid Day
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Force_Valid_Day()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Force Valid Day");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Cached Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_CachedProperties                   Property                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Value                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Cached_Color(EUDS_CachedProperties Property, struct FLinearColor* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Cached Color");

	Params::Ultra_Dynamic_Sky_C_Get_Cached_Color Parms{};

	Parms.Property = Property;

	UObject::ProcessEvent(Func, &Parms);

	if (Value != nullptr)
		*Value = std::move(Parms.Value);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Cached Float
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_CachedProperties                   Property                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Value                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Cached_Float(EUDS_CachedProperties Property, double* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Cached Float");

	Params::Ultra_Dynamic_Sky_C_Get_Cached_Float Parms{};

	Parms.Property = Property;

	UObject::ProcessEvent(Func, &Parms);

	if (Value != nullptr)
		*Value = Parms.Value;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Cached Vector
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_CachedProperties                   Property                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector*                         Value                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Cached_Vector(EUDS_CachedProperties Property, struct FVector* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Cached Vector");

	Params::Ultra_Dynamic_Sky_C_Get_Cached_Vector Parms{};

	Parms.Property = Property;

	UObject::ProcessEvent(Func, &Parms);

	if (Value != nullptr)
		*Value = std::move(Parms.Value);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Cloud Coverage 0-10
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Cloud_Coverage_0                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Cloud_Coverage_0_10(double* Cloud_Coverage_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Cloud Coverage 0-10");

	Params::Ultra_Dynamic_Sky_C_Get_Cloud_Coverage_0_10 Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Cloud_Coverage_0 != nullptr)
		*Cloud_Coverage_0 = Parms.Cloud_Coverage_0;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Cloud Coverage 0-3
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Cloud_Coverage_0                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Cloud_Coverage_0_3(double* Cloud_Coverage_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Cloud Coverage 0-3");

	Params::Ultra_Dynamic_Sky_C_Get_Cloud_Coverage_0_3 Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Cloud_Coverage_0 != nullptr)
		*Cloud_Coverage_0 = Parms.Cloud_Coverage_0;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Cloud Coverage Local
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 Local_Height                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Cloud_Coverage_Local(double* Local_Height)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Cloud Coverage Local");

	Params::Ultra_Dynamic_Sky_C_Get_Cloud_Coverage_Local Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Local_Height != nullptr)
		*Local_Height = Parms.Local_Height;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Current Sky Light Color and Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 Out_Intensity                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Out_Color                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Current_Sky_Light_Color_and_Intensity(double* Out_Intensity, struct FLinearColor* Out_Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Current Sky Light Color and Intensity");

	Params::Ultra_Dynamic_Sky_C_Get_Current_Sky_Light_Color_and_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out_Intensity != nullptr)
		*Out_Intensity = Parms.Out_Intensity;

	if (Out_Color != nullptr)
		*Out_Color = std::move(Parms.Out_Color);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Current Volumetric Cloud Extinction Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Top                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Current_Volumetric_Cloud_Extinction_Scale(double* Top)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Current Volumetric Cloud Extinction Scale");

	Params::Ultra_Dynamic_Sky_C_Get_Current_Volumetric_Cloud_Extinction_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Top != nullptr)
		*Top = Parms.Top;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get DateTime
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FDateTime*                       Current_Date_and_Time                                  (Parm, OutParm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_DateTime(struct FDateTime* Current_Date_and_Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get DateTime");

	Params::Ultra_Dynamic_Sky_C_Get_DateTime Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Current_Date_and_Time != nullptr)
		*Current_Date_and_Time = std::move(Parms.Current_Date_and_Time);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Day of the Week
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32*                                  Index_0                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FString*                          Name_0                                                 (Parm, OutParm, ZeroConstructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Day_of_the_Week(int32* Index_0, class FString* Name_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Day of the Week");

	Params::Ultra_Dynamic_Sky_C_Get_Day_of_the_Week Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Index_0 != nullptr)
		*Index_0 = Parms.Index_0;

	if (Name_0 != nullptr)
		*Name_0 = std::move(Parms.Name_0);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Editor Camera Location
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Get_Editor_Camera_Location()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Editor Camera Location");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Inverted Global Occlusion
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Get_Inverted_Global_Occlusion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Inverted Global Occlusion");

	Params::Ultra_Dynamic_Sky_C_Get_Inverted_Global_Occlusion Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Manual Target Change Speed
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FVector&                         Old_Vector                                             (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   New_Vector                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Manual_Target_Change_Speed(struct FVector& Old_Vector, const struct FVector& New_Vector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Manual Target Change Speed");

	Params::Ultra_Dynamic_Sky_C_Get_Manual_Target_Change_Speed Parms{};

	Parms.Old_Vector = std::move(Old_Vector);
	Parms.New_Vector = std::move(New_Vector);

	UObject::ProcessEvent(Func, &Parms);

	Old_Vector = std::move(Parms.Old_Vector);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Nearby Cloud Cells To Load Asynchronously
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Get_Nearby_Cloud_Cells_To_Load_Asynchronously()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Nearby Cloud Cells To Load Asynchronously");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Runtime Camera Location
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Get_Runtime_Camera_Location()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Runtime Camera Location");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Sky MID Parent Material Instance
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TSoftObjectPtr<class UMaterialInterface>ReturnValue                                            (Parm, OutParm, ReturnParm, HasGetValueTypeHash)

TSoftObjectPtr<class UMaterialInterface> AUltra_Dynamic_Sky_C::Get_Sky_MID_Parent_Material_Instance()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Sky MID Parent Material Instance");

	Params::Ultra_Dynamic_Sky_C_Get_Sky_MID_Parent_Material_Instance Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Starting Cloud Painting Actors
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Get_Starting_Cloud_Painting_Actors()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Starting Cloud Painting Actors");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get TimeCode
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FTimecode*                       Time                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_TimeCode(struct FTimecode* Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get TimeCode");

	Params::Ultra_Dynamic_Sky_C_Get_TimeCode Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Time != nullptr)
		*Time = std::move(Parms.Time);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get UDW Reference
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Get_UDW_Reference()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get UDW Reference");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Get Volumetric Cloud Emissive Colors
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FLinearColor*                    Bottom                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Top                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Get_Volumetric_Cloud_Emissive_Colors(struct FLinearColor* Bottom, struct FLinearColor* Top)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Get Volumetric Cloud Emissive Colors");

	Params::Ultra_Dynamic_Sky_C_Get_Volumetric_Cloud_Emissive_Colors Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Bottom != nullptr)
		*Bottom = std::move(Parms.Bottom);

	if (Top != nullptr)
		*Top = std::move(Parms.Top);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.MS to Time of Day
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   Hours                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Minutes                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Seconds                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Miliseconds                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Time                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::MS_to_Time_of_Day(int32 Hours, int32 Minutes, int32 Seconds, int32 Miliseconds, double* Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "MS to Time of Day");

	Params::Ultra_Dynamic_Sky_C_MS_to_Time_of_Day Parms{};

	Parms.Hours = Hours;
	Parms.Minutes = Minutes;
	Parms.Seconds = Seconds;
	Parms.Miliseconds = Miliseconds;

	UObject::ProcessEvent(Func, &Parms);

	if (Time != nullptr)
		*Time = Parms.Time;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Hard Reset Cache
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Hard_Reset_Cache()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Hard Reset Cache");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Increment Cache Timer
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Increment_Cache_Timer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Increment Cache Timer");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Increment Cloud Movement Cache
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Increment_Cloud_Movement_Cache()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Increment Cloud Movement Cache");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Increment Time of Day Forward
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Amount                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Increment_Time_of_Day_Forward(double Amount)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Increment Time of Day Forward");

	Params::Ultra_Dynamic_Sky_C_Increment_Time_of_Day_Forward Parms{};

	Parms.Amount = Amount;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Initialize Occlusion
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Initialize_Occlusion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Initialize Occlusion");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Inside Outside Cloud Layer
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Inside_Outside_Cloud_Layer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Inside Outside Cloud Layer");

	Params::Ultra_Dynamic_Sky_C_Inside_Outside_Cloud_Layer Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Is Cached Value Changing
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// EUDS_CachedProperties                   Enum                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Yes                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Is_Cached_Value_Changing(EUDS_CachedProperties Enum, bool* Yes)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Is Cached Value Changing");

	Params::Ultra_Dynamic_Sky_C_Is_Cached_Value_Changing Parms{};

	Parms.Enum = Enum;

	UObject::ProcessEvent(Func, &Parms);

	if (Yes != nullptr)
		*Yes = Parms.Yes;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Is Date and Time in Daylight Savings Time
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool AUltra_Dynamic_Sky_C::Is_Date_and_Time_in_Daylight_Savings_Time()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Is Date and Time in Daylight Savings Time");

	Params::Ultra_Dynamic_Sky_C_Is_Date_and_Time_in_Daylight_Savings_Time Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Is Directional Light Casting Shadows
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    Force_Disabled                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Light_Vector                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Yes                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Is_Directional_Light_Casting_Shadows(bool Force_Disabled, const struct FVector& Light_Vector, bool* Yes)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Is Directional Light Casting Shadows");

	Params::Ultra_Dynamic_Sky_C_Is_Directional_Light_Casting_Shadows Parms{};

	Parms.Force_Disabled = Force_Disabled;
	Parms.Light_Vector = std::move(Light_Vector);

	UObject::ProcessEvent(Func, &Parms);

	if (Yes != nullptr)
		*Yes = Parms.Yes;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Is it Daytime?
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool*                                   Yes                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Is_it_Daytime_(bool* Yes)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Is it Daytime?");

	Params::Ultra_Dynamic_Sky_C_Is_it_Daytime_ Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Yes != nullptr)
		*Yes = Parms.Yes;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Lens Flare Parent Material
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TSoftObjectPtr<class UMaterialInterface>*Mat                                                    (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Lens_Flare_Parent_Material(TSoftObjectPtr<class UMaterialInterface>* Mat)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Lens Flare Parent Material");

	Params::Ultra_Dynamic_Sky_C_Lens_Flare_Parent_Material Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Mat != nullptr)
		*Mat = Parms.Mat;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Lights Update Degree Threshold Test
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// const struct FRotator&                  World_Rotation                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// class USceneComponent*                  Light                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool AUltra_Dynamic_Sky_C::Lights_Update_Degree_Threshold_Test(const struct FRotator& World_Rotation, class USceneComponent* Light)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Lights Update Degree Threshold Test");

	Params::Ultra_Dynamic_Sky_C_Lights_Update_Degree_Threshold_Test Parms{};

	Parms.World_Rotation = std::move(World_Rotation);
	Parms.Light = Light;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Load Required Assets
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Load_Required_Assets()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Load Required Assets");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Monitor for Changes
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Monitor_for_Changes()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Monitor for Changes");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Moon Effective Illumination 0-1
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Moon_Effective_Illumination_0_1()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Moon Effective Illumination 0-1");

	Params::Ultra_Dynamic_Sky_C_Moon_Effective_Illumination_0_1 Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Moon Light Specular Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Moon_Light_Specular_Scale()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Moon Light Specular Scale");

	Params::Ultra_Dynamic_Sky_C_Moon_Light_Specular_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Moon Light Volumetric Scattering Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Moon_Light_Volumetric_Scattering_Intensity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Moon Light Volumetric Scattering Intensity");

	Params::Ultra_Dynamic_Sky_C_Moon_Light_Volumetric_Scattering_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Moon Phase Light Multiplier
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Without_Light_Brightness                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Moon_Phase_Light_Multiplier(double* Without_Light_Brightness)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Moon Phase Light Multiplier");

	Params::Ultra_Dynamic_Sky_C_Moon_Phase_Light_Multiplier Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Without_Light_Brightness != nullptr)
		*Without_Light_Brightness = Parms.Without_Light_Brightness;

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Moon Z Vector
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Moon_Z_Vector(struct FVector* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Moon Z Vector");

	Params::Ultra_Dynamic_Sky_C_Moon_Z_Vector Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Night Filter
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    Cached                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Night_Filter(bool Cached)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Night Filter");

	Params::Ultra_Dynamic_Sky_C_Night_Filter Parms{};

	Parms.Cached = Cached;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Night Filtered Night Brightness
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Multiplier                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Night_Filtered_Night_Brightness(double* Multiplier)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Night Filtered Night Brightness");

	Params::Ultra_Dynamic_Sky_C_Night_Filtered_Night_Brightness Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Multiplier != nullptr)
		*Multiplier = Parms.Multiplier;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Notify of Removed Cloud Paint Container
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Notify_of_Removed_Cloud_Paint_Container()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Notify of Removed Cloud Paint Container");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Number of Days in a Year
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   Year_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Count                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Number_of_Days_in_a_Year(int32 Year_0, int32* Count)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Number of Days in a Year");

	Params::Ultra_Dynamic_Sky_C_Number_of_Days_in_a_Year Parms{};

	Parms.Year_0 = Year_0;

	UObject::ProcessEvent(Func, &Parms);

	if (Count != nullptr)
		*Count = Parms.Count;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Offset Date
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Offset                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Offset_Date(int32 Offset)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Offset Date");

	Params::Ultra_Dynamic_Sky_C_Offset_Date Parms{};

	Parms.Offset = Offset;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Offset Date by a Number of Days
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Input_Month                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Input_Day                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Input_Year                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Offset                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Output_Month                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Output_Day                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Output_Year                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Offset_Date_by_a_Number_of_Days(int32 Input_Month, int32 Input_Day, int32 Input_Year, int32 Offset, int32* Output_Month, int32* Output_Day, int32* Output_Year)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Offset Date by a Number of Days");

	Params::Ultra_Dynamic_Sky_C_Offset_Date_by_a_Number_of_Days Parms{};

	Parms.Input_Month = Input_Month;
	Parms.Input_Day = Input_Day;
	Parms.Input_Year = Input_Year;
	Parms.Offset = Offset;

	UObject::ProcessEvent(Func, &Parms);

	if (Output_Month != nullptr)
		*Output_Month = Parms.Output_Month;

	if (Output_Day != nullptr)
		*Output_Day = Parms.Output_Day;

	if (Output_Year != nullptr)
		*Output_Year = Parms.Output_Year;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.OnLoaded_AA91001A446E22425F2F54AAB2159C50
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UObject*                          Loaded                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::OnLoaded_AA91001A446E22425F2F54AAB2159C50(class UObject* Loaded)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "OnLoaded_AA91001A446E22425F2F54AAB2159C50");

	Params::Ultra_Dynamic_Sky_C_OnLoaded_AA91001A446E22425F2F54AAB2159C50 Parms{};

	Parms.Loaded = Loaded;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.OnRep_Replicated Time of Day
// (BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::OnRep_Replicated_Time_of_Day()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "OnRep_Replicated Time of Day");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Open Editor Readme Entry
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FString&                    Entry                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Open_Editor_Readme_Entry(const class FString& Entry)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Open Editor Readme Entry");

	Params::Ultra_Dynamic_Sky_C_Open_Editor_Readme_Entry Parms{};

	Parms.Entry = std::move(Entry);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Open Editor Readme Entry Set
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class FName>&                    Entry                                                  (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void AUltra_Dynamic_Sky_C::Open_Editor_Readme_Entry_Set(TArray<class FName>& Entry)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Open Editor Readme Entry Set");

	Params::Ultra_Dynamic_Sky_C_Open_Editor_Readme_Entry_Set Parms{};

	Parms.Entry = std::move(Entry);

	UObject::ProcessEvent(Func, &Parms);

	Entry = std::move(Parms.Entry);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Overcast Brightness
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Overcast_Brightness()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Overcast Brightness");

	Params::Ultra_Dynamic_Sky_C_Overcast_Brightness Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Overcast Luminance Boost
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Multiplier                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Overcast_Luminance_Boost(double Multiplier, double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Overcast Luminance Boost");

	Params::Ultra_Dynamic_Sky_C_Overcast_Luminance_Boost Parms{};

	Parms.Multiplier = Multiplier;

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Query Project Settings And UDS Version
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Query_Project_Settings_And_UDS_Version()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Query Project Settings And UDS Version");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Randomize Time Of Day
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Randomize_Time_Of_Day()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Randomize Time Of Day");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Recapture Sky Light
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Recapture_Sky_Light()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Recapture Sky Light");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.ReceiveBeginPlay
// (Event, Protected, BlueprintEvent)

void AUltra_Dynamic_Sky_C::ReceiveBeginPlay()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "ReceiveBeginPlay");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.ReceiveEndPlay
// (Event, Protected, BlueprintEvent)
// Parameters:
// EEndPlayReason                          EndPlayReason                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::ReceiveEndPlay(EEndPlayReason EndPlayReason)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "ReceiveEndPlay");

	Params::Ultra_Dynamic_Sky_C_ReceiveEndPlay Parms{};

	Parms.EndPlayReason = EndPlayReason;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.ReceiveTick
// (Event, Public, BlueprintEvent)
// Parameters:
// float                                   DeltaSeconds                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::ReceiveTick(float DeltaSeconds)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "ReceiveTick");

	Params::Ultra_Dynamic_Sky_C_ReceiveTick Parms{};

	Parms.DeltaSeconds = DeltaSeconds;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Release Async Loaded Object
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// TSoftObjectPtr<class UObject>           Object                                                 (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Release_Async_Loaded_Object(TSoftObjectPtr<class UObject> Object)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Release Async Loaded Object");

	Params::Ultra_Dynamic_Sky_C_Release_Async_Loaded_Object Parms{};

	Parms.Object = Object;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Remove Modifier
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Modifier_C*                  Modifier                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Fade_Out_Time                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Remove_Modifier(class UUDS_Modifier_C* Modifier, double Fade_Out_Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Remove Modifier");

	Params::Ultra_Dynamic_Sky_C_Remove_Modifier Parms{};

	Parms.Modifier = Modifier;
	Parms.Fade_Out_Time = Fade_Out_Time;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Replicate Modifier State
// (Net, NetReliable, NetMulticast, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const TArray<class UUDS_Modifier_C*>&   Modifiers                                              (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const TArray<double>&                   Modifier_Alphas_0                                      (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const TArray<double>&                   Modifier_Targets_0                                     (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const TArray<double>&                   Modifier_Speeds_0                                      (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const bool                              Hard_Reset                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Replicate_Modifier_State(const TArray<class UUDS_Modifier_C*>& Modifiers, const TArray<double>& Modifier_Alphas_0, const TArray<double>& Modifier_Targets_0, const TArray<double>& Modifier_Speeds_0, const bool Hard_Reset)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Replicate Modifier State");

	Params::Ultra_Dynamic_Sky_C_Replicate_Modifier_State Parms{};

	Parms.Modifiers = std::move(Modifiers);
	Parms.Modifier_Alphas_0 = std::move(Modifier_Alphas_0);
	Parms.Modifier_Targets_0 = std::move(Modifier_Targets_0);
	Parms.Modifier_Speeds_0 = std::move(Modifier_Speeds_0);
	Parms.Hard_Reset = Hard_Reset;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Restart Real Time Sky Light Capture
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Restart_Real_Time_Sky_Light_Capture()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Restart Real Time Sky Light Capture");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Reverse Day Ended
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Reverse_Day_Ended()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Reverse Day Ended");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Revert Changed Console Variables
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Revert_Changed_Console_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Revert Changed Console Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Revert Modified Properties
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Revert_Modified_Properties()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Revert Modified Properties");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Runtime Or Initializing
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

bool AUltra_Dynamic_Sky_C::Runtime_Or_Initializing()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Runtime Or Initializing");

	Params::Ultra_Dynamic_Sky_C_Runtime_Or_Initializing Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Save Calendar Data
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Save_Calendar_Data()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Save Calendar Data");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Scale Sample Count
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  In                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Scale_Sample_Count(double In, double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Scale Sample Count");

	Params::Ultra_Dynamic_Sky_C_Scale_Sample_Count Parms{};

	Parms.In = In;

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Season Value for Weather from Date and Time
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Day_Offset                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Season                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Season_Value_for_Weather_from_Date_and_Time(int32 Day_Offset, double* Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Season Value for Weather from Date and Time");

	Params::Ultra_Dynamic_Sky_C_Season_Value_for_Weather_from_Date_and_Time Parms{};

	Parms.Day_Offset = Day_Offset;

	UObject::ProcessEvent(Func, &Parms);

	if (Season != nullptr)
		*Season = Parms.Season;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Cloud Coverage
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Cloud_Coverage_0                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Set_Cloud_Coverage(double Cloud_Coverage_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Cloud Coverage");

	Params::Ultra_Dynamic_Sky_C_Set_Cloud_Coverage Parms{};

	Parms.Cloud_Coverage_0 = Cloud_Coverage_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Current Fog Base Colors
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Set_Current_Fog_Base_Colors()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Current Fog Base Colors");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Date and Time
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FDateTime&                 Date_Time                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Set_Date_and_Time(const struct FDateTime& Date_Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Date and Time");

	Params::Ultra_Dynamic_Sky_C_Set_Date_and_Time Parms{};

	Parms.Date_Time = std::move(Date_Time);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Dust Amount
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Dust_Amount_0                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Set_Dust_Amount(double Dust_Amount_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Dust Amount");

	Params::Ultra_Dynamic_Sky_C_Set_Dust_Amount Parms{};

	Parms.Dust_Amount_0 = Dust_Amount_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Fog
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Fog_0                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Set_Fog(double Fog_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Fog");

	Params::Ultra_Dynamic_Sky_C_Set_Fog Parms{};

	Parms.Fog_0 = Fog_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Modifier State
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUDS_Modifier_C*                  Modifier                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// double                                  Alpha                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Set_Modifier_State(class UUDS_Modifier_C* Modifier, double Alpha)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Modifier State");

	Params::Ultra_Dynamic_Sky_C_Set_Modifier_State Parms{};

	Parms.Modifier = Modifier;
	Parms.Alpha = Alpha;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Previous Weather Variables
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Set_Previous_Weather_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Previous Weather Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Startup Variables
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Set_Startup_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Startup Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Sun and Moon Root Rotation
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Set_Sun_and_Moon_Root_Rotation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Sun and Moon Root Rotation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Time Cycle Degrees
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Set_Time_Cycle_Degrees()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Time Cycle Degrees");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Time with Time Code
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FTimecode&                 Time_Code                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Set_Time_with_Time_Code(const struct FTimecode& Time_Code)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Time with Time Code");

	Params::Ultra_Dynamic_Sky_C_Set_Time_with_Time_Code Parms{};

	Parms.Time_Code = std::move(Time_Code);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Unmodified Property Values
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Set_Unmodified_Property_Values()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Unmodified Property Values");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Up Global Volumetric Fog Material
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Set_Up_Global_Volumetric_Fog_Material()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Up Global Volumetric Fog Material");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Set Variables Controlled by Weather
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Set_Variables_Controlled_by_Weather()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Set Variables Controlled by Weather");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Show Editor Warning
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FString&                    Title                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
// const class FString&                    Message                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Show_Editor_Warning(const class FString& Title, const class FString& Message)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Show Editor Warning");

	Params::Ultra_Dynamic_Sky_C_Show_Editor_Warning Parms{};

	Parms.Title = std::move(Title);
	Parms.Message = std::move(Message);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Simplfied Color Sun Scattering
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Simplfied_Color_Sun_Scattering()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Simplfied Color Sun Scattering");

	Params::Ultra_Dynamic_Sky_C_Simplfied_Color_Sun_Scattering Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Simplified Cloud Light Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Simplified_Cloud_Light_Color(struct FLinearColor* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Simplified Cloud Light Color");

	Params::Ultra_Dynamic_Sky_C_Simplified_Cloud_Light_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Simplified Moon Scattering Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor                     ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

struct FLinearColor AUltra_Dynamic_Sky_C::Simplified_Moon_Scattering_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Simplified Moon Scattering Color");

	Params::Ultra_Dynamic_Sky_C_Simplified_Moon_Scattering_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Simplified Sun Glow Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Simplified_Sun_Glow_Color(struct FLinearColor* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Simplified Sun Glow Color");

	Params::Ultra_Dynamic_Sky_C_Simplified_Sun_Glow_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Size Cache Arrays
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Size_Cache_Arrays()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Size Cache Arrays");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Sky Atmosphere Fog Contribution
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Output                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Sky_Atmosphere_Fog_Contribution(double* Output)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Sky Atmosphere Fog Contribution");

	Params::Ultra_Dynamic_Sky_C_Sky_Atmosphere_Fog_Contribution Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Output != nullptr)
		*Output = Parms.Output;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Sky Startup Functions
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Sky_Startup_Functions()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Sky Startup Functions");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Solar Eclipse Circle Mask
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Sun_Angular_Diameter                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Moon_Angular_Diameter                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FVector&                   Moon_Vector                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Moon_Softness                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Fraction_Showing                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Solar_Eclipse_Circle_Mask(double Sun_Angular_Diameter, double Moon_Angular_Diameter, const struct FVector& Moon_Vector, double Moon_Softness, double* Fraction_Showing)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Solar Eclipse Circle Mask");

	Params::Ultra_Dynamic_Sky_C_Solar_Eclipse_Circle_Mask Parms{};

	Parms.Sun_Angular_Diameter = Sun_Angular_Diameter;
	Parms.Moon_Angular_Diameter = Moon_Angular_Diameter;
	Parms.Moon_Vector = std::move(Moon_Vector);
	Parms.Moon_Softness = Moon_Softness;

	UObject::ProcessEvent(Func, &Parms);

	if (Fraction_Showing != nullptr)
		*Fraction_Showing = Parms.Fraction_Showing;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Space Planet Parent MID
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// const struct FUDS_Space_Planet&         Planet                                                 (BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
// TSoftObjectPtr<class UMaterialInterface>*Out                                                    (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Space_Planet_Parent_MID(const struct FUDS_Space_Planet& Planet, TSoftObjectPtr<class UMaterialInterface>* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Space Planet Parent MID");

	Params::Ultra_Dynamic_Sky_C_Space_Planet_Parent_MID Parms{};

	Parms.Planet = std::move(Planet);

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Start Active Timers
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Start_Active_Timers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Start Active Timers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Start Async Loader
// (BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Start_Async_Loader()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Start Async Loader");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Start Editor Tick Handler
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Start_Editor_Tick_Handler()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Start Editor Tick Handler");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Start Instant Exposure
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Start_Instant_Exposure()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Start Instant Exposure");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Start Up UDW If it Exists
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Start_Up_UDW_If_it_Exists()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Start Up UDW If it Exists");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Starting Animate Time of Day Offset
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Starting_Animate_Time_of_Day_Offset()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Starting Animate Time of Day Offset");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Starting Cloud Formation
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Starting_Cloud_Formation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Starting Cloud Formation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Startup Tick
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Startup_Tick()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Startup Tick");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Clouds Lighting Mask
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    XY_Mask                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Static_Clouds_Lighting_Mask(struct FLinearColor* XY_Mask)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Clouds Lighting Mask");

	Params::Ultra_Dynamic_Sky_C_Static_Clouds_Lighting_Mask Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (XY_Mask != nullptr)
		*XY_Mask = std::move(Parms.XY_Mask);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Clouds Tint Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Light                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Shadow                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Static_Clouds_Tint_Color(struct FLinearColor* Light, struct FLinearColor* Shadow)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Clouds Tint Color");

	Params::Ultra_Dynamic_Sky_C_Static_Clouds_Tint_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Light != nullptr)
		*Light = std::move(Parms.Light);

	if (Shadow != nullptr)
		*Shadow = std::move(Parms.Shadow);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Mode Cloud Tick
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Mode_Cloud_Tick()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Mode Cloud Tick");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Mode Startup
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Mode_Startup()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Mode Startup");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - 2D Dynamic Clouds
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___2D_Dynamic_Clouds()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - 2D Dynamic Clouds");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Aurora
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Aurora()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Aurora");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Cloud Movement
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Cloud_Movement()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Cloud Movement");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Cloud Shadows
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Cloud_Shadows()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Cloud Shadows");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Height Fog
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Height_Fog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Height Fog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Lens Flare
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Lens_Flare()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Lens Flare");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Misc
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Misc()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Misc");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Mode Derivatives
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Mode_Derivatives()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Mode Derivatives");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Moon
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Moon()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Moon");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Occlusion
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Occlusion()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Occlusion");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Post Processing
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Post_Processing()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Post Processing");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Simplified Color
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Simplified_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Simplified Color");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Sky Atmosphere
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Sky_Atmosphere()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Sky Atmosphere");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Sky Light
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Sky_Light()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Sky Light");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Sky Material
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Sky_Material()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Sky Material");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Space Layer
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Space_Layer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Space Layer");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Stars
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Stars()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Stars");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Static Clouds
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Static_Clouds()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Static Clouds");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Sun
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Sun()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Sun");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Volumetric Cloud Light Rays
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Volumetric_Cloud_Light_Rays()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Volumetric Cloud Light Rays");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Volumetric Clouds
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Volumetric_Clouds()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Volumetric Clouds");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Static Properties - Water Caustics
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Static_Properties___Water_Caustics()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Static Properties - Water Caustics");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Sun height
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    Cached                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Z                                                      (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Sun_height(bool Cached, double* Z)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Sun height");

	Params::Ultra_Dynamic_Sky_C_Sun_height Parms{};

	Parms.Cached = Cached;

	UObject::ProcessEvent(Func, &Parms);

	if (Z != nullptr)
		*Z = Parms.Z;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Sun Z Vector
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector*                         Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Sun_Z_Vector(struct FVector* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Sun Z Vector");

	Params::Ultra_Dynamic_Sky_C_Sun_Z_Vector Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Swap with Cinematic Runtime Value
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  In                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Cine                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Use_Higher                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Swap_with_Cinematic_Runtime_Value(double In, double Cine, bool Use_Higher, double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Swap with Cinematic Runtime Value");

	Params::Ultra_Dynamic_Sky_C_Swap_with_Cinematic_Runtime_Value Parms{};

	Parms.In = In;
	Parms.Cine = Cine;
	Parms.Use_Higher = Use_Higher;

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Test Point for Painted Cloud Coverage
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Cloud_Coverage_with_Painting                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Test_Point_for_Painted_Cloud_Coverage(const struct FVector& Location, double* Cloud_Coverage_with_Painting)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Test Point for Painted Cloud Coverage");

	Params::Ultra_Dynamic_Sky_C_Test_Point_for_Painted_Cloud_Coverage Parms{};

	Parms.Location = std::move(Location);

	UObject::ProcessEvent(Func, &Parms);

	if (Cloud_Coverage_with_Painting != nullptr)
		*Cloud_Coverage_with_Painting = Parms.Cloud_Coverage_with_Painting;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Three Time Floats
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Day_0                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Dusk                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Night                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Three_Time_Floats(double Day_0, double Dusk, double Night, double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Three Time Floats");

	Params::Ultra_Dynamic_Sky_C_Three_Time_Floats Parms{};

	Parms.Day_0 = Day_0;
	Parms.Dusk = Dusk;
	Parms.Night = Night;

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Tick Function
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Tick_Function()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Tick Function");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Tick Time Transition
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Tick_Time_Transition()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Tick Time Transition");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Time of Day Animation
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Time_of_Day_Animation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Time of Day Animation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Time of Day Offset
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Per_Second                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Time_of_Day_Offset(double* Per_Second)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Time of Day Offset");

	Params::Ultra_Dynamic_Sky_C_Time_of_Day_Offset Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Per_Second != nullptr)
		*Per_Second = Parms.Per_Second;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.S
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Time                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Hour                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Minute                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  Second                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Second_Fraction                                        (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::S(double Time, int32* Hour, int32* Minute, int32* Second, double* Second_Fraction)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "S");

	Params::Ultra_Dynamic_Sky_C_S Parms{};

	Parms.Time = Time;

	UObject::ProcessEvent(Func, &Parms);

	if (Hour != nullptr)
		*Hour = Parms.Hour;

	if (Minute != nullptr)
		*Minute = Parms.Minute;

	if (Second != nullptr)
		*Second = Parms.Second;

	if (Second_Fraction != nullptr)
		*Second_Fraction = Parms.Second_Fraction;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Timed Override with New Changes
// (BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Timed_Override_with_New_Changes()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Timed Override with New Changes");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Toggle Post Process Material
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Index_0                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Enabled                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Toggle_Post_Process_Material(int32 Index_0, bool Enabled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Toggle Post Process Material");

	Params::Ultra_Dynamic_Sky_C_Toggle_Post_Process_Material Parms{};

	Parms.Index_0 = Index_0;
	Parms.Enabled = Enabled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Total Time Elapsed
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

double AUltra_Dynamic_Sky_C::Total_Time_Elapsed()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Total Time Elapsed");

	Params::Ultra_Dynamic_Sky_C_Total_Time_Elapsed Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Transition Sky Light Intensity
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  New_Sky_Light_Intensity_Multiplier                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Transition_Time                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Transition_Sky_Light_Intensity(double New_Sky_Light_Intensity_Multiplier, double Transition_Time)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Transition Sky Light Intensity");

	Params::Ultra_Dynamic_Sky_C_Transition_Sky_Light_Intensity Parms{};

	Parms.New_Sky_Light_Intensity_Multiplier = New_Sky_Light_Intensity_Multiplier;
	Parms.Transition_Time = Transition_Time;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Transition Time of Day
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  New_Time_of_Day                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Transition_Duration__Seconds_                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EEasingFunc                             Easing_Function                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double                                  Easing_Exponent                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Allow_Time_Going_Backwards                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Transition_Time_of_Day(double New_Time_of_Day, double Transition_Duration__Seconds_, EEasingFunc Easing_Function, double Easing_Exponent, bool Allow_Time_Going_Backwards)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Transition Time of Day");

	Params::Ultra_Dynamic_Sky_C_Transition_Time_of_Day Parms{};

	Parms.New_Time_of_Day = New_Time_of_Day;
	Parms.Transition_Duration__Seconds_ = Transition_Duration__Seconds_;
	Parms.Easing_Function = Easing_Function;
	Parms.Easing_Exponent = Easing_Exponent;
	Parms.Allow_Time_Going_Backwards = Allow_Time_Going_Backwards;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Twilight Brightness Falloff
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Z                                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Scale                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Twilight_Brightness_Falloff(double Z, double* Scale)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Twilight Brightness Falloff");

	Params::Ultra_Dynamic_Sky_C_Twilight_Brightness_Falloff Parms{};

	Parms.Z = Z;

	UObject::ProcessEvent(Func, &Parms);

	if (Scale != nullptr)
		*Scale = Parms.Scale;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.UDW Editor Update
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::UDW_Editor_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "UDW Editor Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.UDW Instant Update
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::UDW_Instant_Update()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "UDW Instant Update");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Unfiltered Moon Light Intensity
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Unfiltered_Moon_Light_Intensity(double* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Unfiltered Moon Light Intensity");

	Params::Ultra_Dynamic_Sky_C_Unfiltered_Moon_Light_Intensity Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = Parms.Out;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Active Variables
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Active_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Active Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Cache Group Boolean
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUDS_Cache_Group                        Group                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// TArray<EUDS_CachedProperties>&          Properties_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void AUltra_Dynamic_Sky_C::Update_Cache_Group_Boolean(EUDS_Cache_Group Group, TArray<EUDS_CachedProperties>& Properties_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Cache Group Boolean");

	Params::Ultra_Dynamic_Sky_C_Update_Cache_Group_Boolean Parms{};

	Parms.Group = Group;
	Parms.Properties_0 = std::move(Properties_0);

	UObject::ProcessEvent(Func, &Parms);

	Properties_0 = std::move(Parms.Properties_0);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Cloud Coverage After Painting
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Cloud_Coverage_After_Painting()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Cloud Coverage After Painting");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Cloud Movement
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Cloud_Movement()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Cloud Movement");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Common Derivatives
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Common_Derivatives()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Common Derivatives");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Current Volumetric Clouds MID
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Current_Volumetric_Clouds_MID()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Current Volumetric Clouds MID");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Directional Light Rotations
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Directional_Light_Rotations()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Directional Light Rotations");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Dynamic Sky Light Multiplier
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Dynamic_Sky_Light_Multiplier()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Dynamic Sky Light Multiplier");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Global Volumetric Fog Material
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Global_Volumetric_Fog_Material()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Global Volumetric Fog Material");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update High Priority Properties
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_High_Priority_Properties()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update High Priority Properties");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Lens Flare
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Lens_Flare()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Lens Flare");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Low Priority Properties
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Low_Priority_Properties()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Low Priority Properties");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Lunar Phase
// (Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Hour                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Update_Lunar_Phase(int32 Hour)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Lunar Phase");

	Params::Ultra_Dynamic_Sky_C_Update_Lunar_Phase Parms{};

	Parms.Hour = Hour;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Non-Cached Active Properties
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Non_Cached_Active_Properties()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Non-Cached Active Properties");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Overcast Turbulence
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Overcast_Turbulence()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Overcast Turbulence");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Painted Cloud Coverage Target
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Painted_Cloud_Coverage_Target()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Painted Cloud Coverage Target");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Path Tracer Fog
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Path_Tracer_Fog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Path Tracer Fog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Post Process Blend Weights
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Post_Process_Blend_Weights()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Post Process Blend Weights");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Replicated Time
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Replicated_Time()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Replicated Time");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Settings Based on Scalability
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Settings_Based_on_Scalability()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Settings Based on Scalability");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Sky Atmosphere Location
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Update_Sky_Atmosphere_Location(const struct FVector& Location)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Sky Atmosphere Location");

	Params::Ultra_Dynamic_Sky_C_Update_Sky_Atmosphere_Location Parms{};

	Parms.Location = std::move(Location);

	UObject::ProcessEvent(Func, &Parms);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Space Layer Vectors
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Space_Layer_Vectors()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Space Layer Vectors");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Static Variables
// (Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Static_Variables()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Static Variables");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Total Days Elapsed
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Total_Days_Elapsed()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Total Days Elapsed");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Volumetric Cloud Light Rays
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Volumetric_Cloud_Light_Rays()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Volumetric Cloud Light Rays");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Update Water Level Parameter
// (Protected, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::Update_Water_Level_Parameter()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Update Water Level Parameter");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.UserConstructionScript
// (Event, Public, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::UserConstructionScript()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "UserConstructionScript");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Volumetric Cloud Ambient Light Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FLinearColor*                    Bottom                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Top                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Volumetric_Cloud_Ambient_Light_Color(struct FLinearColor* Bottom, struct FLinearColor* Top)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Volumetric Cloud Ambient Light Color");

	Params::Ultra_Dynamic_Sky_C_Volumetric_Cloud_Ambient_Light_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Bottom != nullptr)
		*Bottom = std::move(Parms.Bottom);

	if (Top != nullptr)
		*Top = std::move(Parms.Top);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Volumetric Cloud Base Cloud Height
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Base_Cloud_Height_0                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Volumetric_Cloud_Base_Cloud_Height(double* Base_Cloud_Height_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Volumetric Cloud Base Cloud Height");

	Params::Ultra_Dynamic_Sky_C_Volumetric_Cloud_Base_Cloud_Height Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Base_Cloud_Height_0 != nullptr)
		*Base_Cloud_Height_0 = Parms.Base_Cloud_Height_0;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Volumetric Cloud First Layer Top Altitude
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Cloud_Top_Altitude                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Volumetric_Cloud_First_Layer_Top_Altitude(double* Cloud_Top_Altitude)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Volumetric Cloud First Layer Top Altitude");

	Params::Ultra_Dynamic_Sky_C_Volumetric_Cloud_First_Layer_Top_Altitude Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Cloud_Top_Altitude != nullptr)
		*Cloud_Top_Altitude = Parms.Cloud_Top_Altitude;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Volumetric Cloud Floor Variation
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 Height_Clear                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Height_Cloudy                                          (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Color                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Volumetric_Cloud_Floor_Variation(double* Height_Clear, double* Height_Cloudy, double* Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Volumetric Cloud Floor Variation");

	Params::Ultra_Dynamic_Sky_C_Volumetric_Cloud_Floor_Variation Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Height_Clear != nullptr)
		*Height_Clear = Parms.Height_Clear;

	if (Height_Cloudy != nullptr)
		*Height_Cloudy = Parms.Height_Cloudy;

	if (Color != nullptr)
		*Color = Parms.Color;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Volumetric Cloud Layer Height
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double                                  Base_Cloud_Height_0                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// double*                                 Layer_Height                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Volumetric_Cloud_Layer_Height(double Base_Cloud_Height_0, double* Layer_Height)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Volumetric Cloud Layer Height");

	Params::Ultra_Dynamic_Sky_C_Volumetric_Cloud_Layer_Height Parms{};

	Parms.Base_Cloud_Height_0 = Base_Cloud_Height_0;

	UObject::ProcessEvent(Func, &Parms);

	if (Layer_Height != nullptr)
		*Layer_Height = Parms.Layer_Height;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Volumetric Cloud Layer Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Layer_Scale                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Volumetric_Cloud_Layer_Scale(double* Layer_Scale)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Volumetric Cloud Layer Scale");

	Params::Ultra_Dynamic_Sky_C_Volumetric_Cloud_Layer_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Layer_Scale != nullptr)
		*Layer_Scale = Parms.Layer_Scale;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Volumetric Clouds Parent Materials
// (Protected, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TSoftObjectPtr<class UMaterialInterface>*Simplified                                             (Parm, OutParm, HasGetValueTypeHash)
// TSoftObjectPtr<class UMaterialInterface>*Complex                                                (Parm, OutParm, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Volumetric_Clouds_Parent_Materials(TSoftObjectPtr<class UMaterialInterface>* Simplified, TSoftObjectPtr<class UMaterialInterface>* Complex)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Volumetric Clouds Parent Materials");

	Params::Ultra_Dynamic_Sky_C_Volumetric_Clouds_Parent_Materials Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Simplified != nullptr)
		*Simplified = Parms.Simplified;

	if (Complex != nullptr)
		*Complex = Parms.Complex;
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Volumetric Clouds SubNoise Scales
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FLinearColor*                    High                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Low                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Volumetric_Clouds_SubNoise_Scales(struct FLinearColor* High, struct FLinearColor* Low)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Volumetric Clouds SubNoise Scales");

	Params::Ultra_Dynamic_Sky_C_Volumetric_Clouds_SubNoise_Scales Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (High != nullptr)
		*High = std::move(Parms.High);

	if (Low != nullptr)
		*Low = std::move(Parms.Low);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.Volumetric Light Ray Strength and Color
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// double*                                 Ray_Strength                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Ray_Color                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::Volumetric_Light_Ray_Strength_and_Color(double* Ray_Strength, struct FLinearColor* Ray_Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "Volumetric Light Ray Strength and Color");

	Params::Ultra_Dynamic_Sky_C_Volumetric_Light_Ray_Strength_and_Color Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Ray_Strength != nullptr)
		*Ray_Strength = Parms.Ray_Strength;

	if (Ray_Color != nullptr)
		*Ray_Color = std::move(Parms.Ray_Color);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.World Space to Drawn Target Pixel Space
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// const struct FVector2D&                 In                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector2D*                       Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void AUltra_Dynamic_Sky_C::World_Space_to_Drawn_Target_Pixel_Space(const struct FVector2D& In, struct FVector2D* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "World Space to Drawn Target Pixel Space");

	Params::Ultra_Dynamic_Sky_C_World_Space_to_Drawn_Target_Pixel_Space Parms{};

	Parms.In = std::move(In);

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Aurora
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Aurora()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Aurora");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C. Offline Rendering
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::_Offline_Rendering()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", " Offline Rendering");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Cloud Movement
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Cloud_Movement()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Cloud Movement");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Cloud Shadows
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Cloud_Shadows()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Cloud Shadows");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Cloud Wisps
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Cloud_Wisps()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Cloud Wisps");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Configuring for Performance
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Configuring_for_Performance()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Configuring for Performance");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Considerations for Mobile
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Considerations_for_Mobile()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Considerations for Mobile");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Directional Light
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Directional_Light()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Directional Light");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Dust
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Dust()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Dust");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Exposure
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Exposure()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Exposure");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Fog Color
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Fog_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Fog Color");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Fog Density
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Fog_Density()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Fog Density");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Interior Adjustments
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Interior_Adjustments()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Interior Adjustments");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Moon
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Moon()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Moon");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Post Processing
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Post_Processing()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Post Processing");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Screen Space Light Shafts
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Screen_Space_Light_Shafts()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Screen Space Light Shafts");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Simplified Color
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Simplified_Color()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Simplified Color");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Simulation
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Simulation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Simulation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Sky Atmosphere
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Sky_Atmosphere()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Sky Atmosphere");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Sky Glow
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Sky_Glow()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Sky Glow");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Sky Light
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Sky_Light()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Sky Light");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Sky Modifiers
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Sky_Modifiers()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Sky Modifiers");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Space Layer
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Space_Layer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Space Layer");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Stars
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Stars()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Stars");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Sun
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Sun()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Sun");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Sun Lens Flare
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Sun_Lens_Flare()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Sun Lens Flare");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 UDS Documentation
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__UDS_Documentation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 UDS Documentation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Underwater Caustics
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Underwater_Caustics()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Underwater Caustics");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Volumetric Cloud Light Rays
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Volumetric_Cloud_Light_Rays()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Volumetric Cloud Light Rays");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Volumetric Cloud Painting
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Volumetric_Cloud_Painting()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Volumetric Cloud Painting");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘 Volumetric Fog
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::__Volumetric_Fog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘 Volumetric Fog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘2D Dynamic Clouds
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::_2D_Dynamic_Clouds()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘2D Dynamic Clouds");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘Static Clouds
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::_Static_Clouds()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘Static Clouds");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘Time of Day
// (Private, HasDefaults, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::_Time_of_Day()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘Time of Day");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Ultra_Dynamic_Sky.Ultra_Dynamic_Sky_C.📘Volumetric Clouds
// (Private, BlueprintCallable, BlueprintEvent)

void AUltra_Dynamic_Sky_C::_Volumetric_Clouds()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Ultra_Dynamic_Sky_C", "📘Volumetric Clouds");

	UObject::ProcessEvent(Func, nullptr);
}

}

