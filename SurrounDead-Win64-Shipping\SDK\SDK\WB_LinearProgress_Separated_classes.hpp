﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_LinearProgress_Separated

#include "Basic.hpp"

#include "Slate_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_LinearProgress_Separated.WB_LinearProgress_Separated_C
// 0x00D0 (0x0390 - 0x02C0)
class UWB_LinearProgress_Separated_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UHorizontalBox*                         HB_Backgrounds;                                    // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         HB_Marquees;                                       // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         HB_ProgressBars;                                   // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           VB_Backgrounds;                                    // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           VB_Marquees;                                       // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           VB_ProgressBars;                                   // 0x02F0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TArray<class UWB_Image_Raw_C*>                Segments_BG;                                       // 0x02F8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	TArray<class UWB_PB_Raw_C*>                   Segments_PB;                                       // 0x0308(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	int32                                         NumSegments;                                       // 0x0318(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31C[0x4];                                      // 0x031C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Spacing;                                           // 0x0320(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Size;                                              // 0x0328(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           BackgroundColor;                                   // 0x0338(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Percent;                                           // 0x0348(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           FillColor;                                         // 0x0350(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bAbsoluteFill;                                     // 0x0360(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_361[0x7];                                      // 0x0361(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UWB_PB_Raw_C*>                   Segments_Marquees;                                 // 0x0368(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	EProgressBarFillType                          FillType;                                          // 0x0378(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsFillFromCenter;                                 // 0x0379(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseBackgroundBlur;                                // 0x037A(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_37B[0x5];                                      // 0x037B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        BlurStrength;                                      // 0x0380(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseShader;                                        // 0x0388(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void AddSegmentsBackground(const struct FLinearColor& Color, bool bUseBackgroundBlur_0, double BlurStrength_0);
	void AddSegmentsMarquee();
	void AddSegmentsProgressBar(int32 NumSegments_0, double Spacing_0, const struct FVector2D& Size_0, const struct FLinearColor& FillColor_0, EProgressBarFillType FillType_0, bool bIsFillFromCenter_0, bool bUseShader_0);
	void ClearSegments_Background();
	void ClearSegments_Marquee();
	void ClearSegments_ProgressBar();
	void ExecuteUbergraph_WB_LinearProgress_Separated(int32 EntryPoint);
	double FindCurrentSegmentPercentValue();
	double FindMaxPaddingValue_Horizontal(double Tolerance);
	double FindMaxPaddingValue_Vertical(double Tolerance);
	int32 FindPercentCurrentSegment();
	double GetSegmentSteps();
	void SetAbsoluteFillMethod(bool bAbsoluteFill_0);
	void SetMarqueeDrawAs(ESlateBrushDrawType DrawAs);
	void SetMarqueeImage(class UObject* Image);
	void SetMarqueeImageSize(const struct FVector2D& ImageSize);
	void SetMarqueeTiling(ESlateBrushTileType Tiling);
	void SetMarqueeTint(const struct FLinearColor& Tint);
	void SetPercent(double Percent_0);
	void SetUseMarquee(bool bSetUseMarquee);
	void UpdateProgressChangeColor(const struct FLinearColor& NewColor, double InterpSpeed);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_LinearProgress_Separated_C">();
	}
	static class UWB_LinearProgress_Separated_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_LinearProgress_Separated_C>();
	}
};
static_assert(alignof(UWB_LinearProgress_Separated_C) == 0x000008, "Wrong alignment on UWB_LinearProgress_Separated_C");
static_assert(sizeof(UWB_LinearProgress_Separated_C) == 0x000390, "Wrong size on UWB_LinearProgress_Separated_C");
static_assert(offsetof(UWB_LinearProgress_Separated_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_LinearProgress_Separated_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, HB_Backgrounds) == 0x0002C8, "Member 'UWB_LinearProgress_Separated_C::HB_Backgrounds' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, HB_Marquees) == 0x0002D0, "Member 'UWB_LinearProgress_Separated_C::HB_Marquees' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, HB_ProgressBars) == 0x0002D8, "Member 'UWB_LinearProgress_Separated_C::HB_ProgressBars' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, VB_Backgrounds) == 0x0002E0, "Member 'UWB_LinearProgress_Separated_C::VB_Backgrounds' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, VB_Marquees) == 0x0002E8, "Member 'UWB_LinearProgress_Separated_C::VB_Marquees' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, VB_ProgressBars) == 0x0002F0, "Member 'UWB_LinearProgress_Separated_C::VB_ProgressBars' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, Segments_BG) == 0x0002F8, "Member 'UWB_LinearProgress_Separated_C::Segments_BG' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, Segments_PB) == 0x000308, "Member 'UWB_LinearProgress_Separated_C::Segments_PB' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, NumSegments) == 0x000318, "Member 'UWB_LinearProgress_Separated_C::NumSegments' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, Spacing) == 0x000320, "Member 'UWB_LinearProgress_Separated_C::Spacing' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, Size) == 0x000328, "Member 'UWB_LinearProgress_Separated_C::Size' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, BackgroundColor) == 0x000338, "Member 'UWB_LinearProgress_Separated_C::BackgroundColor' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, Percent) == 0x000348, "Member 'UWB_LinearProgress_Separated_C::Percent' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, FillColor) == 0x000350, "Member 'UWB_LinearProgress_Separated_C::FillColor' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, bAbsoluteFill) == 0x000360, "Member 'UWB_LinearProgress_Separated_C::bAbsoluteFill' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, Segments_Marquees) == 0x000368, "Member 'UWB_LinearProgress_Separated_C::Segments_Marquees' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, FillType) == 0x000378, "Member 'UWB_LinearProgress_Separated_C::FillType' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, bIsFillFromCenter) == 0x000379, "Member 'UWB_LinearProgress_Separated_C::bIsFillFromCenter' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, bUseBackgroundBlur) == 0x00037A, "Member 'UWB_LinearProgress_Separated_C::bUseBackgroundBlur' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, BlurStrength) == 0x000380, "Member 'UWB_LinearProgress_Separated_C::BlurStrength' has a wrong offset!");
static_assert(offsetof(UWB_LinearProgress_Separated_C, bUseShader) == 0x000388, "Member 'UWB_LinearProgress_Separated_C::bUseShader' has a wrong offset!");

}

