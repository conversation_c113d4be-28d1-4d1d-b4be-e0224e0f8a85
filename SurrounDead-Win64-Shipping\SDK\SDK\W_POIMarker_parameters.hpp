﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_POIMarker

#include "Basic.hpp"

#include "UMG_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function W_POIMarker.W_POIMarker_C.Construct Marker
// 0x0028 (0x0028 - 0x0000)
struct W_POIMarker_C_Construct_Marker final
{
public:
	bool                                          Temp_bool_Variable;                                // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           Temp_struct_Variable;                              // 0x0004(0x0010)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_Select_Default;                             // 0x0018(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_POIMarker_C_Construct_Marker) == 0x000004, "Wrong alignment on W_POIMarker_C_Construct_Marker");
static_assert(sizeof(W_POIMarker_C_Construct_Marker) == 0x000028, "Wrong size on W_POIMarker_C_Construct_Marker");
static_assert(offsetof(W_POIMarker_C_Construct_Marker, Temp_bool_Variable) == 0x000000, "Member 'W_POIMarker_C_Construct_Marker::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_Construct_Marker, Temp_struct_Variable) == 0x000004, "Member 'W_POIMarker_C_Construct_Marker::Temp_struct_Variable' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_Construct_Marker, CallFunc_Greater_IntInt_ReturnValue) == 0x000014, "Member 'W_POIMarker_C_Construct_Marker::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_Construct_Marker, K2Node_Select_Default) == 0x000018, "Member 'W_POIMarker_C_Construct_Marker::K2Node_Select_Default' has a wrong offset!");

// Function W_POIMarker.W_POIMarker_C.ExecuteUbergraph_W_POIMarker
// 0x0158 (0x0158 - 0x0000)
struct W_POIMarker_C_ExecuteUbergraph_W_POIMarker final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0010(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UW_POIMarkerTooltip_C*                  CallFunc_Create_ReturnValue;                       // 0x0028(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0030(0x0038)(IsPlainOldData, NoDestructor)
	struct FPointerEvent                          K2Node_Event_MouseEvent_1;                         // 0x0068(0x0078)(ConstParm)
	struct FPointerEvent                          K2Node_Event_MouseEvent;                           // 0x00E0(0x0078)(ConstParm)
};
static_assert(alignof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker) == 0x000008, "Wrong alignment on W_POIMarker_C_ExecuteUbergraph_W_POIMarker");
static_assert(sizeof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker) == 0x000158, "Wrong size on W_POIMarker_C_ExecuteUbergraph_W_POIMarker");
static_assert(offsetof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker, EntryPoint) == 0x000000, "Member 'W_POIMarker_C_ExecuteUbergraph_W_POIMarker::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000008, "Member 'W_POIMarker_C_ExecuteUbergraph_W_POIMarker::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker, K2Node_CreateDelegate_OutputDelegate) == 0x000010, "Member 'W_POIMarker_C_ExecuteUbergraph_W_POIMarker::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker, CallFunc_PlayAnimationForward_ReturnValue) == 0x000020, "Member 'W_POIMarker_C_ExecuteUbergraph_W_POIMarker::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker, CallFunc_Create_ReturnValue) == 0x000028, "Member 'W_POIMarker_C_ExecuteUbergraph_W_POIMarker::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker, K2Node_Event_MyGeometry) == 0x000030, "Member 'W_POIMarker_C_ExecuteUbergraph_W_POIMarker::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker, K2Node_Event_MouseEvent_1) == 0x000068, "Member 'W_POIMarker_C_ExecuteUbergraph_W_POIMarker::K2Node_Event_MouseEvent_1' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_ExecuteUbergraph_W_POIMarker, K2Node_Event_MouseEvent) == 0x0000E0, "Member 'W_POIMarker_C_ExecuteUbergraph_W_POIMarker::K2Node_Event_MouseEvent' has a wrong offset!");

// Function W_POIMarker.W_POIMarker_C.Get_ToolTipWidget
// 0x0010 (0x0010 - 0x0000)
struct W_POIMarker_C_Get_ToolTipWidget final
{
public:
	class UWidget*                                ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_POIMarker_C_Get_ToolTipWidget) == 0x000008, "Wrong alignment on W_POIMarker_C_Get_ToolTipWidget");
static_assert(sizeof(W_POIMarker_C_Get_ToolTipWidget) == 0x000010, "Wrong size on W_POIMarker_C_Get_ToolTipWidget");
static_assert(offsetof(W_POIMarker_C_Get_ToolTipWidget, ReturnValue) == 0x000000, "Member 'W_POIMarker_C_Get_ToolTipWidget::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_Get_ToolTipWidget, CallFunc_IsValid_ReturnValue) == 0x000008, "Member 'W_POIMarker_C_Get_ToolTipWidget::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function W_POIMarker.W_POIMarker_C.OnMouseButtonDoubleClick
// 0x0230 (0x0230 - 0x0000)
struct W_POIMarker_C_OnMouseButtonDoubleClick final
{
public:
	struct FGeometry                              InMyGeometry;                                      // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          InMouseEvent;                                      // 0x0038(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00B0(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0168(0x00B8)()
	class UBPC_MinimapSystem_C*                   CallFunc_Get_Minimap_Component_ReturnValue;        // 0x0220(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0228(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_POIMarker_C_OnMouseButtonDoubleClick) == 0x000008, "Wrong alignment on W_POIMarker_C_OnMouseButtonDoubleClick");
static_assert(sizeof(W_POIMarker_C_OnMouseButtonDoubleClick) == 0x000230, "Wrong size on W_POIMarker_C_OnMouseButtonDoubleClick");
static_assert(offsetof(W_POIMarker_C_OnMouseButtonDoubleClick, InMyGeometry) == 0x000000, "Member 'W_POIMarker_C_OnMouseButtonDoubleClick::InMyGeometry' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_OnMouseButtonDoubleClick, InMouseEvent) == 0x000038, "Member 'W_POIMarker_C_OnMouseButtonDoubleClick::InMouseEvent' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_OnMouseButtonDoubleClick, ReturnValue) == 0x0000B0, "Member 'W_POIMarker_C_OnMouseButtonDoubleClick::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_OnMouseButtonDoubleClick, CallFunc_Handled_ReturnValue) == 0x000168, "Member 'W_POIMarker_C_OnMouseButtonDoubleClick::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_OnMouseButtonDoubleClick, CallFunc_Get_Minimap_Component_ReturnValue) == 0x000220, "Member 'W_POIMarker_C_OnMouseButtonDoubleClick::CallFunc_Get_Minimap_Component_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_OnMouseButtonDoubleClick, CallFunc_IsValid_ReturnValue) == 0x000228, "Member 'W_POIMarker_C_OnMouseButtonDoubleClick::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function W_POIMarker.W_POIMarker_C.OnMouseEnter
// 0x00B0 (0x00B0 - 0x0000)
struct W_POIMarker_C_OnMouseEnter final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(W_POIMarker_C_OnMouseEnter) == 0x000008, "Wrong alignment on W_POIMarker_C_OnMouseEnter");
static_assert(sizeof(W_POIMarker_C_OnMouseEnter) == 0x0000B0, "Wrong size on W_POIMarker_C_OnMouseEnter");
static_assert(offsetof(W_POIMarker_C_OnMouseEnter, MyGeometry) == 0x000000, "Member 'W_POIMarker_C_OnMouseEnter::MyGeometry' has a wrong offset!");
static_assert(offsetof(W_POIMarker_C_OnMouseEnter, MouseEvent) == 0x000038, "Member 'W_POIMarker_C_OnMouseEnter::MouseEvent' has a wrong offset!");

// Function W_POIMarker.W_POIMarker_C.OnMouseLeave
// 0x0078 (0x0078 - 0x0000)
struct W_POIMarker_C_OnMouseLeave final
{
public:
	struct FPointerEvent                          MouseEvent;                                        // 0x0000(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(W_POIMarker_C_OnMouseLeave) == 0x000008, "Wrong alignment on W_POIMarker_C_OnMouseLeave");
static_assert(sizeof(W_POIMarker_C_OnMouseLeave) == 0x000078, "Wrong size on W_POIMarker_C_OnMouseLeave");
static_assert(offsetof(W_POIMarker_C_OnMouseLeave, MouseEvent) == 0x000000, "Member 'W_POIMarker_C_OnMouseLeave::MouseEvent' has a wrong offset!");

}

