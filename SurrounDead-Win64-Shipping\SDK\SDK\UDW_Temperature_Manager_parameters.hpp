﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDW_Temperature_Manager

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Calculate Temperature
// 0x01C8 (0x01C8 - 0x0000)
struct UDW_Temperature_Manager_C_Calculate_Temperature final
{
public:
	class UUDS_Weather_Settings_C*                Settings;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        Temperature;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Unconverted_Temperature;                           // 0x0010(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Is<PERSON>lainOldData, NoD<PERSON>ru<PERSON>, HasGetValueTypeHash)
	double                                        Bias_Value;                                        // 0x0018(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Percent_FloatFloat_ReturnValue;           // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Current_Sunset_Event_Time_ReturnValue;    // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_1;      // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Abs_ReturnValue;                          // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Abs_ReturnValue_1;                        // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_2;      // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMin_ReturnValue;                         // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Current_Sunrise_Event_Time_ReturnValue;   // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_3;      // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_4;      // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Abs_ReturnValue_2;                        // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Abs_ReturnValue_3;                        // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMin_ReturnValue_1;                       // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_1;            // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_1;           // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_2;            // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_GetForwardVector_ReturnValue;             // 0x00D8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x00F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X;                            // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0100(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0108(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_3;            // 0x0110(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x0118(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0120(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_3;      // 0x0128(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0130(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_4;      // 0x0138(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_2;        // 0x0140(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0148(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_3;        // 0x0150(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_5;      // 0x0158(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue_1;                     // 0x0160(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_4;            // 0x0168(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_6;      // 0x0170(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_7;      // 0x0178(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_2;           // 0x0180(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_3;           // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_4;           // 0x0190(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_5;           // 0x0198(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue_2;                     // 0x01A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_5;            // 0x01A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_6;           // 0x01B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_7;           // 0x01B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_8;           // 0x01C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Temperature_Manager_C_Calculate_Temperature) == 0x000008, "Wrong alignment on UDW_Temperature_Manager_C_Calculate_Temperature");
static_assert(sizeof(UDW_Temperature_Manager_C_Calculate_Temperature) == 0x0001C8, "Wrong size on UDW_Temperature_Manager_C_Calculate_Temperature");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, Settings) == 0x000000, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::Settings' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, Temperature) == 0x000008, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::Temperature' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, Unconverted_Temperature) == 0x000010, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::Unconverted_Temperature' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, Bias_Value) == 0x000018, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::Bias_Value' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_IsValid_ReturnValue) == 0x000020, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_BreakVector2D_X) == 0x000028, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_BreakVector2D_Y) == 0x000030, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Percent_FloatFloat_ReturnValue) == 0x000038, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Percent_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Current_Sunset_Event_Time_ReturnValue) == 0x000040, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Current_Sunset_Event_Time_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Add_DoubleDouble_ReturnValue) == 0x000048, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000050, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Subtract_DoubleDouble_ReturnValue_1) == 0x000058, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Subtract_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Abs_ReturnValue) == 0x000060, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Abs_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Abs_ReturnValue_1) == 0x000068, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Abs_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Subtract_DoubleDouble_ReturnValue_2) == 0x000070, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Subtract_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_FMin_ReturnValue) == 0x000078, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_FMin_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Current_Sunrise_Event_Time_ReturnValue) == 0x000080, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Current_Sunrise_Event_Time_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_MapRangeClamped_ReturnValue) == 0x000088, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Subtract_DoubleDouble_ReturnValue_3) == 0x000090, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Subtract_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Subtract_DoubleDouble_ReturnValue_4) == 0x000098, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Subtract_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Abs_ReturnValue_2) == 0x0000A0, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Abs_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Abs_ReturnValue_3) == 0x0000A8, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Abs_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_FMin_ReturnValue_1) == 0x0000B0, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_FMin_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_MapRangeClamped_ReturnValue_1) == 0x0000B8, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_MapRangeClamped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Add_DoubleDouble_ReturnValue_1) == 0x0000C0, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Add_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0000C8, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_MapRangeClamped_ReturnValue_2) == 0x0000D0, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_MapRangeClamped_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_GetForwardVector_ReturnValue) == 0x0000D8, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_GetForwardVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x0000F0, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_BreakVector_X) == 0x0000F8, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_BreakVector_Y) == 0x000100, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_BreakVector_Z) == 0x000108, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_MapRangeClamped_ReturnValue_3) == 0x000110, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_MapRangeClamped_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x000118, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000120, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue_3) == 0x000128, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000130, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue_4) == 0x000138, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Divide_DoubleDouble_ReturnValue_2) == 0x000140, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Divide_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_FClamp_ReturnValue) == 0x000148, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Divide_DoubleDouble_ReturnValue_3) == 0x000150, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Divide_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue_5) == 0x000158, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_FClamp_ReturnValue_1) == 0x000160, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_FClamp_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_MapRangeClamped_ReturnValue_4) == 0x000168, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_MapRangeClamped_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue_6) == 0x000170, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue_7) == 0x000178, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Add_DoubleDouble_ReturnValue_2) == 0x000180, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Add_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Add_DoubleDouble_ReturnValue_3) == 0x000188, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Add_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Add_DoubleDouble_ReturnValue_4) == 0x000190, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Add_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Add_DoubleDouble_ReturnValue_5) == 0x000198, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Add_DoubleDouble_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_FClamp_ReturnValue_2) == 0x0001A0, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_FClamp_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_MapRangeClamped_ReturnValue_5) == 0x0001A8, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_MapRangeClamped_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Add_DoubleDouble_ReturnValue_6) == 0x0001B0, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Add_DoubleDouble_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Add_DoubleDouble_ReturnValue_7) == 0x0001B8, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Add_DoubleDouble_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Calculate_Temperature, CallFunc_Add_DoubleDouble_ReturnValue_8) == 0x0001C0, "Member 'UDW_Temperature_Manager_C_Calculate_Temperature::CallFunc_Add_DoubleDouble_ReturnValue_8' has a wrong offset!");

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.ExecuteUbergraph_UDW_Temperature_Manager
// 0x0008 (0x0008 - 0x0000)
struct UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEndPlayReason                                K2Node_Event_EndPlayReason;                        // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager) == 0x000004, "Wrong alignment on UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager");
static_assert(sizeof(UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager) == 0x000008, "Wrong size on UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager");
static_assert(offsetof(UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager, EntryPoint) == 0x000000, "Member 'UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager::EntryPoint' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager, K2Node_Event_EndPlayReason) == 0x000004, "Member 'UDW_Temperature_Manager_C_ExecuteUbergraph_UDW_Temperature_Manager::K2Node_Event_EndPlayReason' has a wrong offset!");

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Get Current Min and Max Temperature
// 0x00F0 (0x00F0 - 0x0000)
struct UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature final
{
public:
	struct FVector2D                              Spring_Min_and_Max;                                // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Summer_Min_and_Max;                                // 0x0010(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Autumn_Min_and_Max;                                // 0x0020(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Winter_Min_and_Max;                                // 0x0030(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Range;                                             // 0x0040(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Temperature;                                   // 0x0050(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Min_Temperature;                                   // 0x0058(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FVector2D>                      Seasons_Min_and_Max_Values;                        // 0x0060(0x0010)(Edit, BlueprintVisible)
	TArray<struct FVector2D>                      K2Node_MakeArray_Array;                            // 0x0070(0x0010)(ReferenceParm)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Array_Get_Item;                           // 0x0088(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0098(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9C[0x4];                                       // 0x009C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_BreakVector2D_X;                          // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B1[0x3];                                       // 0x00B1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x00B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Array_Get_Item_1;                         // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x00C0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x00E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_1;           // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature) == 0x000008, "Wrong alignment on UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature");
static_assert(sizeof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature) == 0x0000F0, "Wrong size on UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Spring_Min_and_Max) == 0x000000, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Spring_Min_and_Max' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Summer_Min_and_Max) == 0x000010, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Summer_Min_and_Max' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Autumn_Min_and_Max) == 0x000020, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Autumn_Min_and_Max' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Winter_Min_and_Max) == 0x000030, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Winter_Min_and_Max' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Range) == 0x000040, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Range' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Max_Temperature) == 0x000050, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Max_Temperature' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Min_Temperature) == 0x000058, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Min_Temperature' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Seasons_Min_and_Max_Values) == 0x000060, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Seasons_Min_and_Max_Values' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, K2Node_MakeArray_Array) == 0x000070, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Temp_int_Array_Index_Variable) == 0x000080, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, Temp_int_Loop_Counter_Variable) == 0x000084, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_Array_Get_Item) == 0x000088, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_Array_Length_ReturnValue) == 0x000098, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_BreakVector2D_X) == 0x0000A0, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_BreakVector2D_Y) == 0x0000A8, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_Less_IntInt_ReturnValue) == 0x0000B0, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_Add_IntInt_ReturnValue) == 0x0000B4, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_Array_Get_Item_1) == 0x0000B8, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_MakeVector2D_ReturnValue) == 0x0000C0, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0000D0, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x0000D8, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_Add_DoubleDouble_ReturnValue) == 0x0000E0, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature, CallFunc_Add_DoubleDouble_ReturnValue_1) == 0x0000E8, "Member 'UDW_Temperature_Manager_C_Get_Current_Min_and_Max_Temperature::CallFunc_Add_DoubleDouble_ReturnValue_1' has a wrong offset!");

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Get Temperature
// 0x0030 (0x0030 - 0x0000)
struct UDW_Temperature_Manager_C_Get_Temperature final
{
public:
	double                                        Out;                                               // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue;         // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Lerp_ReturnValue;                         // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Temperature_Manager_C_Get_Temperature) == 0x000008, "Wrong alignment on UDW_Temperature_Manager_C_Get_Temperature");
static_assert(sizeof(UDW_Temperature_Manager_C_Get_Temperature) == 0x000030, "Wrong size on UDW_Temperature_Manager_C_Get_Temperature");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Temperature, Out) == 0x000000, "Member 'UDW_Temperature_Manager_C_Get_Temperature::Out' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Temperature, CallFunc_GetGameTimeInSeconds_ReturnValue) == 0x000008, "Member 'UDW_Temperature_Manager_C_Get_Temperature::CallFunc_GetGameTimeInSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Temperature, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000010, "Member 'UDW_Temperature_Manager_C_Get_Temperature::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Temperature, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000018, "Member 'UDW_Temperature_Manager_C_Get_Temperature::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Temperature, CallFunc_FClamp_ReturnValue) == 0x000020, "Member 'UDW_Temperature_Manager_C_Get_Temperature::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Get_Temperature, CallFunc_Lerp_ReturnValue) == 0x000028, "Member 'UDW_Temperature_Manager_C_Get_Temperature::CallFunc_Lerp_ReturnValue' has a wrong offset!");

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.ReceiveEndPlay
// 0x0001 (0x0001 - 0x0000)
struct UDW_Temperature_Manager_C_ReceiveEndPlay final
{
public:
	EEndPlayReason                                EndPlayReason;                                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Temperature_Manager_C_ReceiveEndPlay) == 0x000001, "Wrong alignment on UDW_Temperature_Manager_C_ReceiveEndPlay");
static_assert(sizeof(UDW_Temperature_Manager_C_ReceiveEndPlay) == 0x000001, "Wrong size on UDW_Temperature_Manager_C_ReceiveEndPlay");
static_assert(offsetof(UDW_Temperature_Manager_C_ReceiveEndPlay, EndPlayReason) == 0x000000, "Member 'UDW_Temperature_Manager_C_ReceiveEndPlay::EndPlayReason' has a wrong offset!");

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Runtime Start Temperature Manager
// 0x0028 (0x0028 - 0x0000)
struct UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager final
{
public:
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0000(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           CallFunc_K2_SetTimer_ReturnValue;                  // 0x0010(0x0008)(NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_RandomFloatInRange_ReturnValue;           // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_K2_SetTimer_Time_ImplicitCast;            // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager) == 0x000008, "Wrong alignment on UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager");
static_assert(sizeof(UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager) == 0x000028, "Wrong size on UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager");
static_assert(offsetof(UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager, K2Node_CreateDelegate_OutputDelegate) == 0x000000, "Member 'UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager, CallFunc_K2_SetTimer_ReturnValue) == 0x000010, "Member 'UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager::CallFunc_K2_SetTimer_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager, CallFunc_RandomFloatInRange_ReturnValue) == 0x000018, "Member 'UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager::CallFunc_RandomFloatInRange_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager, CallFunc_K2_SetTimer_Time_ImplicitCast) == 0x000020, "Member 'UDW_Temperature_Manager_C_Runtime_Start_Temperature_Manager::CallFunc_K2_SetTimer_Time_ImplicitCast' has a wrong offset!");

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Set Up Temperature Manager
// 0x0010 (0x0010 - 0x0000)
struct UDW_Temperature_Manager_C_Set_Up_Temperature_Manager final
{
public:
	class AUltra_Dynamic_Weather_C*               UDW_0;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Weather_State_0;                                   // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Temperature_Manager_C_Set_Up_Temperature_Manager) == 0x000008, "Wrong alignment on UDW_Temperature_Manager_C_Set_Up_Temperature_Manager");
static_assert(sizeof(UDW_Temperature_Manager_C_Set_Up_Temperature_Manager) == 0x000010, "Wrong size on UDW_Temperature_Manager_C_Set_Up_Temperature_Manager");
static_assert(offsetof(UDW_Temperature_Manager_C_Set_Up_Temperature_Manager, UDW_0) == 0x000000, "Member 'UDW_Temperature_Manager_C_Set_Up_Temperature_Manager::UDW_0' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Set_Up_Temperature_Manager, Weather_State_0) == 0x000008, "Member 'UDW_Temperature_Manager_C_Set_Up_Temperature_Manager::Weather_State_0' has a wrong offset!");

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Update Target Temperature
// 0x0010 (0x0010 - 0x0000)
struct UDW_Temperature_Manager_C_Update_Target_Temperature final
{
public:
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue;         // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Calculate_Temperature_Temperature;        // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Temperature_Manager_C_Update_Target_Temperature) == 0x000008, "Wrong alignment on UDW_Temperature_Manager_C_Update_Target_Temperature");
static_assert(sizeof(UDW_Temperature_Manager_C_Update_Target_Temperature) == 0x000010, "Wrong size on UDW_Temperature_Manager_C_Update_Target_Temperature");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Target_Temperature, CallFunc_GetGameTimeInSeconds_ReturnValue) == 0x000000, "Member 'UDW_Temperature_Manager_C_Update_Target_Temperature::CallFunc_GetGameTimeInSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Target_Temperature, CallFunc_Calculate_Temperature_Temperature) == 0x000008, "Member 'UDW_Temperature_Manager_C_Update_Target_Temperature::CallFunc_Calculate_Temperature_Temperature' has a wrong offset!");

// Function UDW_Temperature_Manager.UDW_Temperature_Manager_C.Update Temperature Range
// 0x0050 (0x0050 - 0x0000)
struct UDW_Temperature_Manager_C_Update_Temperature_Range final
{
public:
	class AActor*                                 CallFunc_GetOwner_ReturnValue;                     // 0x0000(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AWeather_Override_Volume_C*             K2Node_DynamicCast_AsWeather_Override_Volume;      // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AActor*                                 CallFunc_GetOwner_ReturnValue_1;                   // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_Get_Current_Min_and_Max_Temperature_Range; // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Weather_C*               K2Node_DynamicCast_AsUltra_Dynamic_Weather;        // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39[0x7];                                       // 0x0039(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_Get_Current_Min_and_Max_Temperature_Range_1; // 0x0040(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDW_Temperature_Manager_C_Update_Temperature_Range) == 0x000008, "Wrong alignment on UDW_Temperature_Manager_C_Update_Temperature_Range");
static_assert(sizeof(UDW_Temperature_Manager_C_Update_Temperature_Range) == 0x000050, "Wrong size on UDW_Temperature_Manager_C_Update_Temperature_Range");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Temperature_Range, CallFunc_GetOwner_ReturnValue) == 0x000000, "Member 'UDW_Temperature_Manager_C_Update_Temperature_Range::CallFunc_GetOwner_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Temperature_Range, K2Node_DynamicCast_AsWeather_Override_Volume) == 0x000008, "Member 'UDW_Temperature_Manager_C_Update_Temperature_Range::K2Node_DynamicCast_AsWeather_Override_Volume' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Temperature_Range, K2Node_DynamicCast_bSuccess) == 0x000010, "Member 'UDW_Temperature_Manager_C_Update_Temperature_Range::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Temperature_Range, CallFunc_GetOwner_ReturnValue_1) == 0x000018, "Member 'UDW_Temperature_Manager_C_Update_Temperature_Range::CallFunc_GetOwner_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Temperature_Range, CallFunc_Get_Current_Min_and_Max_Temperature_Range) == 0x000020, "Member 'UDW_Temperature_Manager_C_Update_Temperature_Range::CallFunc_Get_Current_Min_and_Max_Temperature_Range' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Temperature_Range, K2Node_DynamicCast_AsUltra_Dynamic_Weather) == 0x000030, "Member 'UDW_Temperature_Manager_C_Update_Temperature_Range::K2Node_DynamicCast_AsUltra_Dynamic_Weather' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Temperature_Range, K2Node_DynamicCast_bSuccess_1) == 0x000038, "Member 'UDW_Temperature_Manager_C_Update_Temperature_Range::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(UDW_Temperature_Manager_C_Update_Temperature_Range, CallFunc_Get_Current_Min_and_Max_Temperature_Range_1) == 0x000040, "Member 'UDW_Temperature_Manager_C_Update_Temperature_Range::CallFunc_Get_Current_Min_and_Max_Temperature_Range_1' has a wrong offset!");

}

