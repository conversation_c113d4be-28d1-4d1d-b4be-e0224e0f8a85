﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_PB_Linear_Target

#include "Basic.hpp"

#include "WB_PB_Linear_Target_classes.hpp"
#include "WB_PB_Linear_Target_parameters.hpp"


namespace SDK
{

// Function WB_PB_Linear_Target.WB_PB_Linear_Target_C.ExecuteUbergraph_WB_PB_Linear_Target
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Linear_Target_C::ExecuteUbergraph_WB_PB_Linear_Target(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Linear_Target_C", "ExecuteUbergraph_WB_PB_Linear_Target");

	Params::WB_PB_Linear_Target_C_ExecuteUbergraph_WB_PB_Linear_Target Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Linear_Target.WB_PB_Linear_Target_C.GetPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// double*                                 Percent                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Linear_Target_C::GetPercent(double* Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Linear_Target_C", "GetPercent");

	Params::WB_PB_Linear_Target_C_GetPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Percent != nullptr)
		*Percent = Parms.Percent;
}


// Function WB_PB_Linear_Target.WB_PB_Linear_Target_C.SetBarFillType
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EProgressBarFillType                    BarFillType                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    bUseShader                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Linear_Target_C::SetBarFillType(EProgressBarFillType BarFillType, bool bUseShader)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Linear_Target_C", "SetBarFillType");

	Params::WB_PB_Linear_Target_C_SetBarFillType Parms{};

	Parms.BarFillType = BarFillType;
	Parms.bUseShader = bUseShader;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Linear_Target.WB_PB_Linear_Target_C.SetDefaultValues
// (BlueprintCallable, BlueprintEvent)

void UWB_PB_Linear_Target_C::SetDefaultValues()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Linear_Target_C", "SetDefaultValues");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_PB_Linear_Target.WB_PB_Linear_Target_C.SetFillColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColor                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Linear_Target_C::SetFillColor(const struct FLinearColor& InColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Linear_Target_C", "SetFillColor");

	Params::WB_PB_Linear_Target_C_SetFillColor Parms{};

	Parms.InColor = std::move(InColor);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_PB_Linear_Target.WB_PB_Linear_Target_C.SetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  InPercent                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_PB_Linear_Target_C::SetPercent(double InPercent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_PB_Linear_Target_C", "SetPercent");

	Params::WB_PB_Linear_Target_C_SetPercent Parms{};

	Parms.InPercent = InPercent;

	UObject::ProcessEvent(Func, &Parms);
}

}

