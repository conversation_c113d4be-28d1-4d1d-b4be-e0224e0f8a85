﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_QuestMarker

#include "Basic.hpp"

#include "W_QuestMarker_classes.hpp"
#include "W_QuestMarker_parameters.hpp"


namespace SDK
{

// Function W_QuestMarker.W_QuestMarker_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_QuestMarker_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_QuestMarker_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_QuestMarker.W_QuestMarker_C.Construct Marker
// (Public, BlueprintCallable, BlueprintEvent)

void UW_QuestMarker_C::Construct_Marker()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_QuestMarker_C", "Construct Marker");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_QuestMarker.W_QuestMarker_C.ExecuteUbergraph_W_QuestMarker
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_QuestMarker_C::ExecuteUbergraph_W_QuestMarker(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_QuestMarker_C", "ExecuteUbergraph_W_QuestMarker");

	Params::W_QuestMarker_C_ExecuteUbergraph_W_QuestMarker Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_QuestMarker.W_QuestMarker_C.Get_Marker_Box_ToolTipWidget
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)

class UWidget* UW_QuestMarker_C::Get_Marker_Box_ToolTipWidget()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_QuestMarker_C", "Get_Marker_Box_ToolTipWidget");

	Params::W_QuestMarker_C_Get_Marker_Box_ToolTipWidget Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function W_QuestMarker.W_QuestMarker_C.OnMouseEnter
// (BlueprintCosmetic, Event, Public, HasOutParams, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UW_QuestMarker_C::OnMouseEnter(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_QuestMarker_C", "OnMouseEnter");

	Params::W_QuestMarker_C_OnMouseEnter Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_QuestMarker.W_QuestMarker_C.OnMouseLeave
// (BlueprintCosmetic, Event, Public, HasOutParams, BlueprintEvent)
// Parameters:
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UW_QuestMarker_C::OnMouseLeave(const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_QuestMarker_C", "OnMouseLeave");

	Params::W_QuestMarker_C_OnMouseLeave Parms{};

	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);
}

}

