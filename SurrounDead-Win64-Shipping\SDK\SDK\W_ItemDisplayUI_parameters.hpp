﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_ItemDisplayUI

#include "Basic.hpp"

#include "GameplayTags_structs.hpp"


namespace SDK::Params
{

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.ExecuteUbergraph_W_ItemDisplayUI
// 0x0048 (0x0048 - 0x0000)
struct W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UJSI_Slot_C*                            K2Node_Event_ItemRef;                              // 0x0008(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 K2Node_Event_ActorRef;                             // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Event_NewWeight;                            // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUserWidget*                            K2Node_Event_Widget;                               // 0x0020(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   K2Node_Event_Name;                                 // 0x0028(0x0018)()
	class UBP_InspectorWindowWidget_C*            K2Node_Event_Inspector;                            // 0x0040(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI");
static_assert(sizeof(W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI) == 0x000048, "Wrong size on W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI");
static_assert(offsetof(W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI, EntryPoint) == 0x000000, "Member 'W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI, K2Node_Event_ItemRef) == 0x000008, "Member 'W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI::K2Node_Event_ItemRef' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI, K2Node_Event_ActorRef) == 0x000010, "Member 'W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI::K2Node_Event_ActorRef' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI, K2Node_Event_NewWeight) == 0x000018, "Member 'W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI::K2Node_Event_NewWeight' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI, K2Node_Event_Widget) == 0x000020, "Member 'W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI::K2Node_Event_Widget' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI, K2Node_Event_Name) == 0x000028, "Member 'W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI::K2Node_Event_Name' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI, K2Node_Event_Inspector) == 0x000040, "Member 'W_ItemDisplayUI_C_ExecuteUbergraph_W_ItemDisplayUI::K2Node_Event_Inspector' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.GetAllAttachments
// 0x0010 (0x0010 - 0x0000)
struct W_ItemDisplayUI_C_GetAllAttachments final
{
public:
	TArray<class FName>                           Attachments;                                       // 0x0000(0x0010)(Parm, OutParm)
};
static_assert(alignof(W_ItemDisplayUI_C_GetAllAttachments) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_GetAllAttachments");
static_assert(sizeof(W_ItemDisplayUI_C_GetAllAttachments) == 0x000010, "Wrong size on W_ItemDisplayUI_C_GetAllAttachments");
static_assert(offsetof(W_ItemDisplayUI_C_GetAllAttachments, Attachments) == 0x000000, "Member 'W_ItemDisplayUI_C_GetAllAttachments::Attachments' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.GetContainerByAttachmentType
// 0x0018 (0x0018 - 0x0000)
struct W_ItemDisplayUI_C_GetContainerByAttachmentType final
{
public:
	struct FGameplayTag                           Type;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        JigContainer;                                      // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         ContainerIndex;                                    // 0x0010(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_GetContainerByAttachmentType) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_GetContainerByAttachmentType");
static_assert(sizeof(W_ItemDisplayUI_C_GetContainerByAttachmentType) == 0x000018, "Wrong size on W_ItemDisplayUI_C_GetContainerByAttachmentType");
static_assert(offsetof(W_ItemDisplayUI_C_GetContainerByAttachmentType, Type) == 0x000000, "Member 'W_ItemDisplayUI_C_GetContainerByAttachmentType::Type' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_GetContainerByAttachmentType, JigContainer) == 0x000008, "Member 'W_ItemDisplayUI_C_GetContainerByAttachmentType::JigContainer' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_GetContainerByAttachmentType, ContainerIndex) == 0x000010, "Member 'W_ItemDisplayUI_C_GetContainerByAttachmentType::ContainerIndex' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.GetDropWidget
// 0x0008 (0x0008 - 0x0000)
struct W_ItemDisplayUI_C_GetDropWidget final
{
public:
	class UDropItemBackGwidget_C*                 DropWRef;                                          // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_GetDropWidget) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_GetDropWidget");
static_assert(sizeof(W_ItemDisplayUI_C_GetDropWidget) == 0x000008, "Wrong size on W_ItemDisplayUI_C_GetDropWidget");
static_assert(offsetof(W_ItemDisplayUI_C_GetDropWidget, DropWRef) == 0x000000, "Member 'W_ItemDisplayUI_C_GetDropWidget::DropWRef' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.GetJSIContainerByPlayerSlots
// 0x0020 (0x0020 - 0x0000)
struct W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots final
{
public:
	struct FGameplayTag                           Slot_0;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
	class UJSIContainer_C*                        Container;                                         // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UJSI_Slot_C*                            EquippedItem;                                      // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          IsPending_;                                        // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots");
static_assert(sizeof(W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots) == 0x000020, "Wrong size on W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots");
static_assert(offsetof(W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots, Slot_0) == 0x000000, "Member 'W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots::Slot_0' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots, Container) == 0x000008, "Member 'W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots::Container' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots, EquippedItem) == 0x000010, "Member 'W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots::EquippedItem' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots, IsPending_) == 0x000018, "Member 'W_ItemDisplayUI_C_GetJSIContainerByPlayerSlots::IsPending_' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.GetListOfNonAddContainers
// 0x0010 (0x0010 - 0x0000)
struct W_ItemDisplayUI_C_GetListOfNonAddContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_ItemDisplayUI_C_GetListOfNonAddContainers) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_GetListOfNonAddContainers");
static_assert(sizeof(W_ItemDisplayUI_C_GetListOfNonAddContainers) == 0x000010, "Wrong size on W_ItemDisplayUI_C_GetListOfNonAddContainers");
static_assert(offsetof(W_ItemDisplayUI_C_GetListOfNonAddContainers, Containers) == 0x000000, "Member 'W_ItemDisplayUI_C_GetListOfNonAddContainers::Containers' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.GetLootContent
// 0x0008 (0x0008 - 0x0000)
struct W_ItemDisplayUI_C_GetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_GetLootContent) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_GetLootContent");
static_assert(sizeof(W_ItemDisplayUI_C_GetLootContent) == 0x000008, "Wrong size on W_ItemDisplayUI_C_GetLootContent");
static_assert(offsetof(W_ItemDisplayUI_C_GetLootContent, Widget) == 0x000000, "Member 'W_ItemDisplayUI_C_GetLootContent::Widget' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.GetValidReloadContainers
// 0x0010 (0x0010 - 0x0000)
struct W_ItemDisplayUI_C_GetValidReloadContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
};
static_assert(alignof(W_ItemDisplayUI_C_GetValidReloadContainers) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_GetValidReloadContainers");
static_assert(sizeof(W_ItemDisplayUI_C_GetValidReloadContainers) == 0x000010, "Wrong size on W_ItemDisplayUI_C_GetValidReloadContainers");
static_assert(offsetof(W_ItemDisplayUI_C_GetValidReloadContainers, Containers) == 0x000000, "Member 'W_ItemDisplayUI_C_GetValidReloadContainers::Containers' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.JigSetLootContent
// 0x0020 (0x0020 - 0x0000)
struct W_ItemDisplayUI_C_JigSetLootContent final
{
public:
	class UUserWidget*                            Widget;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FText                                   Name_0;                                            // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(W_ItemDisplayUI_C_JigSetLootContent) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_JigSetLootContent");
static_assert(sizeof(W_ItemDisplayUI_C_JigSetLootContent) == 0x000020, "Wrong size on W_ItemDisplayUI_C_JigSetLootContent");
static_assert(offsetof(W_ItemDisplayUI_C_JigSetLootContent, Widget) == 0x000000, "Member 'W_ItemDisplayUI_C_JigSetLootContent::Widget' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_JigSetLootContent, Name_0) == 0x000008, "Member 'W_ItemDisplayUI_C_JigSetLootContent::Name_0' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.JSIOnWeightUpdated
// 0x0008 (0x0008 - 0x0000)
struct W_ItemDisplayUI_C_JSIOnWeightUpdated final
{
public:
	double                                        NewWeight;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_JSIOnWeightUpdated");
static_assert(sizeof(W_ItemDisplayUI_C_JSIOnWeightUpdated) == 0x000008, "Wrong size on W_ItemDisplayUI_C_JSIOnWeightUpdated");
static_assert(offsetof(W_ItemDisplayUI_C_JSIOnWeightUpdated, NewWeight) == 0x000000, "Member 'W_ItemDisplayUI_C_JSIOnWeightUpdated::NewWeight' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.SetActionbarFollower
// 0x0010 (0x0010 - 0x0000)
struct W_ItemDisplayUI_C_SetActionbarFollower final
{
public:
	class UJSI_Slot_C*                            JigRef;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Return;                                            // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_SetActionbarFollower) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_SetActionbarFollower");
static_assert(sizeof(W_ItemDisplayUI_C_SetActionbarFollower) == 0x000010, "Wrong size on W_ItemDisplayUI_C_SetActionbarFollower");
static_assert(offsetof(W_ItemDisplayUI_C_SetActionbarFollower, JigRef) == 0x000000, "Member 'W_ItemDisplayUI_C_SetActionbarFollower::JigRef' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_SetActionbarFollower, Return) == 0x000008, "Member 'W_ItemDisplayUI_C_SetActionbarFollower::Return' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.SetActorOwner
// 0x0008 (0x0008 - 0x0000)
struct W_ItemDisplayUI_C_SetActorOwner final
{
public:
	class AActor*                                 ActorRef;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_SetActorOwner) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_SetActorOwner");
static_assert(sizeof(W_ItemDisplayUI_C_SetActorOwner) == 0x000008, "Wrong size on W_ItemDisplayUI_C_SetActorOwner");
static_assert(offsetof(W_ItemDisplayUI_C_SetActorOwner, ActorRef) == 0x000000, "Member 'W_ItemDisplayUI_C_SetActorOwner::ActorRef' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.SetInspectorRef
// 0x0008 (0x0008 - 0x0000)
struct W_ItemDisplayUI_C_SetInspectorRef final
{
public:
	class UBP_InspectorWindowWidget_C*            Inspector;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_SetInspectorRef) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_SetInspectorRef");
static_assert(sizeof(W_ItemDisplayUI_C_SetInspectorRef) == 0x000008, "Wrong size on W_ItemDisplayUI_C_SetInspectorRef");
static_assert(offsetof(W_ItemDisplayUI_C_SetInspectorRef, Inspector) == 0x000000, "Member 'W_ItemDisplayUI_C_SetInspectorRef::Inspector' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.SetItemReference
// 0x0008 (0x0008 - 0x0000)
struct W_ItemDisplayUI_C_SetItemReference final
{
public:
	class UJSI_Slot_C*                            ItemRef;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_ItemDisplayUI_C_SetItemReference) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_SetItemReference");
static_assert(sizeof(W_ItemDisplayUI_C_SetItemReference) == 0x000008, "Wrong size on W_ItemDisplayUI_C_SetItemReference");
static_assert(offsetof(W_ItemDisplayUI_C_SetItemReference, ItemRef) == 0x000000, "Member 'W_ItemDisplayUI_C_SetItemReference::ItemRef' has a wrong offset!");

// Function W_ItemDisplayUI.W_ItemDisplayUI_C.GetListOfContainers
// 0x0020 (0x0020 - 0x0000)
struct W_ItemDisplayUI_C_GetListOfContainers final
{
public:
	TArray<class UJSIContainer_C*>                Containers;                                        // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
	TArray<class UJSIContainer_C*>                K2Node_MakeArray_Array;                            // 0x0010(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(W_ItemDisplayUI_C_GetListOfContainers) == 0x000008, "Wrong alignment on W_ItemDisplayUI_C_GetListOfContainers");
static_assert(sizeof(W_ItemDisplayUI_C_GetListOfContainers) == 0x000020, "Wrong size on W_ItemDisplayUI_C_GetListOfContainers");
static_assert(offsetof(W_ItemDisplayUI_C_GetListOfContainers, Containers) == 0x000000, "Member 'W_ItemDisplayUI_C_GetListOfContainers::Containers' has a wrong offset!");
static_assert(offsetof(W_ItemDisplayUI_C_GetListOfContainers, K2Node_MakeArray_Array) == 0x000010, "Member 'W_ItemDisplayUI_C_GetListOfContainers::K2Node_MakeArray_Array' has a wrong offset!");

}

