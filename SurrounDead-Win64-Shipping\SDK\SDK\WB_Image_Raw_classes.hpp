﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Image_Raw

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_Image_Raw.WB_Image_Raw_C
// 0x0018 (0x02D8 - 0x02C0)
class UWB_Image_Raw_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBackgroundBlur*                        Blur;                                              // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Naked_Image;                                       // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WB_Image_Raw(int32 EntryPoint);
	void SetBackgroundBlur(bool IsEnabled, double BlurStrength);
	void SetColor(const struct FLinearColor& Color);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_Image_Raw_C">();
	}
	static class UWB_Image_Raw_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_Image_Raw_C>();
	}
};
static_assert(alignof(UWB_Image_Raw_C) == 0x000008, "Wrong alignment on UWB_Image_Raw_C");
static_assert(sizeof(UWB_Image_Raw_C) == 0x0002D8, "Wrong size on UWB_Image_Raw_C");
static_assert(offsetof(UWB_Image_Raw_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_Image_Raw_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_Image_Raw_C, Blur) == 0x0002C8, "Member 'UWB_Image_Raw_C::Blur' has a wrong offset!");
static_assert(offsetof(UWB_Image_Raw_C, Naked_Image) == 0x0002D0, "Member 'UWB_Image_Raw_C::Naked_Image' has a wrong offset!");

}

