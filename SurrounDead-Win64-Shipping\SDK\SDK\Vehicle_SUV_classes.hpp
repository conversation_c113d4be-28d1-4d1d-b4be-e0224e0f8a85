﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_SUV

#include "Basic.hpp"

#include "BP_VehicleMaster_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass Vehicle_SUV.Vehicle_SUV_C
// 0x0010 (0x04D0 - 0x04C0)
class AVehicle_SUV_C final : public ABP_VehicleMaster_C
{
public:
	class USpotLightComponent*                    LightRight1;                                       // 0x04C0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class USpotLightComponent*                    LightLeft1;                                        // 0x04C8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)

public:
	void UserConstructionScript();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Vehicle_SUV_C">();
	}
	static class AVehicle_SUV_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<AVehicle_SUV_C>();
	}
};
static_assert(alignof(AVehicle_SUV_C) == 0x000008, "Wrong alignment on AVehicle_SUV_C");
static_assert(sizeof(AVehicle_SUV_C) == 0x0004D0, "Wrong size on AVehicle_SUV_C");
static_assert(offsetof(AVehicle_SUV_C, LightRight1) == 0x0004C0, "Member 'AVehicle_SUV_C::LightRight1' has a wrong offset!");
static_assert(offsetof(AVehicle_SUV_C, LightLeft1) == 0x0004C8, "Member 'AVehicle_SUV_C::LightLeft1' has a wrong offset!");

}

