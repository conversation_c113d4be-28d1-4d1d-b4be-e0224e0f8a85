﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Weather_Mask_Projection_Box_Component

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "WeatherMask_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C
// 0x0090 (0x0730 - 0x06A0)
class UWeather_Mask_Projection_Box_Component_C final : public UWeatherMask_C
{
public:
	uint8                                         Pad_692[0x6];                                      // 0x0692(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FPointerToUberGraphFrame               UberGraphFrame_Weather_Mask_Projection_Box_Component_C; // 0x0698(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class USceneCaptureComponent2D*               SceneCaptureComponent2D;                           // 0x06A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        Blur_Radius;                                       // 0x06A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	double                                        Blur_Slope__Wetness_;                              // 0x06B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	double                                        Dust_;                                             // 0x06B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	int32                                         Capture_Pixel_Size;                                // 0x06C0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_6C4[0x4];                                      // 0x06C4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class AActor*>                         Exclude_Actors_from_Occlusion;                     // 0x06C8(0x0010)(Edit, BlueprintVisible, DisableEditOnTemplate, ExposeOnSpawn)
	class UMaterialInstanceDynamic*               Mask_MID;                                          // 0x06D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Height_MID;                                        // 0x06E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Top_Height;                                        // 0x06E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Capture_Box_Size;                                  // 0x06F0(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTextureRenderTarget2D*                 Depth_Render_Target;                               // 0x0708(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	int32                                         Capture_X_Resolution;                              // 0x0710(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_714[0x4];                                      // 0x0714(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Height_Dilation;                                   // 0x0718(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Recycle_Scene_Capture_for_Repeated_Mask_Draws;     // 0x0720(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          UDW_Runtime;                                       // 0x0721(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Calculate_Masking_At_Location(const struct FVector& Location, struct FVector2D* Mask, bool* Cancel_All_Masks);
	void ExecuteUbergraph_Weather_Mask_Projection_Box_Component(int32 EntryPoint);
	void Get_Brush_Scale(struct FVector2D* Out);
	void Prepare_for_Drawing(bool Runtime, class AUltra_Dynamic_Weather_C* UDW_0);
	void ReceiveBeginPlay();
	void Recycle_Render_Target();
	void Unready();
	void Update_Capture();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Weather_Mask_Projection_Box_Component_C">();
	}
	static class UWeather_Mask_Projection_Box_Component_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWeather_Mask_Projection_Box_Component_C>();
	}
};
static_assert(alignof(UWeather_Mask_Projection_Box_Component_C) == 0x000010, "Wrong alignment on UWeather_Mask_Projection_Box_Component_C");
static_assert(sizeof(UWeather_Mask_Projection_Box_Component_C) == 0x000730, "Wrong size on UWeather_Mask_Projection_Box_Component_C");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, UberGraphFrame_Weather_Mask_Projection_Box_Component_C) == 0x000698, "Member 'UWeather_Mask_Projection_Box_Component_C::UberGraphFrame_Weather_Mask_Projection_Box_Component_C' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, SceneCaptureComponent2D) == 0x0006A0, "Member 'UWeather_Mask_Projection_Box_Component_C::SceneCaptureComponent2D' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Blur_Radius) == 0x0006A8, "Member 'UWeather_Mask_Projection_Box_Component_C::Blur_Radius' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Blur_Slope__Wetness_) == 0x0006B0, "Member 'UWeather_Mask_Projection_Box_Component_C::Blur_Slope__Wetness_' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Dust_) == 0x0006B8, "Member 'UWeather_Mask_Projection_Box_Component_C::Dust_' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Capture_Pixel_Size) == 0x0006C0, "Member 'UWeather_Mask_Projection_Box_Component_C::Capture_Pixel_Size' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Exclude_Actors_from_Occlusion) == 0x0006C8, "Member 'UWeather_Mask_Projection_Box_Component_C::Exclude_Actors_from_Occlusion' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Mask_MID) == 0x0006D8, "Member 'UWeather_Mask_Projection_Box_Component_C::Mask_MID' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Height_MID) == 0x0006E0, "Member 'UWeather_Mask_Projection_Box_Component_C::Height_MID' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Top_Height) == 0x0006E8, "Member 'UWeather_Mask_Projection_Box_Component_C::Top_Height' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Capture_Box_Size) == 0x0006F0, "Member 'UWeather_Mask_Projection_Box_Component_C::Capture_Box_Size' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Depth_Render_Target) == 0x000708, "Member 'UWeather_Mask_Projection_Box_Component_C::Depth_Render_Target' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Capture_X_Resolution) == 0x000710, "Member 'UWeather_Mask_Projection_Box_Component_C::Capture_X_Resolution' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Height_Dilation) == 0x000718, "Member 'UWeather_Mask_Projection_Box_Component_C::Height_Dilation' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, Recycle_Scene_Capture_for_Repeated_Mask_Draws) == 0x000720, "Member 'UWeather_Mask_Projection_Box_Component_C::Recycle_Scene_Capture_for_Repeated_Mask_Draws' has a wrong offset!");
static_assert(offsetof(UWeather_Mask_Projection_Box_Component_C, UDW_Runtime) == 0x000721, "Member 'UWeather_Mask_Projection_Box_Component_C::UDW_Runtime' has a wrong offset!");

}

