﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_FindPlayerLocation

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AIModule_structs.hpp"
#include "AIModule_classes.hpp"


namespace SDK
{

// BlueprintGeneratedClass Zombie_FindPlayerLocation.Zombie_FindPlayerLocation_C
// 0x0038 (0x00E0 - 0x00A8)
class UZombie_FindPlayerLocation_C final : public UBTTask_BlueprintBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x00A8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	struct FBlackboardKeySelector                 Actor;                                             // 0x00B0(0x0028)(Edit, BlueprintVisible)
	class AActor*                                 TargetActor;                                       // 0x00D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, <PERSON>D<PERSON>ru<PERSON>, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_Zombie_FindPlayerLocation(int32 EntryPoint);
	void ReceiveExecuteAI(class AAIController* OwnerController, class APawn* ControlledPawn);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Zombie_FindPlayerLocation_C">();
	}
	static class UZombie_FindPlayerLocation_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UZombie_FindPlayerLocation_C>();
	}
};
static_assert(alignof(UZombie_FindPlayerLocation_C) == 0x000008, "Wrong alignment on UZombie_FindPlayerLocation_C");
static_assert(sizeof(UZombie_FindPlayerLocation_C) == 0x0000E0, "Wrong size on UZombie_FindPlayerLocation_C");
static_assert(offsetof(UZombie_FindPlayerLocation_C, UberGraphFrame) == 0x0000A8, "Member 'UZombie_FindPlayerLocation_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UZombie_FindPlayerLocation_C, Actor) == 0x0000B0, "Member 'UZombie_FindPlayerLocation_C::Actor' has a wrong offset!");
static_assert(offsetof(UZombie_FindPlayerLocation_C, TargetActor) == 0x0000D8, "Member 'UZombie_FindPlayerLocation_C::TargetActor' has a wrong offset!");

}

