﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Weather_Mask_Projection_Box_Component

#include "Basic.hpp"

#include "Weather_Mask_Projection_Box_Component_classes.hpp"
#include "Weather_Mask_Projection_Box_Component_parameters.hpp"


namespace SDK
{

// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Calculate Masking At Location
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector&                   Location                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FVector2D*                       Mask                                                   (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool*                                   Cancel_All_Masks                                       (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoD<PERSON><PERSON><PERSON>, HasGetValueTypeHash)

void UWeather_Mask_Projection_Box_Component_C::Calculate_Masking_At_Location(const struct FVector& Location, struct FVector2D* Mask, bool* Cancel_All_Masks)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Projection_Box_Component_C", "Calculate Masking At Location");

	Params::Weather_Mask_Projection_Box_Component_C_Calculate_Masking_At_Location Parms{};

	Parms.Location = std::move(Location);

	UObject::ProcessEvent(Func, &Parms);

	if (Mask != nullptr)
		*Mask = std::move(Parms.Mask);

	if (Cancel_All_Masks != nullptr)
		*Cancel_All_Masks = Parms.Cancel_All_Masks;
}


// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.ExecuteUbergraph_Weather_Mask_Projection_Box_Component
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeather_Mask_Projection_Box_Component_C::ExecuteUbergraph_Weather_Mask_Projection_Box_Component(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Projection_Box_Component_C", "ExecuteUbergraph_Weather_Mask_Projection_Box_Component");

	Params::Weather_Mask_Projection_Box_Component_C_ExecuteUbergraph_Weather_Mask_Projection_Box_Component Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Get Brush Scale
// (Protected, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FVector2D*                       Out                                                    (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWeather_Mask_Projection_Box_Component_C::Get_Brush_Scale(struct FVector2D* Out)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Projection_Box_Component_C", "Get Brush Scale");

	Params::Weather_Mask_Projection_Box_Component_C_Get_Brush_Scale Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Out != nullptr)
		*Out = std::move(Parms.Out);
}


// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Prepare for Drawing
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Runtime                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class AUltra_Dynamic_Weather_C*         UDW_0                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWeather_Mask_Projection_Box_Component_C::Prepare_for_Drawing(bool Runtime, class AUltra_Dynamic_Weather_C* UDW_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Projection_Box_Component_C", "Prepare for Drawing");

	Params::Weather_Mask_Projection_Box_Component_C_Prepare_for_Drawing Parms{};

	Parms.Runtime = Runtime;
	Parms.UDW_0 = UDW_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.ReceiveBeginPlay
// (Event, Public, BlueprintEvent)

void UWeather_Mask_Projection_Box_Component_C::ReceiveBeginPlay()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Projection_Box_Component_C", "ReceiveBeginPlay");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Recycle Render Target
// (Protected, BlueprintCallable, BlueprintEvent)

void UWeather_Mask_Projection_Box_Component_C::Recycle_Render_Target()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Projection_Box_Component_C", "Recycle Render Target");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Unready
// (Public, BlueprintCallable, BlueprintEvent)

void UWeather_Mask_Projection_Box_Component_C::Unready()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Projection_Box_Component_C", "Unready");

	UObject::ProcessEvent(Func, nullptr);
}


// Function Weather_Mask_Projection_Box_Component.Weather_Mask_Projection_Box_Component_C.Update Capture
// (Protected, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWeather_Mask_Projection_Box_Component_C::Update_Capture()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Weather_Mask_Projection_Box_Component_C", "Update Capture");

	UObject::ProcessEvent(Func, nullptr);
}

}

