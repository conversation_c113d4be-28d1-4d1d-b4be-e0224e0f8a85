﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Ultra_Dynamic_Weather

#include "Basic.hpp"

#include "UDS_ControlPointMode_structs.hpp"
#include "UDS_SeasonMode_structs.hpp"
#include "Engine_structs.hpp"
#include "Engine_classes.hpp"
#include "CoreUObject_structs.hpp"
#include "UDS_PropertyType_structs.hpp"
#include "UDS_Particle_Collision_Mode_structs.hpp"
#include "UDS_RandomWeatherTiming_structs.hpp"
#include "UDS_Rain_Splash_RenderMode_structs.hpp"
#include "UDS_TemperatureType_structs.hpp"
#include "UDS_Weather_Display_Names_structs.hpp"
#include "UDS_Season_structs.hpp"
#include "UDS_RunContext_structs.hpp"
#include "UDW_WeatherState_Structure_structs.hpp"
#include "UDS_Weather_State_Variable_structs.hpp"
#include "UDS_Temperature_Sample_Location_structs.hpp"


namespace SDK
{

// BlueprintGeneratedClass Ultra_Dynamic_Weather.Ultra_Dynamic_Weather_C
// 0x18E8 (0x1B80 - 0x0298)
class AUltra_Dynamic_Weather_C final : public AActor
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0298(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UPostProcessComponent*                  PostProcess;                                       // 0x02A0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UAudioComponent*                        Sound_Global;                                      // 0x02A8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UAudioComponent*                        Sound_Directional_Y;                               // 0x02B0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UAudioComponent*                        Sound_Directional_X;                               // 0x02B8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UUDW_Temperature_Manager_C*             Temperature_Manager;                               // 0x02C0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UUDW_Material_State_Manager_C*          Material_State_Manager;                            // 0x02C8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UUDW_Lightning_Spawn_Manager_C*         Lightning_Spawn_Manager;                           // 0x02D0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UUDS_PlayerOcclusion_C*                 Player_Occlusion;                                  // 0x02D8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class URandom_Weather_Variation_C*            Random_Weather_Manager;                            // 0x02E0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UDirectionalLightComponent*             Lightning_Light;                                   // 0x02E8(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class UBillboardComponent*                    Root;                                              // 0x02F0(0x0008)(BlueprintVisible, ZeroConstructor, InstancedReference, NonTransactional, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   UltraDynamicSky;                                   // 0x02F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, Transient, NoDestructor, HasGetValueTypeHash)
	bool                                          Refresh_Settings;                                  // 0x0300(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_301[0x7];                                      // 0x0301(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                Weather;                                           // 0x0308(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Coverage;                                    // 0x0310(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Cloud_Coverage___Manual_Override;                  // 0x0318(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_319[0x7];                                      // 0x0319(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Rain;                                              // 0x0320(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Rain___Manual_Override;                            // 0x0328(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_329[0x7];                                      // 0x0329(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Snow;                                              // 0x0330(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Snow___Manual_Override;                            // 0x0338(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_339[0x7];                                      // 0x0339(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Lightning;                                         // 0x0340(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Lightning___Manual_Override;                       // 0x0348(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_349[0x7];                                      // 0x0349(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Wind_Intensity;                                    // 0x0350(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Wind_Intensity___Manual_Override;                  // 0x0358(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_359[0x7];                                      // 0x0359(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Fog;                                               // 0x0360(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Fog___Manual_Override;                             // 0x0368(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_369[0x7];                                      // 0x0369(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Dust;                                              // 0x0370(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Dust___Manual_Override;                            // 0x0378(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_379[0x7];                                      // 0x0379(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Material_Wetness;                                  // 0x0380(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Material_Wetness___Manual_Override;                // 0x0388(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_389[0x7];                                      // 0x0389(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Material_Snow_Coverage;                            // 0x0390(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Material_Snow_Coverage___Manual_Override;          // 0x0398(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_399[0x7];                                      // 0x0399(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Material_Dust_Coverage;                            // 0x03A0(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Material_Dust_Coverage___Manual_Override;          // 0x03A8(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3A9[0x7];                                      // 0x03A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Wind_Direction;                                    // 0x03B0(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Transition_Duration;                               // 0x03B8(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Transition_Timer;                                  // 0x03C0(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_Particle_Collision_Mode                  Particle_Collision_Mode;                           // 0x03C8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3C9[0x7];                                      // 0x03C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Ceiling_Check_Height;                              // 0x03D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Spawn_Direction_Forward_Bias;                      // 0x03D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Spawn_Box_Height;                                  // 0x03E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Spawn_Distance;                                // 0x03E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Spawn_Distance_Distribution;                       // 0x03F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Minimum_Particle_Distance;                         // 0x03F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                World_Spawn_Offset;                                // 0x0400(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Camera_Forward_Spawn_Offset;                       // 0x0418(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Use_UDS_Water_Level;                               // 0x0420(0x0001)(Edit, BlueprintVisible, BlueprintReadOnly, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ECollisionChannel                             Weather_Particle_Collision_Channel;                // 0x0421(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Rain_Particles;                             // 0x0422(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_423[0x5];                                      // 0x0423(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Rain_Particle_Spawn_Count;                         // 0x0428(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Rain_Drops_Scale;                                  // 0x0430(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Rain_Velocity_Randomization;                       // 0x0438(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Rain_Particle_Color_Multiplier;                    // 0x0440(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Rain_Drops_Alpha;                                  // 0x0450(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Rain_Refraction_Intensity;                         // 0x0458(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Rain_Ambient_Light_Intensity;                      // 0x0460(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Rain_Light_Sparkle;                                // 0x0468(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Rain_Particles_Time_Dilation;                      // 0x0470(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Render_Raindrop_Decals;                            // 0x0474(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Rain_Ripple_Normal_Decals;                         // 0x0475(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_476[0x2];                                      // 0x0476(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Splash_Frequency;                                  // 0x0478(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Splash_Particles_Scale;                            // 0x0480(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Splash_Droplets_Scale;                             // 0x0488(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Splash_Particles_Alpha;                            // 0x0490(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Splash_Minimum_Z_Normal;                           // 0x0498(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Raindrop_Decal_Scale;                              // 0x04A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_Rain_Splash_RenderMode                   Splash_Particles_Rendering_Mode;                   // 0x04A8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4A9[0x7];                                      // 0x04A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Max_Duplicate_Splash_Range;                        // 0x04B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Snow_Particles;                             // 0x04B8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4B9[0x7];                                      // 0x04B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Snow_Particle_Spawn_Count;                         // 0x04C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Flakes_Scale;                                 // 0x04C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Velocity_Randomization;                       // 0x04D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Flakes_Alpha;                                 // 0x04D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Ambient_Light_Intensity;                      // 0x04E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Flakes_Surface_Stick_Duration;                // 0x04E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Particles_Time_Dilation;                      // 0x04F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Snow_Particle_Color_Multiplier;                    // 0x04F8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Spawn_Lightning_Flashes;                           // 0x0508(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_509[0x7];                                      // 0x0509(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Lightning_Flash_Frequency;                         // 0x0510(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Lightning_Flash_Timing_Randomization;              // 0x0518(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	struct FLinearColor                           Lightning_Effect_Tint_Color;                       // 0x0520(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Lightning_Flash_Light_Source;                      // 0x0530(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_531[0x3];                                      // 0x0531(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           Lightning_Flash_Light_Source_Color;                // 0x0534(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_544[0x4];                                      // 0x0544(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Maximum_Lightning_Flash_Light_Intensity;           // 0x0548(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Lightning_Flashes_Cast_Shadows;                    // 0x0550(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Lightning_Flashes_Cast_Light_Shaft_Bloom;          // 0x0551(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_552[0x6];                                      // 0x0552(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Lightning_Flash_Light_Shaft_Intensity;             // 0x0558(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FFloatRange                            Lightning_Flash_Distance_Range;                    // 0x0560(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFloatRange                            Lightning_Flash_Duration;                          // 0x0570(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Lightning_Height_Offset;                           // 0x0580(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Lightning_Bolt_Length;                             // 0x0588(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Lightning_Bolt_Thickness_Scale;                    // 0x0590(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Replicate_Lightning_Direction;                     // 0x0598(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Obscured_Lightning;                         // 0x0599(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_59A[0x6];                                      // 0x059A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Obscured_Lightning_Spawn_Rate;                     // 0x05A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Fog_Particles;                              // 0x05A8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5A9[0x7];                                      // 0x05A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Max_Fog_Particle_Percentage__Rain_;                // 0x05B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Particle_Intensity__Rain_;                     // 0x05B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Fog_Particle_Percentage__Snow_;                // 0x05C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Particle_Intensity__Snow_;                     // 0x05C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Fog_Particle_Percentage__Dust_;                // 0x05D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Particle_Intensity__Dust_;                     // 0x05D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Particles_Draw_Distance;                       // 0x05E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class AWindDirectionalSource*                 Wind_Directional_Source_Actor;                     // 0x05E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Material_Wetness;                              // 0x05F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Material_Snow_Coverage;                        // 0x05F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Dust_Coverage;                                 // 0x0600(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Material_Snow_Color;                               // 0x0608(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Material_Dust_Color;                               // 0x0618(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Material_Water_Roughness;                          // 0x0628(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Tiling_Raindrop_Ripples_Scale;                     // 0x0630(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Simulate_Changing_Material_State_Over_Time;        // 0x0638(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_639[0x7];                                      // 0x0639(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Wetness_Coverage_Duration;                         // 0x0640(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Wetness_Dry_Duration;                              // 0x0648(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Wetness_Dry_Speed_in_Sunlight;                     // 0x0650(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Wetness_Dry_Speed_without_Sunlight;                // 0x0658(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Coverage_Duration;                            // 0x0660(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Melt_Duration;                                // 0x0668(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Melt_Speed_Above_Freezing;                    // 0x0670(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Melt_Speed_Below_Freezing;                    // 0x0678(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Coverage_Duration;                            // 0x0680(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Clear_Duration;                               // 0x0688(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Clear_Speed_when_Windy;                       // 0x0690(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Clear_Speed_when_Calm;                        // 0x0698(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Melted_Snow_Coverage_Contributes_to_Wetness;       // 0x06A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Coverage_Lights_Up_Height_Fog;                // 0x06A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Wind_Force_Scale;                                  // 0x06B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	EUDS_RandomWeatherTiming                      Random_Weather_Variation;                          // 0x06B8(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6B9[0x3];                                      // 0x06B9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFloatRange                            Random_Weather_Change_Interval;                    // 0x06BC(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Random_Weather_Change_Hour;                        // 0x06CC(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Transition_Length;                                 // 0x06D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Begin_Play_Weather_Is_Random;                      // 0x06D8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6D9[0x7];                                      // 0x06D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Climate_Preset_C*                  Apply_Climate_Preset;                              // 0x06E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TMap<class UUDS_Weather_Settings_C*, double>  Weather_Type_Probabilities__Spring_;               // 0x06E8(0x0050)(Edit, BlueprintVisible)
	TMap<class UUDS_Weather_Settings_C*, double>  Weather_Type_Probabilities__Summer_;               // 0x0738(0x0050)(Edit, BlueprintVisible)
	TMap<class UUDS_Weather_Settings_C*, double>  Weather_Type_Probabilities__Autumn_;               // 0x0788(0x0050)(Edit, BlueprintVisible)
	TMap<class UUDS_Weather_Settings_C*, double>  Weather_Type_Probabilities__Winter_;               // 0x07D8(0x0050)(Edit, BlueprintVisible)
	int32                                         Random_Weather_Forecast_Steps;                     // 0x0828(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Blend_Season_Probabilities;                        // 0x082C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Avoid_Extreme_Cloud_Coverage_Shifts;               // 0x082D(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Avoid_Repeating_Weather_Types;                     // 0x082E(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	bool                                          Avoid_Changing_Directly_from_Snow_to_Rain__Or_Vice_Versa_; // 0x082F(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	TMap<class UUDS_Weather_Settings_C*, double>  Weather_Specific_Interval_Multipliers;             // 0x0830(0x0050)(Edit, BlueprintVisible, AdvancedDisplay)
	double                                        Extreme_Cloud_Coverage_Shift_Theshold;             // 0x0880(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Enable_Weather_Sound_Effects;                      // 0x0888(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_889[0x7];                                      // 0x0889(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Rain_Volume;                                       // 0x0890(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Distant_Thunder_Volume;                            // 0x0898(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Close_Thunder_Volume;                              // 0x08A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Close_Thunder_Delay_Per_KM;                        // 0x08A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Wind_Volume;                                       // 0x08B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Wind_Whistling_Volume;                             // 0x08B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Particle_Camera_Offset;                        // 0x08C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Wind_Directional_Source_Intensity_Scale;           // 0x08C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Obscured_Lightning_Scale;                          // 0x08D0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Lightning_Flash_Max_Angle_From_Forward;            // 0x08D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Show_Lightning_Flashes_in_Level_Editor;            // 0x08E0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_8E1[0x7];                                      // 0x08E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Daytime_Lightning_Flash_Intensity;                 // 0x08E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Nighttime_Lightning_Flash_Intensity;               // 0x08F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Lightning_Flash_Active;                            // 0x08F8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8F9[0x7];                                      // 0x08F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Lightning_Flash_Time_Elapsed;                      // 0x0900(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Lightning_Flash_Length;                            // 0x0908(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_TemperatureType                          Temperature_Scale;                                 // 0x0910(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_911[0x7];                                      // 0x0911(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              Summer_Temperature_Min_and_Max;                    // 0x0918(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Autumn_Temperature_Min_and_Max;                    // 0x0928(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Winter_Temperature_Min_and_Max;                    // 0x0938(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Spring_Temperature_Min_and_Max;                    // 0x0948(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Daytime_Temperature_Bias;                          // 0x0958(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Nighttime_Temperature_Bias;                        // 0x0960(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Evening_Temperature_Bias;                          // 0x0968(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Overcast_Temperature_Bias;                         // 0x0970(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Raining_Temperature_Bias;                          // 0x0978(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snowing_Temperature_Bias;                          // 0x0980(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Foggy_Temperature_Bias;                            // 0x0988(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dusty_Temperature_Bias;                            // 0x0990(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Randomize_Temperature;                             // 0x0998(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Interior_Temperature;                              // 0x09A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Interior_Insulation;                               // 0x09A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	EUDS_ControlPointMode                         Control_Point_Location_Source;                     // 0x09B0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9B1[0x7];                                      // 0x09B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Custom_Control_Point_Location;                     // 0x09B8(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Weather_Speed;                                     // 0x09D0(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	int32                                         Random_Seed;                                       // 0x09D8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_9DC[0x4];                                      // 0x09DC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Snow_Depth;                                        // 0x09E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Depth;                                        // 0x09E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Z_Normal_Cutoff;                              // 0x09F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Z_Normal_Falloff;                             // 0x09F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Texture_Scale;                                // 0x0A00(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Interaction_Render_Target_Area;               // 0x0A08(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Interaction_Fade_Speed_Idle;                  // 0x0A10(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Interaction_Fade_Speed_Active;                // 0x0A18(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Player_Camera_Location;                            // 0x0A20(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Control_Point_Location;                            // 0x0A38(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Puddle_Coverage;                                   // 0x0A50(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Puddles_Z_Normal_Cutoff;                           // 0x0A58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Puddles_Z_Normal_Falloff;                          // 0x0A60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Base_Wetness_when_Raining;                         // 0x0A68(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Base_Wetness_when_Clear;                           // 0x0A70(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Puddle_Sharpness;                                  // 0x0A78(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Variation_Clouds_Scale;                            // 0x0A80(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Interaction_Edge_Piling;                      // 0x0A88(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Interaction_Depth;                            // 0x0A90(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Interaction_Parallax_Sample_Scale;            // 0x0A98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Interaction_Scale;                            // 0x0AA0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Interaction_Scatter;                          // 0x0AA8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Interaction_Size_Scatter;                     // 0x0AB0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Dust_Interactions_Update_Period;                   // 0x0AB8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	TArray<class UPhysicalMaterial*>              Dust_Sounds_and_Particles;                         // 0x0AC0(0x0010)(Edit, BlueprintVisible, AdvancedDisplay)
	TArray<class UPhysicalMaterial*>              Physical_Materials_which_disable_Puddle_Sounds_and_Particles; // 0x0AD0(0x0010)(Edit, BlueprintVisible, AdvancedDisplay)
	bool                                          Enable_WOV_Material_Effect_Target;                 // 0x0AE0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AE1[0x7];                                      // 0x0AE1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        WOV_Target_Size;                                   // 0x0AE8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        WOV_Target_Update_Threshold__Inside_Volume_;       // 0x0AF0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        WOV_Target_Update_Threshold__Outside_Volume_;      // 0x0AF8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	TArray<class AWeather_Override_Volume_C*>     Weather_Override_Volumes;                          // 0x0B00(0x0010)(Edit, BlueprintVisible, DisableEditOnTemplate, Transient, DisableEditOnInstance)
	bool                                          Use_Custom_Lightning_Location;                     // 0x0B10(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B11[0x7];                                      // 0x0B11(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Custom_Lightning_Location;                         // 0x0B18(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Custom_Lightning_Target;                           // 0x0B30(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Current_Lightning_Intensity;                       // 0x0B48(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Current_Lightning_Intensity_Scale;                 // 0x0B50(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Current_Lightning_Location;                        // 0x0B58(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Current_Lightning_Angle;                           // 0x0B70(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AWeather_Override_Volume_C*             Current_Weather_Override_Volume;                   // 0x0B78(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, Transient, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	TMap<class AWeather_Override_Volume_C*, double> Current_WOVs_Applied;                            // 0x0B80(0x0050)(Edit, BlueprintVisible, DisableEditOnTemplate, Transient, DisableEditOnInstance)
	bool                                          Currently_in_a_Weather_Override_Volume;            // 0x0BD0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_BD1[0x7];                                      // 0x0BD1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTextureRenderTarget2D*                 DLWE_Mask_Target;                                  // 0x0BD8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	struct FVector                                DLWE_Recenter_Offset;                              // 0x0BE0(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FVector2D>                      Cloud_Reference_Array;                             // 0x0BF8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<struct FVector4>                       DLWE_Interactions_Buffer;                          // 0x0C08(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          Use_Occlusion_To_Attenuate_Sounds_In_Interiors;    // 0x0C18(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C19[0x7];                                      // 0x0C19(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Max_Attenuation;                                   // 0x0C20(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Min_LPF_Frequency;                                 // 0x0C28(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	class UUDS_OcclusionSettings_C*               Occlusion_Settings;                                // 0x0C30(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Post_Process_Wind_Fog;                      // 0x0C38(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C39[0x7];                                      // 0x0C39(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        PPWF_Intensity_From_Fog;                           // 0x0C40(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        PPWF_Intensity_from_Rain;                          // 0x0C48(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        PPWF_Intensity_From_Snow;                          // 0x0C50(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        PPWF_Intensity_from_Dust;                          // 0x0C58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        PPWF_Intensity_Scale;                              // 0x0C60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        PPWF_Fade_Distance;                                // 0x0C68(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        PPWF_Max_Sample_Distance;                          // 0x0C70(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        PPWF_Sample_Step_Distance;                         // 0x0C78(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        PPWF_3D_Noise_Scale;                               // 0x0C80(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Post_Process_Wind_Fog_MID;                         // 0x0C88(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Temperature_Update_Period;                         // 0x0C90(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Time_Random_Offset;                                // 0x0C98(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Wind_Direction_Variation;                   // 0x0CA0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_CA1[0x7];                                      // 0x0CA1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Maximum_Wind_Direction_Variation;                  // 0x0CA8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Variation_Speed;                                   // 0x0CB0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Current_Wind_Direction;                            // 0x0CB8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMap<class FName, EUDS_PropertyType>          Properties;                                        // 0x0CC0(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FVector                                Occlusion_Location;                                // 0x0D10(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              Started_Raining;                                   // 0x0D28(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              Started_Snowing;                                   // 0x0D38(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              Finished_Raining;                                  // 0x0D48(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              Finished_Snowing;                                  // 0x0D58(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              Getting_Cloudy;                                    // 0x0D68(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              Clouds_Clearing;                                   // 0x0D78(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	bool                                          ED_Raining;                                        // 0x0D88(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ED_Snowy;                                          // 0x0D89(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ED_Cloudy;                                         // 0x0D8A(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Screen_Droplets;                            // 0x0D8B(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D8C[0x4];                                      // 0x0D8C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Screen_Center_Strength;                            // 0x0D90(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Edge_Strength;                              // 0x0D98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Camera_Facing_Rain_Bias;                           // 0x0DA0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Droplet_Tiling;                                    // 0x0DA8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Blur_Radius;                                       // 0x0DB0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Wet_Screen_when_Emerging_From_Water;               // 0x0DB8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_DB9[0x7];                                      // 0x0DB9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Screen_Droplets_Clear_Speed;                       // 0x0DC0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Show_Screen_Droplets_in_Editor;                    // 0x0DC8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_DC9[0x7];                                      // 0x0DC9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               Screen_Droplets_MID;                               // 0x0DD0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Drips_Intensity;                                   // 0x0DD8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Drops_Intensity;                                   // 0x0DE0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Drops_Time;                                        // 0x0DE8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Screen_Droplets_Parent_Material;                   // 0x0DF0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	bool                                          Obscured_Lightning_System_Spawning;                // 0x0E18(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Dust_Particles;                             // 0x0E19(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E1A[0x6];                                      // 0x0E1A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Dust_Particle_Spawn_Count;                         // 0x0E20(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Particle_Scale;                               // 0x0E28(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Velocity_Randomization;                       // 0x0E30(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Dust_Particle_Color;                               // 0x0E38(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Particle_Alpha;                               // 0x0E48(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Wind_Debris;                                // 0x0E50(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E51[0x7];                                      // 0x0E51(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Wind_Debris_Particle_Spawn_Count;                  // 0x0E58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Debris_Scale;                                      // 0x0E60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Debris_Particle_Alpha;                             // 0x0E68(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              Wind_Debris_Atlas;                                 // 0x0E70(0x0028)(Edit, BlueprintVisible, HasGetValueTypeHash)
	class UTextureRenderTarget2D*                 Weather_Mask_Target;                               // 0x0E98(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	int32                                         Weather_Mask_Target_Size;                          // 0x0EA0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Triggered_Starting_Dispatchers;                    // 0x0EA4(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_EA5[0x3];                                      // 0x0EA5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Weather_Settings_C*                Old_Weather_State;                                 // 0x0EA8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Global_Weather_State;                              // 0x0EB0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Local_Weather_State;                               // 0x0EB8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Update_Buffer;                                     // 0x0EC0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         Dust_Particles_Time_Dilation;                      // 0x0EC8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_SeasonMode                               Season_Mode;                                       // 0x0ECC(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_ECD[0x3];                                      // 0x0ECD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Season;                                            // 0x0ED0(0x0008)(Edit, BlueprintVisible, Net, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	TArray<double>                                Individual_Seasons;                                // 0x0ED8(0x0010)(Edit, BlueprintVisible)
	class UUDS_Weather_Settings_C*                Manual_Weather_State;                              // 0x0EE8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              Clouds_Diverse_Texture;                            // 0x0EF0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UNiagaraSystem>          Rain_Particle_Niagara_System;                      // 0x0F18(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UNiagaraSystem>          Snow_Particle_Niagara_System;                      // 0x0F40(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UNiagaraSystem>          Dust_Particle_Niagara_System;                      // 0x0F68(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UNiagaraSystem>          Wind_Debris_Niagara_System;                        // 0x0F90(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UNiagaraSystem>          Obscured_Lightning_Niagara_System;                 // 0x0FB8(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Post_Process_Wind_Fog_Parent_Material;             // 0x0FE0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              Sand_Forming;                                      // 0x1008(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              Sand_Clearing;                                     // 0x1018(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	bool                                          ED_Dusty;                                          // 0x1028(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Enable_Rainbow;                                    // 0x1029(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_102A[0x6];                                     // 0x102A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Max_Rainbow_Strength;                              // 0x1030(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Strength_From_Rain;                                // 0x1038(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Strength_From_Fog;                                 // 0x1040(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Strength_In_Any_Weather;                           // 0x1048(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_0;                                             // 0x1050(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Mask_Rainbow_Above_Clouds;                         // 0x1058(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Mask_Rainbow_Below_Water;                          // 0x1060(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_Weather_Display_Names                    ED_CurrentWeather;                                 // 0x1068(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1069[0x7];                                     // 0x1069(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(EUDS_Weather_Display_Names Weather_Name)> Weather_Display_Name_Changed; // 0x1070(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FTimerHandle                           DLWE_Interaction_Timer;                            // 0x1080(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          Support_Virtual_Heightfield_Mesh;                  // 0x1088(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_1089[0x7];                                     // 0x1089(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ARuntimeVirtualTextureVolume*           VHFM_Runtime_Virtual_Texture_Volume;               // 0x1090(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        VHFM_Invalidate_Long_Range_Distance;               // 0x1098(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        VHFM_Invalidate_Long_Range_Threshold;              // 0x10A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        VHFM_Invalidate_Mid_Range_Distance;                // 0x10A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        VHFM_Invalidate_Mid_Range_Threshold;               // 0x10B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        VHFM_Invalidate_Short_Range_Distance;              // 0x10B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        VHFM_Invalidate_Short_Range_Threshold;             // 0x10C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	struct FVector                                VHFM_Invalidate_Long_Range_Buffer;                 // 0x10C8(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	struct FVector                                VHFM_Invalidate_Mid_Range_Buffer;                  // 0x10E0(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	struct FVector                                VHFM_Invalidate_Short_Range_Buffer;                // 0x10F8(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Fog_Particles_Active;                              // 0x1110(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1111[0x7];                                     // 0x1111(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Last_Sparse_Movement_Update_Location;              // 0x1118(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class USoundMix*                              Outdoor_Sound_Mix_Modifier;                        // 0x1130(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Max_Attenuation_For_UDS_Outdoor_Sound;             // 0x1138(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	TSoftObjectPtr<class UTextureRenderTarget2D>  Weather_Volume_Target;                             // 0x1140(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               WOV_Target_Brush_MID;                              // 0x1168(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          WOV_Material_Effect_Target_Active;                 // 0x1170(0x0001)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Shutting_Down;                                     // 0x1171(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1172[0x6];                                     // 0x1172(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 UDS_Version;                                       // 0x1178(0x0010)(Edit, BlueprintVisible, ZeroConstructor, HasGetValueTypeHash)
	class UUDS_VersionInfo_C*                     UDS_Version_Info;                                  // 0x1188(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	int32                                         Anti_Aliasing_Method;                              // 0x1190(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Season_Day_Offset;                                 // 0x1194(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              WOV_Wind_Direction;                                // 0x1198(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Current_Lightning_Target_Offset;                   // 0x11A8(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterial>               Splash_Material_Decal;                             // 0x11C0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterial>               Splash_Material_Translucent;                       // 0x11E8(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Rainbow_MID;                                       // 0x1210(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Current_Rainbow_Strength;                          // 0x1218(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Target_Rainbow_Strength;                           // 0x1220(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Rainbow_Material_2D;                               // 0x1228(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Rainbow_Material_Volumetric;                       // 0x1250(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	double                                        Weather_Particle_Motion_Stretch;                   // 0x1278(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Particle_Sprite_Motion_Blur_With_Camera_Movement;  // 0x1280(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Previous_Temperature_Scale;                        // 0x1288(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Apply_Changes_Above_Volumetric_Cloud_Layer;        // 0x1289(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_128A[0x6];                                     // 0x128A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Rain_Multiplier_Above_Clouds;                      // 0x1290(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snow_Multiplier_Above_Clouds;                      // 0x1298(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Multiplier_Above_Clouds;                      // 0x12A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Multiplier_Above_Clouds;                       // 0x12A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Lightning_Multiplier_Above_Clouds;                 // 0x12B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Wind_Multiplier_Above_Clouds;                      // 0x12B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(EUDS_Season Season)> Season_Changed;                               // 0x12C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	int32                                         ED_Season;                                         // 0x12D0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Transition_Active;                                 // 0x12D4(0x0001)(Edit, BlueprintVisible, Net, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12D5[0x3];                                     // 0x12D5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Transition_Alpha;                                  // 0x12D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Rain_Update_Needed;                                // 0x12E0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Snow_Update_Needed;                                // 0x12E1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Lightning_Update_Needed;                           // 0x12E2(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Wind_Intensity_Update_Needed;                      // 0x12E3(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cloud_Coverage_Update_Needed;                      // 0x12E4(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Fog_Update_Needed;                                 // 0x12E5(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Dust_Update_Needed;                                // 0x12E6(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Material_Wetness_Update_Needed;                    // 0x12E7(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Material_Snow_Update_Needed;                       // 0x12E8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Material_Dust_Update_Needed;                       // 0x12E9(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Wind_Direction_Update_Needed;                      // 0x12EA(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12EB[0x5];                                     // 0x12EB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Wind_Direction_Update_Buffer;                      // 0x12F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Wind_Direction_Last_Frame_Buffer;                  // 0x12F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class AWeather_Override_Volume_C*>     Nearby_Weather_Override_Volumes;                   // 0x1300(0x0010)(Edit, BlueprintVisible, DisableEditOnTemplate, Transient, DisableEditOnInstance)
	double                                        Current_PPWF_Intensity;                            // 0x1310(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Target_PPWF_Intensity;                             // 0x1318(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Screen_Droplets_Active;                            // 0x1320(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1321[0x7];                                     // 0x1321(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Screen_Droplets_Target_Drips_Intensity;            // 0x1328(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FWeightedBlendable                     Screen_Droplets_WB;                                // 0x1330(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	double                                        Screen_Droplets_Camera_Exposure;                   // 0x1340(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Applied_Rain_Velocity;                             // 0x1348(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Water_Level_Material_Falloff;                      // 0x1360(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Player_Camera_Underwater;                          // 0x1368(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1369[0x7];                                     // 0x1369(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Shared_Material_Parameter_Snowy;                   // 0x1370(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Shared_Material_Parameter_Dusty;                   // 0x1378(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterial>               Splash_Droplet_Material_Decal;                     // 0x1380(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterial>               Splash_Droplet_Material_Translucent;               // 0x13A8(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TArray<class UWeather_Mask_Projection_Box_Component_C*> Last_Update_Projection_Box_Components;   // 0x13D0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	double                                        Periodic_Updates_Timer;                            // 0x13E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Periodic_Update_Step;                              // 0x13E8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13EC[0x4];                                     // 0x13EC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UPrimitiveComponent*>            Last_Update_Mask_Components;                       // 0x13F0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	struct FRandomStream                          Random_Stream;                                     // 0x1400(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor)
	double                                        Weather_Particle_DOF_Scale;                        // 0x1408(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CPU_Particle_Buffer_Length;                        // 0x1410(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Scale_Distant_Particle_Alpha;                      // 0x1418(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Distant_Particle_Range;                            // 0x1420(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Warm_Up_Weather_Particles_On_Begin_Play;           // 0x1428(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Apply_Custom_Weather_Particle_Camera_Transform;    // 0x1429(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_142A[0x6];                                     // 0x142A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Custom_Weather_Particle_Camera_Location;           // 0x1430(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	struct FVector                                Custom_Weather_Particle_Camera_Forward_Vector;     // 0x1448(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Global_Weather_Updated;                            // 0x1460(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Local_Weather_Updated;                             // 0x1461(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1462[0x6];                                     // 0x1462(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TMap<class UUDS_Weather_Settings_C*, double>  Old_State_Source_Map;                              // 0x1468(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          Call__Custom_Weather_Behavior__Functions;          // 0x14B8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	uint8                                         Pad_14B9[0x7];                                     // 0x14B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UUDS_Weather_Settings_C*>        Last_Update_Sources;                               // 0x14C0(0x0010)(Edit, BlueprintVisible)
	bool                                          Enable_Heat_Distortion;                            // 0x14D0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14D1[0x7];                                     // 0x14D1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Heat_Distortion_Max_Intensity;                     // 0x14D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Heat_Distortion_From_Temperature;                  // 0x14E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	struct FFloatRange                            Heat_Distortion_Temperature_Range;                 // 0x14E8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Manual_Heat_Distortion;                            // 0x14F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Heat_DIstortion_Start_Distance;                    // 0x1500(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Heat_DIstortion_Falloff;                           // 0x1508(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Heat_Distortion_Tiling;                            // 0x1510(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Heat_Distortion_Speed;                             // 0x1518(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Heat_Distortion_Chromatic_Separation;              // 0x1520(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Heat_Distortion_Zenith_Mask;                       // 0x1528(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Heat_Distortion_Horizon_Mask;                      // 0x1530(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Heat_Distortion_Horizon_Exponent;                  // 0x1538(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	double                                        Heat_Distortion_Temporal_Dither;                   // 0x1540(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Show_Heat_Distortion_in_Editor;                    // 0x1548(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1549[0x7];                                     // 0x1549(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Target_Heat_Distortion_Value;                      // 0x1550(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Current_Heat_Distortion_Value;                     // 0x1558(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Heat_Distortion_Parent_Material;                   // 0x1560(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Heat_Distortion_MID;                               // 0x1588(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              State_Change___Rain;                               // 0x1590(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              State_Change___Snow;                               // 0x15A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              State_Change___Wind_Intensity;                     // 0x15B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              State_Change___Dust;                               // 0x15C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              State_Change___Fog;                                // 0x15D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              Lightning_0;                                       // 0x15E0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              State_Change___Wind_Direction;                     // 0x15F0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              State_Change___Cloud_Coverage;                     // 0x1600(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              State_Change___Material_Wetness;                   // 0x1610(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              State_Change___Material_Snow;                      // 0x1620(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              State_Change___Material_Dust;                      // 0x1630(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	bool                                          Tick_Behavior_Active;                              // 0x1640(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1641[0x7];                                     // 0x1641(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Editor_Camera_Position;                            // 0x1648(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUDS_RunContext                               Run_Context;                                       // 0x1660(0x0001)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Sharing_Occlusion_With_UDS;                        // 0x1661(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1662[0x6];                                     // 0x1662(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_OcclusionState_C*                  Weather_Occlusion_State;                           // 0x1668(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Time_of_Last_Sound_Effects_Cache;                  // 0x1670(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Last_Editor_Tick_Periodic_Update;                  // 0x1678(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Last_Editor_Tick_Sound_Occlusion_Update;           // 0x1680(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Last_Editor_Tick_Time;                             // 0x1688(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUDS_PlayerOcclusion_C*                 Active_Occlusion_Component;                        // 0x1690(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UUDS_OcclusionState_C*                  Sky_Occlusion_State;                               // 0x1698(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	float                                         Tick_Delta_Seconds;                                // 0x16A0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_16A4[0x4];                                     // 0x16A4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FRotator                               Player_Camera_Rotation;                            // 0x16A8(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	TSoftClassPtr<class UClass>                   Editor_Utility_Opener_Class;                       // 0x16C0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	double                                        Raining_Dispatcher_Threshold;                      // 0x16E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snowing_Dispatcher_Threshold;                      // 0x16F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sand_Dispatcher_Threshold;                         // 0x16F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cloudy_Dispatcher_Threshold;                       // 0x1700(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUDS_RenderTarget_State_C*              WOV_Render_Target_State;                           // 0x1708(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	class UUDS_RenderTarget_State_C*              Weather_Mask_Render_Target_State;                  // 0x1710(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	class UUDS_RenderTarget_State_C*              DLWE_Render_Target_State;                          // 0x1718(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        Level_Editor_Lightning_Timer;                      // 0x1720(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Level_Editor_Lightning_Period;                     // 0x1728(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               Editor_Camera_Rotation;                            // 0x1730(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	double                                        Level_Editor_Lightning_Interrupt_Timer;            // 0x1748(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Sound_State_Update_Period;                         // 0x1750(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	TSoftObjectPtr<class USoundBase>              Directional_Sound_Asset;                           // 0x1758(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class USoundBase>              Global_Sound_Asset;                                // 0x1780(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	int32                                         Custom_Lightning_Seed;                             // 0x17A8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_17AC[0x4];                                     // 0x17AC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void()>              Temperature_Range_Update;                          // 0x17B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	double                                        Last_Season_Value;                                 // 0x17C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Season_Change_Weather_Refresh_Threshold;           // 0x17C8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              Random_Weather_Season_Refresh;                     // 0x17D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FUDW_WeatherState_Structure            Old_Weather_State_Struct;                          // 0x17E0(0x0050)(Edit, BlueprintVisible, Net, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          DLWE_Snow_Trails_Activated;                        // 0x1830(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Dynamic_Puddles_Active;                            // 0x1831(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1832[0x2];                                     // 0x1832(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Dust_0;                                            // 0x1834(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Transition_Weather_State_B;                        // 0x1838(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Local_Weather_Location;                            // 0x1840(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class AWeather_Override_Volume_C*>     Last_Applied_WOVs;                                 // 0x1858(0x0010)(Edit, BlueprintVisible, DisableEditOnTemplate, Transient, DisableEditOnInstance)
	bool                                          Weather_Mask_Target_Active;                        // 0x1868(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Mobile;                                            // 0x1869(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Last_Update_Manual_Weather_Overridden;             // 0x186A(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Lightning_System_Toggle;                           // 0x186B(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Clear_Weather_Mask_Prep;                           // 0x186C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_186D[0x3];                                     // 0x186D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void()>              Lightning_Flash_Started;                           // 0x1870(0x0010)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TArray<class AActor*>                         Radial_Storm_Actors;                               // 0x1880(0x0010)(Edit, BlueprintVisible, DisableEditOnTemplate, Transient, DisableEditOnInstance)
	bool                                          Applied_Radial_Storms_Last_Update;                 // 0x1890(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Apply_Version_Specific_Correction;                 // 0x1891(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          WOV_Material_Effect_Target_Allowed;                // 0x1892(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1893[0x5];                                     // 0x1893(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UMaterialInstanceDynamic*               Radial_Storm_Target_Draw_MID;                      // 0x1898(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	class UStaticMeshComponent*                   Rainbow_Mesh;                                      // 0x18A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UNiagaraComponent*                      Wind_Debris_Particles;                             // 0x18A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FWeightedBlendable                     Post_Process_Wind_Fog_WB;                          // 0x18B0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	struct FWeightedBlendable                     Heat_Distortion_WB;                                // 0x18C0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	TArray<bool>                                  Post_Process_Mats_Toggle_State;                    // 0x18D0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TSoftObjectPtr<class UMaterialInterface>      Rain_Particle_Mat__AMB_;                           // 0x18E0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Rain_Particle_Mat__ADOF_;                          // 0x1908(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Snow_Particle_Mat__AMB_;                           // 0x1930(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	TSoftObjectPtr<class UMaterialInterface>      Snow_Particle_Mat__ADOF_;                          // 0x1958(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	bool                                          Motion_Blur;                                       // 0x1980(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1981[0x7];                                     // 0x1981(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Wind_Direction_Target;                             // 0x1988(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Wind_Direction_Change_Speed;                       // 0x1990(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        UDS_Cloud_Coverage;                                // 0x1998(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        UDS_Fog;                                           // 0x19A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        UDS_Dust_Amount;                                   // 0x19A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        UDS_Cloud_Direction;                               // 0x19B0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        UDS_Cloud_Speed;                                   // 0x19B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        UDS_Fog_Vertical_Velocity;                         // 0x19C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class AActor*>                         Nearby_Radial_Storm_Actors;                        // 0x19C8(0x0010)(Edit, BlueprintVisible, DisableEditOnTemplate, Transient, DisableEditOnInstance)
	bool                                          Enable_Screen_Frost;                               // 0x19D8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19D9[0x7];                                     // 0x19D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Screen_Frost_From_Snow;                            // 0x19E0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_From_Material_Snow;                   // 0x19E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Manual_Screen_Frost;                               // 0x19F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, Interp, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Multiplier_in_Interior;               // 0x19F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Form_Duration;                        // 0x1A00(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Clear_Duration;                       // 0x1A08(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Screen_Frost_Color;                                // 0x1A10(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Falloff_Shape;                        // 0x1A20(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Falloff_Exponent;                     // 0x1A28(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Center_Mask;                          // 0x1A30(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Edge_Mask;                            // 0x1A38(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Distortion_Strength;                  // 0x1A40(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Distortion_Alpha;                     // 0x1A48(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Texture_Scale;                        // 0x1A50(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Scatter;                              // 0x1A58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Screen_Frost_Sharpness;                            // 0x1A60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Randomize_Screen_Frost_Texture_at_Runtime;         // 0x1A68(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A69[0x7];                                     // 0x1A69(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TSoftObjectPtr<class UTexture2D>              Screen_Frost_Scatter_Texture;                      // 0x1A70(0x0028)(Edit, BlueprintVisible, AdvancedDisplay, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              Screen_Frost_Normal_Texture;                       // 0x1A98(0x0028)(Edit, BlueprintVisible, AdvancedDisplay, HasGetValueTypeHash)
	bool                                          Show_Screen_Frost_in_Editor;                       // 0x1AC0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Screen_Frost_Active;                               // 0x1AC1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1AC2[0x6];                                     // 0x1AC2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Screen_Frost_Strength_Target;                      // 0x1AC8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Current_Screen_Frost_Strength;                     // 0x1AD0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               Screen_Frost_MID;                                  // 0x1AD8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	struct FWeightedBlendable                     Screen_Frost_WB;                                   // 0x1AE0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	TSoftObjectPtr<class UMaterialInterface>      Screen_Frost_Parent_Material;                      // 0x1AF0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	bool                                          Manual_Overrides_Applied;                          // 0x1B18(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1B19[0x7];                                     // 0x1B19(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UObject*>                        Loaded_Objects;                                    // 0x1B20(0x0010)(Edit, BlueprintVisible, Transient, DisableEditOnInstance)
	class UNiagaraComponent*                      Rain_Particles;                                    // 0x1B30(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UNiagaraComponent*                      Snow_Particles;                                    // 0x1B38(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UNiagaraComponent*                      Dust_Particles;                                    // 0x1B40(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UNiagaraComponent*                      Obscured_Lightning_Particles;                      // 0x1B48(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UNiagaraComponent*                      Lightning_Flash_1;                                 // 0x1B50(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UNiagaraComponent*                      Lightning_Flash_2;                                 // 0x1B58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class USceneCaptureComponent2D*               Projection_Box_Scene_Capture_Component;            // 0x1B60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Transient_Weather_State;                           // 0x1B68(0x0008)(Edit, BlueprintVisible, ZeroConstructor, Transient, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	class UUDS_Weather_Settings_C*                Temperature_Weather_State;                         // 0x1B70(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               DLWE_Trail_Brush_MID;                              // 0x1B78(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)

public:
	void Active_Occlusion_State(class UUDS_OcclusionState_C** State);
	void Add_Constructed_Components();
	void Add_Weather_Override_Volume_to_Array(class AWeather_Override_Volume_C* Weather_Override_Volume);
	bool Allow_Render_Target_Drawing();
	bool Any_Manual_Overrides_Applied();
	void Apply_Climate_Preset_Object(class UUDS_Climate_Preset_C* Climate);
	void Apply_Interior_Temperature_with_Occlusion(double Temp, double Occlusion, double* Out);
	void Apply_Local_Temperature_Effects(double Temp, double Occlusion, const struct FVector& Location, double* Temp_Out);
	void Apply_Manual_State_Override_Values(bool For_Local_Weather);
	void Apply_Max_to_Material_Effects(class UUDS_Weather_Settings_C* Weather_0, double* Snow_Coverage, double* Wetness, double* Param_Dust_0);
	void Apply_Radial_Storm_Actors_to_Local_Weather(bool For_Local_Weather);
	void Apply_Sound_Effects_Volume_Levels();
	void Apply_Sound_Update_Periods();
	void Apply_Weather_Changes_Above_Cloud_Layer(bool For_Local_Weather);
	void Apply_Weather_Override_Volume_State(class AWeather_Override_Volume_C* WOV, double Alpha, bool For_Local_Weather);
	void Apply_Weather_Override_Volumes(bool For_Local_Weather, TMap<class AWeather_Override_Volume_C*, double>* WOVs_Applied);
	void Bind_to_Game_User_Settings();
	void Bind_to_UDS_Dispatchers();
	void Call_Custom_Weather_Behavior();
	void Call_Editor_Dispatchers();
	void Change_to_Random_Weather_Variation(double Time_to_Transition_to_Random_Weather__Seconds_, EUDS_RandomWeatherTiming Random_Weather_Mode);
	void Change_Weather(class UUDS_Weather_Settings_C* New_Weather_Type, double Time_To_Transition_To_New_Weather__Seconds_);
	void Change_Wind_Direction(double New_Wind_Direction, double Change_Duration);
	void Check_for_Events_to_Dispatch();
	void Check_for_Render_Target_Recentering();
	void Check_for_Weather_Value_Update_Threshold(double New_Value, double& Buffer_Value, double Threshold, double Range_Max, bool& Update_Needed_Bool, EUDS_Weather_State_Variable State_Variable);
	void Check_Point_for_Puddles_Snow_Or_Dust(const struct FVector& Location, const struct FVector& Ground_Normal, class UPhysicalMaterial* Physical_Material, double* Puddle_Depth, double* Snow_Depth_0, double* Dust_Depth_0);
	void Check_to_Change_Temperature_Scale();
	void Check_UDS_Version();
	void Clear_All_Material_Parameters_to_Zero_Coverage();
	void Clear_All_Render_Targets();
	void Clients_Transition_Start(double Duration);
	void Close_Thunder_Sound_Delay(double* Delay);
	double Combined_Wind_Direction();
	void Func_Dust_0(const struct FVector& Location, double Size);
	void Construct_All_Weather_State_Objects();
	void Construct_Weather_Mask_Target_State();
	void Construct_Weather_State_Object_if_Invalid(class UUDS_Weather_Settings_C*& State);
	void Construct_WOV_Render_Target_State();
	void Construction_Script_Function();
	void Convert_Temperature_Scale(double Input, EUDS_TemperatureType In_Scale, EUDS_TemperatureType Out_Scale, bool Relative_Degrees, bool Snap, double* Output);
	void Copy_Manual_State_Object_to_Variables();
	void Copy_Weather_State(class UUDS_Weather_Settings_C* Source, class UUDS_Weather_Settings_C* Target, bool Set_Material_Effects, bool Copy_Sources);
	void Copy_Weather_State_Structure_to_Object(class UUDS_Weather_Settings_C* State, const struct FUDW_WeatherState_Structure& Struct);
	void Create_Cloud_Reference_Array();
	void Create_Current_Local_Weather_State(const struct FVector& Test_Location);
	void Current_Dust_Velocity(struct FVector* Velocity);
	void Current_Rain_Velocity(struct FVector* Velocity);
	void Current_Snow_Velocity(struct FVector* Velocity);
	void Current_Wind_Debris_Velocity(struct FVector* Velocity);
	void Currently_Cloudy(bool* Yes);
	void Currently_Dusty(bool* Yes);
	void Currently_Raining(bool* Yes);
	void Currently_Snowing(bool* Yes);
	void Daily_Season_Update();
	void DLWE_Active_Update();
	void Dust_Niagara_System_Asset(TSoftObjectPtr<class UNiagaraSystem>* Out);
	double Dust_Spawn_Rate();
	double Dust_Sprite_Alpha();
	void Dust_System_Finished(class UNiagaraComponent* PSystem);
	void Editor_Lightning_Internal();
	void Editor_Tick(const struct FVector& Editor_Camera_Location, const struct FRotator& Editor_Camera_Rotation_0, double Delta_Time, bool* Completed);
	void ExecuteUbergraph_Ultra_Dynamic_Weather(int32 EntryPoint);
	void Fade_DLWE_Target_Over_Time();
	void Fill_Starting_Update_Buffer();
	void Filter_Probability_Map(const TMap<class UUDS_Weather_Settings_C*, double>& Probability_Map, class UUDS_Weather_Settings_C* Current_Random_Type, TMap<class UUDS_Weather_Settings_C*, double>* Filtered_Probability_Map);
	void Filter_Radial_Storm_Array();
	void Filter_Weather_Override_Volumes_Array();
	void Flash_Lightning(double Angle, bool Use_Custom_Lightning_Location_0, const struct FVector& Custom_Lightning_Location_0, const struct FVector& Custom_Target_Location, int32 Lightning_Bolt_Seed);
	void Fog_Vertical_Velocity(double* Out);
	void Force_Tick();
	void Game_User_Settings_Update();
	void Generate_Weather_State_At_Location(const struct FVector& Location, class UUDS_Weather_Settings_C* Settings_Object, TMap<class AWeather_Override_Volume_C*, double>* WOVs_Applied_at_Location);
	void Get_Control_Point_Location(struct FVector* Location);
	void _Pawn_Locations();
	void Get_Current_Sound_Occlusion_Values(double* Non_Directional_Occlusion, double* X__Occlusion, double* Y__Occlusion, double* X__Occlusion_0, double* Y__Occlusion_0, double* Upward_Occlusion);
	void Get_Current_Temperature(EUDS_Temperature_Sample_Location Sample_Location, const struct FVector& Custom_Sample_Location, EUDS_TemperatureType Scale, double* Output);
	void Get_Display_Name_for_Current_Weather(class FString* As_String, EUDS_Weather_Display_Names* As_Enumerator);
	void Get_Local_Weather_State_Values(double* Cloud_Coverage_0, double* Wind_Intensity_0, double* Rain_0, double* Snow_0, double* Param_Dust_0, double* Param_Fog_0, double* Param_Lightning_0);
	void Get_New_Target_Global_Weather_State(class UUDS_Weather_Settings_C** Out, bool* Changing, bool* Manual_State);
	void Get_Normalized_Wind_Direction(struct FVector* Wind_Vector);
	void Get_Projection_Box_Scene_Capture_2D(class USceneCaptureComponent2D** Out);
	void Get_Random_Weather_Forecast(TArray<class UUDS_Weather_Settings_C*>* Upcoming_Weather_Objects);
	void Get_Season(double* Season_0, EUDS_Season* Season_Enum);
	void Get_Sound_Directional_Occlusion(TArray<double>* Out);
	void Get_Sound_Global_Occlusion(double* Out);
	void Get_Sound_Upward_Occlusion(double* Out);
	void Get_Starting_Radial_Storms();
	void Get_Starting_Weather_Override_Volumes();
	void Get_Target_Heat_Distortion();
	void Get_UDS_Values_Controlled_by_UDW(double* Cloud_Coverage_0, double* Param_Fog_0, double* Dust_Amount, double* Cloud_Direction, double* Wind_Speed_Multiplier, double* Fog_Vertical_Velocity);
	void Get_UDS_Weather_Override_Bool(bool* Cloud_Coverage_0, bool* Param_Fog_0, bool* Param_Dust_0);
	void Get_UDW_State_for_Saving(struct FUDS_and_UDW_State* UDW_State);
	void Get_Weather_Presets_Used_By_Local_Weather(TMap<class UUDS_Weather_Settings_C*, double>* Sources);
	void Get_WOVs_Applied_to_Location(const struct FVector& Location, TArray<class AWeather_Override_Volume_C*>& WOV_Array, TMap<class AWeather_Override_Volume_C*, double>* Applied_WOV_Map);
	void Global_Lightning_Internal(double Angle, double Lightning_Threshold);
	void Hourly_Season_Update(int32 Hour);
	void _Dust_at_Location(const struct FVector& Location, double Radius, double Speed, bool Param_Dust_0, bool Affect_Puddles, bool* Hit_Puddle, bool* Param_Dust_1);
	void Increment_Global_Material_Effects();
	void Increment_Transition_Between_States();
	void Increment_Wind_Direction_Transition();
	void Initialize_Occlusion();
	void Initialize_Random_Weather_Variation();
	void Initialize_Weather(class AUltra_Dynamic_Sky_C* UDS);
	void Instant_Sound_Update();
	void Invalidate_VHFM_Level(double Threshold, double Distance, struct FVector& Buffer, const struct FVector& Current, bool* Continue);
	void Invalidate_VHFM_With_Material_States();
	void Latent_Weather_Mask_Update();
	void Latent_WOV_Target_Update();
	void Lerp_Between_Weather_States(class UUDS_Weather_Settings_C* A, class UUDS_Weather_Settings_C* B, double Alpha, class UUDS_Weather_Settings_C* Target_State, bool Set_Material_Effects, bool Use_Bias_for_Material_Effects, bool Lerp_Sources);
	void Lerp_State_Source_Maps(class UUDS_Weather_Settings_C* A, class UUDS_Weather_Settings_C* B, double Alpha, class UUDS_Weather_Settings_C* Target_Object);
	void Lerp_Yaw_Angles(double Angle_A, double Angle_B, double Alpha, double* Out);
	void Level_Editor_Lightning_Flash_Spawning();
	void Level_Editor_WOV_Update();
	void Lightning_Bolt_Target_Offset(struct FVector* Out);
	void Lightning_Distance_Range(double* Range);
	void Lightning_Flash_Location(bool* Found_Valid_Location, struct FVector* Loc);
	void Lightning_Flash_Period(double* Period);
	double Night_Scale();
	void Lightning_World_Height(double* Height);
	void Load_Required_Assets();
	void Load_Soft_Object_Array(TArray<TSoftObjectPtr<class UObject>>& In);
	void Make_Dust_Component();
	void Make_Lightning_Flash_Systems();
	void Make_Manual_State();
	void Make_Obscured_Lightning_Component();
	void Make_Outdoor_Sound_Mix();
	void Make_Rain_Component();
	void Make_Random_Stream();
	void Make_Snow_Component();
	void Make_Wind_Debris_Component();
	void Manual_Override_to_State_Value(class UUDS_Weather_Settings_C* Target, int32 Index_0);
	void Material_Effect_Draw_Color_from_State(class UUDS_Weather_Settings_C* State, double Alpha, struct FLinearColor* Color);
	void Monitor_Local_Weather_Changes();
	double Obscured_Lightning_Current_Spawn_Rate();
	void Obscured_Lightning_System_Finished(class UNiagaraComponent* PSystem);
	void Open_Editor_Readme_Entry(const class FString& Entry_Row);
	void Open_Editor_Readme_Entry_Set(const TArray<class FName>& Entries);
	void Populate_Weather_State(class UUDS_Weather_Settings_C* State, double Cloud_Coverage_0, double Rain_0, double Snow_0, double Param_Lightning_0, double Wind_Intensity_0, double Param_Fog_0, double Param_Dust_0, double Material_Wetness_0, double Material_Snow, double Material_Dust);
	void PPWF_Intensity(double* Out);
	void Query_Project_Settings();
	void Rain_Niagara_System_Asset(TSoftObjectPtr<class UNiagaraSystem>* Out);
	double Rain_Spawn_Rate();
	void Rain_System_Finished(class UNiagaraComponent* PSystem);
	double Rainbow_Strength();
	void Randomize_Screen_Frost_Offset();
	void ReceiveBeginPlay();
	void ReceiveEndPlay(EEndPlayReason EndPlayReason);
	void Recenter_DLWE_Render_Target();
	void Report_Removal_Of_Mask_Component(class UWeatherMask_C* Component);
	void Report_Removed_Radial_Storm(class AActor* Storm, bool* Success);
	void Restart_Random_Weather_Variation();
	bool Runtime_Or_Initializing();
	void Screen_Droplets_Periodic_Updates();
	void Screen_Frost_Startup();
	void Second_Frame_Startup_Functions();
	void Set_All_Update_Checks(bool On);
	void Set_Current_Control_Point_Location();
	void Set_Current_Global_Weather_State();
	void Dust_Parameters();
	void Set_Random_Time_Offset();
	void Set_Replicated_Old_Weather_State();
	void Set_Season(double Season_0);
	void Set_Shared_Weather_Particle_Parameters(class UNiagaraComponent* System);
	void Set_Startup_Variables();
	void Set_UDS_Reference();
	void Set_Weather_Values_Prepped_for_UDS();
	void Set_WOV_Render_Target_Mapping();
	double Sky_Cloud_Speed();
	double _Dust_Velocity_Randomization(double Low_Wind, double High_Wind);
	void Snow_Niagara_System_Asset(TSoftObjectPtr<class UNiagaraSystem>* Out);
	double Snow_Spawn_Rate();
	void Snow_System_Finished(class UNiagaraComponent* PSystem);
	void Sort_Weather_Override_Volumes();
	void Sparse_Movement_Updates();
	void Start_Active_Timers();
	void Start_Lightning_Flash();
	void Start_Up_DLWE_Interaction_System();
	void Start_Up_Render_Targets();
	void Startup_Static_Mode();
	void Static_Mode_Tick();
	void Static_Properties___DLWE();
	void Static_Properties___Dust();
	void Static_Properties___Heat_Distortion();
	void Static_Properties___Lightning();
	void Static_Properties___Material_Effects();
	void Static_Properties___Occlusion();
	void Static_Properties___Post_Process_Wind_Fog();
	void Static_Properties___Post_Processing();
	void Static_Properties___Rain();
	void Static_Properties___Rainbow();
	void Static_Properties___Screen_Droplets();
	void Static_Properties___Screen_Frost();
	void Static_Properties___Shared_Particles();
	void Static_Properties___Snow();
	void Static_Properties___Sound_Effects();
	void Static_Properties___Wind_Debris();
	double Target_Screen_Frost_Strength();
	void Test_Actor_for_Weather_Exposure(class AActor* Actor, bool Test_Colliding_Components_Only, class USceneComponent* Custom_Component_for_Bounds, double* Rain_Exposure, double* Snow_Exposure, double* Wind_Exposure, double* Dust_Exposure);
	void Test_Component_for_Wind_Exposure(class UPrimitiveComponent* Component, double* Wind_Exposure);
	void Tick_Function();
	void Toggle_Post_Process_Material(int32 Index_0, bool Enabled);
	void Trace_Bounds_for_Exposure_Values(const struct FVector& Bounds_Origin, const struct FVector& Bounds_Extent, TArray<class AActor*>& Actors_to_Ignore, bool Test_Weather, double* Exposure_Value);
	void UDS_Reconstruct(bool* Success);
	void UDS_Weather_Variable_Overrides(bool Override_Clouds, double Cloud_Coverage_0, bool Override_Fog, double Param_Fog_0, bool Override_Dust, double Param_Dust_0, bool* Success);
	void UDW_Runtime_Tick(double Delta_Time);
	void UDW_State_Apply(const struct FUDS_and_UDW_State& State, bool* Completed);
	void Update_Active_Dust_Parameters();
	void Update_Active_Rain_Parameters();
	void Update_Active_Snow_Parameters();
	void Update_Active_Variables();
	void Update_Active_Wind_Debris_Parameters();
	void Update_Current_Global_And_Local_Weather_State();
	void Update_Custom_Weather_Particle_Camera();
	void Update_DLWE_Interaction_Mode();
	void Update_DLWE_Snow_Compressions();
	void Update_Fog_Particle_Parameters(class UFXSystemComponent* Target, double Max_Particle_Percentage, double Weather_Intensity, double Particle_Intensity);
	void Update_Heat_Distortion();
	void Update_Lightning_Flash_Light();
	void Update_Material_Effect_Parameters();
	void Update_Obscured_Lightning_Parameters();
	void Update_Old_State_With_Replicated_Variables();
	void Update_Outdoor_Sound_Mix();
	void Update_Post_Process_Wind_Fog();
	void Update_Rainbow();
	void Update_Screen_Droplets();
	void Update_Screen_Frost();
	void Update_Season();
	void Update_Sound_Occlusion_Parameters();
	void Update_Sounds_With_Weather_State();
	void Update_Static_Variables();
	void Update_Target_Screen_Frost();
	void Update_Underwater_State();
	void Update_Weather_Mask_Target();
	void Update_Wind_Directional_Source_Actor();
	void Update_WOV_Render_Target();
	void UserConstructionScript();
	void Version_Specific_Correction();
	void Warm_Up_Niagara_Systems();
	void Weather_Startup_Functions();
	void Weather_State_Object_to_Structure(class UUDS_Weather_Settings_C* State, struct FUDW_WeatherState_Structure* Structure);
	void Wind_Debris_Niagara_System_Asset(TSoftObjectPtr<class UNiagaraSystem>* Out);
	double Wind_Debris_Spawn_Rate();
	void Wind_Debris_System_Finished(class UNiagaraComponent* PSystem);
	struct FVector Wind_Force_Vector();
	void Wind_Rotation(struct FRotator* Rot);
	void __Dust_Particles();
	void __Dynamic_Landscape_Weather_Effects();
	void __Event_Dispatchers();
	void __Heat_Distortion();
	void __Lightning();
	void __Manual_Weather_State();
	void __Material_Effects();
	void __Post_Process_Wind_Fog();
	void __Rain_Particles();
	void __Rainbow();
	void __Random_Weather_Variation();
	void __Screen_Droplets();
	void __Screen_Frost();
	void __Season();
	void __Snow_Particles();
	void __Sound_Effects();
	void __Sound_Occlusion();
	void __Temperature();
	void __Volumetric_Fog_Particles();
	void __Water_Level();
	void __Weather_Above_Volumetric_Clouds();
	void __Weather_Documentation();
	void __Weather_Mask_Tools();
	void __Weather_Override_Volumes();
	void __Weather_Particles();
	void __Wind_Debris();
	void __Wind_Direction();
	void __Wind_Directional_Source();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"Ultra_Dynamic_Weather_C">();
	}
	static class AUltra_Dynamic_Weather_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<AUltra_Dynamic_Weather_C>();
	}
};
static_assert(alignof(AUltra_Dynamic_Weather_C) == 0x000008, "Wrong alignment on AUltra_Dynamic_Weather_C");
static_assert(sizeof(AUltra_Dynamic_Weather_C) == 0x001B80, "Wrong size on AUltra_Dynamic_Weather_C");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UberGraphFrame) == 0x000298, "Member 'AUltra_Dynamic_Weather_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PostProcess) == 0x0002A0, "Member 'AUltra_Dynamic_Weather_C::PostProcess' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Sound_Global) == 0x0002A8, "Member 'AUltra_Dynamic_Weather_C::Sound_Global' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Sound_Directional_Y) == 0x0002B0, "Member 'AUltra_Dynamic_Weather_C::Sound_Directional_Y' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Sound_Directional_X) == 0x0002B8, "Member 'AUltra_Dynamic_Weather_C::Sound_Directional_X' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Temperature_Manager) == 0x0002C0, "Member 'AUltra_Dynamic_Weather_C::Temperature_Manager' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_State_Manager) == 0x0002C8, "Member 'AUltra_Dynamic_Weather_C::Material_State_Manager' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Spawn_Manager) == 0x0002D0, "Member 'AUltra_Dynamic_Weather_C::Lightning_Spawn_Manager' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Player_Occlusion) == 0x0002D8, "Member 'AUltra_Dynamic_Weather_C::Player_Occlusion' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Random_Weather_Manager) == 0x0002E0, "Member 'AUltra_Dynamic_Weather_C::Random_Weather_Manager' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Light) == 0x0002E8, "Member 'AUltra_Dynamic_Weather_C::Lightning_Light' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Root) == 0x0002F0, "Member 'AUltra_Dynamic_Weather_C::Root' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UltraDynamicSky) == 0x0002F8, "Member 'AUltra_Dynamic_Weather_C::UltraDynamicSky' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Refresh_Settings) == 0x000300, "Member 'AUltra_Dynamic_Weather_C::Refresh_Settings' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather) == 0x000308, "Member 'AUltra_Dynamic_Weather_C::Weather' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Cloud_Coverage) == 0x000310, "Member 'AUltra_Dynamic_Weather_C::Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Cloud_Coverage___Manual_Override) == 0x000318, "Member 'AUltra_Dynamic_Weather_C::Cloud_Coverage___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain) == 0x000320, "Member 'AUltra_Dynamic_Weather_C::Rain' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain___Manual_Override) == 0x000328, "Member 'AUltra_Dynamic_Weather_C::Rain___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow) == 0x000330, "Member 'AUltra_Dynamic_Weather_C::Snow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow___Manual_Override) == 0x000338, "Member 'AUltra_Dynamic_Weather_C::Snow___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning) == 0x000340, "Member 'AUltra_Dynamic_Weather_C::Lightning' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning___Manual_Override) == 0x000348, "Member 'AUltra_Dynamic_Weather_C::Lightning___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Intensity) == 0x000350, "Member 'AUltra_Dynamic_Weather_C::Wind_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Intensity___Manual_Override) == 0x000358, "Member 'AUltra_Dynamic_Weather_C::Wind_Intensity___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog) == 0x000360, "Member 'AUltra_Dynamic_Weather_C::Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog___Manual_Override) == 0x000368, "Member 'AUltra_Dynamic_Weather_C::Fog___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust) == 0x000370, "Member 'AUltra_Dynamic_Weather_C::Dust' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust___Manual_Override) == 0x000378, "Member 'AUltra_Dynamic_Weather_C::Dust___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Wetness) == 0x000380, "Member 'AUltra_Dynamic_Weather_C::Material_Wetness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Wetness___Manual_Override) == 0x000388, "Member 'AUltra_Dynamic_Weather_C::Material_Wetness___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Snow_Coverage) == 0x000390, "Member 'AUltra_Dynamic_Weather_C::Material_Snow_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Snow_Coverage___Manual_Override) == 0x000398, "Member 'AUltra_Dynamic_Weather_C::Material_Snow_Coverage___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Dust_Coverage) == 0x0003A0, "Member 'AUltra_Dynamic_Weather_C::Material_Dust_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Dust_Coverage___Manual_Override) == 0x0003A8, "Member 'AUltra_Dynamic_Weather_C::Material_Dust_Coverage___Manual_Override' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Direction) == 0x0003B0, "Member 'AUltra_Dynamic_Weather_C::Wind_Direction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Transition_Duration) == 0x0003B8, "Member 'AUltra_Dynamic_Weather_C::Transition_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Transition_Timer) == 0x0003C0, "Member 'AUltra_Dynamic_Weather_C::Transition_Timer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Particle_Collision_Mode) == 0x0003C8, "Member 'AUltra_Dynamic_Weather_C::Particle_Collision_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Ceiling_Check_Height) == 0x0003D0, "Member 'AUltra_Dynamic_Weather_C::Ceiling_Check_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Spawn_Direction_Forward_Bias) == 0x0003D8, "Member 'AUltra_Dynamic_Weather_C::Spawn_Direction_Forward_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Spawn_Box_Height) == 0x0003E0, "Member 'AUltra_Dynamic_Weather_C::Spawn_Box_Height' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Spawn_Distance) == 0x0003E8, "Member 'AUltra_Dynamic_Weather_C::Max_Spawn_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Spawn_Distance_Distribution) == 0x0003F0, "Member 'AUltra_Dynamic_Weather_C::Spawn_Distance_Distribution' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Minimum_Particle_Distance) == 0x0003F8, "Member 'AUltra_Dynamic_Weather_C::Minimum_Particle_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, World_Spawn_Offset) == 0x000400, "Member 'AUltra_Dynamic_Weather_C::World_Spawn_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Camera_Forward_Spawn_Offset) == 0x000418, "Member 'AUltra_Dynamic_Weather_C::Camera_Forward_Spawn_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Use_UDS_Water_Level) == 0x000420, "Member 'AUltra_Dynamic_Weather_C::Use_UDS_Water_Level' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Particle_Collision_Channel) == 0x000421, "Member 'AUltra_Dynamic_Weather_C::Weather_Particle_Collision_Channel' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Rain_Particles) == 0x000422, "Member 'AUltra_Dynamic_Weather_C::Enable_Rain_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Particle_Spawn_Count) == 0x000428, "Member 'AUltra_Dynamic_Weather_C::Rain_Particle_Spawn_Count' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Drops_Scale) == 0x000430, "Member 'AUltra_Dynamic_Weather_C::Rain_Drops_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Velocity_Randomization) == 0x000438, "Member 'AUltra_Dynamic_Weather_C::Rain_Velocity_Randomization' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Particle_Color_Multiplier) == 0x000440, "Member 'AUltra_Dynamic_Weather_C::Rain_Particle_Color_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Drops_Alpha) == 0x000450, "Member 'AUltra_Dynamic_Weather_C::Rain_Drops_Alpha' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Refraction_Intensity) == 0x000458, "Member 'AUltra_Dynamic_Weather_C::Rain_Refraction_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Ambient_Light_Intensity) == 0x000460, "Member 'AUltra_Dynamic_Weather_C::Rain_Ambient_Light_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Light_Sparkle) == 0x000468, "Member 'AUltra_Dynamic_Weather_C::Rain_Light_Sparkle' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Particles_Time_Dilation) == 0x000470, "Member 'AUltra_Dynamic_Weather_C::Rain_Particles_Time_Dilation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Render_Raindrop_Decals) == 0x000474, "Member 'AUltra_Dynamic_Weather_C::Render_Raindrop_Decals' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Ripple_Normal_Decals) == 0x000475, "Member 'AUltra_Dynamic_Weather_C::Rain_Ripple_Normal_Decals' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Frequency) == 0x000478, "Member 'AUltra_Dynamic_Weather_C::Splash_Frequency' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Particles_Scale) == 0x000480, "Member 'AUltra_Dynamic_Weather_C::Splash_Particles_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Droplets_Scale) == 0x000488, "Member 'AUltra_Dynamic_Weather_C::Splash_Droplets_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Particles_Alpha) == 0x000490, "Member 'AUltra_Dynamic_Weather_C::Splash_Particles_Alpha' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Minimum_Z_Normal) == 0x000498, "Member 'AUltra_Dynamic_Weather_C::Splash_Minimum_Z_Normal' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Raindrop_Decal_Scale) == 0x0004A0, "Member 'AUltra_Dynamic_Weather_C::Raindrop_Decal_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Particles_Rendering_Mode) == 0x0004A8, "Member 'AUltra_Dynamic_Weather_C::Splash_Particles_Rendering_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Duplicate_Splash_Range) == 0x0004B0, "Member 'AUltra_Dynamic_Weather_C::Max_Duplicate_Splash_Range' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Snow_Particles) == 0x0004B8, "Member 'AUltra_Dynamic_Weather_C::Enable_Snow_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Particle_Spawn_Count) == 0x0004C0, "Member 'AUltra_Dynamic_Weather_C::Snow_Particle_Spawn_Count' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Flakes_Scale) == 0x0004C8, "Member 'AUltra_Dynamic_Weather_C::Snow_Flakes_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Velocity_Randomization) == 0x0004D0, "Member 'AUltra_Dynamic_Weather_C::Snow_Velocity_Randomization' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Flakes_Alpha) == 0x0004D8, "Member 'AUltra_Dynamic_Weather_C::Snow_Flakes_Alpha' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Ambient_Light_Intensity) == 0x0004E0, "Member 'AUltra_Dynamic_Weather_C::Snow_Ambient_Light_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Flakes_Surface_Stick_Duration) == 0x0004E8, "Member 'AUltra_Dynamic_Weather_C::Snow_Flakes_Surface_Stick_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Particles_Time_Dilation) == 0x0004F0, "Member 'AUltra_Dynamic_Weather_C::Snow_Particles_Time_Dilation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Particle_Color_Multiplier) == 0x0004F8, "Member 'AUltra_Dynamic_Weather_C::Snow_Particle_Color_Multiplier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Spawn_Lightning_Flashes) == 0x000508, "Member 'AUltra_Dynamic_Weather_C::Spawn_Lightning_Flashes' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Frequency) == 0x000510, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Frequency' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Timing_Randomization) == 0x000518, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Timing_Randomization' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Effect_Tint_Color) == 0x000520, "Member 'AUltra_Dynamic_Weather_C::Lightning_Effect_Tint_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Light_Source) == 0x000530, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Light_Source' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Light_Source_Color) == 0x000534, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Light_Source_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Maximum_Lightning_Flash_Light_Intensity) == 0x000548, "Member 'AUltra_Dynamic_Weather_C::Maximum_Lightning_Flash_Light_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flashes_Cast_Shadows) == 0x000550, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flashes_Cast_Shadows' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flashes_Cast_Light_Shaft_Bloom) == 0x000551, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flashes_Cast_Light_Shaft_Bloom' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Light_Shaft_Intensity) == 0x000558, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Light_Shaft_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Distance_Range) == 0x000560, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Distance_Range' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Duration) == 0x000570, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Height_Offset) == 0x000580, "Member 'AUltra_Dynamic_Weather_C::Lightning_Height_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Bolt_Length) == 0x000588, "Member 'AUltra_Dynamic_Weather_C::Lightning_Bolt_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Bolt_Thickness_Scale) == 0x000590, "Member 'AUltra_Dynamic_Weather_C::Lightning_Bolt_Thickness_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Replicate_Lightning_Direction) == 0x000598, "Member 'AUltra_Dynamic_Weather_C::Replicate_Lightning_Direction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Obscured_Lightning) == 0x000599, "Member 'AUltra_Dynamic_Weather_C::Enable_Obscured_Lightning' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Obscured_Lightning_Spawn_Rate) == 0x0005A0, "Member 'AUltra_Dynamic_Weather_C::Obscured_Lightning_Spawn_Rate' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Fog_Particles) == 0x0005A8, "Member 'AUltra_Dynamic_Weather_C::Enable_Fog_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Fog_Particle_Percentage__Rain_) == 0x0005B0, "Member 'AUltra_Dynamic_Weather_C::Max_Fog_Particle_Percentage__Rain_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog_Particle_Intensity__Rain_) == 0x0005B8, "Member 'AUltra_Dynamic_Weather_C::Fog_Particle_Intensity__Rain_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Fog_Particle_Percentage__Snow_) == 0x0005C0, "Member 'AUltra_Dynamic_Weather_C::Max_Fog_Particle_Percentage__Snow_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog_Particle_Intensity__Snow_) == 0x0005C8, "Member 'AUltra_Dynamic_Weather_C::Fog_Particle_Intensity__Snow_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Fog_Particle_Percentage__Dust_) == 0x0005D0, "Member 'AUltra_Dynamic_Weather_C::Max_Fog_Particle_Percentage__Dust_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog_Particle_Intensity__Dust_) == 0x0005D8, "Member 'AUltra_Dynamic_Weather_C::Fog_Particle_Intensity__Dust_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog_Particles_Draw_Distance) == 0x0005E0, "Member 'AUltra_Dynamic_Weather_C::Fog_Particles_Draw_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Directional_Source_Actor) == 0x0005E8, "Member 'AUltra_Dynamic_Weather_C::Wind_Directional_Source_Actor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Material_Wetness) == 0x0005F0, "Member 'AUltra_Dynamic_Weather_C::Max_Material_Wetness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Material_Snow_Coverage) == 0x0005F8, "Member 'AUltra_Dynamic_Weather_C::Max_Material_Snow_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Dust_Coverage) == 0x000600, "Member 'AUltra_Dynamic_Weather_C::Max_Dust_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Snow_Color) == 0x000608, "Member 'AUltra_Dynamic_Weather_C::Material_Snow_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Dust_Color) == 0x000618, "Member 'AUltra_Dynamic_Weather_C::Material_Dust_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Water_Roughness) == 0x000628, "Member 'AUltra_Dynamic_Weather_C::Material_Water_Roughness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Tiling_Raindrop_Ripples_Scale) == 0x000630, "Member 'AUltra_Dynamic_Weather_C::Tiling_Raindrop_Ripples_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Simulate_Changing_Material_State_Over_Time) == 0x000638, "Member 'AUltra_Dynamic_Weather_C::Simulate_Changing_Material_State_Over_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wetness_Coverage_Duration) == 0x000640, "Member 'AUltra_Dynamic_Weather_C::Wetness_Coverage_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wetness_Dry_Duration) == 0x000648, "Member 'AUltra_Dynamic_Weather_C::Wetness_Dry_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wetness_Dry_Speed_in_Sunlight) == 0x000650, "Member 'AUltra_Dynamic_Weather_C::Wetness_Dry_Speed_in_Sunlight' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wetness_Dry_Speed_without_Sunlight) == 0x000658, "Member 'AUltra_Dynamic_Weather_C::Wetness_Dry_Speed_without_Sunlight' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Coverage_Duration) == 0x000660, "Member 'AUltra_Dynamic_Weather_C::Snow_Coverage_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Melt_Duration) == 0x000668, "Member 'AUltra_Dynamic_Weather_C::Snow_Melt_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Melt_Speed_Above_Freezing) == 0x000670, "Member 'AUltra_Dynamic_Weather_C::Snow_Melt_Speed_Above_Freezing' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Melt_Speed_Below_Freezing) == 0x000678, "Member 'AUltra_Dynamic_Weather_C::Snow_Melt_Speed_Below_Freezing' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Coverage_Duration) == 0x000680, "Member 'AUltra_Dynamic_Weather_C::Dust_Coverage_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Clear_Duration) == 0x000688, "Member 'AUltra_Dynamic_Weather_C::Dust_Clear_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Clear_Speed_when_Windy) == 0x000690, "Member 'AUltra_Dynamic_Weather_C::Dust_Clear_Speed_when_Windy' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Clear_Speed_when_Calm) == 0x000698, "Member 'AUltra_Dynamic_Weather_C::Dust_Clear_Speed_when_Calm' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Melted_Snow_Coverage_Contributes_to_Wetness) == 0x0006A0, "Member 'AUltra_Dynamic_Weather_C::Melted_Snow_Coverage_Contributes_to_Wetness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Coverage_Lights_Up_Height_Fog) == 0x0006A8, "Member 'AUltra_Dynamic_Weather_C::Snow_Coverage_Lights_Up_Height_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Force_Scale) == 0x0006B0, "Member 'AUltra_Dynamic_Weather_C::Wind_Force_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Random_Weather_Variation) == 0x0006B8, "Member 'AUltra_Dynamic_Weather_C::Random_Weather_Variation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Random_Weather_Change_Interval) == 0x0006BC, "Member 'AUltra_Dynamic_Weather_C::Random_Weather_Change_Interval' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Random_Weather_Change_Hour) == 0x0006CC, "Member 'AUltra_Dynamic_Weather_C::Random_Weather_Change_Hour' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Transition_Length) == 0x0006D0, "Member 'AUltra_Dynamic_Weather_C::Transition_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Begin_Play_Weather_Is_Random) == 0x0006D8, "Member 'AUltra_Dynamic_Weather_C::Begin_Play_Weather_Is_Random' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Apply_Climate_Preset) == 0x0006E0, "Member 'AUltra_Dynamic_Weather_C::Apply_Climate_Preset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Type_Probabilities__Spring_) == 0x0006E8, "Member 'AUltra_Dynamic_Weather_C::Weather_Type_Probabilities__Spring_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Type_Probabilities__Summer_) == 0x000738, "Member 'AUltra_Dynamic_Weather_C::Weather_Type_Probabilities__Summer_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Type_Probabilities__Autumn_) == 0x000788, "Member 'AUltra_Dynamic_Weather_C::Weather_Type_Probabilities__Autumn_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Type_Probabilities__Winter_) == 0x0007D8, "Member 'AUltra_Dynamic_Weather_C::Weather_Type_Probabilities__Winter_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Random_Weather_Forecast_Steps) == 0x000828, "Member 'AUltra_Dynamic_Weather_C::Random_Weather_Forecast_Steps' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Blend_Season_Probabilities) == 0x00082C, "Member 'AUltra_Dynamic_Weather_C::Blend_Season_Probabilities' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Avoid_Extreme_Cloud_Coverage_Shifts) == 0x00082D, "Member 'AUltra_Dynamic_Weather_C::Avoid_Extreme_Cloud_Coverage_Shifts' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Avoid_Repeating_Weather_Types) == 0x00082E, "Member 'AUltra_Dynamic_Weather_C::Avoid_Repeating_Weather_Types' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Avoid_Changing_Directly_from_Snow_to_Rain__Or_Vice_Versa_) == 0x00082F, "Member 'AUltra_Dynamic_Weather_C::Avoid_Changing_Directly_from_Snow_to_Rain__Or_Vice_Versa_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Specific_Interval_Multipliers) == 0x000830, "Member 'AUltra_Dynamic_Weather_C::Weather_Specific_Interval_Multipliers' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Extreme_Cloud_Coverage_Shift_Theshold) == 0x000880, "Member 'AUltra_Dynamic_Weather_C::Extreme_Cloud_Coverage_Shift_Theshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Weather_Sound_Effects) == 0x000888, "Member 'AUltra_Dynamic_Weather_C::Enable_Weather_Sound_Effects' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Volume) == 0x000890, "Member 'AUltra_Dynamic_Weather_C::Rain_Volume' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Distant_Thunder_Volume) == 0x000898, "Member 'AUltra_Dynamic_Weather_C::Distant_Thunder_Volume' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Close_Thunder_Volume) == 0x0008A0, "Member 'AUltra_Dynamic_Weather_C::Close_Thunder_Volume' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Close_Thunder_Delay_Per_KM) == 0x0008A8, "Member 'AUltra_Dynamic_Weather_C::Close_Thunder_Delay_Per_KM' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Volume) == 0x0008B0, "Member 'AUltra_Dynamic_Weather_C::Wind_Volume' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Whistling_Volume) == 0x0008B8, "Member 'AUltra_Dynamic_Weather_C::Wind_Whistling_Volume' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog_Particle_Camera_Offset) == 0x0008C0, "Member 'AUltra_Dynamic_Weather_C::Fog_Particle_Camera_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Directional_Source_Intensity_Scale) == 0x0008C8, "Member 'AUltra_Dynamic_Weather_C::Wind_Directional_Source_Intensity_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Obscured_Lightning_Scale) == 0x0008D0, "Member 'AUltra_Dynamic_Weather_C::Obscured_Lightning_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Max_Angle_From_Forward) == 0x0008D8, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Max_Angle_From_Forward' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Show_Lightning_Flashes_in_Level_Editor) == 0x0008E0, "Member 'AUltra_Dynamic_Weather_C::Show_Lightning_Flashes_in_Level_Editor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Daytime_Lightning_Flash_Intensity) == 0x0008E8, "Member 'AUltra_Dynamic_Weather_C::Daytime_Lightning_Flash_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Nighttime_Lightning_Flash_Intensity) == 0x0008F0, "Member 'AUltra_Dynamic_Weather_C::Nighttime_Lightning_Flash_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Active) == 0x0008F8, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Time_Elapsed) == 0x000900, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Time_Elapsed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Length) == 0x000908, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Temperature_Scale) == 0x000910, "Member 'AUltra_Dynamic_Weather_C::Temperature_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Summer_Temperature_Min_and_Max) == 0x000918, "Member 'AUltra_Dynamic_Weather_C::Summer_Temperature_Min_and_Max' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Autumn_Temperature_Min_and_Max) == 0x000928, "Member 'AUltra_Dynamic_Weather_C::Autumn_Temperature_Min_and_Max' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Winter_Temperature_Min_and_Max) == 0x000938, "Member 'AUltra_Dynamic_Weather_C::Winter_Temperature_Min_and_Max' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Spring_Temperature_Min_and_Max) == 0x000948, "Member 'AUltra_Dynamic_Weather_C::Spring_Temperature_Min_and_Max' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Daytime_Temperature_Bias) == 0x000958, "Member 'AUltra_Dynamic_Weather_C::Daytime_Temperature_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Nighttime_Temperature_Bias) == 0x000960, "Member 'AUltra_Dynamic_Weather_C::Nighttime_Temperature_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Evening_Temperature_Bias) == 0x000968, "Member 'AUltra_Dynamic_Weather_C::Evening_Temperature_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Overcast_Temperature_Bias) == 0x000970, "Member 'AUltra_Dynamic_Weather_C::Overcast_Temperature_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Raining_Temperature_Bias) == 0x000978, "Member 'AUltra_Dynamic_Weather_C::Raining_Temperature_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snowing_Temperature_Bias) == 0x000980, "Member 'AUltra_Dynamic_Weather_C::Snowing_Temperature_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Foggy_Temperature_Bias) == 0x000988, "Member 'AUltra_Dynamic_Weather_C::Foggy_Temperature_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dusty_Temperature_Bias) == 0x000990, "Member 'AUltra_Dynamic_Weather_C::Dusty_Temperature_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Randomize_Temperature) == 0x000998, "Member 'AUltra_Dynamic_Weather_C::Randomize_Temperature' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Interior_Temperature) == 0x0009A0, "Member 'AUltra_Dynamic_Weather_C::Interior_Temperature' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Interior_Insulation) == 0x0009A8, "Member 'AUltra_Dynamic_Weather_C::Interior_Insulation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Control_Point_Location_Source) == 0x0009B0, "Member 'AUltra_Dynamic_Weather_C::Control_Point_Location_Source' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Custom_Control_Point_Location) == 0x0009B8, "Member 'AUltra_Dynamic_Weather_C::Custom_Control_Point_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Speed) == 0x0009D0, "Member 'AUltra_Dynamic_Weather_C::Weather_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Random_Seed) == 0x0009D8, "Member 'AUltra_Dynamic_Weather_C::Random_Seed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Depth) == 0x0009E0, "Member 'AUltra_Dynamic_Weather_C::Snow_Depth' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Depth) == 0x0009E8, "Member 'AUltra_Dynamic_Weather_C::Dust_Depth' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Z_Normal_Cutoff) == 0x0009F0, "Member 'AUltra_Dynamic_Weather_C::Dust_Z_Normal_Cutoff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Z_Normal_Falloff) == 0x0009F8, "Member 'AUltra_Dynamic_Weather_C::Dust_Z_Normal_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Texture_Scale) == 0x000A00, "Member 'AUltra_Dynamic_Weather_C::Dust_Texture_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interaction_Render_Target_Area) == 0x000A08, "Member 'AUltra_Dynamic_Weather_C::Dust_Interaction_Render_Target_Area' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interaction_Fade_Speed_Idle) == 0x000A10, "Member 'AUltra_Dynamic_Weather_C::Dust_Interaction_Fade_Speed_Idle' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interaction_Fade_Speed_Active) == 0x000A18, "Member 'AUltra_Dynamic_Weather_C::Dust_Interaction_Fade_Speed_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Player_Camera_Location) == 0x000A20, "Member 'AUltra_Dynamic_Weather_C::Player_Camera_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Control_Point_Location) == 0x000A38, "Member 'AUltra_Dynamic_Weather_C::Control_Point_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Puddle_Coverage) == 0x000A50, "Member 'AUltra_Dynamic_Weather_C::Puddle_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Puddles_Z_Normal_Cutoff) == 0x000A58, "Member 'AUltra_Dynamic_Weather_C::Puddles_Z_Normal_Cutoff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Puddles_Z_Normal_Falloff) == 0x000A60, "Member 'AUltra_Dynamic_Weather_C::Puddles_Z_Normal_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Base_Wetness_when_Raining) == 0x000A68, "Member 'AUltra_Dynamic_Weather_C::Base_Wetness_when_Raining' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Base_Wetness_when_Clear) == 0x000A70, "Member 'AUltra_Dynamic_Weather_C::Base_Wetness_when_Clear' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Puddle_Sharpness) == 0x000A78, "Member 'AUltra_Dynamic_Weather_C::Puddle_Sharpness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Variation_Clouds_Scale) == 0x000A80, "Member 'AUltra_Dynamic_Weather_C::Variation_Clouds_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interaction_Edge_Piling) == 0x000A88, "Member 'AUltra_Dynamic_Weather_C::Dust_Interaction_Edge_Piling' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interaction_Depth) == 0x000A90, "Member 'AUltra_Dynamic_Weather_C::Dust_Interaction_Depth' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interaction_Parallax_Sample_Scale) == 0x000A98, "Member 'AUltra_Dynamic_Weather_C::Dust_Interaction_Parallax_Sample_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interaction_Scale) == 0x000AA0, "Member 'AUltra_Dynamic_Weather_C::Dust_Interaction_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interaction_Scatter) == 0x000AA8, "Member 'AUltra_Dynamic_Weather_C::Dust_Interaction_Scatter' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interaction_Size_Scatter) == 0x000AB0, "Member 'AUltra_Dynamic_Weather_C::Dust_Interaction_Size_Scatter' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Interactions_Update_Period) == 0x000AB8, "Member 'AUltra_Dynamic_Weather_C::Dust_Interactions_Update_Period' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Sounds_and_Particles) == 0x000AC0, "Member 'AUltra_Dynamic_Weather_C::Dust_Sounds_and_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Physical_Materials_which_disable_Puddle_Sounds_and_Particles) == 0x000AD0, "Member 'AUltra_Dynamic_Weather_C::Physical_Materials_which_disable_Puddle_Sounds_and_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_WOV_Material_Effect_Target) == 0x000AE0, "Member 'AUltra_Dynamic_Weather_C::Enable_WOV_Material_Effect_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, WOV_Target_Size) == 0x000AE8, "Member 'AUltra_Dynamic_Weather_C::WOV_Target_Size' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, WOV_Target_Update_Threshold__Inside_Volume_) == 0x000AF0, "Member 'AUltra_Dynamic_Weather_C::WOV_Target_Update_Threshold__Inside_Volume_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, WOV_Target_Update_Threshold__Outside_Volume_) == 0x000AF8, "Member 'AUltra_Dynamic_Weather_C::WOV_Target_Update_Threshold__Outside_Volume_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Override_Volumes) == 0x000B00, "Member 'AUltra_Dynamic_Weather_C::Weather_Override_Volumes' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Use_Custom_Lightning_Location) == 0x000B10, "Member 'AUltra_Dynamic_Weather_C::Use_Custom_Lightning_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Custom_Lightning_Location) == 0x000B18, "Member 'AUltra_Dynamic_Weather_C::Custom_Lightning_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Custom_Lightning_Target) == 0x000B30, "Member 'AUltra_Dynamic_Weather_C::Custom_Lightning_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Lightning_Intensity) == 0x000B48, "Member 'AUltra_Dynamic_Weather_C::Current_Lightning_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Lightning_Intensity_Scale) == 0x000B50, "Member 'AUltra_Dynamic_Weather_C::Current_Lightning_Intensity_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Lightning_Location) == 0x000B58, "Member 'AUltra_Dynamic_Weather_C::Current_Lightning_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Lightning_Angle) == 0x000B70, "Member 'AUltra_Dynamic_Weather_C::Current_Lightning_Angle' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Weather_Override_Volume) == 0x000B78, "Member 'AUltra_Dynamic_Weather_C::Current_Weather_Override_Volume' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_WOVs_Applied) == 0x000B80, "Member 'AUltra_Dynamic_Weather_C::Current_WOVs_Applied' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Currently_in_a_Weather_Override_Volume) == 0x000BD0, "Member 'AUltra_Dynamic_Weather_C::Currently_in_a_Weather_Override_Volume' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, DLWE_Mask_Target) == 0x000BD8, "Member 'AUltra_Dynamic_Weather_C::DLWE_Mask_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, DLWE_Recenter_Offset) == 0x000BE0, "Member 'AUltra_Dynamic_Weather_C::DLWE_Recenter_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Cloud_Reference_Array) == 0x000BF8, "Member 'AUltra_Dynamic_Weather_C::Cloud_Reference_Array' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, DLWE_Interactions_Buffer) == 0x000C08, "Member 'AUltra_Dynamic_Weather_C::DLWE_Interactions_Buffer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Use_Occlusion_To_Attenuate_Sounds_In_Interiors) == 0x000C18, "Member 'AUltra_Dynamic_Weather_C::Use_Occlusion_To_Attenuate_Sounds_In_Interiors' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Attenuation) == 0x000C20, "Member 'AUltra_Dynamic_Weather_C::Max_Attenuation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Min_LPF_Frequency) == 0x000C28, "Member 'AUltra_Dynamic_Weather_C::Min_LPF_Frequency' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Occlusion_Settings) == 0x000C30, "Member 'AUltra_Dynamic_Weather_C::Occlusion_Settings' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Post_Process_Wind_Fog) == 0x000C38, "Member 'AUltra_Dynamic_Weather_C::Enable_Post_Process_Wind_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PPWF_Intensity_From_Fog) == 0x000C40, "Member 'AUltra_Dynamic_Weather_C::PPWF_Intensity_From_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PPWF_Intensity_from_Rain) == 0x000C48, "Member 'AUltra_Dynamic_Weather_C::PPWF_Intensity_from_Rain' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PPWF_Intensity_From_Snow) == 0x000C50, "Member 'AUltra_Dynamic_Weather_C::PPWF_Intensity_From_Snow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PPWF_Intensity_from_Dust) == 0x000C58, "Member 'AUltra_Dynamic_Weather_C::PPWF_Intensity_from_Dust' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PPWF_Intensity_Scale) == 0x000C60, "Member 'AUltra_Dynamic_Weather_C::PPWF_Intensity_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PPWF_Fade_Distance) == 0x000C68, "Member 'AUltra_Dynamic_Weather_C::PPWF_Fade_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PPWF_Max_Sample_Distance) == 0x000C70, "Member 'AUltra_Dynamic_Weather_C::PPWF_Max_Sample_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PPWF_Sample_Step_Distance) == 0x000C78, "Member 'AUltra_Dynamic_Weather_C::PPWF_Sample_Step_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, PPWF_3D_Noise_Scale) == 0x000C80, "Member 'AUltra_Dynamic_Weather_C::PPWF_3D_Noise_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Post_Process_Wind_Fog_MID) == 0x000C88, "Member 'AUltra_Dynamic_Weather_C::Post_Process_Wind_Fog_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Temperature_Update_Period) == 0x000C90, "Member 'AUltra_Dynamic_Weather_C::Temperature_Update_Period' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Time_Random_Offset) == 0x000C98, "Member 'AUltra_Dynamic_Weather_C::Time_Random_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Wind_Direction_Variation) == 0x000CA0, "Member 'AUltra_Dynamic_Weather_C::Enable_Wind_Direction_Variation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Maximum_Wind_Direction_Variation) == 0x000CA8, "Member 'AUltra_Dynamic_Weather_C::Maximum_Wind_Direction_Variation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Variation_Speed) == 0x000CB0, "Member 'AUltra_Dynamic_Weather_C::Variation_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Wind_Direction) == 0x000CB8, "Member 'AUltra_Dynamic_Weather_C::Current_Wind_Direction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Properties) == 0x000CC0, "Member 'AUltra_Dynamic_Weather_C::Properties' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Occlusion_Location) == 0x000D10, "Member 'AUltra_Dynamic_Weather_C::Occlusion_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Started_Raining) == 0x000D28, "Member 'AUltra_Dynamic_Weather_C::Started_Raining' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Started_Snowing) == 0x000D38, "Member 'AUltra_Dynamic_Weather_C::Started_Snowing' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Finished_Raining) == 0x000D48, "Member 'AUltra_Dynamic_Weather_C::Finished_Raining' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Finished_Snowing) == 0x000D58, "Member 'AUltra_Dynamic_Weather_C::Finished_Snowing' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Getting_Cloudy) == 0x000D68, "Member 'AUltra_Dynamic_Weather_C::Getting_Cloudy' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Clouds_Clearing) == 0x000D78, "Member 'AUltra_Dynamic_Weather_C::Clouds_Clearing' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, ED_Raining) == 0x000D88, "Member 'AUltra_Dynamic_Weather_C::ED_Raining' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, ED_Snowy) == 0x000D89, "Member 'AUltra_Dynamic_Weather_C::ED_Snowy' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, ED_Cloudy) == 0x000D8A, "Member 'AUltra_Dynamic_Weather_C::ED_Cloudy' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Screen_Droplets) == 0x000D8B, "Member 'AUltra_Dynamic_Weather_C::Enable_Screen_Droplets' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Center_Strength) == 0x000D90, "Member 'AUltra_Dynamic_Weather_C::Screen_Center_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Edge_Strength) == 0x000D98, "Member 'AUltra_Dynamic_Weather_C::Screen_Edge_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Camera_Facing_Rain_Bias) == 0x000DA0, "Member 'AUltra_Dynamic_Weather_C::Camera_Facing_Rain_Bias' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Droplet_Tiling) == 0x000DA8, "Member 'AUltra_Dynamic_Weather_C::Droplet_Tiling' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Blur_Radius) == 0x000DB0, "Member 'AUltra_Dynamic_Weather_C::Blur_Radius' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wet_Screen_when_Emerging_From_Water) == 0x000DB8, "Member 'AUltra_Dynamic_Weather_C::Wet_Screen_when_Emerging_From_Water' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Droplets_Clear_Speed) == 0x000DC0, "Member 'AUltra_Dynamic_Weather_C::Screen_Droplets_Clear_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Show_Screen_Droplets_in_Editor) == 0x000DC8, "Member 'AUltra_Dynamic_Weather_C::Show_Screen_Droplets_in_Editor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Droplets_MID) == 0x000DD0, "Member 'AUltra_Dynamic_Weather_C::Screen_Droplets_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Drips_Intensity) == 0x000DD8, "Member 'AUltra_Dynamic_Weather_C::Drips_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Drops_Intensity) == 0x000DE0, "Member 'AUltra_Dynamic_Weather_C::Drops_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Drops_Time) == 0x000DE8, "Member 'AUltra_Dynamic_Weather_C::Drops_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Droplets_Parent_Material) == 0x000DF0, "Member 'AUltra_Dynamic_Weather_C::Screen_Droplets_Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Obscured_Lightning_System_Spawning) == 0x000E18, "Member 'AUltra_Dynamic_Weather_C::Obscured_Lightning_System_Spawning' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Dust_Particles) == 0x000E19, "Member 'AUltra_Dynamic_Weather_C::Enable_Dust_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Particle_Spawn_Count) == 0x000E20, "Member 'AUltra_Dynamic_Weather_C::Dust_Particle_Spawn_Count' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Particle_Scale) == 0x000E28, "Member 'AUltra_Dynamic_Weather_C::Dust_Particle_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Velocity_Randomization) == 0x000E30, "Member 'AUltra_Dynamic_Weather_C::Dust_Velocity_Randomization' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Particle_Color) == 0x000E38, "Member 'AUltra_Dynamic_Weather_C::Dust_Particle_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Particle_Alpha) == 0x000E48, "Member 'AUltra_Dynamic_Weather_C::Dust_Particle_Alpha' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Wind_Debris) == 0x000E50, "Member 'AUltra_Dynamic_Weather_C::Enable_Wind_Debris' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Debris_Particle_Spawn_Count) == 0x000E58, "Member 'AUltra_Dynamic_Weather_C::Wind_Debris_Particle_Spawn_Count' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Debris_Scale) == 0x000E60, "Member 'AUltra_Dynamic_Weather_C::Debris_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Debris_Particle_Alpha) == 0x000E68, "Member 'AUltra_Dynamic_Weather_C::Debris_Particle_Alpha' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Debris_Atlas) == 0x000E70, "Member 'AUltra_Dynamic_Weather_C::Wind_Debris_Atlas' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Mask_Target) == 0x000E98, "Member 'AUltra_Dynamic_Weather_C::Weather_Mask_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Mask_Target_Size) == 0x000EA0, "Member 'AUltra_Dynamic_Weather_C::Weather_Mask_Target_Size' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Triggered_Starting_Dispatchers) == 0x000EA4, "Member 'AUltra_Dynamic_Weather_C::Triggered_Starting_Dispatchers' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Old_Weather_State) == 0x000EA8, "Member 'AUltra_Dynamic_Weather_C::Old_Weather_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Global_Weather_State) == 0x000EB0, "Member 'AUltra_Dynamic_Weather_C::Global_Weather_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Local_Weather_State) == 0x000EB8, "Member 'AUltra_Dynamic_Weather_C::Local_Weather_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Update_Buffer) == 0x000EC0, "Member 'AUltra_Dynamic_Weather_C::Update_Buffer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Particles_Time_Dilation) == 0x000EC8, "Member 'AUltra_Dynamic_Weather_C::Dust_Particles_Time_Dilation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Season_Mode) == 0x000ECC, "Member 'AUltra_Dynamic_Weather_C::Season_Mode' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Season) == 0x000ED0, "Member 'AUltra_Dynamic_Weather_C::Season' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Individual_Seasons) == 0x000ED8, "Member 'AUltra_Dynamic_Weather_C::Individual_Seasons' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Manual_Weather_State) == 0x000EE8, "Member 'AUltra_Dynamic_Weather_C::Manual_Weather_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Clouds_Diverse_Texture) == 0x000EF0, "Member 'AUltra_Dynamic_Weather_C::Clouds_Diverse_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Particle_Niagara_System) == 0x000F18, "Member 'AUltra_Dynamic_Weather_C::Rain_Particle_Niagara_System' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Particle_Niagara_System) == 0x000F40, "Member 'AUltra_Dynamic_Weather_C::Snow_Particle_Niagara_System' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Particle_Niagara_System) == 0x000F68, "Member 'AUltra_Dynamic_Weather_C::Dust_Particle_Niagara_System' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Debris_Niagara_System) == 0x000F90, "Member 'AUltra_Dynamic_Weather_C::Wind_Debris_Niagara_System' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Obscured_Lightning_Niagara_System) == 0x000FB8, "Member 'AUltra_Dynamic_Weather_C::Obscured_Lightning_Niagara_System' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Post_Process_Wind_Fog_Parent_Material) == 0x000FE0, "Member 'AUltra_Dynamic_Weather_C::Post_Process_Wind_Fog_Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Sand_Forming) == 0x001008, "Member 'AUltra_Dynamic_Weather_C::Sand_Forming' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Sand_Clearing) == 0x001018, "Member 'AUltra_Dynamic_Weather_C::Sand_Clearing' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, ED_Dusty) == 0x001028, "Member 'AUltra_Dynamic_Weather_C::ED_Dusty' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Rainbow) == 0x001029, "Member 'AUltra_Dynamic_Weather_C::Enable_Rainbow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Rainbow_Strength) == 0x001030, "Member 'AUltra_Dynamic_Weather_C::Max_Rainbow_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Strength_From_Rain) == 0x001038, "Member 'AUltra_Dynamic_Weather_C::Strength_From_Rain' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Strength_From_Fog) == 0x001040, "Member 'AUltra_Dynamic_Weather_C::Strength_From_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Strength_In_Any_Weather) == 0x001048, "Member 'AUltra_Dynamic_Weather_C::Strength_In_Any_Weather' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog_0) == 0x001050, "Member 'AUltra_Dynamic_Weather_C::Fog_0' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Mask_Rainbow_Above_Clouds) == 0x001058, "Member 'AUltra_Dynamic_Weather_C::Mask_Rainbow_Above_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Mask_Rainbow_Below_Water) == 0x001060, "Member 'AUltra_Dynamic_Weather_C::Mask_Rainbow_Below_Water' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, ED_CurrentWeather) == 0x001068, "Member 'AUltra_Dynamic_Weather_C::ED_CurrentWeather' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Display_Name_Changed) == 0x001070, "Member 'AUltra_Dynamic_Weather_C::Weather_Display_Name_Changed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, DLWE_Interaction_Timer) == 0x001080, "Member 'AUltra_Dynamic_Weather_C::DLWE_Interaction_Timer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Support_Virtual_Heightfield_Mesh) == 0x001088, "Member 'AUltra_Dynamic_Weather_C::Support_Virtual_Heightfield_Mesh' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Runtime_Virtual_Texture_Volume) == 0x001090, "Member 'AUltra_Dynamic_Weather_C::VHFM_Runtime_Virtual_Texture_Volume' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Invalidate_Long_Range_Distance) == 0x001098, "Member 'AUltra_Dynamic_Weather_C::VHFM_Invalidate_Long_Range_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Invalidate_Long_Range_Threshold) == 0x0010A0, "Member 'AUltra_Dynamic_Weather_C::VHFM_Invalidate_Long_Range_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Invalidate_Mid_Range_Distance) == 0x0010A8, "Member 'AUltra_Dynamic_Weather_C::VHFM_Invalidate_Mid_Range_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Invalidate_Mid_Range_Threshold) == 0x0010B0, "Member 'AUltra_Dynamic_Weather_C::VHFM_Invalidate_Mid_Range_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Invalidate_Short_Range_Distance) == 0x0010B8, "Member 'AUltra_Dynamic_Weather_C::VHFM_Invalidate_Short_Range_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Invalidate_Short_Range_Threshold) == 0x0010C0, "Member 'AUltra_Dynamic_Weather_C::VHFM_Invalidate_Short_Range_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Invalidate_Long_Range_Buffer) == 0x0010C8, "Member 'AUltra_Dynamic_Weather_C::VHFM_Invalidate_Long_Range_Buffer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Invalidate_Mid_Range_Buffer) == 0x0010E0, "Member 'AUltra_Dynamic_Weather_C::VHFM_Invalidate_Mid_Range_Buffer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, VHFM_Invalidate_Short_Range_Buffer) == 0x0010F8, "Member 'AUltra_Dynamic_Weather_C::VHFM_Invalidate_Short_Range_Buffer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog_Particles_Active) == 0x001110, "Member 'AUltra_Dynamic_Weather_C::Fog_Particles_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Sparse_Movement_Update_Location) == 0x001118, "Member 'AUltra_Dynamic_Weather_C::Last_Sparse_Movement_Update_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Outdoor_Sound_Mix_Modifier) == 0x001130, "Member 'AUltra_Dynamic_Weather_C::Outdoor_Sound_Mix_Modifier' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Max_Attenuation_For_UDS_Outdoor_Sound) == 0x001138, "Member 'AUltra_Dynamic_Weather_C::Max_Attenuation_For_UDS_Outdoor_Sound' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Volume_Target) == 0x001140, "Member 'AUltra_Dynamic_Weather_C::Weather_Volume_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, WOV_Target_Brush_MID) == 0x001168, "Member 'AUltra_Dynamic_Weather_C::WOV_Target_Brush_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, WOV_Material_Effect_Target_Active) == 0x001170, "Member 'AUltra_Dynamic_Weather_C::WOV_Material_Effect_Target_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Shutting_Down) == 0x001171, "Member 'AUltra_Dynamic_Weather_C::Shutting_Down' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UDS_Version) == 0x001178, "Member 'AUltra_Dynamic_Weather_C::UDS_Version' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UDS_Version_Info) == 0x001188, "Member 'AUltra_Dynamic_Weather_C::UDS_Version_Info' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Anti_Aliasing_Method) == 0x001190, "Member 'AUltra_Dynamic_Weather_C::Anti_Aliasing_Method' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Season_Day_Offset) == 0x001194, "Member 'AUltra_Dynamic_Weather_C::Season_Day_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, WOV_Wind_Direction) == 0x001198, "Member 'AUltra_Dynamic_Weather_C::WOV_Wind_Direction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Lightning_Target_Offset) == 0x0011A8, "Member 'AUltra_Dynamic_Weather_C::Current_Lightning_Target_Offset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Material_Decal) == 0x0011C0, "Member 'AUltra_Dynamic_Weather_C::Splash_Material_Decal' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Material_Translucent) == 0x0011E8, "Member 'AUltra_Dynamic_Weather_C::Splash_Material_Translucent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rainbow_MID) == 0x001210, "Member 'AUltra_Dynamic_Weather_C::Rainbow_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Rainbow_Strength) == 0x001218, "Member 'AUltra_Dynamic_Weather_C::Current_Rainbow_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Target_Rainbow_Strength) == 0x001220, "Member 'AUltra_Dynamic_Weather_C::Target_Rainbow_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rainbow_Material_2D) == 0x001228, "Member 'AUltra_Dynamic_Weather_C::Rainbow_Material_2D' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rainbow_Material_Volumetric) == 0x001250, "Member 'AUltra_Dynamic_Weather_C::Rainbow_Material_Volumetric' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Particle_Motion_Stretch) == 0x001278, "Member 'AUltra_Dynamic_Weather_C::Weather_Particle_Motion_Stretch' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Particle_Sprite_Motion_Blur_With_Camera_Movement) == 0x001280, "Member 'AUltra_Dynamic_Weather_C::Particle_Sprite_Motion_Blur_With_Camera_Movement' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Previous_Temperature_Scale) == 0x001288, "Member 'AUltra_Dynamic_Weather_C::Previous_Temperature_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Apply_Changes_Above_Volumetric_Cloud_Layer) == 0x001289, "Member 'AUltra_Dynamic_Weather_C::Apply_Changes_Above_Volumetric_Cloud_Layer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Multiplier_Above_Clouds) == 0x001290, "Member 'AUltra_Dynamic_Weather_C::Rain_Multiplier_Above_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Multiplier_Above_Clouds) == 0x001298, "Member 'AUltra_Dynamic_Weather_C::Snow_Multiplier_Above_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Multiplier_Above_Clouds) == 0x0012A0, "Member 'AUltra_Dynamic_Weather_C::Dust_Multiplier_Above_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog_Multiplier_Above_Clouds) == 0x0012A8, "Member 'AUltra_Dynamic_Weather_C::Fog_Multiplier_Above_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Multiplier_Above_Clouds) == 0x0012B0, "Member 'AUltra_Dynamic_Weather_C::Lightning_Multiplier_Above_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Multiplier_Above_Clouds) == 0x0012B8, "Member 'AUltra_Dynamic_Weather_C::Wind_Multiplier_Above_Clouds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Season_Changed) == 0x0012C0, "Member 'AUltra_Dynamic_Weather_C::Season_Changed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, ED_Season) == 0x0012D0, "Member 'AUltra_Dynamic_Weather_C::ED_Season' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Transition_Active) == 0x0012D4, "Member 'AUltra_Dynamic_Weather_C::Transition_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Transition_Alpha) == 0x0012D8, "Member 'AUltra_Dynamic_Weather_C::Transition_Alpha' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Update_Needed) == 0x0012E0, "Member 'AUltra_Dynamic_Weather_C::Rain_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Update_Needed) == 0x0012E1, "Member 'AUltra_Dynamic_Weather_C::Snow_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Update_Needed) == 0x0012E2, "Member 'AUltra_Dynamic_Weather_C::Lightning_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Intensity_Update_Needed) == 0x0012E3, "Member 'AUltra_Dynamic_Weather_C::Wind_Intensity_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Cloud_Coverage_Update_Needed) == 0x0012E4, "Member 'AUltra_Dynamic_Weather_C::Cloud_Coverage_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Fog_Update_Needed) == 0x0012E5, "Member 'AUltra_Dynamic_Weather_C::Fog_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Update_Needed) == 0x0012E6, "Member 'AUltra_Dynamic_Weather_C::Dust_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Wetness_Update_Needed) == 0x0012E7, "Member 'AUltra_Dynamic_Weather_C::Material_Wetness_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Snow_Update_Needed) == 0x0012E8, "Member 'AUltra_Dynamic_Weather_C::Material_Snow_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Material_Dust_Update_Needed) == 0x0012E9, "Member 'AUltra_Dynamic_Weather_C::Material_Dust_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Direction_Update_Needed) == 0x0012EA, "Member 'AUltra_Dynamic_Weather_C::Wind_Direction_Update_Needed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Direction_Update_Buffer) == 0x0012F0, "Member 'AUltra_Dynamic_Weather_C::Wind_Direction_Update_Buffer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Direction_Last_Frame_Buffer) == 0x0012F8, "Member 'AUltra_Dynamic_Weather_C::Wind_Direction_Last_Frame_Buffer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Nearby_Weather_Override_Volumes) == 0x001300, "Member 'AUltra_Dynamic_Weather_C::Nearby_Weather_Override_Volumes' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_PPWF_Intensity) == 0x001310, "Member 'AUltra_Dynamic_Weather_C::Current_PPWF_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Target_PPWF_Intensity) == 0x001318, "Member 'AUltra_Dynamic_Weather_C::Target_PPWF_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Droplets_Active) == 0x001320, "Member 'AUltra_Dynamic_Weather_C::Screen_Droplets_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Droplets_Target_Drips_Intensity) == 0x001328, "Member 'AUltra_Dynamic_Weather_C::Screen_Droplets_Target_Drips_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Droplets_WB) == 0x001330, "Member 'AUltra_Dynamic_Weather_C::Screen_Droplets_WB' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Droplets_Camera_Exposure) == 0x001340, "Member 'AUltra_Dynamic_Weather_C::Screen_Droplets_Camera_Exposure' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Applied_Rain_Velocity) == 0x001348, "Member 'AUltra_Dynamic_Weather_C::Applied_Rain_Velocity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Water_Level_Material_Falloff) == 0x001360, "Member 'AUltra_Dynamic_Weather_C::Water_Level_Material_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Player_Camera_Underwater) == 0x001368, "Member 'AUltra_Dynamic_Weather_C::Player_Camera_Underwater' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Shared_Material_Parameter_Snowy) == 0x001370, "Member 'AUltra_Dynamic_Weather_C::Shared_Material_Parameter_Snowy' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Shared_Material_Parameter_Dusty) == 0x001378, "Member 'AUltra_Dynamic_Weather_C::Shared_Material_Parameter_Dusty' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Droplet_Material_Decal) == 0x001380, "Member 'AUltra_Dynamic_Weather_C::Splash_Droplet_Material_Decal' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Splash_Droplet_Material_Translucent) == 0x0013A8, "Member 'AUltra_Dynamic_Weather_C::Splash_Droplet_Material_Translucent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Update_Projection_Box_Components) == 0x0013D0, "Member 'AUltra_Dynamic_Weather_C::Last_Update_Projection_Box_Components' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Periodic_Updates_Timer) == 0x0013E0, "Member 'AUltra_Dynamic_Weather_C::Periodic_Updates_Timer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Periodic_Update_Step) == 0x0013E8, "Member 'AUltra_Dynamic_Weather_C::Periodic_Update_Step' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Update_Mask_Components) == 0x0013F0, "Member 'AUltra_Dynamic_Weather_C::Last_Update_Mask_Components' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Random_Stream) == 0x001400, "Member 'AUltra_Dynamic_Weather_C::Random_Stream' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Particle_DOF_Scale) == 0x001408, "Member 'AUltra_Dynamic_Weather_C::Weather_Particle_DOF_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, CPU_Particle_Buffer_Length) == 0x001410, "Member 'AUltra_Dynamic_Weather_C::CPU_Particle_Buffer_Length' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Scale_Distant_Particle_Alpha) == 0x001418, "Member 'AUltra_Dynamic_Weather_C::Scale_Distant_Particle_Alpha' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Distant_Particle_Range) == 0x001420, "Member 'AUltra_Dynamic_Weather_C::Distant_Particle_Range' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Warm_Up_Weather_Particles_On_Begin_Play) == 0x001428, "Member 'AUltra_Dynamic_Weather_C::Warm_Up_Weather_Particles_On_Begin_Play' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Apply_Custom_Weather_Particle_Camera_Transform) == 0x001429, "Member 'AUltra_Dynamic_Weather_C::Apply_Custom_Weather_Particle_Camera_Transform' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Custom_Weather_Particle_Camera_Location) == 0x001430, "Member 'AUltra_Dynamic_Weather_C::Custom_Weather_Particle_Camera_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Custom_Weather_Particle_Camera_Forward_Vector) == 0x001448, "Member 'AUltra_Dynamic_Weather_C::Custom_Weather_Particle_Camera_Forward_Vector' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Global_Weather_Updated) == 0x001460, "Member 'AUltra_Dynamic_Weather_C::Global_Weather_Updated' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Local_Weather_Updated) == 0x001461, "Member 'AUltra_Dynamic_Weather_C::Local_Weather_Updated' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Old_State_Source_Map) == 0x001468, "Member 'AUltra_Dynamic_Weather_C::Old_State_Source_Map' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Call__Custom_Weather_Behavior__Functions) == 0x0014B8, "Member 'AUltra_Dynamic_Weather_C::Call__Custom_Weather_Behavior__Functions' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Update_Sources) == 0x0014C0, "Member 'AUltra_Dynamic_Weather_C::Last_Update_Sources' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Heat_Distortion) == 0x0014D0, "Member 'AUltra_Dynamic_Weather_C::Enable_Heat_Distortion' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Max_Intensity) == 0x0014D8, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Max_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_From_Temperature) == 0x0014E0, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_From_Temperature' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Temperature_Range) == 0x0014E8, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Temperature_Range' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Manual_Heat_Distortion) == 0x0014F8, "Member 'AUltra_Dynamic_Weather_C::Manual_Heat_Distortion' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_DIstortion_Start_Distance) == 0x001500, "Member 'AUltra_Dynamic_Weather_C::Heat_DIstortion_Start_Distance' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_DIstortion_Falloff) == 0x001508, "Member 'AUltra_Dynamic_Weather_C::Heat_DIstortion_Falloff' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Tiling) == 0x001510, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Tiling' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Speed) == 0x001518, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Chromatic_Separation) == 0x001520, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Chromatic_Separation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Zenith_Mask) == 0x001528, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Zenith_Mask' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Horizon_Mask) == 0x001530, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Horizon_Mask' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Horizon_Exponent) == 0x001538, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Horizon_Exponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Temporal_Dither) == 0x001540, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Temporal_Dither' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Show_Heat_Distortion_in_Editor) == 0x001548, "Member 'AUltra_Dynamic_Weather_C::Show_Heat_Distortion_in_Editor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Target_Heat_Distortion_Value) == 0x001550, "Member 'AUltra_Dynamic_Weather_C::Target_Heat_Distortion_Value' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Heat_Distortion_Value) == 0x001558, "Member 'AUltra_Dynamic_Weather_C::Current_Heat_Distortion_Value' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_Parent_Material) == 0x001560, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_MID) == 0x001588, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Rain) == 0x001590, "Member 'AUltra_Dynamic_Weather_C::State_Change___Rain' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Snow) == 0x0015A0, "Member 'AUltra_Dynamic_Weather_C::State_Change___Snow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Wind_Intensity) == 0x0015B0, "Member 'AUltra_Dynamic_Weather_C::State_Change___Wind_Intensity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Dust) == 0x0015C0, "Member 'AUltra_Dynamic_Weather_C::State_Change___Dust' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Fog) == 0x0015D0, "Member 'AUltra_Dynamic_Weather_C::State_Change___Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_0) == 0x0015E0, "Member 'AUltra_Dynamic_Weather_C::Lightning_0' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Wind_Direction) == 0x0015F0, "Member 'AUltra_Dynamic_Weather_C::State_Change___Wind_Direction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Cloud_Coverage) == 0x001600, "Member 'AUltra_Dynamic_Weather_C::State_Change___Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Material_Wetness) == 0x001610, "Member 'AUltra_Dynamic_Weather_C::State_Change___Material_Wetness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Material_Snow) == 0x001620, "Member 'AUltra_Dynamic_Weather_C::State_Change___Material_Snow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, State_Change___Material_Dust) == 0x001630, "Member 'AUltra_Dynamic_Weather_C::State_Change___Material_Dust' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Tick_Behavior_Active) == 0x001640, "Member 'AUltra_Dynamic_Weather_C::Tick_Behavior_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Editor_Camera_Position) == 0x001648, "Member 'AUltra_Dynamic_Weather_C::Editor_Camera_Position' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Run_Context) == 0x001660, "Member 'AUltra_Dynamic_Weather_C::Run_Context' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Sharing_Occlusion_With_UDS) == 0x001661, "Member 'AUltra_Dynamic_Weather_C::Sharing_Occlusion_With_UDS' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Occlusion_State) == 0x001668, "Member 'AUltra_Dynamic_Weather_C::Weather_Occlusion_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Time_of_Last_Sound_Effects_Cache) == 0x001670, "Member 'AUltra_Dynamic_Weather_C::Time_of_Last_Sound_Effects_Cache' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Editor_Tick_Periodic_Update) == 0x001678, "Member 'AUltra_Dynamic_Weather_C::Last_Editor_Tick_Periodic_Update' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Editor_Tick_Sound_Occlusion_Update) == 0x001680, "Member 'AUltra_Dynamic_Weather_C::Last_Editor_Tick_Sound_Occlusion_Update' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Editor_Tick_Time) == 0x001688, "Member 'AUltra_Dynamic_Weather_C::Last_Editor_Tick_Time' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Active_Occlusion_Component) == 0x001690, "Member 'AUltra_Dynamic_Weather_C::Active_Occlusion_Component' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Sky_Occlusion_State) == 0x001698, "Member 'AUltra_Dynamic_Weather_C::Sky_Occlusion_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Tick_Delta_Seconds) == 0x0016A0, "Member 'AUltra_Dynamic_Weather_C::Tick_Delta_Seconds' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Player_Camera_Rotation) == 0x0016A8, "Member 'AUltra_Dynamic_Weather_C::Player_Camera_Rotation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Editor_Utility_Opener_Class) == 0x0016C0, "Member 'AUltra_Dynamic_Weather_C::Editor_Utility_Opener_Class' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Raining_Dispatcher_Threshold) == 0x0016E8, "Member 'AUltra_Dynamic_Weather_C::Raining_Dispatcher_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snowing_Dispatcher_Threshold) == 0x0016F0, "Member 'AUltra_Dynamic_Weather_C::Snowing_Dispatcher_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Sand_Dispatcher_Threshold) == 0x0016F8, "Member 'AUltra_Dynamic_Weather_C::Sand_Dispatcher_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Cloudy_Dispatcher_Threshold) == 0x001700, "Member 'AUltra_Dynamic_Weather_C::Cloudy_Dispatcher_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, WOV_Render_Target_State) == 0x001708, "Member 'AUltra_Dynamic_Weather_C::WOV_Render_Target_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Mask_Render_Target_State) == 0x001710, "Member 'AUltra_Dynamic_Weather_C::Weather_Mask_Render_Target_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, DLWE_Render_Target_State) == 0x001718, "Member 'AUltra_Dynamic_Weather_C::DLWE_Render_Target_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Level_Editor_Lightning_Timer) == 0x001720, "Member 'AUltra_Dynamic_Weather_C::Level_Editor_Lightning_Timer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Level_Editor_Lightning_Period) == 0x001728, "Member 'AUltra_Dynamic_Weather_C::Level_Editor_Lightning_Period' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Editor_Camera_Rotation) == 0x001730, "Member 'AUltra_Dynamic_Weather_C::Editor_Camera_Rotation' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Level_Editor_Lightning_Interrupt_Timer) == 0x001748, "Member 'AUltra_Dynamic_Weather_C::Level_Editor_Lightning_Interrupt_Timer' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Sound_State_Update_Period) == 0x001750, "Member 'AUltra_Dynamic_Weather_C::Sound_State_Update_Period' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Directional_Sound_Asset) == 0x001758, "Member 'AUltra_Dynamic_Weather_C::Directional_Sound_Asset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Global_Sound_Asset) == 0x001780, "Member 'AUltra_Dynamic_Weather_C::Global_Sound_Asset' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Custom_Lightning_Seed) == 0x0017A8, "Member 'AUltra_Dynamic_Weather_C::Custom_Lightning_Seed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Temperature_Range_Update) == 0x0017B0, "Member 'AUltra_Dynamic_Weather_C::Temperature_Range_Update' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Season_Value) == 0x0017C0, "Member 'AUltra_Dynamic_Weather_C::Last_Season_Value' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Season_Change_Weather_Refresh_Threshold) == 0x0017C8, "Member 'AUltra_Dynamic_Weather_C::Season_Change_Weather_Refresh_Threshold' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Random_Weather_Season_Refresh) == 0x0017D0, "Member 'AUltra_Dynamic_Weather_C::Random_Weather_Season_Refresh' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Old_Weather_State_Struct) == 0x0017E0, "Member 'AUltra_Dynamic_Weather_C::Old_Weather_State_Struct' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, DLWE_Snow_Trails_Activated) == 0x001830, "Member 'AUltra_Dynamic_Weather_C::DLWE_Snow_Trails_Activated' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dynamic_Puddles_Active) == 0x001831, "Member 'AUltra_Dynamic_Weather_C::Dynamic_Puddles_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_0) == 0x001834, "Member 'AUltra_Dynamic_Weather_C::Dust_0' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Transition_Weather_State_B) == 0x001838, "Member 'AUltra_Dynamic_Weather_C::Transition_Weather_State_B' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Local_Weather_Location) == 0x001840, "Member 'AUltra_Dynamic_Weather_C::Local_Weather_Location' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Applied_WOVs) == 0x001858, "Member 'AUltra_Dynamic_Weather_C::Last_Applied_WOVs' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Weather_Mask_Target_Active) == 0x001868, "Member 'AUltra_Dynamic_Weather_C::Weather_Mask_Target_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Mobile) == 0x001869, "Member 'AUltra_Dynamic_Weather_C::Mobile' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Last_Update_Manual_Weather_Overridden) == 0x00186A, "Member 'AUltra_Dynamic_Weather_C::Last_Update_Manual_Weather_Overridden' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_System_Toggle) == 0x00186B, "Member 'AUltra_Dynamic_Weather_C::Lightning_System_Toggle' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Clear_Weather_Mask_Prep) == 0x00186C, "Member 'AUltra_Dynamic_Weather_C::Clear_Weather_Mask_Prep' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_Started) == 0x001870, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_Started' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Radial_Storm_Actors) == 0x001880, "Member 'AUltra_Dynamic_Weather_C::Radial_Storm_Actors' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Applied_Radial_Storms_Last_Update) == 0x001890, "Member 'AUltra_Dynamic_Weather_C::Applied_Radial_Storms_Last_Update' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Apply_Version_Specific_Correction) == 0x001891, "Member 'AUltra_Dynamic_Weather_C::Apply_Version_Specific_Correction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, WOV_Material_Effect_Target_Allowed) == 0x001892, "Member 'AUltra_Dynamic_Weather_C::WOV_Material_Effect_Target_Allowed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Radial_Storm_Target_Draw_MID) == 0x001898, "Member 'AUltra_Dynamic_Weather_C::Radial_Storm_Target_Draw_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rainbow_Mesh) == 0x0018A0, "Member 'AUltra_Dynamic_Weather_C::Rainbow_Mesh' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Debris_Particles) == 0x0018A8, "Member 'AUltra_Dynamic_Weather_C::Wind_Debris_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Post_Process_Wind_Fog_WB) == 0x0018B0, "Member 'AUltra_Dynamic_Weather_C::Post_Process_Wind_Fog_WB' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Heat_Distortion_WB) == 0x0018C0, "Member 'AUltra_Dynamic_Weather_C::Heat_Distortion_WB' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Post_Process_Mats_Toggle_State) == 0x0018D0, "Member 'AUltra_Dynamic_Weather_C::Post_Process_Mats_Toggle_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Particle_Mat__AMB_) == 0x0018E0, "Member 'AUltra_Dynamic_Weather_C::Rain_Particle_Mat__AMB_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Particle_Mat__ADOF_) == 0x001908, "Member 'AUltra_Dynamic_Weather_C::Rain_Particle_Mat__ADOF_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Particle_Mat__AMB_) == 0x001930, "Member 'AUltra_Dynamic_Weather_C::Snow_Particle_Mat__AMB_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Particle_Mat__ADOF_) == 0x001958, "Member 'AUltra_Dynamic_Weather_C::Snow_Particle_Mat__ADOF_' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Motion_Blur) == 0x001980, "Member 'AUltra_Dynamic_Weather_C::Motion_Blur' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Direction_Target) == 0x001988, "Member 'AUltra_Dynamic_Weather_C::Wind_Direction_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Wind_Direction_Change_Speed) == 0x001990, "Member 'AUltra_Dynamic_Weather_C::Wind_Direction_Change_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UDS_Cloud_Coverage) == 0x001998, "Member 'AUltra_Dynamic_Weather_C::UDS_Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UDS_Fog) == 0x0019A0, "Member 'AUltra_Dynamic_Weather_C::UDS_Fog' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UDS_Dust_Amount) == 0x0019A8, "Member 'AUltra_Dynamic_Weather_C::UDS_Dust_Amount' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UDS_Cloud_Direction) == 0x0019B0, "Member 'AUltra_Dynamic_Weather_C::UDS_Cloud_Direction' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UDS_Cloud_Speed) == 0x0019B8, "Member 'AUltra_Dynamic_Weather_C::UDS_Cloud_Speed' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, UDS_Fog_Vertical_Velocity) == 0x0019C0, "Member 'AUltra_Dynamic_Weather_C::UDS_Fog_Vertical_Velocity' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Nearby_Radial_Storm_Actors) == 0x0019C8, "Member 'AUltra_Dynamic_Weather_C::Nearby_Radial_Storm_Actors' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Enable_Screen_Frost) == 0x0019D8, "Member 'AUltra_Dynamic_Weather_C::Enable_Screen_Frost' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_From_Snow) == 0x0019E0, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_From_Snow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_From_Material_Snow) == 0x0019E8, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_From_Material_Snow' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Manual_Screen_Frost) == 0x0019F0, "Member 'AUltra_Dynamic_Weather_C::Manual_Screen_Frost' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Multiplier_in_Interior) == 0x0019F8, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Multiplier_in_Interior' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Form_Duration) == 0x001A00, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Form_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Clear_Duration) == 0x001A08, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Clear_Duration' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Color) == 0x001A10, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Color' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Falloff_Shape) == 0x001A20, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Falloff_Shape' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Falloff_Exponent) == 0x001A28, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Falloff_Exponent' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Center_Mask) == 0x001A30, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Center_Mask' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Edge_Mask) == 0x001A38, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Edge_Mask' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Distortion_Strength) == 0x001A40, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Distortion_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Distortion_Alpha) == 0x001A48, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Distortion_Alpha' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Texture_Scale) == 0x001A50, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Texture_Scale' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Scatter) == 0x001A58, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Scatter' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Sharpness) == 0x001A60, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Sharpness' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Randomize_Screen_Frost_Texture_at_Runtime) == 0x001A68, "Member 'AUltra_Dynamic_Weather_C::Randomize_Screen_Frost_Texture_at_Runtime' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Scatter_Texture) == 0x001A70, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Scatter_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Normal_Texture) == 0x001A98, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Normal_Texture' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Show_Screen_Frost_in_Editor) == 0x001AC0, "Member 'AUltra_Dynamic_Weather_C::Show_Screen_Frost_in_Editor' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Active) == 0x001AC1, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Active' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Strength_Target) == 0x001AC8, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Strength_Target' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Current_Screen_Frost_Strength) == 0x001AD0, "Member 'AUltra_Dynamic_Weather_C::Current_Screen_Frost_Strength' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_MID) == 0x001AD8, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_MID' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_WB) == 0x001AE0, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_WB' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Screen_Frost_Parent_Material) == 0x001AF0, "Member 'AUltra_Dynamic_Weather_C::Screen_Frost_Parent_Material' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Manual_Overrides_Applied) == 0x001B18, "Member 'AUltra_Dynamic_Weather_C::Manual_Overrides_Applied' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Loaded_Objects) == 0x001B20, "Member 'AUltra_Dynamic_Weather_C::Loaded_Objects' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Rain_Particles) == 0x001B30, "Member 'AUltra_Dynamic_Weather_C::Rain_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Snow_Particles) == 0x001B38, "Member 'AUltra_Dynamic_Weather_C::Snow_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Dust_Particles) == 0x001B40, "Member 'AUltra_Dynamic_Weather_C::Dust_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Obscured_Lightning_Particles) == 0x001B48, "Member 'AUltra_Dynamic_Weather_C::Obscured_Lightning_Particles' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_1) == 0x001B50, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_1' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Lightning_Flash_2) == 0x001B58, "Member 'AUltra_Dynamic_Weather_C::Lightning_Flash_2' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Projection_Box_Scene_Capture_Component) == 0x001B60, "Member 'AUltra_Dynamic_Weather_C::Projection_Box_Scene_Capture_Component' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Transient_Weather_State) == 0x001B68, "Member 'AUltra_Dynamic_Weather_C::Transient_Weather_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, Temperature_Weather_State) == 0x001B70, "Member 'AUltra_Dynamic_Weather_C::Temperature_Weather_State' has a wrong offset!");
static_assert(offsetof(AUltra_Dynamic_Weather_C, DLWE_Trail_Brush_MID) == 0x001B78, "Member 'AUltra_Dynamic_Weather_C::DLWE_Trail_Brush_MID' has a wrong offset!");

}

