﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeMenu

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "WBP_NarrativeActivatableWidget_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WBP_NarrativeMenu.WBP_NarrativeMenu_C
// 0x0018 (0x0448 - 0x0430)
class UWBP_NarrativeMenu_C : public UWBP_NarrativeActivatableWidget_C
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame_WBP_NarrativeMenu_C;                // 0x0430(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	bool                                          ShowCursorAndFocusUI;                              // 0x0438(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_439[0x7];                                      // 0x0439(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWBP_NarrativeHUD_C*                    OwningHUD;                                         // 0x0440(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)

public:
	void Destruct();
	void ExecuteUbergraph_WBP_NarrativeMenu(int32 EntryPoint);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WBP_NarrativeMenu_C">();
	}
	static class UWBP_NarrativeMenu_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWBP_NarrativeMenu_C>();
	}
};
static_assert(alignof(UWBP_NarrativeMenu_C) == 0x000008, "Wrong alignment on UWBP_NarrativeMenu_C");
static_assert(sizeof(UWBP_NarrativeMenu_C) == 0x000448, "Wrong size on UWBP_NarrativeMenu_C");
static_assert(offsetof(UWBP_NarrativeMenu_C, UberGraphFrame_WBP_NarrativeMenu_C) == 0x000430, "Member 'UWBP_NarrativeMenu_C::UberGraphFrame_WBP_NarrativeMenu_C' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeMenu_C, ShowCursorAndFocusUI) == 0x000438, "Member 'UWBP_NarrativeMenu_C::ShowCursorAndFocusUI' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeMenu_C, OwningHUD) == 0x000440, "Member 'UWBP_NarrativeMenu_C::OwningHUD' has a wrong offset!");

}

