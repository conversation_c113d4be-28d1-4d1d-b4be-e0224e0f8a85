﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Zombie_EnemyInRange

#include "Basic.hpp"

#include "Zombie_EnemyInRange_classes.hpp"
#include "Zombie_EnemyInRange_parameters.hpp"


namespace SDK
{

// Function Zombie_EnemyInRange.Zombie_EnemyInRange_C.ExecuteUbergraph_Zombie_EnemyInRange
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UZombie_EnemyInRange_C::ExecuteUbergraph_Zombie_EnemyInRange(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Zombie_EnemyInRange_C", "ExecuteUbergraph_Zombie_EnemyInRange");

	Params::Zombie_EnemyInRange_C_ExecuteUbergraph_Zombie_EnemyInRange Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function Zombie_EnemyInRange.Zombie_EnemyInRange_C.ReceiveActivationAI
// (Event, Protected, BlueprintEvent)
// Parameters:
// class AAIController*                    OwnerController                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class APawn*                            ControlledPawn                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UZombie_EnemyInRange_C::ReceiveActivationAI(class AAIController* OwnerController, class APawn* ControlledPawn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("Zombie_EnemyInRange_C", "ReceiveActivationAI");

	Params::Zombie_EnemyInRange_C_ReceiveActivationAI Parms{};

	Parms.OwnerController = OwnerController;
	Parms.ControlledPawn = ControlledPawn;

	UObject::ProcessEvent(Func, &Parms);
}

}

