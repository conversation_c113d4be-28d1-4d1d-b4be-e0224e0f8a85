﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_BatteryChargerUI

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "BP_MainSpecialContainer_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_BatteryChargerUI.W_BatteryChargerUI_C
// 0x0018 (0x0318 - 0x0300)
class UW_BatteryChargerUI_C final : public UBP_MainSpecialContainer_C
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame_W_BatteryChargerUI_C;               // 0x0300(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UEquipmentSlotTitle_C*                  EquipmentSlotTitle;                                // 0x0308(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        JSIContainer_1;                                    // 0x0310(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_W_BatteryChargerUI(int32 EntryPoint);
	void GetAllAttachments(TArray<class FName>* Attachments);
	void GetContainerByAttachmentType(const struct FGameplayTag& Type, class UJSIContainer_C** JigContainer, int32* ContainerIndex);
	void GetDropWidget(class UDropItemBackGwidget_C** DropWRef);
	void GetJSIContainerByPlayerSlots(const struct FGameplayTag& Slot_0, class UJSIContainer_C** Container, class UJSI_Slot_C** EquippedItem, bool* IsPending_);
	void GetListOfNonAddContainers(TArray<class UJSIContainer_C*>* Containers);
	void GetLootContent(class UUserWidget** Widget);
	void GetValidReloadContainers(TArray<class UJSIContainer_C*>* Containers);
	void PreInitSpecialContainer();
	void SetActionbarFollower(class UJSI_Slot_C* JigRef, bool* Return);

	void GetListOfContainers(TArray<class UJSIContainer_C*>* Containers) const;

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_BatteryChargerUI_C">();
	}
	static class UW_BatteryChargerUI_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_BatteryChargerUI_C>();
	}
};
static_assert(alignof(UW_BatteryChargerUI_C) == 0x000008, "Wrong alignment on UW_BatteryChargerUI_C");
static_assert(sizeof(UW_BatteryChargerUI_C) == 0x000318, "Wrong size on UW_BatteryChargerUI_C");
static_assert(offsetof(UW_BatteryChargerUI_C, UberGraphFrame_W_BatteryChargerUI_C) == 0x000300, "Member 'UW_BatteryChargerUI_C::UberGraphFrame_W_BatteryChargerUI_C' has a wrong offset!");
static_assert(offsetof(UW_BatteryChargerUI_C, EquipmentSlotTitle) == 0x000308, "Member 'UW_BatteryChargerUI_C::EquipmentSlotTitle' has a wrong offset!");
static_assert(offsetof(UW_BatteryChargerUI_C, JSIContainer_1) == 0x000310, "Member 'UW_BatteryChargerUI_C::JSIContainer_1' has a wrong offset!");

}

