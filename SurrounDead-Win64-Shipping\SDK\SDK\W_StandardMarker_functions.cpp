﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_StandardMarker

#include "Basic.hpp"

#include "W_StandardMarker_classes.hpp"
#include "W_StandardMarker_parameters.hpp"


namespace SDK
{

// Function W_StandardMarker.W_StandardMarker_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_StandardMarker_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_StandardMarker_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_StandardMarker.W_StandardMarker_C.Construct Marker
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UW_StandardMarker_C::Construct_Marker()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_StandardMarker_C", "Construct Marker");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_StandardMarker.W_StandardMarker_C.ExecuteUbergraph_W_StandardMarker
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_StandardMarker_C::ExecuteUbergraph_W_StandardMarker(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_StandardMarker_C", "ExecuteUbergraph_W_StandardMarker");

	Params::W_StandardMarker_C_ExecuteUbergraph_W_StandardMarker Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_StandardMarker.W_StandardMarker_C.OnMouseButtonDoubleClick
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 InMyGeometry                                           (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             InMouseEvent                                           (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UW_StandardMarker_C::OnMouseButtonDoubleClick(const struct FGeometry& InMyGeometry, const struct FPointerEvent& InMouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_StandardMarker_C", "OnMouseButtonDoubleClick");

	Params::W_StandardMarker_C_OnMouseButtonDoubleClick Parms{};

	Parms.InMyGeometry = std::move(InMyGeometry);
	Parms.InMouseEvent = std::move(InMouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function W_StandardMarker.W_StandardMarker_C.Play Hide Animation
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EUMGSequencePlayMode                    PlayMode                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Destroy_On_Hide                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_StandardMarker_C::Play_Hide_Animation(EUMGSequencePlayMode PlayMode, bool Destroy_On_Hide)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_StandardMarker_C", "Play Hide Animation");

	Params::W_StandardMarker_C_Play_Hide_Animation Parms{};

	Parms.PlayMode = PlayMode;
	Parms.Destroy_On_Hide = Destroy_On_Hide;

	UObject::ProcessEvent(Func, &Parms);
}

}

