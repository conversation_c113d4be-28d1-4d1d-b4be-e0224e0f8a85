﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_TemperatureType

#include "Basic.hpp"


namespace SDK
{

// UserDefinedEnum UDS_TemperatureType.UDS_TemperatureType
// NumValues: 0x0003
enum class EUDS_TemperatureType : uint8
{
	NewEnumerator0                           = 0,
	NewEnumerator1                           = 1,
	UDS_MAX                                  = 2,
};

}

