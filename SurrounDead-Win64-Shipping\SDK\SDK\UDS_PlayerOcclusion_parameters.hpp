﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_PlayerOcclusion

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "Niagara_structs.hpp"


namespace SDK::Params
{

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Apply Directional Occlusion Modifiers
// 0x00C0 (0x00C0 - 0x0000)
struct UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers final
{
public:
	struct FLinearColor                           In;                                                // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Out;                                               // 0x0010(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Multiply_LinearColorLinearColor_ReturnValue; // 0x0030(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Add_LinearColorLinearColor_ReturnValue;   // 0x0040(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue_1;                     // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue_2;                     // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue_3;                     // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor_1;                   // 0x0070(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_R_ImplicitCast;                  // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_G_ImplicitCast;                  // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_B_ImplicitCast;                  // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast;                  // 0x008C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_Value_ImplicitCast;                // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_Value_ImplicitCast_1;              // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_Value_ImplicitCast_2;              // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_Value_ImplicitCast_3;              // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_R_ImplicitCast_1;                // 0x00B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast_1;                // 0x00B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_B_ImplicitCast_1;                // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_G_ImplicitCast_1;                // 0x00BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers");
static_assert(sizeof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers) == 0x0000C0, "Wrong size on UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, In) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::In' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, Out) == 0x000010, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::Out' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_LinearColor) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_Multiply_LinearColorLinearColor_ReturnValue) == 0x000030, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_Multiply_LinearColorLinearColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_Add_LinearColorLinearColor_ReturnValue) == 0x000040, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_Add_LinearColorLinearColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_FClamp_ReturnValue) == 0x000050, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_FClamp_ReturnValue_1) == 0x000058, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_FClamp_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_FClamp_ReturnValue_2) == 0x000060, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_FClamp_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_FClamp_ReturnValue_3) == 0x000068, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_FClamp_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_LinearColor_1) == 0x000070, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_LinearColor_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_R_ImplicitCast) == 0x000080, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_R_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_G_ImplicitCast) == 0x000084, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_G_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_B_ImplicitCast) == 0x000088, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_A_ImplicitCast) == 0x00008C, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_FClamp_Value_ImplicitCast) == 0x000090, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_FClamp_Value_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_FClamp_Value_ImplicitCast_1) == 0x000098, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_FClamp_Value_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_FClamp_Value_ImplicitCast_2) == 0x0000A0, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_FClamp_Value_ImplicitCast_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, CallFunc_FClamp_Value_ImplicitCast_3) == 0x0000A8, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::CallFunc_FClamp_Value_ImplicitCast_3' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_R_ImplicitCast_1) == 0x0000B0, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_R_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_A_ImplicitCast_1) == 0x0000B4, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_A_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_B_ImplicitCast_1) == 0x0000B8, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_B_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers, K2Node_MakeStruct_G_ImplicitCast_1) == 0x0000BC, "Member 'UDS_PlayerOcclusion_C_Apply_Directional_Occlusion_Modifiers::K2Node_MakeStruct_G_ImplicitCast_1' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Check For Portal Components
// 0x0230 (0x0230 - 0x0000)
struct UDS_PlayerOcclusion_C_Check_For_Portal_Components final
{
public:
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C[0x4];                                        // 0x000C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<EObjectTypeQuery>                      K2Node_MakeArray_Array;                            // 0x0010(0x0010)(ConstParm, ReferenceParm)
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue;         // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue;        // 0x0028(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FHitResult>                     CallFunc_LineTraceMultiForObjects_OutHits;         // 0x0040(0x0010)(ReferenceParm, ContainsInstancedReference)
	bool                                          CallFunc_LineTraceMultiForObjects_ReturnValue;     // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51[0x7];                                       // 0x0051(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FHitResult                             CallFunc_Array_Get_Item;                           // 0x0058(0x00E8)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0140(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BreakHitResult_bBlockingHit;              // 0x0144(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BreakHitResult_bInitialOverlap;           // 0x0145(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_146[0x2];                                      // 0x0146(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_BreakHitResult_Time;                      // 0x0148(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakHitResult_Distance;                  // 0x014C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_Location;                  // 0x0150(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_ImpactPoint;               // 0x0168(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_Normal;                    // 0x0180(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_ImpactNormal;              // 0x0198(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPhysicalMaterial*                      CallFunc_BreakHitResult_PhysMat;                   // 0x01B0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 CallFunc_BreakHitResult_HitActor;                  // 0x01B8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UPrimitiveComponent*                    CallFunc_BreakHitResult_HitComponent;              // 0x01C0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_BreakHitResult_HitBoneName;               // 0x01C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_BreakHitResult_BoneName;                  // 0x01D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakHitResult_HitItem;                   // 0x01D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakHitResult_ElementIndex;              // 0x01DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakHitResult_FaceIndex;                 // 0x01E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1E4[0x4];                                      // 0x01E4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_BreakHitResult_TraceStart;                // 0x01E8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_TraceEnd;                  // 0x0200(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0218(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_219[0x7];                                      // 0x0219(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUDS_Occlusion_Portal_C*                K2Node_DynamicCast_AsUDS_Occlusion_Portal;         // 0x0220(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0228(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_229[0x3];                                      // 0x0229(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x022C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Check_For_Portal_Components) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Check_For_Portal_Components");
static_assert(sizeof(UDS_PlayerOcclusion_C_Check_For_Portal_Components) == 0x000230, "Wrong size on UDS_PlayerOcclusion_C_Check_For_Portal_Components");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, Temp_int_Array_Index_Variable) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, Temp_int_Loop_Counter_Variable) == 0x000004, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_Add_IntInt_ReturnValue) == 0x000008, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, K2Node_MakeArray_Array) == 0x000010, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_GetGameTimeInSeconds_ReturnValue) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_GetGameTimeInSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_Subtract_VectorVector_ReturnValue) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_Subtract_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_LineTraceMultiForObjects_OutHits) == 0x000040, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_LineTraceMultiForObjects_OutHits' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_LineTraceMultiForObjects_ReturnValue) == 0x000050, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_LineTraceMultiForObjects_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_Array_Get_Item) == 0x000058, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_Array_Length_ReturnValue) == 0x000140, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_bBlockingHit) == 0x000144, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_bBlockingHit' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_bInitialOverlap) == 0x000145, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_bInitialOverlap' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_Time) == 0x000148, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_Time' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_Distance) == 0x00014C, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_Distance' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_Location) == 0x000150, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_Location' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_ImpactPoint) == 0x000168, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_ImpactPoint' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_Normal) == 0x000180, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_Normal' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_ImpactNormal) == 0x000198, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_ImpactNormal' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_PhysMat) == 0x0001B0, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_PhysMat' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_HitActor) == 0x0001B8, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_HitActor' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_HitComponent) == 0x0001C0, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_HitComponent' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_HitBoneName) == 0x0001C8, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_HitBoneName' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_BoneName) == 0x0001D0, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_BoneName' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_HitItem) == 0x0001D8, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_HitItem' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_ElementIndex) == 0x0001DC, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_ElementIndex' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_FaceIndex) == 0x0001E0, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_FaceIndex' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_TraceStart) == 0x0001E8, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_TraceStart' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_BreakHitResult_TraceEnd) == 0x000200, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_BreakHitResult_TraceEnd' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_Less_IntInt_ReturnValue) == 0x000218, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, K2Node_DynamicCast_AsUDS_Occlusion_Portal) == 0x000220, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::K2Node_DynamicCast_AsUDS_Occlusion_Portal' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, K2Node_DynamicCast_bSuccess) == 0x000228, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Check_For_Portal_Components, CallFunc_Array_Add_ReturnValue) == 0x00022C, "Member 'UDS_PlayerOcclusion_C_Check_For_Portal_Components::CallFunc_Array_Add_ReturnValue' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Custom Global Occlusion Sample
// 0x01A8 (0x01A8 - 0x0000)
struct UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample final
{
public:
	struct FVector                                Location;                                          // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Global_Occlusion;                                  // 0x0018(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Ray_Hits;                                          // 0x0020(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24[0x4];                                       // 0x0024(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Total_Occlusion;                                   // 0x0028(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Test_Point_for_Occlusion_Volumes_Final_Multiplier; // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0044(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_45[0x3];                                       // 0x0045(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Divide_IntInt_ReturnValue;                // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4C[0x4];                                       // 0x004C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Divide_IntInt_ReturnValue_1;              // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x005C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Conv_IntToDouble_ReturnValue_1;           // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0078(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7C[0x4];                                       // 0x007C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Array_Get_Item;                           // 0x0088(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue;             // 0x00A0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHitResult                             CallFunc_LineTraceSingle_OutHit;                   // 0x00B8(0x00E8)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	bool                                          CallFunc_LineTraceSingle_ReturnValue;              // 0x01A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample");
static_assert(sizeof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample) == 0x0001A8, "Wrong size on UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, Location) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::Location' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, Global_Occlusion) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::Global_Occlusion' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, Ray_Hits) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::Ray_Hits' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, Total_Occlusion) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::Total_Occlusion' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Test_Point_for_Occlusion_Volumes_Final_Multiplier) == 0x000030, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Test_Point_for_Occlusion_Volumes_Final_Multiplier' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, Temp_int_Variable) == 0x000038, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Add_IntInt_ReturnValue) == 0x00003C, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, Temp_int_Variable_1) == 0x000040, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000044, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Divide_IntInt_ReturnValue) == 0x000048, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Divide_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000050, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Divide_IntInt_ReturnValue_1) == 0x000058, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Divide_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Subtract_IntInt_ReturnValue) == 0x00005C, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000060, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Conv_IntToDouble_ReturnValue_1) == 0x000068, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Conv_IntToDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000070, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Add_IntInt_ReturnValue_1) == 0x000078, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_MapRangeClamped_ReturnValue) == 0x000080, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Array_Get_Item) == 0x000088, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_Add_VectorVector_ReturnValue) == 0x0000A0, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_Add_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_LineTraceSingle_OutHit) == 0x0000B8, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_LineTraceSingle_OutHit' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample, CallFunc_LineTraceSingle_ReturnValue) == 0x0001A0, "Member 'UDS_PlayerOcclusion_C_Custom_Global_Occlusion_Sample::CallFunc_LineTraceSingle_ReturnValue' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Delayed Startup
// 0x0030 (0x0030 - 0x0000)
struct UDS_PlayerOcclusion_C_Delayed_Startup final
{
public:
	struct FTimerHandle                           CallFunc_K2_SetTimerForNextTick_ReturnValue;       // 0x0000(0x0008)(NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualExactly_VectorVector_ReturnValue;    // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           CallFunc_K2_SetTimer_ReturnValue;                  // 0x0010(0x0008)(NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           CallFunc_K2_SetTimer_ReturnValue_1;                // 0x0018(0x0008)(NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           CallFunc_K2_SetTimer_ReturnValue_2;                // 0x0020(0x0008)(NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_K2_SetTimer_Time_ImplicitCast;            // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Delayed_Startup) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Delayed_Startup");
static_assert(sizeof(UDS_PlayerOcclusion_C_Delayed_Startup) == 0x000030, "Wrong size on UDS_PlayerOcclusion_C_Delayed_Startup");
static_assert(offsetof(UDS_PlayerOcclusion_C_Delayed_Startup, CallFunc_K2_SetTimerForNextTick_ReturnValue) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Delayed_Startup::CallFunc_K2_SetTimerForNextTick_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Delayed_Startup, CallFunc_EqualExactly_VectorVector_ReturnValue) == 0x000008, "Member 'UDS_PlayerOcclusion_C_Delayed_Startup::CallFunc_EqualExactly_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Delayed_Startup, CallFunc_K2_SetTimer_ReturnValue) == 0x000010, "Member 'UDS_PlayerOcclusion_C_Delayed_Startup::CallFunc_K2_SetTimer_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Delayed_Startup, CallFunc_K2_SetTimer_ReturnValue_1) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Delayed_Startup::CallFunc_K2_SetTimer_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Delayed_Startup, CallFunc_K2_SetTimer_ReturnValue_2) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Delayed_Startup::CallFunc_K2_SetTimer_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Delayed_Startup, CallFunc_K2_SetTimer_Time_ImplicitCast) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Delayed_Startup::CallFunc_K2_SetTimer_Time_ImplicitCast' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Editor Tick
// 0x0068 (0x0068 - 0x0000)
struct UDS_PlayerOcclusion_C_Editor_Tick final
{
public:
	struct FVector                                Occlusion_Location;                                // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue;         // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue_1;       // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue_2;       // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39[0x7];                                       // 0x0039(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_1;      // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_1;       // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_49[0x7];                                       // 0x0049(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue_3;       // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_2;      // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_2;       // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Editor_Tick) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Editor_Tick");
static_assert(sizeof(UDS_PlayerOcclusion_C_Editor_Tick) == 0x000068, "Wrong size on UDS_PlayerOcclusion_C_Editor_Tick");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, Occlusion_Location) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::Occlusion_Location' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_GetGameTimeInSeconds_ReturnValue) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_GetGameTimeInSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_GetGameTimeInSeconds_ReturnValue_1) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_GetGameTimeInSeconds_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_GetGameTimeInSeconds_ReturnValue_2) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_GetGameTimeInSeconds_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000030, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000038, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_Subtract_DoubleDouble_ReturnValue_1) == 0x000040, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_Subtract_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_Greater_DoubleDouble_ReturnValue_1) == 0x000048, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_Greater_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_GetGameTimeInSeconds_ReturnValue_3) == 0x000050, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_GetGameTimeInSeconds_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_Subtract_DoubleDouble_ReturnValue_2) == 0x000058, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_Subtract_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Editor_Tick, CallFunc_Greater_DoubleDouble_ReturnValue_2) == 0x000060, "Member 'UDS_PlayerOcclusion_C_Editor_Tick::CallFunc_Greater_DoubleDouble_ReturnValue_2' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.ExecuteUbergraph_UDS_PlayerOcclusion
// 0x0110 (0x0110 - 0x0000)
struct UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FBasicParticleData>             K2Node_Event_Data;                                 // 0x0008(0x0010)(ConstParm, ReferenceParm)
	class UNiagaraSystem*                         K2Node_Event_NiagaraSystem;                        // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FVector                                K2Node_Event_SimulationPositionOffset;             // 0x0020(0x0018)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FBasicParticleData                     CallFunc_Array_Get_Item;                           // 0x0038(0x0038)(NoDestructor)
	double                                        CallFunc_BreakVector_X;                            // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X_1;                          // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y_1;                          // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z_1;                          // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x00A0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Apply_Directional_Occlusion_Modifiers_Out; // 0x00B0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EEndPlayReason                                K2Node_Event_EndPlayReason;                        // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_E1[0x7];                                       // 0x00E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x00F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast;                  // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_R_ImplicitCast;                  // 0x00FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_G_ImplicitCast;                  // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_B_ImplicitCast;                  // 0x0104(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_ParameterValue_ImplicitCast; // 0x0108(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion");
static_assert(sizeof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion) == 0x000110, "Wrong size on UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, EntryPoint) == 0x000000, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::EntryPoint' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, K2Node_Event_Data) == 0x000008, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::K2Node_Event_Data' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, K2Node_Event_NiagaraSystem) == 0x000018, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::K2Node_Event_NiagaraSystem' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, K2Node_Event_SimulationPositionOffset) == 0x000020, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::K2Node_Event_SimulationPositionOffset' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_Array_Get_Item) == 0x000038, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_BreakVector_X) == 0x000070, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_BreakVector_Y) == 0x000078, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_BreakVector_Z) == 0x000080, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_BreakVector_X_1) == 0x000088, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_BreakVector_X_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_BreakVector_Y_1) == 0x000090, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_BreakVector_Y_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_BreakVector_Z_1) == 0x000098, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_BreakVector_Z_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, K2Node_MakeStruct_LinearColor) == 0x0000A0, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_Apply_Directional_Occlusion_Modifiers_Out) == 0x0000B0, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_Apply_Directional_Occlusion_Modifiers_Out' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0000C0, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x0000C8, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_Add_DoubleDouble_ReturnValue) == 0x0000D0, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_FClamp_ReturnValue) == 0x0000D8, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, K2Node_Event_EndPlayReason) == 0x0000E0, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::K2Node_Event_EndPlayReason' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x0000E8, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_MapRangeClamped_ReturnValue) == 0x0000F0, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, K2Node_MakeStruct_A_ImplicitCast) == 0x0000F8, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::K2Node_MakeStruct_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, K2Node_MakeStruct_R_ImplicitCast) == 0x0000FC, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::K2Node_MakeStruct_R_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, K2Node_MakeStruct_G_ImplicitCast) == 0x000100, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::K2Node_MakeStruct_G_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, K2Node_MakeStruct_B_ImplicitCast) == 0x000104, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::K2Node_MakeStruct_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion, CallFunc_SetScalarParameterValue_ParameterValue_ImplicitCast) == 0x000108, "Member 'UDS_PlayerOcclusion_C_ExecuteUbergraph_UDS_PlayerOcclusion::CallFunc_SetScalarParameterValue_ParameterValue_ImplicitCast' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Get Sample Location
// 0x0068 (0x0068 - 0x0000)
struct UDS_PlayerOcclusion_C_Get_Sample_Location final
{
public:
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue;      // 0x0000(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  CallFunc_GetPlayerPawn_ReturnValue;                // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_K2_GetActorLocation_ReturnValue;          // 0x0028(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class APlayerCameraManager*                   CallFunc_GetPlayerCameraManager_ReturnValue;       // 0x0040(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0049(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4A[0x6];                                       // 0x004A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_GetCameraLocation_ReturnValue;            // 0x0050(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Get_Sample_Location) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Get_Sample_Location");
static_assert(sizeof(UDS_PlayerOcclusion_C_Get_Sample_Location) == 0x000068, "Wrong size on UDS_PlayerOcclusion_C_Get_Sample_Location");
static_assert(offsetof(UDS_PlayerOcclusion_C_Get_Sample_Location, CallFunc_K2_GetComponentLocation_ReturnValue) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Get_Sample_Location::CallFunc_K2_GetComponentLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Get_Sample_Location, CallFunc_GetPlayerPawn_ReturnValue) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Get_Sample_Location::CallFunc_GetPlayerPawn_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Get_Sample_Location, CallFunc_IsValid_ReturnValue) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Get_Sample_Location::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Get_Sample_Location, CallFunc_K2_GetActorLocation_ReturnValue) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Get_Sample_Location::CallFunc_K2_GetActorLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Get_Sample_Location, CallFunc_GetPlayerCameraManager_ReturnValue) == 0x000040, "Member 'UDS_PlayerOcclusion_C_Get_Sample_Location::CallFunc_GetPlayerCameraManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Get_Sample_Location, CallFunc_IsValid_ReturnValue_1) == 0x000048, "Member 'UDS_PlayerOcclusion_C_Get_Sample_Location::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Get_Sample_Location, K2Node_SwitchEnum_CmpSuccess) == 0x000049, "Member 'UDS_PlayerOcclusion_C_Get_Sample_Location::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Get_Sample_Location, CallFunc_GetCameraLocation_ReturnValue) == 0x000050, "Member 'UDS_PlayerOcclusion_C_Get_Sample_Location::CallFunc_GetCameraLocation_ReturnValue' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Incremental Occlusion Traces
// 0x0270 (0x0270 - 0x0000)
struct UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces final
{
public:
	double                                        Upward_Total_Contribution;                         // 0x0000(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Traces_This_Frame;                                 // 0x0008(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Directional_Total_Contribution;                    // 0x000C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Current_Trace_Occlusion;                           // 0x0020(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Array_Get_Item;                           // 0x0030(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Array_Get_Item_1;                         // 0x0048(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Array_Get_Item_2;                         // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Array_Get_Item_3;                         // 0x0060(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Array_Get_Item_4;                         // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Array_Get_Item_5;                         // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue;               // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EDrawDebugTrace                               CallFunc_Trace_Debugs_ReturnValue;                 // 0x0084(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_85[0x3];                                       // 0x0085(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue;             // 0x0090(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Vector_Distance_ReturnValue;              // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x00B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B1[0x7];                                       // 0x00B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue;         // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue_1;       // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Round_ReturnValue;                        // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Max_ReturnValue;                          // 0x00DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Min_ReturnValue;                          // 0x00E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_SelectInt_ReturnValue;                    // 0x00E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x00EC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_ED[0x3];                                       // 0x00ED(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x00F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F4[0x4];                                       // 0x00F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue_1;           // 0x0100(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_1;      // 0x0108(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue_2;      // 0x0110(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0118(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHitResult                             CallFunc_LineTraceSingle_OutHit;                   // 0x0120(0x00E8)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	bool                                          CallFunc_LineTraceSingle_ReturnValue;              // 0x0208(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_209[0x7];                                      // 0x0209(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_SelectFloat_ReturnValue;                  // 0x0210(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0218(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_219[0x3];                                      // 0x0219(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_Add_LinearColorLinearColor_ReturnValue;   // 0x021C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Subtract_LinearColorLinearColor_ReturnValue; // 0x022C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x023C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Multiply_LinearColorLinearColor_ReturnValue; // 0x024C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_LineTraceSingle_DrawTime_ImplicitCast;    // 0x025C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_R_ImplicitCast;                  // 0x0260(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_G_ImplicitCast;                  // 0x0264(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_B_ImplicitCast;                  // 0x0268(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast;                  // 0x026C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces");
static_assert(sizeof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces) == 0x000270, "Wrong size on UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, Upward_Total_Contribution) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::Upward_Total_Contribution' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, Traces_This_Frame) == 0x000008, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::Traces_This_Frame' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, Directional_Total_Contribution) == 0x00000C, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::Directional_Total_Contribution' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, Current_Trace_Occlusion) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::Current_Trace_Occlusion' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Add_IntInt_ReturnValue) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Array_Get_Item) == 0x000030, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Array_Get_Item_1) == 0x000048, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Array_Get_Item_2) == 0x000058, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Array_Get_Item_3) == 0x000060, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Array_Get_Item_4) == 0x000070, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Array_Get_Item_4' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Array_Get_Item_5) == 0x000078, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Array_Get_Item_5' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Percent_IntInt_ReturnValue) == 0x000080, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Percent_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Trace_Debugs_ReturnValue) == 0x000084, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Trace_Debugs_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000088, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Add_VectorVector_ReturnValue) == 0x000090, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Add_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Vector_Distance_ReturnValue) == 0x0000A8, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Vector_Distance_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x0000B0, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_GetGameTimeInSeconds_ReturnValue) == 0x0000B8, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_GetGameTimeInSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_GetGameTimeInSeconds_ReturnValue_1) == 0x0000C0, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_GetGameTimeInSeconds_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x0000C8, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x0000D0, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Round_ReturnValue) == 0x0000D8, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Round_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Max_ReturnValue) == 0x0000DC, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Max_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Min_ReturnValue) == 0x0000E0, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Min_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_SelectInt_ReturnValue) == 0x0000E4, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_SelectInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, Temp_int_Variable) == 0x0000E8, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_LessEqual_IntInt_ReturnValue) == 0x0000EC, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Add_IntInt_ReturnValue_1) == 0x0000F0, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Add_DoubleDouble_ReturnValue) == 0x0000F8, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Add_DoubleDouble_ReturnValue_1) == 0x000100, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Add_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Subtract_DoubleDouble_ReturnValue_1) == 0x000108, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Subtract_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Subtract_DoubleDouble_ReturnValue_2) == 0x000110, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Subtract_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000118, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_LineTraceSingle_OutHit) == 0x000120, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_LineTraceSingle_OutHit' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_LineTraceSingle_ReturnValue) == 0x000208, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_LineTraceSingle_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_SelectFloat_ReturnValue) == 0x000210, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_SelectFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Greater_IntInt_ReturnValue) == 0x000218, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Add_LinearColorLinearColor_ReturnValue) == 0x00021C, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Add_LinearColorLinearColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Subtract_LinearColorLinearColor_ReturnValue) == 0x00022C, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Subtract_LinearColorLinearColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, K2Node_MakeStruct_LinearColor) == 0x00023C, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_Multiply_LinearColorLinearColor_ReturnValue) == 0x00024C, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_Multiply_LinearColorLinearColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, CallFunc_LineTraceSingle_DrawTime_ImplicitCast) == 0x00025C, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::CallFunc_LineTraceSingle_DrawTime_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, K2Node_MakeStruct_R_ImplicitCast) == 0x000260, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::K2Node_MakeStruct_R_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, K2Node_MakeStruct_G_ImplicitCast) == 0x000264, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::K2Node_MakeStruct_G_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, K2Node_MakeStruct_B_ImplicitCast) == 0x000268, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::K2Node_MakeStruct_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces, K2Node_MakeStruct_A_ImplicitCast) == 0x00026C, "Member 'UDS_PlayerOcclusion_C_Incremental_Occlusion_Traces::K2Node_MakeStruct_A_ImplicitCast' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Initialize
// 0x0001 (0x0001 - 0x0000)
struct UDS_PlayerOcclusion_C_Initialize final
{
public:
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Initialize) == 0x000001, "Wrong alignment on UDS_PlayerOcclusion_C_Initialize");
static_assert(sizeof(UDS_PlayerOcclusion_C_Initialize) == 0x000001, "Wrong size on UDS_PlayerOcclusion_C_Initialize");
static_assert(offsetof(UDS_PlayerOcclusion_C_Initialize, CallFunc_IsValid_ReturnValue) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Initialize::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Periodic Context Checks
// 0x03B0 (0x03B0 - 0x0000)
struct UDS_PlayerOcclusion_C_Periodic_Context_Checks final
{
public:
	double                                        Portal_Distance_Mask;                              // 0x0000(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Portal_Direction;                                  // 0x0008(0x0018)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUDS_Occlusion_Portal_C*                Current_Portal;                                    // 0x0020(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue;      // 0x0030(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUDS_Occlusion_Portal_C*                CallFunc_Array_Get_Item;                           // 0x0048(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsNotEmpty_ReturnValue;             // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51[0x3];                                       // 0x0051(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0054(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetGameTimeInSeconds_ReturnValue;         // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Test_Point_for_Occlusion_Volumes_Final_Multiplier; // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_69[0x7];                                       // 0x0069(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetScalarParameterValue_ReturnValue;      // 0x0078(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EDrawDebugTrace                               CallFunc_Trace_Debugs_ReturnValue;                 // 0x007C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x007D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7E[0x2];                                       // 0x007E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0084(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0085(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_86[0x2];                                       // 0x0086(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8C[0x4];                                       // 0x008C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_GetForwardVector_ReturnValue;             // 0x0090(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_GetForwardVector_ReturnValue_1;           // 0x00A8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsActive_ReturnValue;                     // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C1[0x7];                                       // 0x00C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_1;       // 0x00D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_2;       // 0x00D9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_DA[0x6];                                       // 0x00DA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Multiply_VectorVector_ReturnValue;        // 0x00E0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Normal_ReturnValue;                       // 0x00F8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue_1;    // 0x0110(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Portal_Direction_Mask_Mask;               // 0x0128(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Vector_Distance_ReturnValue;              // 0x0138(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Conv_DoubleToVector_ReturnValue;          // 0x0140(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Multiply_VectorVector_ReturnValue_1;      // 0x0158(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_2;      // 0x0170(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x0178(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Multiply_LinearColorLinearColor_ReturnValue; // 0x0188(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0198(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Add_LinearColorLinearColor_ReturnValue;   // 0x01A0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x01B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MultiplyMultiply_FloatFloat_ReturnValue;  // 0x01B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue_2;    // 0x01C0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue;        // 0x01D8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Normal_ReturnValue_1;                     // 0x01F0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Dot_VectorVector_ReturnValue;             // 0x0208(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_3;       // 0x0210(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_211[0x7];                                      // 0x0211(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_SelectVector_ReturnValue;                 // 0x0218(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetComponentLocation_ReturnValue_3;    // 0x0230(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHitResult                             CallFunc_LineTraceSingle_OutHit;                   // 0x0248(0x00E8)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	bool                                          CallFunc_LineTraceSingle_ReturnValue;              // 0x0330(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0331(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_332[0x6];                                      // 0x0332(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue_1;      // 0x0338(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_VSize_ReturnValue;                        // 0x0350(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X;                            // 0x0358(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0360(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0368(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue_1;          // 0x0370(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0371(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_372[0x6];                                      // 0x0372(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_SelectFloat_ReturnValue;                  // 0x0378(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetScalarParameterValue_ReturnValue_1;    // 0x0380(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue_4;       // 0x0384(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_385[0x3];                                      // 0x0385(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Greater_DoubleDouble_A_ImplicitCast;      // 0x0388(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_R_ImplicitCast;                  // 0x0390(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_G_ImplicitCast;                  // 0x0394(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_B_ImplicitCast;                  // 0x0398(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast;                  // 0x039C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_LineTraceSingle_DrawTime_ImplicitCast;    // 0x03A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3A4[0x4];                                      // 0x03A4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Greater_DoubleDouble_A_ImplicitCast_1;    // 0x03A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Periodic_Context_Checks) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Periodic_Context_Checks");
static_assert(sizeof(UDS_PlayerOcclusion_C_Periodic_Context_Checks) == 0x0003B0, "Wrong size on UDS_PlayerOcclusion_C_Periodic_Context_Checks");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, Portal_Distance_Mask) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::Portal_Distance_Mask' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, Portal_Direction) == 0x000008, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::Portal_Direction' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, Current_Portal) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::Current_Portal' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, Temp_int_Array_Index_Variable) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_K2_GetComponentLocation_ReturnValue) == 0x000030, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_K2_GetComponentLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Array_Get_Item) == 0x000048, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Array_IsNotEmpty_ReturnValue) == 0x000050, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Array_IsNotEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Array_Length_ReturnValue) == 0x000054, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_GetGameTimeInSeconds_ReturnValue) == 0x000058, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_GetGameTimeInSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Test_Point_for_Occlusion_Volumes_Final_Multiplier) == 0x000060, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Test_Point_for_Occlusion_Volumes_Final_Multiplier' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000068, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000070, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_GetScalarParameterValue_ReturnValue) == 0x000078, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_GetScalarParameterValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Trace_Debugs_ReturnValue) == 0x00007C, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Trace_Debugs_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x00007D, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, Temp_int_Loop_Counter_Variable) == 0x000080, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, K2Node_SwitchEnum_CmpSuccess) == 0x000084, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Less_IntInt_ReturnValue) == 0x000085, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Add_IntInt_ReturnValue) == 0x000088, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_GetForwardVector_ReturnValue) == 0x000090, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_GetForwardVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_GetForwardVector_ReturnValue_1) == 0x0000A8, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_GetForwardVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_IsActive_ReturnValue) == 0x0000C0, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_IsActive_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x0000C8, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Add_DoubleDouble_ReturnValue) == 0x0000D0, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Greater_DoubleDouble_ReturnValue_1) == 0x0000D8, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Greater_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Greater_DoubleDouble_ReturnValue_2) == 0x0000D9, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Greater_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Multiply_VectorVector_ReturnValue) == 0x0000E0, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Multiply_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Normal_ReturnValue) == 0x0000F8, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Normal_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_K2_GetComponentLocation_ReturnValue_1) == 0x000110, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_K2_GetComponentLocation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Portal_Direction_Mask_Mask) == 0x000128, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Portal_Direction_Mask_Mask' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Vector_Distance_ReturnValue) == 0x000138, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Vector_Distance_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Conv_DoubleToVector_ReturnValue) == 0x000140, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Conv_DoubleToVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Multiply_VectorVector_ReturnValue_1) == 0x000158, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Multiply_VectorVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Multiply_DoubleDouble_ReturnValue_2) == 0x000170, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Multiply_DoubleDouble_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, K2Node_MakeStruct_LinearColor) == 0x000178, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Multiply_LinearColorLinearColor_ReturnValue) == 0x000188, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Multiply_LinearColorLinearColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000198, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Add_LinearColorLinearColor_ReturnValue) == 0x0001A0, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Add_LinearColorLinearColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x0001B0, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_MultiplyMultiply_FloatFloat_ReturnValue) == 0x0001B8, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_MultiplyMultiply_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_K2_GetComponentLocation_ReturnValue_2) == 0x0001C0, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_K2_GetComponentLocation_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Subtract_VectorVector_ReturnValue) == 0x0001D8, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Subtract_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Normal_ReturnValue_1) == 0x0001F0, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Normal_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Dot_VectorVector_ReturnValue) == 0x000208, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Dot_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Greater_DoubleDouble_ReturnValue_3) == 0x000210, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Greater_DoubleDouble_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_SelectVector_ReturnValue) == 0x000218, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_SelectVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_K2_GetComponentLocation_ReturnValue_3) == 0x000230, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_K2_GetComponentLocation_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_LineTraceSingle_OutHit) == 0x000248, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_LineTraceSingle_OutHit' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_LineTraceSingle_ReturnValue) == 0x000330, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_LineTraceSingle_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Not_PreBool_ReturnValue) == 0x000331, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Subtract_VectorVector_ReturnValue_1) == 0x000338, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Subtract_VectorVector_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_VSize_ReturnValue) == 0x000350, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_VSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_BreakVector_X) == 0x000358, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_BreakVector_Y) == 0x000360, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_BreakVector_Z) == 0x000368, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Less_DoubleDouble_ReturnValue_1) == 0x000370, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Less_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_BooleanAND_ReturnValue) == 0x000371, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_SelectFloat_ReturnValue) == 0x000378, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_SelectFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_GetScalarParameterValue_ReturnValue_1) == 0x000380, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_GetScalarParameterValue_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Greater_DoubleDouble_ReturnValue_4) == 0x000384, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Greater_DoubleDouble_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Greater_DoubleDouble_A_ImplicitCast) == 0x000388, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Greater_DoubleDouble_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, K2Node_MakeStruct_R_ImplicitCast) == 0x000390, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::K2Node_MakeStruct_R_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, K2Node_MakeStruct_G_ImplicitCast) == 0x000394, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::K2Node_MakeStruct_G_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, K2Node_MakeStruct_B_ImplicitCast) == 0x000398, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::K2Node_MakeStruct_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, K2Node_MakeStruct_A_ImplicitCast) == 0x00039C, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::K2Node_MakeStruct_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_LineTraceSingle_DrawTime_ImplicitCast) == 0x0003A0, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_LineTraceSingle_DrawTime_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Periodic_Context_Checks, CallFunc_Greater_DoubleDouble_A_ImplicitCast_1) == 0x0003A8, "Member 'UDS_PlayerOcclusion_C_Periodic_Context_Checks::CallFunc_Greater_DoubleDouble_A_ImplicitCast_1' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Portal Direction Mask
// 0x0090 (0x0090 - 0x0000)
struct UDS_PlayerOcclusion_C_Portal_Direction_Mask final
{
public:
	struct FVector                                Direction;                                         // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Mask;                                              // 0x0018(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_X;                            // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Y;                            // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector_Z;                            // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue_1;                     // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue_2;                     // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue_3;                     // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x0070(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_R_ImplicitCast;                  // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_G_ImplicitCast;                  // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast;                  // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_B_ImplicitCast;                  // 0x008C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Portal_Direction_Mask) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Portal_Direction_Mask");
static_assert(sizeof(UDS_PlayerOcclusion_C_Portal_Direction_Mask) == 0x000090, "Wrong size on UDS_PlayerOcclusion_C_Portal_Direction_Mask");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, Direction) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::Direction' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, Mask) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::Mask' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, CallFunc_BreakVector_X) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, CallFunc_BreakVector_Y) == 0x000030, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, CallFunc_BreakVector_Z) == 0x000038, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, CallFunc_FClamp_ReturnValue) == 0x000040, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000048, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, CallFunc_FClamp_ReturnValue_1) == 0x000050, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::CallFunc_FClamp_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, CallFunc_FClamp_ReturnValue_2) == 0x000058, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::CallFunc_FClamp_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000060, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, CallFunc_FClamp_ReturnValue_3) == 0x000068, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::CallFunc_FClamp_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, K2Node_MakeStruct_LinearColor) == 0x000070, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, K2Node_MakeStruct_R_ImplicitCast) == 0x000080, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::K2Node_MakeStruct_R_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, K2Node_MakeStruct_G_ImplicitCast) == 0x000084, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::K2Node_MakeStruct_G_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, K2Node_MakeStruct_A_ImplicitCast) == 0x000088, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::K2Node_MakeStruct_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Portal_Direction_Mask, K2Node_MakeStruct_B_ImplicitCast) == 0x00008C, "Member 'UDS_PlayerOcclusion_C_Portal_Direction_Mask::K2Node_MakeStruct_B_ImplicitCast' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.ReceiveEndPlay
// 0x0001 (0x0001 - 0x0000)
struct UDS_PlayerOcclusion_C_ReceiveEndPlay final
{
public:
	EEndPlayReason                                EndPlayReason;                                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_ReceiveEndPlay) == 0x000001, "Wrong alignment on UDS_PlayerOcclusion_C_ReceiveEndPlay");
static_assert(sizeof(UDS_PlayerOcclusion_C_ReceiveEndPlay) == 0x000001, "Wrong size on UDS_PlayerOcclusion_C_ReceiveEndPlay");
static_assert(offsetof(UDS_PlayerOcclusion_C_ReceiveEndPlay, EndPlayReason) == 0x000000, "Member 'UDS_PlayerOcclusion_C_ReceiveEndPlay::EndPlayReason' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.ReceiveParticleData
// 0x0030 (0x0030 - 0x0000)
struct UDS_PlayerOcclusion_C_ReceiveParticleData final
{
public:
	TArray<struct FBasicParticleData>             Data;                                              // 0x0000(0x0010)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	class UNiagaraSystem*                         NiagaraSystem;                                     // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FVector                                SimulationPositionOffset;                          // 0x0018(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_ReceiveParticleData) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_ReceiveParticleData");
static_assert(sizeof(UDS_PlayerOcclusion_C_ReceiveParticleData) == 0x000030, "Wrong size on UDS_PlayerOcclusion_C_ReceiveParticleData");
static_assert(offsetof(UDS_PlayerOcclusion_C_ReceiveParticleData, Data) == 0x000000, "Member 'UDS_PlayerOcclusion_C_ReceiveParticleData::Data' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ReceiveParticleData, NiagaraSystem) == 0x000010, "Member 'UDS_PlayerOcclusion_C_ReceiveParticleData::NiagaraSystem' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_ReceiveParticleData, SimulationPositionOffset) == 0x000018, "Member 'UDS_PlayerOcclusion_C_ReceiveParticleData::SimulationPositionOffset' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Runtime Tick
// 0x0020 (0x0020 - 0x0000)
struct UDS_PlayerOcclusion_C_Runtime_Tick final
{
public:
	struct FTimerHandle                           CallFunc_K2_SetTimer_ReturnValue;                  // 0x0000(0x0008)(NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DoubleDouble_ReturnValue;      // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           CallFunc_K2_SetTimerForNextTick_ReturnValue;       // 0x0010(0x0008)(NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_K2_SetTimer_Time_ImplicitCast;            // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Runtime_Tick) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Runtime_Tick");
static_assert(sizeof(UDS_PlayerOcclusion_C_Runtime_Tick) == 0x000020, "Wrong size on UDS_PlayerOcclusion_C_Runtime_Tick");
static_assert(offsetof(UDS_PlayerOcclusion_C_Runtime_Tick, CallFunc_K2_SetTimer_ReturnValue) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Runtime_Tick::CallFunc_K2_SetTimer_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Runtime_Tick, CallFunc_EqualEqual_DoubleDouble_ReturnValue) == 0x000008, "Member 'UDS_PlayerOcclusion_C_Runtime_Tick::CallFunc_EqualEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Runtime_Tick, CallFunc_K2_SetTimerForNextTick_ReturnValue) == 0x000010, "Member 'UDS_PlayerOcclusion_C_Runtime_Tick::CallFunc_K2_SetTimerForNextTick_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Runtime_Tick, CallFunc_K2_SetTimer_Time_ImplicitCast) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Runtime_Tick::CallFunc_K2_SetTimer_Time_ImplicitCast' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Start Up GPU Distance Field System
// 0x0040 (0x0040 - 0x0000)
struct UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System final
{
public:
	class UObject*                                CallFunc_Conv_SoftObjectReferenceToObject_ReturnValue; // 0x0000(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                CallFunc_LoadAsset_Blocking_ReturnValue;           // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UNiagaraSystem*                         K2Node_DynamicCast_AsNiagara_System;               // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Round_ReturnValue;                        // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AActor*                                 CallFunc_GetOwner_ReturnValue;                     // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UNiagaraComponent*                      CallFunc_AddComponentByClass_ReturnValue;          // 0x0038(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System");
static_assert(sizeof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System) == 0x000040, "Wrong size on UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System");
static_assert(offsetof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System, CallFunc_Conv_SoftObjectReferenceToObject_ReturnValue) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System::CallFunc_Conv_SoftObjectReferenceToObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System, CallFunc_LoadAsset_Blocking_ReturnValue) == 0x000008, "Member 'UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System::CallFunc_LoadAsset_Blocking_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System, K2Node_DynamicCast_AsNiagara_System) == 0x000010, "Member 'UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System::K2Node_DynamicCast_AsNiagara_System' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System, CallFunc_Round_ReturnValue) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System::CallFunc_Round_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System, CallFunc_GetOwner_ReturnValue) == 0x000030, "Member 'UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System::CallFunc_GetOwner_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System, CallFunc_AddComponentByClass_ReturnValue) == 0x000038, "Member 'UDS_PlayerOcclusion_C_Start_Up_GPU_Distance_Field_System::CallFunc_AddComponentByClass_ReturnValue' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Test Point for Occlusion Volumes
// 0x0260 (0x0260 - 0x0000)
struct UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes final
{
public:
	struct FVector                                Location;                                          // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Final_Multiplier;                                  // 0x0018(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Multiplier;                                        // 0x0020(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_Add_VectorVector_ReturnValue;             // 0x0038(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<EObjectTypeQuery>                      K2Node_MakeArray_Array;                            // 0x0050(0x0010)(ConstParm, ReferenceParm)
	TArray<struct FHitResult>                     CallFunc_LineTraceMultiForObjects_OutHits;         // 0x0060(0x0010)(ReferenceParm, ContainsInstancedReference)
	bool                                          CallFunc_LineTraceMultiForObjects_ReturnValue;     // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue;            // 0x0071(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_72[0x6];                                       // 0x0072(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FHitResult                             CallFunc_Array_Get_Item;                           // 0x0078(0x00E8)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	bool                                          CallFunc_BreakHitResult_bBlockingHit;              // 0x0160(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BreakHitResult_bInitialOverlap;           // 0x0161(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_162[0x2];                                      // 0x0162(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_BreakHitResult_Time;                      // 0x0164(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakHitResult_Distance;                  // 0x0168(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_16C[0x4];                                      // 0x016C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_BreakHitResult_Location;                  // 0x0170(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_ImpactPoint;               // 0x0188(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_Normal;                    // 0x01A0(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_ImpactNormal;              // 0x01B8(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPhysicalMaterial*                      CallFunc_BreakHitResult_PhysMat;                   // 0x01D0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 CallFunc_BreakHitResult_HitActor;                  // 0x01D8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UPrimitiveComponent*                    CallFunc_BreakHitResult_HitComponent;              // 0x01E0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_BreakHitResult_HitBoneName;               // 0x01E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_BreakHitResult_BoneName;                  // 0x01F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakHitResult_HitItem;                   // 0x01F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakHitResult_ElementIndex;              // 0x01FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakHitResult_FaceIndex;                 // 0x0200(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_204[0x4];                                      // 0x0204(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_BreakHitResult_TraceStart;                // 0x0208(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_BreakHitResult_TraceEnd;                  // 0x0220(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0238(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_23C[0x4];                                      // 0x023C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AUDS_Occlusion_Volume_C*                K2Node_DynamicCast_AsUDS_Occlusion_Volume;         // 0x0240(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0248(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0249(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24A[0x6];                                      // 0x024A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Query_Occlusion_Multiplier_Multiplier;    // 0x0250(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0258(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes");
static_assert(sizeof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes) == 0x000260, "Wrong size on UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, Location) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::Location' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, Final_Multiplier) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::Final_Multiplier' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, Multiplier) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::Multiplier' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, Temp_int_Array_Index_Variable) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, Temp_int_Loop_Counter_Variable) == 0x00002C, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_Add_IntInt_ReturnValue) == 0x000030, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_Add_VectorVector_ReturnValue) == 0x000038, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_Add_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, K2Node_MakeArray_Array) == 0x000050, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_LineTraceMultiForObjects_OutHits) == 0x000060, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_LineTraceMultiForObjects_OutHits' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_LineTraceMultiForObjects_ReturnValue) == 0x000070, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_LineTraceMultiForObjects_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_NotEqual_ByteByte_ReturnValue) == 0x000071, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_NotEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_Array_Get_Item) == 0x000078, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_bBlockingHit) == 0x000160, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_bBlockingHit' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_bInitialOverlap) == 0x000161, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_bInitialOverlap' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_Time) == 0x000164, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_Time' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_Distance) == 0x000168, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_Distance' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_Location) == 0x000170, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_Location' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_ImpactPoint) == 0x000188, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_ImpactPoint' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_Normal) == 0x0001A0, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_Normal' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_ImpactNormal) == 0x0001B8, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_ImpactNormal' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_PhysMat) == 0x0001D0, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_PhysMat' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_HitActor) == 0x0001D8, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_HitActor' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_HitComponent) == 0x0001E0, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_HitComponent' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_HitBoneName) == 0x0001E8, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_HitBoneName' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_BoneName) == 0x0001F0, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_BoneName' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_HitItem) == 0x0001F8, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_HitItem' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_ElementIndex) == 0x0001FC, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_ElementIndex' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_FaceIndex) == 0x000200, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_FaceIndex' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_TraceStart) == 0x000208, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_TraceStart' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_BreakHitResult_TraceEnd) == 0x000220, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_BreakHitResult_TraceEnd' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_Array_Length_ReturnValue) == 0x000238, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, K2Node_DynamicCast_AsUDS_Occlusion_Volume) == 0x000240, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::K2Node_DynamicCast_AsUDS_Occlusion_Volume' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, K2Node_DynamicCast_bSuccess) == 0x000248, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_Less_IntInt_ReturnValue) == 0x000249, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_Query_Occlusion_Multiplier_Multiplier) == 0x000250, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_Query_Occlusion_Multiplier_Multiplier' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000258, "Member 'UDS_PlayerOcclusion_C_Test_Point_for_Occlusion_Volumes::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Trace Debugs
// 0x0005 (0x0005 - 0x0000)
struct UDS_PlayerOcclusion_C_Trace_Debugs final
{
public:
	EDrawDebugTrace                               ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EDrawDebugTrace                               Temp_byte_Variable;                                // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EDrawDebugTrace                               Temp_byte_Variable_1;                              // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EDrawDebugTrace                               K2Node_Select_Default;                             // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Trace_Debugs) == 0x000001, "Wrong alignment on UDS_PlayerOcclusion_C_Trace_Debugs");
static_assert(sizeof(UDS_PlayerOcclusion_C_Trace_Debugs) == 0x000005, "Wrong size on UDS_PlayerOcclusion_C_Trace_Debugs");
static_assert(offsetof(UDS_PlayerOcclusion_C_Trace_Debugs, ReturnValue) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Trace_Debugs::ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Trace_Debugs, Temp_bool_Variable) == 0x000001, "Member 'UDS_PlayerOcclusion_C_Trace_Debugs::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Trace_Debugs, Temp_byte_Variable) == 0x000002, "Member 'UDS_PlayerOcclusion_C_Trace_Debugs::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Trace_Debugs, Temp_byte_Variable_1) == 0x000003, "Member 'UDS_PlayerOcclusion_C_Trace_Debugs::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Trace_Debugs, K2Node_Select_Default) == 0x000004, "Member 'UDS_PlayerOcclusion_C_Trace_Debugs::K2Node_Select_Default' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Update Current Occlusion
// 0x0108 (0x0108 - 0x0000)
struct UDS_PlayerOcclusion_C_Update_Current_Occlusion final
{
public:
	double                                        Time_Delta;                                        // 0x0000(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x0008(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Divide_LinearColorLinearColor_ReturnValue; // 0x0018(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Apply_Directional_Occlusion_Modifiers_Out; // 0x0028(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor_1;                   // 0x0038(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue;          // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue;             // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Conv_IntToDouble_ReturnValue_1;           // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Divide_DoubleDouble_ReturnValue_1;        // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Add_DoubleDouble_ReturnValue;             // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FClamp_ReturnValue;                       // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_99[0x3];                                       // 0x0099(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_CInterpTo_ReturnValue;                    // 0x009C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AC[0x4];                                       // 0x00AC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FInterpTo_ReturnValue;                    // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetWorldDeltaSeconds_ReturnValue;         // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_FMax_ReturnValue;                         // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_SelectFloat_ReturnValue;                  // 0x00C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_SelectColor_ReturnValue;                  // 0x00D0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_ParameterValue_ImplicitCast; // 0x00E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_R_ImplicitCast;                  // 0x00E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_G_ImplicitCast;                  // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_B_ImplicitCast;                  // 0x00EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast;                  // 0x00F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_R_ImplicitCast_1;                // 0x00F4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_G_ImplicitCast_1;                // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_B_ImplicitCast_1;                // 0x00FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_MakeStruct_A_ImplicitCast_1;                // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_CInterpTo_DeltaTime_ImplicitCast;         // 0x0104(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Update_Current_Occlusion) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Update_Current_Occlusion");
static_assert(sizeof(UDS_PlayerOcclusion_C_Update_Current_Occlusion) == 0x000108, "Wrong size on UDS_PlayerOcclusion_C_Update_Current_Occlusion");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, Time_Delta) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::Time_Delta' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_LinearColor) == 0x000008, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Divide_LinearColorLinearColor_ReturnValue) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Divide_LinearColorLinearColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Apply_Directional_Occlusion_Modifiers_Out) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Apply_Directional_Occlusion_Modifiers_Out' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_LinearColor_1) == 0x000038, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_LinearColor_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Divide_DoubleDouble_ReturnValue) == 0x000048, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Divide_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x000050, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Conv_IntToDouble_ReturnValue) == 0x000058, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Conv_IntToDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Conv_IntToDouble_ReturnValue_1) == 0x000060, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Conv_IntToDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Divide_DoubleDouble_ReturnValue_1) == 0x000068, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Divide_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000070, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Add_DoubleDouble_ReturnValue) == 0x000078, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Add_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_FClamp_ReturnValue) == 0x000080, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000088, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_MapRangeClamped_ReturnValue) == 0x000090, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_SwitchEnum_CmpSuccess) == 0x000098, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_CInterpTo_ReturnValue) == 0x00009C, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_CInterpTo_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_FInterpTo_ReturnValue) == 0x0000B0, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_FInterpTo_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_GetWorldDeltaSeconds_ReturnValue) == 0x0000B8, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_GetWorldDeltaSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_FMax_ReturnValue) == 0x0000C0, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_FMax_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_SelectFloat_ReturnValue) == 0x0000C8, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_SelectFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_SelectColor_ReturnValue) == 0x0000D0, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_SelectColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_SetScalarParameterValue_ParameterValue_ImplicitCast) == 0x0000E0, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_SetScalarParameterValue_ParameterValue_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_R_ImplicitCast) == 0x0000E4, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_R_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_G_ImplicitCast) == 0x0000E8, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_G_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_B_ImplicitCast) == 0x0000EC, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_B_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_A_ImplicitCast) == 0x0000F0, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_A_ImplicitCast' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_R_ImplicitCast_1) == 0x0000F4, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_R_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_G_ImplicitCast_1) == 0x0000F8, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_G_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_B_ImplicitCast_1) == 0x0000FC, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_B_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, K2Node_MakeStruct_A_ImplicitCast_1) == 0x000100, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::K2Node_MakeStruct_A_ImplicitCast_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Current_Occlusion, CallFunc_CInterpTo_DeltaTime_ImplicitCast) == 0x000104, "Member 'UDS_PlayerOcclusion_C_Update_Current_Occlusion::CallFunc_CInterpTo_DeltaTime_ImplicitCast' has a wrong offset!");

// Function UDS_PlayerOcclusion.UDS_PlayerOcclusion_C.Update Ignored Actors
// 0x0048 (0x0048 - 0x0000)
struct UDS_PlayerOcclusion_C_Update_Ignored_Actors final
{
public:
	class APlayerController*                      CallFunc_GetPlayerController_ReturnValue;          // 0x0000(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APlayerController*                      CallFunc_GetPlayerController_ReturnValue_1;        // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APawn*                                  CallFunc_K2_GetPawn_ReturnValue;                   // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetPlayerController_ReturnValue_2;        // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  CallFunc_K2_GetPawn_ReturnValue_1;                 // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	TArray<class AActor*>                         K2Node_MakeArray_Array;                            // 0x0038(0x0010)(ReferenceParm)
};
static_assert(alignof(UDS_PlayerOcclusion_C_Update_Ignored_Actors) == 0x000008, "Wrong alignment on UDS_PlayerOcclusion_C_Update_Ignored_Actors");
static_assert(sizeof(UDS_PlayerOcclusion_C_Update_Ignored_Actors) == 0x000048, "Wrong size on UDS_PlayerOcclusion_C_Update_Ignored_Actors");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Ignored_Actors, CallFunc_GetPlayerController_ReturnValue) == 0x000000, "Member 'UDS_PlayerOcclusion_C_Update_Ignored_Actors::CallFunc_GetPlayerController_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Ignored_Actors, CallFunc_GetPlayerController_ReturnValue_1) == 0x000008, "Member 'UDS_PlayerOcclusion_C_Update_Ignored_Actors::CallFunc_GetPlayerController_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Ignored_Actors, CallFunc_IsValid_ReturnValue) == 0x000010, "Member 'UDS_PlayerOcclusion_C_Update_Ignored_Actors::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Ignored_Actors, CallFunc_K2_GetPawn_ReturnValue) == 0x000018, "Member 'UDS_PlayerOcclusion_C_Update_Ignored_Actors::CallFunc_K2_GetPawn_ReturnValue' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Ignored_Actors, CallFunc_IsValid_ReturnValue_1) == 0x000020, "Member 'UDS_PlayerOcclusion_C_Update_Ignored_Actors::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Ignored_Actors, CallFunc_GetPlayerController_ReturnValue_2) == 0x000028, "Member 'UDS_PlayerOcclusion_C_Update_Ignored_Actors::CallFunc_GetPlayerController_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Ignored_Actors, CallFunc_K2_GetPawn_ReturnValue_1) == 0x000030, "Member 'UDS_PlayerOcclusion_C_Update_Ignored_Actors::CallFunc_K2_GetPawn_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(UDS_PlayerOcclusion_C_Update_Ignored_Actors, K2Node_MakeArray_Array) == 0x000038, "Member 'UDS_PlayerOcclusion_C_Update_Ignored_Actors::K2Node_MakeArray_Array' has a wrong offset!");

}

