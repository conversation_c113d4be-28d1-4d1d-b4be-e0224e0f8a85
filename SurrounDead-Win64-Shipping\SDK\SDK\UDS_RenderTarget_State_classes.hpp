﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_RenderTarget_State

#include "Basic.hpp"

#include "Engine_classes.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK
{

// BlueprintGeneratedClass UDS_RenderTarget_State.UDS_RenderTarget_State_C
// 0x0088 (0x00B8 - 0x0030)
class UUDS_RenderTarget_State_C final : public UPrimaryDataAsset
{
public:
	bool                                          Is_Active;                                         // 0x0030(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                Center_Location;                                   // 0x0038(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	double                                        Size;                                              // 0x0050(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTextureRenderTarget2D*                 Render_Target;                                     // 0x0058(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	class UTextureRenderTarget2D*                 Buffer_Target;                                     // 0x0060(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	int32                                         Resolution;                                        // 0x0068(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Half_Size;                                         // 0x0070(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Size_to_Res_Ratio;                                 // 0x0078(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                Top_Corner;                                        // 0x0080(0x0018)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Center_2D;                                         // 0x0098(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Top_Corner_2D;                                     // 0x00A8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Canvas_Brush_Location(const struct FVector2D& In, struct FVector2D* Out);
	void Canvas_Brush_Size(const struct FVector2D& In, struct FVector2D* Out);
	void Set_Location(const struct FVector& Center_Location_0, struct FLinearColor* Mapping_Vector4);
	void Set_Render_Target(class UTextureRenderTarget2D* Render_Target_0, class UTextureRenderTarget2D* Buffer_Target_0);
	void Set_Size(double Size_0);
	void Target_Extent_2D(struct FVector2D* Extent);
	void Target_Needs_Recenter(const struct FVector& Control_Location, const struct FVector& Axis_Mask, bool* Yes);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"UDS_RenderTarget_State_C">();
	}
	static class UUDS_RenderTarget_State_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UUDS_RenderTarget_State_C>();
	}
};
static_assert(alignof(UUDS_RenderTarget_State_C) == 0x000008, "Wrong alignment on UUDS_RenderTarget_State_C");
static_assert(sizeof(UUDS_RenderTarget_State_C) == 0x0000B8, "Wrong size on UUDS_RenderTarget_State_C");
static_assert(offsetof(UUDS_RenderTarget_State_C, Is_Active) == 0x000030, "Member 'UUDS_RenderTarget_State_C::Is_Active' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Center_Location) == 0x000038, "Member 'UUDS_RenderTarget_State_C::Center_Location' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Size) == 0x000050, "Member 'UUDS_RenderTarget_State_C::Size' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Render_Target) == 0x000058, "Member 'UUDS_RenderTarget_State_C::Render_Target' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Buffer_Target) == 0x000060, "Member 'UUDS_RenderTarget_State_C::Buffer_Target' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Resolution) == 0x000068, "Member 'UUDS_RenderTarget_State_C::Resolution' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Half_Size) == 0x000070, "Member 'UUDS_RenderTarget_State_C::Half_Size' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Size_to_Res_Ratio) == 0x000078, "Member 'UUDS_RenderTarget_State_C::Size_to_Res_Ratio' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Top_Corner) == 0x000080, "Member 'UUDS_RenderTarget_State_C::Top_Corner' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Center_2D) == 0x000098, "Member 'UUDS_RenderTarget_State_C::Center_2D' has a wrong offset!");
static_assert(offsetof(UUDS_RenderTarget_State_C, Top_Corner_2D) == 0x0000A8, "Member 'UUDS_RenderTarget_State_C::Top_Corner_2D' has a wrong offset!");

}

