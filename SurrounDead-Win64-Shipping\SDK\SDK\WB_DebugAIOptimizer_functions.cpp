﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_DebugAIOptimizer

#include "Basic.hpp"

#include "WB_DebugAIOptimizer_classes.hpp"
#include "WB_DebugAIOptimizer_parameters.hpp"


namespace SDK
{

// Function WB_DebugAIOptimizer.WB_DebugAIOptimizer_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWB_DebugAIOptimizer_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_DebugAIOptimizer_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_DebugAIOptimizer.WB_DebugAIOptimizer_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWB_DebugAIOptimizer_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_DebugAIOptimizer_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_DebugAIOptimizer.WB_DebugAIOptimizer_C.DrawDebug
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWB_DebugAIOptimizer_C::DrawDebug()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_DebugAIOptimizer_C", "DrawDebug");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WB_DebugAIOptimizer.WB_DebugAIOptimizer_C.ExecuteUbergraph_WB_DebugAIOptimizer
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_DebugAIOptimizer_C::ExecuteUbergraph_WB_DebugAIOptimizer(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_DebugAIOptimizer_C", "ExecuteUbergraph_WB_DebugAIOptimizer");

	Params::WB_DebugAIOptimizer_C_ExecuteUbergraph_WB_DebugAIOptimizer Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_DebugAIOptimizer.WB_DebugAIOptimizer_C.OnSubsystemEnabledChanged_Event_0
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    bIsEnabled_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_DebugAIOptimizer_C::OnSubsystemEnabledChanged_Event_0(bool bIsEnabled_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_DebugAIOptimizer_C", "OnSubsystemEnabledChanged_Event_0");

	Params::WB_DebugAIOptimizer_C_OnSubsystemEnabledChanged_Event_0 Parms{};

	Parms.bIsEnabled_0 = bIsEnabled_0;

	UObject::ProcessEvent(Func, &Parms);
}

}

