﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeHUDNotification

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CommonUI_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WBP_NarrativeHUDNotification.WBP_NarrativeHUDNotification_C
// 0x0058 (0x0340 - 0x02E8)
class UWBP_NarrativeHUDNotification_C final : public UCommonUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02E8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       FadeOut;                                           // 0x02F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UCommonTextBlock*                       CommonTextBlock_NotificationText;                  // 0x02F8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_124;                                         // 0x0300(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   Text;                                              // 0x0308(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	double                                        Duration;                                          // 0x0320(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class UWBP_NarrativeHUDNotification_C* Notification)> NotificationExpired; // 0x0328(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	double                                        AnimLength;                                        // 0x0338(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Begin_Expire();
	void Construct();
	void ExecuteUbergraph_WBP_NarrativeHUDNotification(int32 EntryPoint);
	void Expire();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WBP_NarrativeHUDNotification_C">();
	}
	static class UWBP_NarrativeHUDNotification_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWBP_NarrativeHUDNotification_C>();
	}
};
static_assert(alignof(UWBP_NarrativeHUDNotification_C) == 0x000008, "Wrong alignment on UWBP_NarrativeHUDNotification_C");
static_assert(sizeof(UWBP_NarrativeHUDNotification_C) == 0x000340, "Wrong size on UWBP_NarrativeHUDNotification_C");
static_assert(offsetof(UWBP_NarrativeHUDNotification_C, UberGraphFrame) == 0x0002E8, "Member 'UWBP_NarrativeHUDNotification_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUDNotification_C, FadeOut) == 0x0002F0, "Member 'UWBP_NarrativeHUDNotification_C::FadeOut' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUDNotification_C, CommonTextBlock_NotificationText) == 0x0002F8, "Member 'UWBP_NarrativeHUDNotification_C::CommonTextBlock_NotificationText' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUDNotification_C, Image_124) == 0x000300, "Member 'UWBP_NarrativeHUDNotification_C::Image_124' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUDNotification_C, Text) == 0x000308, "Member 'UWBP_NarrativeHUDNotification_C::Text' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUDNotification_C, Duration) == 0x000320, "Member 'UWBP_NarrativeHUDNotification_C::Duration' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUDNotification_C, NotificationExpired) == 0x000328, "Member 'UWBP_NarrativeHUDNotification_C::NotificationExpired' has a wrong offset!");
static_assert(offsetof(UWBP_NarrativeHUDNotification_C, AnimLength) == 0x000338, "Member 'UWBP_NarrativeHUDNotification_C::AnimLength' has a wrong offset!");

}

