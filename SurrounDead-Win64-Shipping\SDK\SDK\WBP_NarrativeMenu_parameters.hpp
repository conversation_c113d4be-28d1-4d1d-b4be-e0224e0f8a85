﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeMenu

#include "Basic.hpp"


namespace SDK::Params
{

// Function WBP_NarrativeMenu.WBP_NarrativeMenu_C.ExecuteUbergraph_WBP_NarrativeMenu
// 0x0038 (0x0038 - 0x0000)
struct WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, NoDestru<PERSON>, HasGetValueTypeHash)
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_1;            // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_2;            // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_3;            // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_4;            // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu) == 0x000008, "Wrong alignment on WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu");
static_assert(sizeof(WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu) == 0x000038, "Wrong size on WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu");
static_assert(offsetof(WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu, EntryPoint) == 0x000000, "Member 'WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu::EntryPoint' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu, CallFunc_GetOwningPlayer_ReturnValue) == 0x000008, "Member 'WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu, CallFunc_GetOwningPlayer_ReturnValue_1) == 0x000010, "Member 'WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu::CallFunc_GetOwningPlayer_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu, CallFunc_GetOwningPlayer_ReturnValue_2) == 0x000018, "Member 'WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu::CallFunc_GetOwningPlayer_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu, CallFunc_IsValid_ReturnValue) == 0x000020, "Member 'WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu, CallFunc_GetOwningPlayer_ReturnValue_3) == 0x000028, "Member 'WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu::CallFunc_GetOwningPlayer_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu, CallFunc_GetOwningPlayer_ReturnValue_4) == 0x000030, "Member 'WBP_NarrativeMenu_C_ExecuteUbergraph_WBP_NarrativeMenu::CallFunc_GetOwningPlayer_ReturnValue_4' has a wrong offset!");

}

