﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: ZombieBoss_EnemyInRange

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function ZombieBoss_EnemyInRange.ZombieBoss_EnemyInRange_C.ExecuteUbergraph_ZombieBoss_EnemyInRange
// 0x0088 (0x0088 - 0x0000)
struct ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AActor*                                 CallFunc_GetBlackboardValueAsActor_ReturnValue;    // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AAIController*                          K2Node_Event_OwnerController;                      // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  K2Node_Event_ControlledPawn;                       // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_K2_GetActorLocation_ReturnValue;          // 0x0028(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class USD_AIComponent_C*                      CallFunc_GetComponentByClass_ReturnValue;          // 0x0040(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_K2_GetActorLocation_ReturnValue_1;        // 0x0048(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_Subtract_VectorVector_ReturnValue;        // 0x0060(0x0018)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_VSize_ReturnValue;                        // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue;       // 0x0080(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange) == 0x000008, "Wrong alignment on ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange");
static_assert(sizeof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange) == 0x000088, "Wrong size on ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, EntryPoint) == 0x000000, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::EntryPoint' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, CallFunc_GetBlackboardValueAsActor_ReturnValue) == 0x000008, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::CallFunc_GetBlackboardValueAsActor_ReturnValue' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, K2Node_Event_OwnerController) == 0x000010, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::K2Node_Event_OwnerController' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, K2Node_Event_ControlledPawn) == 0x000018, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::K2Node_Event_ControlledPawn' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, CallFunc_IsValid_ReturnValue) == 0x000020, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, CallFunc_K2_GetActorLocation_ReturnValue) == 0x000028, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::CallFunc_K2_GetActorLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, CallFunc_GetComponentByClass_ReturnValue) == 0x000040, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::CallFunc_GetComponentByClass_ReturnValue' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, CallFunc_K2_GetActorLocation_ReturnValue_1) == 0x000048, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::CallFunc_K2_GetActorLocation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, CallFunc_Subtract_VectorVector_ReturnValue) == 0x000060, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::CallFunc_Subtract_VectorVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, CallFunc_VSize_ReturnValue) == 0x000078, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::CallFunc_VSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange, CallFunc_LessEqual_DoubleDouble_ReturnValue) == 0x000080, "Member 'ZombieBoss_EnemyInRange_C_ExecuteUbergraph_ZombieBoss_EnemyInRange::CallFunc_LessEqual_DoubleDouble_ReturnValue' has a wrong offset!");

// Function ZombieBoss_EnemyInRange.ZombieBoss_EnemyInRange_C.ReceiveActivationAI
// 0x0010 (0x0010 - 0x0000)
struct ZombieBoss_EnemyInRange_C_ReceiveActivationAI final
{
public:
	class AAIController*                          OwnerController;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APawn*                                  ControlledPawn;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(ZombieBoss_EnemyInRange_C_ReceiveActivationAI) == 0x000008, "Wrong alignment on ZombieBoss_EnemyInRange_C_ReceiveActivationAI");
static_assert(sizeof(ZombieBoss_EnemyInRange_C_ReceiveActivationAI) == 0x000010, "Wrong size on ZombieBoss_EnemyInRange_C_ReceiveActivationAI");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ReceiveActivationAI, OwnerController) == 0x000000, "Member 'ZombieBoss_EnemyInRange_C_ReceiveActivationAI::OwnerController' has a wrong offset!");
static_assert(offsetof(ZombieBoss_EnemyInRange_C_ReceiveActivationAI, ControlledPawn) == 0x000008, "Member 'ZombieBoss_EnemyInRange_C_ReceiveActivationAI::ControlledPawn' has a wrong offset!");

}

