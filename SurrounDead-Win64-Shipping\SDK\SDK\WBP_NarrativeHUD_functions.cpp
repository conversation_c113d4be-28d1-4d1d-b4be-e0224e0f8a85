﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeHUD

#include "Basic.hpp"

#include "WBP_NarrativeHUD_classes.hpp"
#include "WBP_NarrativeHUD_parameters.hpp"


namespace SDK
{

// Function WBP_NarrativeHUD.WBP_NarrativeHUD_C.ExecuteUbergraph_WBP_NarrativeHUD
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_NarrativeHUD_C::ExecuteUbergraph_WBP_NarrativeHUD(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeHUD_C", "ExecuteUbergraph_WBP_NarrativeHUD");

	Params::WBP_NarrativeHUD_C_ExecuteUbergraph_WBP_NarrativeHUD Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_NarrativeHUD.WBP_NarrativeHUD_C.Open Quantity Selector
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   MinAmount                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   MaxAmount                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const class FText&                      InstructionText                                        (BlueprintVisible, BlueprintReadOnly, Parm)
// class UWBP_QuantitySelector_C**         Quantity_Selector                                      (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UWBP_NarrativeHUD_C::Open_Quantity_Selector(int32 MinAmount, int32 MaxAmount, const class FText& InstructionText, class UWBP_QuantitySelector_C** Quantity_Selector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeHUD_C", "Open Quantity Selector");

	Params::WBP_NarrativeHUD_C_Open_Quantity_Selector Parms{};

	Parms.MinAmount = MinAmount;
	Parms.MaxAmount = MaxAmount;
	Parms.InstructionText = std::move(InstructionText);

	UObject::ProcessEvent(Func, &Parms);

	if (Quantity_Selector != nullptr)
		*Quantity_Selector = Parms.Quantity_Selector;
}


// Function WBP_NarrativeHUD.WBP_NarrativeHUD_C.OpenMenu
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TSubclassOf<class UWBP_NarrativeMenu_C> ActivatableWidgetClass                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
// class UWBP_NarrativeMenu_C*             ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)

class UWBP_NarrativeMenu_C* UWBP_NarrativeHUD_C::OpenMenu(TSubclassOf<class UWBP_NarrativeMenu_C> ActivatableWidgetClass)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeHUD_C", "OpenMenu");

	Params::WBP_NarrativeHUD_C_OpenMenu Parms{};

	Parms.ActivatableWidgetClass = ActivatableWidgetClass;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WBP_NarrativeHUD.WBP_NarrativeHUD_C.ShowNotification
// (Event, Public, HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      NotificationText                                       (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const float                             Duration                                               (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_NarrativeHUD_C::ShowNotification(const class FText& NotificationText, const float Duration)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeHUD_C", "ShowNotification");

	Params::WBP_NarrativeHUD_C_ShowNotification Parms{};

	Parms.NotificationText = std::move(NotificationText);
	Parms.Duration = Duration;

	UObject::ProcessEvent(Func, &Parms);
}

}

