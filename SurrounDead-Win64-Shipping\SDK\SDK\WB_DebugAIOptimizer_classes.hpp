﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_DebugAIOptimizer

#include "Basic.hpp"

#include "AIOptimizer_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_DebugAIOptimizer.WB_DebugAIOptimizer_C
// 0x0068 (0x0328 - 0x02C0)
class UWB_DebugAIOptimizer_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UTextBlock*                             Text_Despawned_1;                                  // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Text_NotUpdated_1;                                 // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Text_PendingDespawn_1;                             // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Text_PendingSpawn_1;                               // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Text_Spawned_1;                                    // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Text_SpawnedClose_1;                               // 0x02F0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Text_SpawnedFar_1;                                 // 0x02F8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Text_SpawnedMedium_1;                              // 0x0300(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	bool                                          DrawDistanceTexts;                                 // 0x0308(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          DrawDebugLines;                                    // 0x0309(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_30A[0x6];                                      // 0x030A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           TimerHandle_DrawDebug;                             // 0x0310(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	double                                        UpdateInterval;                                    // 0x0318(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ShowDespawnedSubjects;                             // 0x0320(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          SubsystemEnabled;                                  // 0x0321(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Construct();
	void Destruct();
	void DrawDebug();
	void ExecuteUbergraph_WB_DebugAIOptimizer(int32 EntryPoint);
	void OnSubsystemEnabledChanged_Event_0(bool bIsEnabled_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_DebugAIOptimizer_C">();
	}
	static class UWB_DebugAIOptimizer_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_DebugAIOptimizer_C>();
	}
};
static_assert(alignof(UWB_DebugAIOptimizer_C) == 0x000008, "Wrong alignment on UWB_DebugAIOptimizer_C");
static_assert(sizeof(UWB_DebugAIOptimizer_C) == 0x000328, "Wrong size on UWB_DebugAIOptimizer_C");
static_assert(offsetof(UWB_DebugAIOptimizer_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_DebugAIOptimizer_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, Text_Despawned_1) == 0x0002C8, "Member 'UWB_DebugAIOptimizer_C::Text_Despawned_1' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, Text_NotUpdated_1) == 0x0002D0, "Member 'UWB_DebugAIOptimizer_C::Text_NotUpdated_1' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, Text_PendingDespawn_1) == 0x0002D8, "Member 'UWB_DebugAIOptimizer_C::Text_PendingDespawn_1' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, Text_PendingSpawn_1) == 0x0002E0, "Member 'UWB_DebugAIOptimizer_C::Text_PendingSpawn_1' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, Text_Spawned_1) == 0x0002E8, "Member 'UWB_DebugAIOptimizer_C::Text_Spawned_1' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, Text_SpawnedClose_1) == 0x0002F0, "Member 'UWB_DebugAIOptimizer_C::Text_SpawnedClose_1' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, Text_SpawnedFar_1) == 0x0002F8, "Member 'UWB_DebugAIOptimizer_C::Text_SpawnedFar_1' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, Text_SpawnedMedium_1) == 0x000300, "Member 'UWB_DebugAIOptimizer_C::Text_SpawnedMedium_1' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, DrawDistanceTexts) == 0x000308, "Member 'UWB_DebugAIOptimizer_C::DrawDistanceTexts' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, DrawDebugLines) == 0x000309, "Member 'UWB_DebugAIOptimizer_C::DrawDebugLines' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, TimerHandle_DrawDebug) == 0x000310, "Member 'UWB_DebugAIOptimizer_C::TimerHandle_DrawDebug' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, UpdateInterval) == 0x000318, "Member 'UWB_DebugAIOptimizer_C::UpdateInterval' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, ShowDespawnedSubjects) == 0x000320, "Member 'UWB_DebugAIOptimizer_C::ShowDespawnedSubjects' has a wrong offset!");
static_assert(offsetof(UWB_DebugAIOptimizer_C, SubsystemEnabled) == 0x000321, "Member 'UWB_DebugAIOptimizer_C::SubsystemEnabled' has a wrong offset!");

}

