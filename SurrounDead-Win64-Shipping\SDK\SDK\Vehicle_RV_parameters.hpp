﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Vehicle_RV

#include "Basic.hpp"

#include "GameplayTags_structs.hpp"


namespace SDK::Params
{

// Function Vehicle_RV.Vehicle_RV_C.ExecuteUbergraph_Vehicle_RV
// 0x0048 (0x0048 - 0x0000)
struct Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class USleepingUI_C*                          CallFunc_Create_ReturnValue;                       // 0x0008(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class AActor* InteractingActor)> K2Node_CreateDelegate_OutputDelegate;            // 0x0010(0x0010)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AActor*                                 K2Node_CustomEvent_InteractingActor;               // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class AUltra_Dynamic_Sky_C*                   CallFunc_GetUDS_UDS;                               // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class APlayerController*                      CallFunc_GetPlayerController_ReturnValue;          // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_DoubleDouble_ReturnValue;       // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_DoubleDouble_ReturnValue;    // 0x0039(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x003A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3B[0x5];                                       // 0x003B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class APawn*                                  CallFunc_GetPlayerPawn_ReturnValue;                // 0x0040(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV) == 0x000008, "Wrong alignment on Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV");
static_assert(sizeof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV) == 0x000048, "Wrong size on Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, EntryPoint) == 0x000000, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::EntryPoint' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, CallFunc_Create_ReturnValue) == 0x000008, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, K2Node_CreateDelegate_OutputDelegate) == 0x000010, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, K2Node_CustomEvent_InteractingActor) == 0x000020, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::K2Node_CustomEvent_InteractingActor' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, CallFunc_GetUDS_UDS) == 0x000028, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::CallFunc_GetUDS_UDS' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, CallFunc_GetPlayerController_ReturnValue) == 0x000030, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::CallFunc_GetPlayerController_ReturnValue' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, CallFunc_LessEqual_DoubleDouble_ReturnValue) == 0x000038, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::CallFunc_LessEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, CallFunc_GreaterEqual_DoubleDouble_ReturnValue) == 0x000039, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::CallFunc_GreaterEqual_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, CallFunc_BooleanOR_ReturnValue) == 0x00003A, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV, CallFunc_GetPlayerPawn_ReturnValue) == 0x000040, "Member 'Vehicle_RV_C_ExecuteUbergraph_Vehicle_RV::CallFunc_GetPlayerPawn_ReturnValue' has a wrong offset!");

// Function Vehicle_RV.Vehicle_RV_C.SleepInteractionOption
// 0x0008 (0x0008 - 0x0000)
struct Vehicle_RV_C_SleepInteractionOption final
{
public:
	class AActor*                                 InteractingActor;                                  // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Vehicle_RV_C_SleepInteractionOption) == 0x000008, "Wrong alignment on Vehicle_RV_C_SleepInteractionOption");
static_assert(sizeof(Vehicle_RV_C_SleepInteractionOption) == 0x000008, "Wrong size on Vehicle_RV_C_SleepInteractionOption");
static_assert(offsetof(Vehicle_RV_C_SleepInteractionOption, InteractingActor) == 0x000000, "Member 'Vehicle_RV_C_SleepInteractionOption::InteractingActor' has a wrong offset!");

// Function Vehicle_RV.Vehicle_RV_C.UserConstructionScript
// 0x0010 (0x0010 - 0x0000)
struct Vehicle_RV_C_UserConstructionScript final
{
public:
	TArray<class USpotLightComponent*>            K2Node_MakeArray_Array;                            // 0x0000(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(Vehicle_RV_C_UserConstructionScript) == 0x000008, "Wrong alignment on Vehicle_RV_C_UserConstructionScript");
static_assert(sizeof(Vehicle_RV_C_UserConstructionScript) == 0x000010, "Wrong size on Vehicle_RV_C_UserConstructionScript");
static_assert(offsetof(Vehicle_RV_C_UserConstructionScript, K2Node_MakeArray_Array) == 0x000000, "Member 'Vehicle_RV_C_UserConstructionScript::K2Node_MakeArray_Array' has a wrong offset!");

// Function Vehicle_RV.Vehicle_RV_C.GetInteractOptions
// 0x00A0 (0x00A0 - 0x0000)
struct Vehicle_RV_C_GetInteractOptions final
{
public:
	TMap<struct FGameplayTag, class FText>        Options;                                           // 0x0000(0x0050)(Parm, OutParm)
	TMap<struct FGameplayTag, class FText>        K2Node_MakeMap_Map;                                // 0x0050(0x0050)()
};
static_assert(alignof(Vehicle_RV_C_GetInteractOptions) == 0x000008, "Wrong alignment on Vehicle_RV_C_GetInteractOptions");
static_assert(sizeof(Vehicle_RV_C_GetInteractOptions) == 0x0000A0, "Wrong size on Vehicle_RV_C_GetInteractOptions");
static_assert(offsetof(Vehicle_RV_C_GetInteractOptions, Options) == 0x000000, "Member 'Vehicle_RV_C_GetInteractOptions::Options' has a wrong offset!");
static_assert(offsetof(Vehicle_RV_C_GetInteractOptions, K2Node_MakeMap_Map) == 0x000050, "Member 'Vehicle_RV_C_GetInteractOptions::K2Node_MakeMap_Map' has a wrong offset!");

}

