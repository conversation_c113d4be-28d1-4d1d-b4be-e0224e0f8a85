/*
* Call of Duty: Modern Warfare Controller Mod for SurrounDead
* Main DLL entry point and hook implementation
*/

#include "COD_Controller_Mod.hpp"
#include <iostream>
#include <fstream>

// Console for debugging
void CreateConsole() {
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    SetConsoleTitle(L"COD Controller Mod - SurrounDead");
}

// Log function
void Log(const std::string& message) {
    std::cout << "[COD Controller] " << message << std::endl;
    
    // Also log to file
    std::ofstream logFile("cod_controller_mod.log", std::ios::app);
    if (logFile.is_open()) {
        logFile << "[COD Controller] " << message << std::endl;
        logFile.close();
    }
}

// Initialize the mod
bool InitializeMod() {
    Log("Initializing Call of Duty Controller Mod...");
    
    // Create the controller mod instance
    g_controller_mod = new CODControllerMod();
    
    if (!g_controller_mod->Initialize()) {
        Log("Failed to initialize controller mod!");
        return false;
    }
    
    // Start the input processing
    g_controller_mod->Start();
    
    Log("Controller mod initialized successfully!");
    Log("Controls:");
    Log("  Left Stick: Movement");
    Log("  Right Stick: Camera/Look");
    Log("  A Button: Jump");
    Log("  B Button: Reload");
    Log("  X Button: Use/Interact");
    Log("  Y Button: Switch Weapon");
    Log("  LT: Aim Down Sights");
    Log("  RT: Fire");
    Log("  LB: Grenade");
    Log("  RB: Tactical");
    Log("  LS Click: Sprint");
    Log("  RS Click: Melee");
    Log("  D-Pad Up: Prone");
    Log("  D-Pad Down: Crouch");
    Log("  D-Pad Left: Last Weapon");
    Log("  D-Pad Right: Inventory");
    Log("  Back: Scoreboard");
    Log("  Start: Menu");
    
    return true;
}

// Cleanup the mod
void CleanupMod() {
    Log("Cleaning up controller mod...");
    
    if (g_controller_mod) {
        g_controller_mod->Stop();
        delete g_controller_mod;
        g_controller_mod = nullptr;
    }
    
    Log("Controller mod cleaned up.");
}

// Console command handler
void HandleConsoleCommands() {
    std::string command;
    while (std::getline(std::cin, command)) {
        if (command == "help") {
            std::cout << "Available commands:" << std::endl;
            std::cout << "  help - Show this help" << std::endl;
            std::cout << "  status - Show controller status" << std::endl;
            std::cout << "  sensitivity <value> - Set look sensitivity (0.1-10.0)" << std::endl;
            std::cout << "  aimassist <on/off> - Toggle aim assist" << std::endl;
            std::cout << "  reload - Reload settings" << std::endl;
            std::cout << "  exit - Exit the mod" << std::endl;
        }
        else if (command == "status") {
            if (g_controller_mod) {
                std::cout << "Controller connected: " << (g_controller_mod->IsControllerConnected() ? "Yes" : "No") << std::endl;
            }
        }
        else if (command.substr(0, 11) == "sensitivity") {
            if (command.length() > 12) {
                try {
                    float sensitivity = std::stof(command.substr(12));
                    if (sensitivity >= 0.1f && sensitivity <= 10.0f) {
                        if (g_controller_mod) {
                            g_controller_mod->SetSensitivity(sensitivity);
                            std::cout << "Sensitivity set to: " << sensitivity << std::endl;
                        }
                    } else {
                        std::cout << "Sensitivity must be between 0.1 and 10.0" << std::endl;
                    }
                } catch (...) {
                    std::cout << "Invalid sensitivity value" << std::endl;
                }
            } else {
                std::cout << "Usage: sensitivity <value>" << std::endl;
            }
        }
        else if (command == "aimassist on") {
            if (g_controller_mod) {
                g_controller_mod->SetAimAssist(true);
                std::cout << "Aim assist enabled" << std::endl;
            }
        }
        else if (command == "aimassist off") {
            if (g_controller_mod) {
                g_controller_mod->SetAimAssist(false);
                std::cout << "Aim assist disabled" << std::endl;
            }
        }
        else if (command == "reload") {
            if (g_controller_mod) {
                g_controller_mod->LoadSettings();
                std::cout << "Settings reloaded" << std::endl;
            }
        }
        else if (command == "exit") {
            break;
        }
        else if (!command.empty()) {
            std::cout << "Unknown command. Type 'help' for available commands." << std::endl;
        }
    }
}

// DLL Main Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // Disable thread library calls for performance
        DisableThreadLibraryCalls(hModule);
        
        // Create console for debugging (optional)
        #ifdef _DEBUG
        CreateConsole();
        #endif
        
        // Initialize the mod in a separate thread to avoid blocking
        CreateThread(nullptr, 0, [](LPVOID) -> DWORD {
            // Wait a bit for the game to initialize
            Sleep(3000);
            
            if (InitializeMod()) {
                Log("Mod loaded successfully! Controller should now work with COD controls.");
                
                #ifdef _DEBUG
                // Handle console commands in debug mode
                std::thread console_thread(HandleConsoleCommands);
                console_thread.detach();
                #endif
            } else {
                Log("Failed to load mod!");
            }
            
            return 0;
        }, nullptr, 0, nullptr);
        
        break;
        
    case DLL_PROCESS_DETACH:
        CleanupMod();
        
        #ifdef _DEBUG
        FreeConsole();
        #endif
        
        break;
    }
    return TRUE;
}

// Export functions for external control
extern "C" {
    __declspec(dllexport) bool SetControllerSensitivity(float sensitivity) {
        if (g_controller_mod && sensitivity >= 0.1f && sensitivity <= 10.0f) {
            g_controller_mod->SetSensitivity(sensitivity);
            return true;
        }
        return false;
    }
    
    __declspec(dllexport) bool SetAimAssist(bool enabled, float strength = 0.6f) {
        if (g_controller_mod) {
            g_controller_mod->SetAimAssist(enabled, strength);
            return true;
        }
        return false;
    }
    
    __declspec(dllexport) bool IsControllerConnected() {
        if (g_controller_mod) {
            return g_controller_mod->IsControllerConnected();
        }
        return false;
    }
    
    __declspec(dllexport) void ReloadSettings() {
        if (g_controller_mod) {
            g_controller_mod->LoadSettings();
        }
    }
}
