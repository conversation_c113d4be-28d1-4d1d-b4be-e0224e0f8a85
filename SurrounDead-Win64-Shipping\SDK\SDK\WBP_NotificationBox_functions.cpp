﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NotificationBox

#include "Basic.hpp"

#include "WBP_NotificationBox_classes.hpp"
#include "WBP_NotificationBox_parameters.hpp"


namespace SDK
{

// Function WBP_NotificationBox.WBP_NotificationBox_C.NotificationExpired
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWBP_NarrativeHUDNotification_C*  Notification                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UWBP_NotificationBox_C::NotificationExpired(class UWBP_NarrativeHUDNotification_C* Notification)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NotificationBox_C", "NotificationExpired");

	Params::WBP_NotificationBox_C_NotificationExpired Parms{};

	Parms.Notification = Notification;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_NotificationBox.WBP_NotificationBox_C.ShowNotification
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      Text_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm)
// double                                  Duration                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_NotificationBox_C::ShowNotification(const class FText& Text_0, double Duration)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NotificationBox_C", "ShowNotification");

	Params::WBP_NotificationBox_C_ShowNotification Parms{};

	Parms.Text_0 = std::move(Text_0);
	Parms.Duration = Duration;

	UObject::ProcessEvent(Func, &Parms);
}

}

