﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_WorldMarker

#include "Basic.hpp"

#include "UMG_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function W_WorldMarker.W_WorldMarker_C.ExecuteUbergraph_W_WorldMarker
// 0x0148 (0x0148 - 0x0000)
struct W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0008(0x0008)(ZeroCons<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UW_WorldMarkerTooltip_C*                CallFunc_Create_ReturnValue;                       // 0x0018(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0020(0x0038)(IsPlainOldData, NoDestructor)
	struct FPointerEvent                          K2Node_Event_MouseEvent_1;                         // 0x0058(0x0078)(ConstParm)
	struct FPointerEvent                          K2Node_Event_MouseEvent;                           // 0x00D0(0x0078)(ConstParm)
};
static_assert(alignof(W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker) == 0x000008, "Wrong alignment on W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker");
static_assert(sizeof(W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker) == 0x000148, "Wrong size on W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker");
static_assert(offsetof(W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker, EntryPoint) == 0x000000, "Member 'W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker::EntryPoint' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000008, "Member 'W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker, CallFunc_PlayAnimationForward_ReturnValue) == 0x000010, "Member 'W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker, CallFunc_Create_ReturnValue) == 0x000018, "Member 'W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker, K2Node_Event_MyGeometry) == 0x000020, "Member 'W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker, K2Node_Event_MouseEvent_1) == 0x000058, "Member 'W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker::K2Node_Event_MouseEvent_1' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker, K2Node_Event_MouseEvent) == 0x0000D0, "Member 'W_WorldMarker_C_ExecuteUbergraph_W_WorldMarker::K2Node_Event_MouseEvent' has a wrong offset!");

// Function W_WorldMarker.W_WorldMarker_C.Get_ToolTipWidget
// 0x0010 (0x0010 - 0x0000)
struct W_WorldMarker_C_Get_ToolTipWidget final
{
public:
	class UWidget*                                ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable;                                // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              Temp_byte_Variable_1;                              // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x000B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_TextIsEmpty_ReturnValue;                  // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              K2Node_Select_Default;                             // 0x000D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_WorldMarker_C_Get_ToolTipWidget) == 0x000008, "Wrong alignment on W_WorldMarker_C_Get_ToolTipWidget");
static_assert(sizeof(W_WorldMarker_C_Get_ToolTipWidget) == 0x000010, "Wrong size on W_WorldMarker_C_Get_ToolTipWidget");
static_assert(offsetof(W_WorldMarker_C_Get_ToolTipWidget, ReturnValue) == 0x000000, "Member 'W_WorldMarker_C_Get_ToolTipWidget::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_Get_ToolTipWidget, Temp_bool_Variable) == 0x000008, "Member 'W_WorldMarker_C_Get_ToolTipWidget::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_Get_ToolTipWidget, Temp_byte_Variable) == 0x000009, "Member 'W_WorldMarker_C_Get_ToolTipWidget::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_Get_ToolTipWidget, Temp_byte_Variable_1) == 0x00000A, "Member 'W_WorldMarker_C_Get_ToolTipWidget::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_Get_ToolTipWidget, CallFunc_IsValid_ReturnValue) == 0x00000B, "Member 'W_WorldMarker_C_Get_ToolTipWidget::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_Get_ToolTipWidget, CallFunc_TextIsEmpty_ReturnValue) == 0x00000C, "Member 'W_WorldMarker_C_Get_ToolTipWidget::CallFunc_TextIsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_Get_ToolTipWidget, K2Node_Select_Default) == 0x00000D, "Member 'W_WorldMarker_C_Get_ToolTipWidget::K2Node_Select_Default' has a wrong offset!");

// Function W_WorldMarker.W_WorldMarker_C.OnMouseButtonDoubleClick
// 0x0230 (0x0230 - 0x0000)
struct W_WorldMarker_C_OnMouseButtonDoubleClick final
{
public:
	struct FGeometry                              InMyGeometry;                                      // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          InMouseEvent;                                      // 0x0038(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00B0(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0168(0x00B8)()
	class UBPC_MinimapSystem_C*                   CallFunc_Get_Minimap_Component_ReturnValue;        // 0x0220(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0228(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(W_WorldMarker_C_OnMouseButtonDoubleClick) == 0x000008, "Wrong alignment on W_WorldMarker_C_OnMouseButtonDoubleClick");
static_assert(sizeof(W_WorldMarker_C_OnMouseButtonDoubleClick) == 0x000230, "Wrong size on W_WorldMarker_C_OnMouseButtonDoubleClick");
static_assert(offsetof(W_WorldMarker_C_OnMouseButtonDoubleClick, InMyGeometry) == 0x000000, "Member 'W_WorldMarker_C_OnMouseButtonDoubleClick::InMyGeometry' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_OnMouseButtonDoubleClick, InMouseEvent) == 0x000038, "Member 'W_WorldMarker_C_OnMouseButtonDoubleClick::InMouseEvent' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_OnMouseButtonDoubleClick, ReturnValue) == 0x0000B0, "Member 'W_WorldMarker_C_OnMouseButtonDoubleClick::ReturnValue' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_OnMouseButtonDoubleClick, CallFunc_Handled_ReturnValue) == 0x000168, "Member 'W_WorldMarker_C_OnMouseButtonDoubleClick::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_OnMouseButtonDoubleClick, CallFunc_Get_Minimap_Component_ReturnValue) == 0x000220, "Member 'W_WorldMarker_C_OnMouseButtonDoubleClick::CallFunc_Get_Minimap_Component_ReturnValue' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_OnMouseButtonDoubleClick, CallFunc_IsValid_ReturnValue) == 0x000228, "Member 'W_WorldMarker_C_OnMouseButtonDoubleClick::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function W_WorldMarker.W_WorldMarker_C.OnMouseEnter
// 0x00B0 (0x00B0 - 0x0000)
struct W_WorldMarker_C_OnMouseEnter final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(W_WorldMarker_C_OnMouseEnter) == 0x000008, "Wrong alignment on W_WorldMarker_C_OnMouseEnter");
static_assert(sizeof(W_WorldMarker_C_OnMouseEnter) == 0x0000B0, "Wrong size on W_WorldMarker_C_OnMouseEnter");
static_assert(offsetof(W_WorldMarker_C_OnMouseEnter, MyGeometry) == 0x000000, "Member 'W_WorldMarker_C_OnMouseEnter::MyGeometry' has a wrong offset!");
static_assert(offsetof(W_WorldMarker_C_OnMouseEnter, MouseEvent) == 0x000038, "Member 'W_WorldMarker_C_OnMouseEnter::MouseEvent' has a wrong offset!");

// Function W_WorldMarker.W_WorldMarker_C.OnMouseLeave
// 0x0078 (0x0078 - 0x0000)
struct W_WorldMarker_C_OnMouseLeave final
{
public:
	struct FPointerEvent                          MouseEvent;                                        // 0x0000(0x0078)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(W_WorldMarker_C_OnMouseLeave) == 0x000008, "Wrong alignment on W_WorldMarker_C_OnMouseLeave");
static_assert(sizeof(W_WorldMarker_C_OnMouseLeave) == 0x000078, "Wrong size on W_WorldMarker_C_OnMouseLeave");
static_assert(offsetof(W_WorldMarker_C_OnMouseLeave, MouseEvent) == 0x000000, "Member 'W_WorldMarker_C_OnMouseLeave::MouseEvent' has a wrong offset!");

}

