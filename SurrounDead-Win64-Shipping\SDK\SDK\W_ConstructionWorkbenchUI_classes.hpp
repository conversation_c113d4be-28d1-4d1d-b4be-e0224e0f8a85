﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_ConstructionWorkbenchUI

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_ConstructionWorkbenchUI.W_ConstructionWorkbenchUI_C
// 0x00F8 (0x03B8 - 0x02C0)
class UW_ConstructionWorkbenchUI_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UButton*                                Button_81;                                         // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UButton*                                Button_Decrease;                                   // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UButton*                                Button_Increase;                                   // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UEquipmentSlotTitle_C*                  EquipmentSlotTitle;                                // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UEquipmentSlotTitle_C*                  EquipmentSlotTitle_1;                              // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        JSIContainer;                                      // 0x02F0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        JSIContainer_1;                                    // 0x02F8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                LoadingT;                                          // 0x0300(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UEquipmentSlotTitle_C*                  ModularTitle;                                      // 0x0308(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UEquipmentSlotTitle_C*                  StandardTitle;                                     // 0x0310(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_75;                                      // 0x0318(0x0008)(ExportObject, ZeroConstructor, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UThrobber*                              Throbber_567;                                      // 0x0320(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMap<class UJSI_Slot_C*, int32>               AddedItems;                                        // 0x0328(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	class UJSI_Slot_C*                            CurrentFaded;                                      // 0x0378(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UBP_JigMultiplayer_C*                   MPComponent;                                       // 0x0380(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	TArray<struct FGuid>                          RequestConsume;                                    // 0x0388(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	int32                                         CraftIndex;                                        // 0x0398(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39C[0x4];                                      // 0x039C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class ABuildable_ConstructionBench_C*         StationOwner;                                      // 0x03A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, NoDestructor, HasGetValueTypeHash)
	class UBP_JigMultiplayer_C*                   PlayerMPComp;                                      // 0x03A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CountIndex;                                        // 0x03B0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Add_Required_Items();
	void BindEvents();
	void BndEvt__BP_CraftingWidget_Button_Decrease_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature();
	void BndEvt__BP_CraftingWidget_Button_Increase_K2Node_ComponentBoundEvent_3_OnButtonClickedEvent__DelegateSignature();
	void BndEvt__Button_81_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature();
	void CheckIngredientsAvailability(bool* Proceed);
	void Construct();
	void Destruct();
	void EventOnMouseButtonDown(class UJSIContainer_C* Container, class UJSI_Slot_C* SlotRef, const struct FKey& Button);
	void ExecuteUbergraph_W_ConstructionWorkbenchUI(int32 EntryPoint);
	void ForceInitSpecialcontainer();
	void GetAllAttachments(TArray<class FName>* Attachments);
	void GetContainerByAttachmentType(const struct FGameplayTag& Type, class UJSIContainer_C** JigContainer, int32* ContainerIndex);
	void GetDropWidget(class UDropItemBackGwidget_C** DropWRef);
	void GetJSIContainerByPlayerSlots(const struct FGameplayTag& Slot_0, class UJSIContainer_C** Container, class UJSI_Slot_C** EquippedItem, bool* IsPending_);
	void GetListOfNonAddContainers(TArray<class UJSIContainer_C*>* Containers);
	void GetLootContent(class UUserWidget** Widget);
	class FText GetText();
	void GetValidReloadContainers(TArray<class UJSIContainer_C*>* Containers);
	void HandleCraftingButtons();
	void InitializeInventory();
	void JigSetLootContent(class UUserWidget* Widget, const class FText& Name_0);
	void JSICheckStatus();
	void JSIOnWeightUpdated(double NewWeight);
	void OnCreatedFromUtility();
	void OnInitialized();
	void OnModularMouseButtonDown(const struct FKey& Button);
	void OnStandardMouseButtonDown(const struct FKey& Button);
	void SetActionbarFollower(class UJSI_Slot_C* JigRef, bool* Return);
	void SetActorOwner(class AActor* ActorRef);
	void SetCraftableItems();
	void SetInspectorRef(class UBP_InspectorWindowWidget_C* Inspector);
	void SetItemReference(class UJSI_Slot_C* ItemRef);

	void GetListOfContainers(TArray<class UJSIContainer_C*>* Containers) const;

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_ConstructionWorkbenchUI_C">();
	}
	static class UW_ConstructionWorkbenchUI_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_ConstructionWorkbenchUI_C>();
	}
};
static_assert(alignof(UW_ConstructionWorkbenchUI_C) == 0x000008, "Wrong alignment on UW_ConstructionWorkbenchUI_C");
static_assert(sizeof(UW_ConstructionWorkbenchUI_C) == 0x0003B8, "Wrong size on UW_ConstructionWorkbenchUI_C");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, UberGraphFrame) == 0x0002C0, "Member 'UW_ConstructionWorkbenchUI_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, Button_81) == 0x0002C8, "Member 'UW_ConstructionWorkbenchUI_C::Button_81' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, Button_Decrease) == 0x0002D0, "Member 'UW_ConstructionWorkbenchUI_C::Button_Decrease' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, Button_Increase) == 0x0002D8, "Member 'UW_ConstructionWorkbenchUI_C::Button_Increase' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, EquipmentSlotTitle) == 0x0002E0, "Member 'UW_ConstructionWorkbenchUI_C::EquipmentSlotTitle' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, EquipmentSlotTitle_1) == 0x0002E8, "Member 'UW_ConstructionWorkbenchUI_C::EquipmentSlotTitle_1' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, JSIContainer) == 0x0002F0, "Member 'UW_ConstructionWorkbenchUI_C::JSIContainer' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, JSIContainer_1) == 0x0002F8, "Member 'UW_ConstructionWorkbenchUI_C::JSIContainer_1' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, LoadingT) == 0x000300, "Member 'UW_ConstructionWorkbenchUI_C::LoadingT' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, ModularTitle) == 0x000308, "Member 'UW_ConstructionWorkbenchUI_C::ModularTitle' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, StandardTitle) == 0x000310, "Member 'UW_ConstructionWorkbenchUI_C::StandardTitle' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, TextBlock_75) == 0x000318, "Member 'UW_ConstructionWorkbenchUI_C::TextBlock_75' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, Throbber_567) == 0x000320, "Member 'UW_ConstructionWorkbenchUI_C::Throbber_567' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, AddedItems) == 0x000328, "Member 'UW_ConstructionWorkbenchUI_C::AddedItems' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, CurrentFaded) == 0x000378, "Member 'UW_ConstructionWorkbenchUI_C::CurrentFaded' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, MPComponent) == 0x000380, "Member 'UW_ConstructionWorkbenchUI_C::MPComponent' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, RequestConsume) == 0x000388, "Member 'UW_ConstructionWorkbenchUI_C::RequestConsume' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, CraftIndex) == 0x000398, "Member 'UW_ConstructionWorkbenchUI_C::CraftIndex' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, StationOwner) == 0x0003A0, "Member 'UW_ConstructionWorkbenchUI_C::StationOwner' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, PlayerMPComp) == 0x0003A8, "Member 'UW_ConstructionWorkbenchUI_C::PlayerMPComp' has a wrong offset!");
static_assert(offsetof(UW_ConstructionWorkbenchUI_C, CountIndex) == 0x0003B0, "Member 'UW_ConstructionWorkbenchUI_C::CountIndex' has a wrong offset!");

}

