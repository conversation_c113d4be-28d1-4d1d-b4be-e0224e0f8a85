﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WBP_NarrativeHUDNotification

#include "Basic.hpp"

#include "WBP_NarrativeHUDNotification_classes.hpp"
#include "WBP_NarrativeHUDNotification_parameters.hpp"


namespace SDK
{

// Function WBP_NarrativeHUDNotification.WBP_NarrativeHUDNotification_C.Begin Expire
// (BlueprintCallable, BlueprintEvent)

void UWBP_NarrativeHUDNotification_C::Begin_Expire()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeHUDNotification_C", "Begin Expire");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WBP_NarrativeHUDNotification.WBP_NarrativeHUDNotification_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWBP_NarrativeHUDNotification_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeHUDNotification_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WBP_NarrativeHUDNotification.WBP_NarrativeHUDNotification_C.ExecuteUbergraph_WBP_NarrativeHUDNotification
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWBP_NarrativeHUDNotification_C::ExecuteUbergraph_WBP_NarrativeHUDNotification(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeHUDNotification_C", "ExecuteUbergraph_WBP_NarrativeHUDNotification");

	Params::WBP_NarrativeHUDNotification_C_ExecuteUbergraph_WBP_NarrativeHUDNotification Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WBP_NarrativeHUDNotification.WBP_NarrativeHUDNotification_C.Expire
// (BlueprintCallable, BlueprintEvent)

void UWBP_NarrativeHUDNotification_C::Expire()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WBP_NarrativeHUDNotification_C", "Expire");

	UObject::ProcessEvent(Func, nullptr);
}

}

