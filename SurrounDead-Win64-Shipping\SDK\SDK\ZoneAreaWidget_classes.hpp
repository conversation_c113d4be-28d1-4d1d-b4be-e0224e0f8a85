﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: ZoneAreaWidget

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass ZoneAreaWidget.ZoneAreaWidget_C
// 0x0030 (0x02F0 - 0x02C0)
class UZoneAreaWidget_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       FadeStop;                                          // 0x02C8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       FadeStart;                                         // 0x02D0(0x0008)(BlueprintVisible, BlueprintReadOn<PERSON>, ZeroConstructor, Transient, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UTextBlock*                             Area;                                              // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_182;                                         // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Title;                                             // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void Construct();
	void ExecuteUbergraph_ZoneAreaWidget(int32 EntryPoint);
	void FadeStartFinished();
	void SetName(const class FText& Name_0, class UTexture* Texture, const struct FLinearColor& Color, bool Entering_);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"ZoneAreaWidget_C">();
	}
	static class UZoneAreaWidget_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UZoneAreaWidget_C>();
	}
};
static_assert(alignof(UZoneAreaWidget_C) == 0x000008, "Wrong alignment on UZoneAreaWidget_C");
static_assert(sizeof(UZoneAreaWidget_C) == 0x0002F0, "Wrong size on UZoneAreaWidget_C");
static_assert(offsetof(UZoneAreaWidget_C, UberGraphFrame) == 0x0002C0, "Member 'UZoneAreaWidget_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UZoneAreaWidget_C, FadeStop) == 0x0002C8, "Member 'UZoneAreaWidget_C::FadeStop' has a wrong offset!");
static_assert(offsetof(UZoneAreaWidget_C, FadeStart) == 0x0002D0, "Member 'UZoneAreaWidget_C::FadeStart' has a wrong offset!");
static_assert(offsetof(UZoneAreaWidget_C, Area) == 0x0002D8, "Member 'UZoneAreaWidget_C::Area' has a wrong offset!");
static_assert(offsetof(UZoneAreaWidget_C, Image_182) == 0x0002E0, "Member 'UZoneAreaWidget_C::Image_182' has a wrong offset!");
static_assert(offsetof(UZoneAreaWidget_C, Title) == 0x0002E8, "Member 'UZoneAreaWidget_C::Title' has a wrong offset!");

}

