﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_PB_Raw

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"
#include "Slate_structs.hpp"


namespace SDK::Params
{

// Function WB_PB_Raw.WB_PB_Raw_C.ExecuteUbergraph_WB_PB_Raw
// 0x00A0 (0x00A0 - 0x0000)
struct WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_CustomEvent_Tiling_1;                       // 0x0004(0x0001)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushDrawType                           K2Node_CustomEvent_DrawAs;                         // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          K2Node_CustomEvent_BarFillType;                    // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseShader;                     // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_Tint;                           // 0x0008(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_InColor_2;                      // 0x0018(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              K2Node_CustomEvent_ImageSize;                      // 0x0028(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_InColor_1;                      // 0x0038(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UObject*                                K2Node_CustomEvent_Image;                          // 0x0048(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                K2Node_CustomEvent_FillImage;                      // 0x0050(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_IsMarquee;                      // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_59[0x7];                                       // 0x0059(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              K2Node_CustomEvent_FillImageSize;                  // 0x0060(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_InColor;                        // 0x0070(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_CustomEvent_Tiling;                         // 0x0080(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_81[0x7];                                       // 0x0081(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_InPercent;                      // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Margin;                         // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushDrawType                           K2Node_CustomEvent_Draw_As;                        // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw) == 0x000008, "Wrong alignment on WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw");
static_assert(sizeof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw) == 0x0000A0, "Wrong size on WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, EntryPoint) == 0x000000, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_Tiling_1) == 0x000004, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_Tiling_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_DrawAs) == 0x000005, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_DrawAs' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_BarFillType) == 0x000006, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_BarFillType' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_bUseShader) == 0x000007, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_bUseShader' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_Tint) == 0x000008, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_Tint' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_InColor_2) == 0x000018, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_InColor_2' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_ImageSize) == 0x000028, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_ImageSize' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_InColor_1) == 0x000038, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_InColor_1' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_Image) == 0x000048, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_Image' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_FillImage) == 0x000050, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_FillImage' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_IsMarquee) == 0x000058, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_IsMarquee' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_FillImageSize) == 0x000060, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_FillImageSize' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_InColor) == 0x000070, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_InColor' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_Tiling) == 0x000080, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_Tiling' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_InPercent) == 0x000088, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_InPercent' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_Margin) == 0x000090, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_Margin' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw, K2Node_CustomEvent_Draw_As) == 0x000098, "Member 'WB_PB_Raw_C_ExecuteUbergraph_WB_PB_Raw::K2Node_CustomEvent_Draw_As' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetBackgroundTint
// 0x0010 (0x0010 - 0x0000)
struct WB_PB_Raw_C_SetBackgroundTint final
{
public:
	struct FLinearColor                           InColor;                                           // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetBackgroundTint) == 0x000004, "Wrong alignment on WB_PB_Raw_C_SetBackgroundTint");
static_assert(sizeof(WB_PB_Raw_C_SetBackgroundTint) == 0x000010, "Wrong size on WB_PB_Raw_C_SetBackgroundTint");
static_assert(offsetof(WB_PB_Raw_C_SetBackgroundTint, InColor) == 0x000000, "Member 'WB_PB_Raw_C_SetBackgroundTint::InColor' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetBarFillType
// 0x0002 (0x0002 - 0x0000)
struct WB_PB_Raw_C_SetBarFillType final
{
public:
	EProgressBarFillType                          BarFillType;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseShader;                                        // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetBarFillType) == 0x000001, "Wrong alignment on WB_PB_Raw_C_SetBarFillType");
static_assert(sizeof(WB_PB_Raw_C_SetBarFillType) == 0x000002, "Wrong size on WB_PB_Raw_C_SetBarFillType");
static_assert(offsetof(WB_PB_Raw_C_SetBarFillType, BarFillType) == 0x000000, "Member 'WB_PB_Raw_C_SetBarFillType::BarFillType' has a wrong offset!");
static_assert(offsetof(WB_PB_Raw_C_SetBarFillType, bUseShader) == 0x000001, "Member 'WB_PB_Raw_C_SetBarFillType::bUseShader' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetColor
// 0x0010 (0x0010 - 0x0000)
struct WB_PB_Raw_C_SetColor final
{
public:
	struct FLinearColor                           InColor;                                           // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetColor) == 0x000004, "Wrong alignment on WB_PB_Raw_C_SetColor");
static_assert(sizeof(WB_PB_Raw_C_SetColor) == 0x000010, "Wrong size on WB_PB_Raw_C_SetColor");
static_assert(offsetof(WB_PB_Raw_C_SetColor, InColor) == 0x000000, "Member 'WB_PB_Raw_C_SetColor::InColor' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetFillColorAndOpacity
// 0x0010 (0x0010 - 0x0000)
struct WB_PB_Raw_C_SetFillColorAndOpacity final
{
public:
	struct FLinearColor                           InColor;                                           // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetFillColorAndOpacity) == 0x000004, "Wrong alignment on WB_PB_Raw_C_SetFillColorAndOpacity");
static_assert(sizeof(WB_PB_Raw_C_SetFillColorAndOpacity) == 0x000010, "Wrong size on WB_PB_Raw_C_SetFillColorAndOpacity");
static_assert(offsetof(WB_PB_Raw_C_SetFillColorAndOpacity, InColor) == 0x000000, "Member 'WB_PB_Raw_C_SetFillColorAndOpacity::InColor' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImage
// 0x0008 (0x0008 - 0x0000)
struct WB_PB_Raw_C_SetFillImage final
{
public:
	class UObject*                                FillImage;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetFillImage) == 0x000008, "Wrong alignment on WB_PB_Raw_C_SetFillImage");
static_assert(sizeof(WB_PB_Raw_C_SetFillImage) == 0x000008, "Wrong size on WB_PB_Raw_C_SetFillImage");
static_assert(offsetof(WB_PB_Raw_C_SetFillImage, FillImage) == 0x000000, "Member 'WB_PB_Raw_C_SetFillImage::FillImage' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImageDrawAs
// 0x0001 (0x0001 - 0x0000)
struct WB_PB_Raw_C_SetFillImageDrawAs final
{
public:
	ESlateBrushDrawType                           Draw_As;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetFillImageDrawAs) == 0x000001, "Wrong alignment on WB_PB_Raw_C_SetFillImageDrawAs");
static_assert(sizeof(WB_PB_Raw_C_SetFillImageDrawAs) == 0x000001, "Wrong size on WB_PB_Raw_C_SetFillImageDrawAs");
static_assert(offsetof(WB_PB_Raw_C_SetFillImageDrawAs, Draw_As) == 0x000000, "Member 'WB_PB_Raw_C_SetFillImageDrawAs::Draw_As' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImageMargin
// 0x0008 (0x0008 - 0x0000)
struct WB_PB_Raw_C_SetFillImageMargin final
{
public:
	double                                        Margin;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetFillImageMargin) == 0x000008, "Wrong alignment on WB_PB_Raw_C_SetFillImageMargin");
static_assert(sizeof(WB_PB_Raw_C_SetFillImageMargin) == 0x000008, "Wrong size on WB_PB_Raw_C_SetFillImageMargin");
static_assert(offsetof(WB_PB_Raw_C_SetFillImageMargin, Margin) == 0x000000, "Member 'WB_PB_Raw_C_SetFillImageMargin::Margin' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImageSize
// 0x0010 (0x0010 - 0x0000)
struct WB_PB_Raw_C_SetFillImageSize final
{
public:
	struct FVector2D                              FillImageSize;                                     // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetFillImageSize) == 0x000008, "Wrong alignment on WB_PB_Raw_C_SetFillImageSize");
static_assert(sizeof(WB_PB_Raw_C_SetFillImageSize) == 0x000010, "Wrong size on WB_PB_Raw_C_SetFillImageSize");
static_assert(offsetof(WB_PB_Raw_C_SetFillImageSize, FillImageSize) == 0x000000, "Member 'WB_PB_Raw_C_SetFillImageSize::FillImageSize' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetFillImageTiling
// 0x0001 (0x0001 - 0x0000)
struct WB_PB_Raw_C_SetFillImageTiling final
{
public:
	ESlateBrushTileType                           Tiling;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetFillImageTiling) == 0x000001, "Wrong alignment on WB_PB_Raw_C_SetFillImageTiling");
static_assert(sizeof(WB_PB_Raw_C_SetFillImageTiling) == 0x000001, "Wrong size on WB_PB_Raw_C_SetFillImageTiling");
static_assert(offsetof(WB_PB_Raw_C_SetFillImageTiling, Tiling) == 0x000000, "Member 'WB_PB_Raw_C_SetFillImageTiling::Tiling' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetMarquee
// 0x0001 (0x0001 - 0x0000)
struct WB_PB_Raw_C_SetMarquee final
{
public:
	bool                                          IsMarquee;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetMarquee) == 0x000001, "Wrong alignment on WB_PB_Raw_C_SetMarquee");
static_assert(sizeof(WB_PB_Raw_C_SetMarquee) == 0x000001, "Wrong size on WB_PB_Raw_C_SetMarquee");
static_assert(offsetof(WB_PB_Raw_C_SetMarquee, IsMarquee) == 0x000000, "Member 'WB_PB_Raw_C_SetMarquee::IsMarquee' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeDrawAs
// 0x0001 (0x0001 - 0x0000)
struct WB_PB_Raw_C_SetMarqueeDrawAs final
{
public:
	ESlateBrushDrawType                           DrawAs;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetMarqueeDrawAs) == 0x000001, "Wrong alignment on WB_PB_Raw_C_SetMarqueeDrawAs");
static_assert(sizeof(WB_PB_Raw_C_SetMarqueeDrawAs) == 0x000001, "Wrong size on WB_PB_Raw_C_SetMarqueeDrawAs");
static_assert(offsetof(WB_PB_Raw_C_SetMarqueeDrawAs, DrawAs) == 0x000000, "Member 'WB_PB_Raw_C_SetMarqueeDrawAs::DrawAs' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeImage
// 0x0008 (0x0008 - 0x0000)
struct WB_PB_Raw_C_SetMarqueeImage final
{
public:
	class UObject*                                Image;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetMarqueeImage) == 0x000008, "Wrong alignment on WB_PB_Raw_C_SetMarqueeImage");
static_assert(sizeof(WB_PB_Raw_C_SetMarqueeImage) == 0x000008, "Wrong size on WB_PB_Raw_C_SetMarqueeImage");
static_assert(offsetof(WB_PB_Raw_C_SetMarqueeImage, Image) == 0x000000, "Member 'WB_PB_Raw_C_SetMarqueeImage::Image' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeImageSize
// 0x0010 (0x0010 - 0x0000)
struct WB_PB_Raw_C_SetMarqueeImageSize final
{
public:
	struct FVector2D                              ImageSize;                                         // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetMarqueeImageSize) == 0x000008, "Wrong alignment on WB_PB_Raw_C_SetMarqueeImageSize");
static_assert(sizeof(WB_PB_Raw_C_SetMarqueeImageSize) == 0x000010, "Wrong size on WB_PB_Raw_C_SetMarqueeImageSize");
static_assert(offsetof(WB_PB_Raw_C_SetMarqueeImageSize, ImageSize) == 0x000000, "Member 'WB_PB_Raw_C_SetMarqueeImageSize::ImageSize' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeTiling
// 0x0001 (0x0001 - 0x0000)
struct WB_PB_Raw_C_SetMarqueeTiling final
{
public:
	ESlateBrushTileType                           Tiling;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetMarqueeTiling) == 0x000001, "Wrong alignment on WB_PB_Raw_C_SetMarqueeTiling");
static_assert(sizeof(WB_PB_Raw_C_SetMarqueeTiling) == 0x000001, "Wrong size on WB_PB_Raw_C_SetMarqueeTiling");
static_assert(offsetof(WB_PB_Raw_C_SetMarqueeTiling, Tiling) == 0x000000, "Member 'WB_PB_Raw_C_SetMarqueeTiling::Tiling' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetMarqueeTint
// 0x0010 (0x0010 - 0x0000)
struct WB_PB_Raw_C_SetMarqueeTint final
{
public:
	struct FLinearColor                           Tint;                                              // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetMarqueeTint) == 0x000004, "Wrong alignment on WB_PB_Raw_C_SetMarqueeTint");
static_assert(sizeof(WB_PB_Raw_C_SetMarqueeTint) == 0x000010, "Wrong size on WB_PB_Raw_C_SetMarqueeTint");
static_assert(offsetof(WB_PB_Raw_C_SetMarqueeTint, Tint) == 0x000000, "Member 'WB_PB_Raw_C_SetMarqueeTint::Tint' has a wrong offset!");

// Function WB_PB_Raw.WB_PB_Raw_C.SetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_PB_Raw_C_SetPercent final
{
public:
	double                                        InPercent;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_PB_Raw_C_SetPercent) == 0x000008, "Wrong alignment on WB_PB_Raw_C_SetPercent");
static_assert(sizeof(WB_PB_Raw_C_SetPercent) == 0x000008, "Wrong size on WB_PB_Raw_C_SetPercent");
static_assert(offsetof(WB_PB_Raw_C_SetPercent, InPercent) == 0x000000, "Member 'WB_PB_Raw_C_SetPercent::InPercent' has a wrong offset!");

}

