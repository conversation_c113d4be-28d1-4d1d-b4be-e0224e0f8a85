﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_LargeLootContainerUI

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass W_LargeLootContainerUI.W_LargeLootContainerUI_C
// 0x0030 (0x02F0 - 0x02C0)
class UW_LargeLootContainerUI_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UButton*                                Button_66;                                         // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             LootAllText;                                       // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UJSIContainer_C*                        MainContainer;                                     // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TArray<class UJSI_Slot_C*>                    Array_Of_Items;                                    // 0x02E0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)

public:
	void BndEvt__LargeContainer_Widget_Button_66_K2Node_ComponentBoundEvent_2_OnButtonClickedEvent__DelegateSignature();
	void ExecuteUbergraph_W_LargeLootContainerUI(int32 EntryPoint);
	void ForceInitSpecialcontainer();
	void GetAllAttachments(TArray<class FName>* Attachments);
	void GetContainerByAttachmentType(const struct FGameplayTag& Type, class UJSIContainer_C** JigContainer, int32* ContainerIndex);
	void GetDropWidget(class UDropItemBackGwidget_C** DropWRef);
	void GetJSIContainerByPlayerSlots(const struct FGameplayTag& Slot_0, class UJSIContainer_C** Container, class UJSI_Slot_C** EquippedItem, bool* IsPending_);
	void GetListOfNonAddContainers(TArray<class UJSIContainer_C*>* Containers);
	void GetLootContent(class UUserWidget** Widget);
	void GetValidReloadContainers(TArray<class UJSIContainer_C*>* Containers);
	void JigSetLootContent(class UUserWidget* Widget, const class FText& Name_0);
	void JSICheckStatus();
	void JSIOnWeightUpdated(double NewWeight);
	void OnCreatedFromUtility();
	void OnInitialized();
	void SetActionbarFollower(class UJSI_Slot_C* JigRef, bool* Return);
	void SetActorOwner(class AActor* ActorRef);
	void SetInspectorRef(class UBP_InspectorWindowWidget_C* Inspector);
	void SetItemReference(class UJSI_Slot_C* ItemRef);
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);

	void GetListOfContainers(TArray<class UJSIContainer_C*>* Containers) const;

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"W_LargeLootContainerUI_C">();
	}
	static class UW_LargeLootContainerUI_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UW_LargeLootContainerUI_C>();
	}
};
static_assert(alignof(UW_LargeLootContainerUI_C) == 0x000008, "Wrong alignment on UW_LargeLootContainerUI_C");
static_assert(sizeof(UW_LargeLootContainerUI_C) == 0x0002F0, "Wrong size on UW_LargeLootContainerUI_C");
static_assert(offsetof(UW_LargeLootContainerUI_C, UberGraphFrame) == 0x0002C0, "Member 'UW_LargeLootContainerUI_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UW_LargeLootContainerUI_C, Button_66) == 0x0002C8, "Member 'UW_LargeLootContainerUI_C::Button_66' has a wrong offset!");
static_assert(offsetof(UW_LargeLootContainerUI_C, LootAllText) == 0x0002D0, "Member 'UW_LargeLootContainerUI_C::LootAllText' has a wrong offset!");
static_assert(offsetof(UW_LargeLootContainerUI_C, MainContainer) == 0x0002D8, "Member 'UW_LargeLootContainerUI_C::MainContainer' has a wrong offset!");
static_assert(offsetof(UW_LargeLootContainerUI_C, Array_Of_Items) == 0x0002E0, "Member 'UW_LargeLootContainerUI_C::Array_Of_Items' has a wrong offset!");

}

