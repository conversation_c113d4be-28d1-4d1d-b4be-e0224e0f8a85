﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_PB_Linear_Base

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"
#include "Slate_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_PB_Linear_Base.WB_PB_Linear_Base_C
// 0x0080 (0x0340 - 0x02C0)
class UWB_PB_Linear_Base_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWB_BaseProgressBar_C*                  BasePB;                                            // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 DesignTimeGradient;                                // 0x02D0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWB_BaseProgressBar_C*                  GradientPB;                                        // 0x02D8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UOverlay*                               OV_DesignTimeGradient;                             // 0x02E0(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class URetainerBox*                           RB_Gradient;                                       // 0x02E8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	bool                                          bIsDesignTime;                                     // 0x02F0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2F1[0x7];                                      // 0x02F1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              Size;                                              // 0x02F8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           FillColor;                                         // 0x0308(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           GradientColor;                                     // 0x0318(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           ProgressChangeColor;                               // 0x0328(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsChanging;                                       // 0x0338(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void Construct();
	void ExecuteUbergraph_WB_PB_Linear_Base(int32 EntryPoint);
	struct FVector2D FindFillSize(class UObject* Object);
	void GetPercent(double* Percent);
	void Mirror_DesignTimeGradient(bool Mirror);
	void PreConstruct(bool IsDesignTime);
	void Reconstruct();
	void Rotate_DesignRetainerGradient(double Rotation);
	void Rotate_DesignTimeGradient(double Angle);
	void SetBarFillType(EProgressBarFillType FillType, bool bUseShader);
	void SetFillColor(const struct FLinearColor& InColor, double GradientPower, ESlateBrushTileType Tiling);
	void SetFillColorMask(class UObject* Value);
	void SetGradientMask(class UTexture2D* GradientTexture);
	void SetGradientPercent(double Value);
	void SetPercent(double InPercent);
	void SetSize(const struct FVector2D& Size_0);
	void SetUseGradient(bool UseGradient);
	void UpdateProgressChangeColor(const struct FLinearColor& NewColor, double InterpSpeed, bool IsChanging);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_PB_Linear_Base_C">();
	}
	static class UWB_PB_Linear_Base_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_PB_Linear_Base_C>();
	}
};
static_assert(alignof(UWB_PB_Linear_Base_C) == 0x000008, "Wrong alignment on UWB_PB_Linear_Base_C");
static_assert(sizeof(UWB_PB_Linear_Base_C) == 0x000340, "Wrong size on UWB_PB_Linear_Base_C");
static_assert(offsetof(UWB_PB_Linear_Base_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_PB_Linear_Base_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, BasePB) == 0x0002C8, "Member 'UWB_PB_Linear_Base_C::BasePB' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, DesignTimeGradient) == 0x0002D0, "Member 'UWB_PB_Linear_Base_C::DesignTimeGradient' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, GradientPB) == 0x0002D8, "Member 'UWB_PB_Linear_Base_C::GradientPB' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, OV_DesignTimeGradient) == 0x0002E0, "Member 'UWB_PB_Linear_Base_C::OV_DesignTimeGradient' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, RB_Gradient) == 0x0002E8, "Member 'UWB_PB_Linear_Base_C::RB_Gradient' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, bIsDesignTime) == 0x0002F0, "Member 'UWB_PB_Linear_Base_C::bIsDesignTime' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, Size) == 0x0002F8, "Member 'UWB_PB_Linear_Base_C::Size' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, FillColor) == 0x000308, "Member 'UWB_PB_Linear_Base_C::FillColor' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, GradientColor) == 0x000318, "Member 'UWB_PB_Linear_Base_C::GradientColor' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, ProgressChangeColor) == 0x000328, "Member 'UWB_PB_Linear_Base_C::ProgressChangeColor' has a wrong offset!");
static_assert(offsetof(UWB_PB_Linear_Base_C, bIsChanging) == 0x000338, "Member 'UWB_PB_Linear_Base_C::bIsChanging' has a wrong offset!");

}

