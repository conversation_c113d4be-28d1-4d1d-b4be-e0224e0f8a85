﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_PB_Raw

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"
#include "Slate_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WB_PB_Raw.WB_PB_Raw_C
// 0x0010 (0x02D0 - 0x02C0)
class UWB_PB_Raw_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWB_BaseProgressBar_C*                  WB_BaseProgressBar;                                // 0x02C8(0x0008)(BlueprintVisible, ExportObject, BlueprintReadOnly, ZeroConstructor, DisableEditOnInstance, InstancedReference, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WB_PB_Raw(int32 EntryPoint);
	void SetBackgroundTint(const struct FLinearColor& InColor);
	void SetBarFillType(EProgressBarFillType BarFillType, bool bUseShader);
	void SetColor(const struct FLinearColor& InColor);
	void SetFillColorAndOpacity(const struct FLinearColor& InColor);
	void SetFillImage(class UObject* FillImage);
	void SetFillImageDrawAs(ESlateBrushDrawType Draw_As);
	void SetFillImageMargin(double Margin);
	void SetFillImageSize(const struct FVector2D& FillImageSize);
	void SetFillImageTiling(ESlateBrushTileType Tiling);
	void SetMarquee(bool IsMarquee);
	void SetMarqueeDrawAs(ESlateBrushDrawType DrawAs);
	void SetMarqueeImage(class UObject* Image);
	void SetMarqueeImageSize(const struct FVector2D& ImageSize);
	void SetMarqueeTiling(ESlateBrushTileType Tiling);
	void SetMarqueeTint(const struct FLinearColor& Tint);
	void SetPercent(double InPercent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WB_PB_Raw_C">();
	}
	static class UWB_PB_Raw_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWB_PB_Raw_C>();
	}
};
static_assert(alignof(UWB_PB_Raw_C) == 0x000008, "Wrong alignment on UWB_PB_Raw_C");
static_assert(sizeof(UWB_PB_Raw_C) == 0x0002D0, "Wrong size on UWB_PB_Raw_C");
static_assert(offsetof(UWB_PB_Raw_C, UberGraphFrame) == 0x0002C0, "Member 'UWB_PB_Raw_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWB_PB_Raw_C, WB_BaseProgressBar) == 0x0002C8, "Member 'UWB_PB_Raw_C::WB_BaseProgressBar' has a wrong offset!");

}

