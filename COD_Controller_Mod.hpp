#pragma once

/*
* Call of Duty: Modern Warfare Controller Mod for SurrounDead
* Using the complete SDK for direct memory access and input injection
*/

#include "SurrounDead-Win64-Shipping/SDK/SDK.hpp"
#include <Windows.h>
#include <Xinput.h>
#include <thread>
#include <chrono>

#pragma comment(lib, "xinput.lib")

using namespace SDK;

class CODControllerMod {
private:
    // Game base address and key offsets
    uintptr_t game_base;
    
    // Player references
    ABP_PlayerCharacter_C* player_character;
    ABP_MasterPlayerController_C* player_controller;
    UPlayerInput* player_input;
    
    // Controller state
    XINPUT_STATE controller_state;
    XINPUT_STATE prev_controller_state;
    bool controller_connected;
    
    // Settings
    struct ControllerSettings {
        float look_sensitivity = 2.5f;
        float move_sensitivity = 1.0f;
        float ads_sensitivity_multiplier = 0.6f;
        float deadzone = 0.25f;
        float trigger_deadzone = 0.1f;
        bool invert_y = false;
        bool vibration_enabled = true;
        bool aim_assist_enabled = true;
        float aim_assist_strength = 0.6f;
    } settings;
    
    // State tracking
    bool is_aiming = false;
    bool is_sprinting = false;
    bool is_crouching = false;
    
    // Threading
    std::thread input_thread;
    bool running = false;

public:
    CODControllerMod();
    ~CODControllerMod();
    
    // Core functions
    bool Initialize();
    void Start();
    void Stop();
    void ProcessInput();
    
    // Input handling
    void HandleMovement();
    void HandleCamera();
    void HandleActions();
    void HandleTriggers();
    void HandleDPad();
    void HandleSpecialButtons();
    
    // Advanced features
    void ApplyAimAssist(float& aim_x, float& aim_y);
    void ApplySensitivityCurve(float& input_value);
    void HandleVibration();
    
    // Utility functions
    bool IsControllerConnected();
    void UpdatePlayerReferences();
    float ApplyDeadzone(float input, float deadzone);
    bool IsButtonPressed(WORD button);
    bool IsButtonJustPressed(WORD button);
    bool IsButtonJustReleased(WORD button);
    
    // Settings
    void LoadSettings();
    void SaveSettings();
    void SetSensitivity(float sensitivity) { settings.look_sensitivity = sensitivity; }
    void SetAimAssist(bool enabled, float strength = 0.6f) { 
        settings.aim_assist_enabled = enabled; 
        settings.aim_assist_strength = strength; 
    }
};

// Implementation
CODControllerMod::CODControllerMod() : 
    game_base(0), 
    player_character(nullptr), 
    player_controller(nullptr),
    player_input(nullptr),
    controller_connected(false),
    running(false) {
    
    ZeroMemory(&controller_state, sizeof(XINPUT_STATE));
    ZeroMemory(&prev_controller_state, sizeof(XINPUT_STATE));
}

CODControllerMod::~CODControllerMod() {
    Stop();
}

bool CODControllerMod::Initialize() {
    // Get game base address
    game_base = reinterpret_cast<uintptr_t>(GetModuleHandleA("SurrounDead.exe"));
    if (!game_base) {
        return false;
    }
    
    LoadSettings();
    return true;
}

void CODControllerMod::Start() {
    if (running) return;
    
    running = true;
    input_thread = std::thread([this]() {
        while (running) {
            ProcessInput();
            std::this_thread::sleep_for(std::chrono::milliseconds(1)); // ~1000 FPS polling
        }
    });
}

void CODControllerMod::Stop() {
    running = false;
    if (input_thread.joinable()) {
        input_thread.join();
    }
}

void CODControllerMod::ProcessInput() {
    // Store previous state
    prev_controller_state = controller_state;
    
    // Get current controller state
    DWORD result = XInputGetState(0, &controller_state);
    controller_connected = (result == ERROR_SUCCESS);
    
    if (!controller_connected) return;
    
    // Update player references
    UpdatePlayerReferences();
    if (!player_character || !player_controller) return;
    
    // Process all input types
    HandleMovement();
    HandleCamera();
    HandleActions();
    HandleTriggers();
    HandleDPad();
    HandleSpecialButtons();
    HandleVibration();
}

void CODControllerMod::HandleMovement() {
    // Left stick for movement
    float move_x = ApplyDeadzone(controller_state.Gamepad.sThumbLX / 32767.0f, settings.deadzone);
    float move_y = ApplyDeadzone(controller_state.Gamepad.sThumbLY / 32767.0f, settings.deadzone);
    
    // Apply sensitivity
    move_x *= settings.move_sensitivity;
    move_y *= settings.move_sensitivity;
    
    // Inject movement input directly
    if (player_input) {
        // Call the movement input functions directly
        if (abs(move_y) > 0.01f) {
            player_controller->AddMovementInput(player_character->GetActorForwardVector(), move_y);
        }
        if (abs(move_x) > 0.01f) {
            player_controller->AddMovementInput(player_character->GetActorRightVector(), move_x);
        }
    }
}

void CODControllerMod::HandleCamera() {
    // Right stick for camera
    float look_x = ApplyDeadzone(controller_state.Gamepad.sThumbRX / 32767.0f, settings.deadzone);
    float look_y = ApplyDeadzone(controller_state.Gamepad.sThumbRY / 32767.0f, settings.deadzone);
    
    if (abs(look_x) < 0.01f && abs(look_y) < 0.01f) return;
    
    // Apply sensitivity
    float sensitivity = settings.look_sensitivity;
    if (is_aiming) {
        sensitivity *= settings.ads_sensitivity_multiplier;
    }
    
    look_x *= sensitivity;
    look_y *= sensitivity;
    
    // Invert Y if enabled
    if (settings.invert_y) {
        look_y = -look_y;
    }
    
    // Apply aim assist
    if (settings.aim_assist_enabled) {
        ApplyAimAssist(look_x, look_y);
    }
    
    // Apply sensitivity curve
    ApplySensitivityCurve(look_x);
    ApplySensitivityCurve(look_y);
    
    // Inject camera input
    if (player_controller) {
        player_controller->AddYawInput(look_x);
        player_controller->AddPitchInput(-look_y); // Negative for correct direction
    }
}

void CODControllerMod::HandleActions() {
    // Face buttons (A/B/X/Y)
    if (IsButtonJustPressed(XINPUT_GAMEPAD_A)) {
        // A Button - Jump
        if (player_character) {
            player_character->Jump();
        }
    }
    
    if (IsButtonJustPressed(XINPUT_GAMEPAD_B)) {
        // B Button - Reload
        if (player_character) {
            // Call reload function - need to find the exact function name
            // player_character->Reload(); // This would be the actual call
        }
    }
    
    if (IsButtonJustPressed(XINPUT_GAMEPAD_X)) {
        // X Button - Use/Interact
        if (player_character) {
            // Call interact function
            // player_character->Interact(); // This would be the actual call
        }
    }
    
    if (IsButtonJustPressed(XINPUT_GAMEPAD_Y)) {
        // Y Button - Switch weapon
        if (player_character) {
            // Call weapon switch function
            // player_character->SwitchWeapon(); // This would be the actual call
        }
    }
}

void CODControllerMod::HandleTriggers() {
    // Left Trigger - Aim Down Sights
    float left_trigger = controller_state.Gamepad.bLeftTrigger / 255.0f;
    bool should_aim = left_trigger > settings.trigger_deadzone;
    
    if (should_aim != is_aiming) {
        is_aiming = should_aim;
        if (player_character) {
            if (is_aiming) {
                // Start ADS
                // player_character->StartADS();
            } else {
                // Stop ADS
                // player_character->StopADS();
            }
        }
    }
    
    // Right Trigger - Fire
    float right_trigger = controller_state.Gamepad.bRightTrigger / 255.0f;
    if (right_trigger > settings.trigger_deadzone) {
        if (player_character) {
            // Fire weapon
            // player_character->Fire();
        }
    }
}

void CODControllerMod::HandleDPad() {
    // D-Pad controls
    if (IsButtonJustPressed(XINPUT_GAMEPAD_DPAD_UP)) {
        // D-Pad Up - Prone
        if (player_character) {
            // player_character->Prone();
        }
    }
    
    if (IsButtonJustPressed(XINPUT_GAMEPAD_DPAD_DOWN)) {
        // D-Pad Down - Crouch
        is_crouching = !is_crouching;
        if (player_character) {
            if (is_crouching) {
                player_character->Crouch();
            } else {
                player_character->UnCrouch();
            }
        }
    }
    
    if (IsButtonJustPressed(XINPUT_GAMEPAD_DPAD_LEFT)) {
        // D-Pad Left - Last weapon
        if (player_character) {
            // player_character->SwitchToLastWeapon();
        }
    }
    
    if (IsButtonJustPressed(XINPUT_GAMEPAD_DPAD_RIGHT)) {
        // D-Pad Right - Inventory
        if (player_character) {
            // player_character->OpenInventory();
        }
    }
}

void CODControllerMod::HandleSpecialButtons() {
    // Left stick click - Sprint
    if (IsButtonPressed(XINPUT_GAMEPAD_LEFT_THUMB)) {
        if (!is_sprinting) {
            is_sprinting = true;
            if (player_character) {
                // player_character->StartSprint();
            }
        }
    } else {
        if (is_sprinting) {
            is_sprinting = false;
            if (player_character) {
                // player_character->StopSprint();
            }
        }
    }

    // Right stick click - Melee
    if (IsButtonJustPressed(XINPUT_GAMEPAD_RIGHT_THUMB)) {
        if (player_character) {
            // player_character->Melee();
        }
    }

    // Shoulder buttons
    if (IsButtonJustPressed(XINPUT_GAMEPAD_LEFT_SHOULDER)) {
        // LB - Grenade
        if (player_character) {
            // player_character->ThrowGrenade();
        }
    }

    if (IsButtonJustPressed(XINPUT_GAMEPAD_RIGHT_SHOULDER)) {
        // RB - Tactical equipment
        if (player_character) {
            // player_character->UseTactical();
        }
    }

    // Special buttons
    if (IsButtonJustPressed(XINPUT_GAMEPAD_BACK)) {
        // Back button - Scoreboard
        if (player_controller) {
            // player_controller->ShowScoreboard();
        }
    }

    if (IsButtonJustPressed(XINPUT_GAMEPAD_START)) {
        // Start button - Menu
        if (player_controller) {
            // player_controller->OpenMenu();
        }
    }
}

void CODControllerMod::ApplyAimAssist(float& aim_x, float& aim_y) {
    if (!settings.aim_assist_enabled || !is_aiming) return;

    // Simple aim assist implementation
    // In a real implementation, you'd check for nearby enemies and adjust aim
    float assist_strength = settings.aim_assist_strength;

    // Reduce sensitivity when aiming at targets (simplified)
    if (abs(aim_x) > 0.1f || abs(aim_y) > 0.1f) {
        aim_x *= (1.0f - assist_strength * 0.3f);
        aim_y *= (1.0f - assist_strength * 0.3f);
    }
}

void CODControllerMod::ApplySensitivityCurve(float& input_value) {
    // Apply a curve to make small movements more precise
    float abs_input = abs(input_value);
    float sign = (input_value >= 0) ? 1.0f : -1.0f;

    // Quadratic curve for more precision at low values
    float curved_input = abs_input * abs_input;
    input_value = curved_input * sign;
}

void CODControllerMod::HandleVibration() {
    if (!settings.vibration_enabled) return;

    XINPUT_VIBRATION vibration = {0};

    // Add vibration based on game events
    // This would need to be hooked into game events like taking damage, firing, etc.

    XInputSetState(0, &vibration);
}

void CODControllerMod::UpdatePlayerReferences() {
    if (!game_base) return;

    // Get the world context
    auto world = UWorld::GetWorld();
    if (!world) return;

    // Get the first player controller
    auto pc = world->GetFirstPlayerController();
    if (pc) {
        player_controller = static_cast<ABP_MasterPlayerController_C*>(pc);

        // Get the possessed pawn (player character)
        if (player_controller->GetPawn()) {
            player_character = static_cast<ABP_PlayerCharacter_C*>(player_controller->GetPawn());
        }

        // Get player input
        player_input = player_controller->PlayerInput;
    }
}

float CODControllerMod::ApplyDeadzone(float input, float deadzone) {
    if (abs(input) < deadzone) {
        return 0.0f;
    }

    // Scale the input to account for deadzone
    float sign = (input >= 0) ? 1.0f : -1.0f;
    float scaled = (abs(input) - deadzone) / (1.0f - deadzone);
    return scaled * sign;
}

bool CODControllerMod::IsButtonPressed(WORD button) {
    return (controller_state.Gamepad.wButtons & button) != 0;
}

bool CODControllerMod::IsButtonJustPressed(WORD button) {
    bool current = (controller_state.Gamepad.wButtons & button) != 0;
    bool previous = (prev_controller_state.Gamepad.wButtons & button) != 0;
    return current && !previous;
}

bool CODControllerMod::IsButtonJustReleased(WORD button) {
    bool current = (controller_state.Gamepad.wButtons & button) != 0;
    bool previous = (prev_controller_state.Gamepad.wButtons & button) != 0;
    return !current && previous;
}

bool CODControllerMod::IsControllerConnected() {
    return controller_connected;
}

void CODControllerMod::LoadSettings() {
    // Load settings from file or registry
    // For now, use defaults
}

void CODControllerMod::SaveSettings() {
    // Save settings to file or registry
}

// Global instance
CODControllerMod* g_controller_mod = nullptr;
