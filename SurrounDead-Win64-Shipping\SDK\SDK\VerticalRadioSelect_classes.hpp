﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: VerticalRadioSelect

#include "Basic.hpp"

#include "AutoSettings_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass VerticalRadioSelect.VerticalRadioSelect_C
// 0x0000 (0x0300 - 0x0300)
class UVerticalRadioSelect_C final : public URadioSelect
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"VerticalRadioSelect_C">();
	}
	static class UVerticalRadioSelect_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UVerticalRadioSelect_C>();
	}
};
static_assert(alignof(UVerticalRadioSelect_C) == 0x000008, "Wrong alignment on UVerticalRadioSelect_C");
static_assert(sizeof(UVerticalRadioSelect_C) == 0x000300, "Wrong size on UVerticalRadioSelect_C");

}

