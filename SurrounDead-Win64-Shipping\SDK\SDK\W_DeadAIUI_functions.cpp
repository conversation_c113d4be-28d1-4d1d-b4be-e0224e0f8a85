﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: W_DeadAIUI

#include "Basic.hpp"

#include "W_DeadAIUI_classes.hpp"
#include "W_DeadAIUI_parameters.hpp"


namespace SDK
{

// Function W_DeadAIUI.W_DeadAIUI_C.BndEvt__DeadZombieUI_Button_66_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UW_DeadAIUI_C::BndEvt__DeadZombieUI_Button_66_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "BndEvt__DeadZombieUI_Button_66_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_DeadAIUI.W_DeadAIUI_C.ExecuteUbergraph_W_DeadAIUI
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::ExecuteUbergraph_W_DeadAIUI(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "ExecuteUbergraph_W_DeadAIUI");

	Params::W_DeadAIUI_C_ExecuteUbergraph_W_DeadAIUI Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_DeadAIUI.W_DeadAIUI_C.ForceInitSpecialcontainer
// (Public, BlueprintCallable, BlueprintEvent)

void UW_DeadAIUI_C::ForceInitSpecialcontainer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "ForceInitSpecialcontainer");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_DeadAIUI.W_DeadAIUI_C.GetAllAttachments
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class FName>*                    Attachments                                            (Parm, OutParm)

void UW_DeadAIUI_C::GetAllAttachments(TArray<class FName>* Attachments)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "GetAllAttachments");

	Params::W_DeadAIUI_C_GetAllAttachments Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Attachments != nullptr)
		*Attachments = std::move(Parms.Attachments);
}


// Function W_DeadAIUI.W_DeadAIUI_C.GetContainerByAttachmentType
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGameplayTag&              Type                                                   (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
// class UJSIContainer_C**                 JigContainer                                           (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// int32*                                  ContainerIndex                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::GetContainerByAttachmentType(const struct FGameplayTag& Type, class UJSIContainer_C** JigContainer, int32* ContainerIndex)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "GetContainerByAttachmentType");

	Params::W_DeadAIUI_C_GetContainerByAttachmentType Parms{};

	Parms.Type = std::move(Type);

	UObject::ProcessEvent(Func, &Parms);

	if (JigContainer != nullptr)
		*JigContainer = Parms.JigContainer;

	if (ContainerIndex != nullptr)
		*ContainerIndex = Parms.ContainerIndex;
}


// Function W_DeadAIUI.W_DeadAIUI_C.GetDropWidget
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UDropItemBackGwidget_C**          DropWRef                                               (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::GetDropWidget(class UDropItemBackGwidget_C** DropWRef)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "GetDropWidget");

	Params::W_DeadAIUI_C_GetDropWidget Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (DropWRef != nullptr)
		*DropWRef = Parms.DropWRef;
}


// Function W_DeadAIUI.W_DeadAIUI_C.GetJSIContainerByPlayerSlots
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGameplayTag&              Slot_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor, HasGetValueTypeHash)
// class UJSIContainer_C**                 Container                                              (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// class UJSI_Slot_C**                     EquippedItem                                           (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// bool*                                   IsPending_                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::GetJSIContainerByPlayerSlots(const struct FGameplayTag& Slot_0, class UJSIContainer_C** Container, class UJSI_Slot_C** EquippedItem, bool* IsPending_)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "GetJSIContainerByPlayerSlots");

	Params::W_DeadAIUI_C_GetJSIContainerByPlayerSlots Parms{};

	Parms.Slot_0 = std::move(Slot_0);

	UObject::ProcessEvent(Func, &Parms);

	if (Container != nullptr)
		*Container = Parms.Container;

	if (EquippedItem != nullptr)
		*EquippedItem = Parms.EquippedItem;

	if (IsPending_ != nullptr)
		*IsPending_ = Parms.IsPending_;
}


// Function W_DeadAIUI.W_DeadAIUI_C.GetListOfNonAddContainers
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class UJSIContainer_C*>*         Containers                                             (Parm, OutParm, ContainsInstancedReference)

void UW_DeadAIUI_C::GetListOfNonAddContainers(TArray<class UJSIContainer_C*>* Containers)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "GetListOfNonAddContainers");

	Params::W_DeadAIUI_C_GetListOfNonAddContainers Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Containers != nullptr)
		*Containers = std::move(Parms.Containers);
}


// Function W_DeadAIUI.W_DeadAIUI_C.GetLootContent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUserWidget**                     Widget                                                 (Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::GetLootContent(class UUserWidget** Widget)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "GetLootContent");

	Params::W_DeadAIUI_C_GetLootContent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Widget != nullptr)
		*Widget = Parms.Widget;
}


// Function W_DeadAIUI.W_DeadAIUI_C.GetValidReloadContainers
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<class UJSIContainer_C*>*         Containers                                             (Parm, OutParm, ContainsInstancedReference)

void UW_DeadAIUI_C::GetValidReloadContainers(TArray<class UJSIContainer_C*>* Containers)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "GetValidReloadContainers");

	Params::W_DeadAIUI_C_GetValidReloadContainers Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Containers != nullptr)
		*Containers = std::move(Parms.Containers);
}


// Function W_DeadAIUI.W_DeadAIUI_C.JigSetLootContent
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UUserWidget*                      Widget                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// const class FText&                      Name_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm)

void UW_DeadAIUI_C::JigSetLootContent(class UUserWidget* Widget, const class FText& Name_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "JigSetLootContent");

	Params::W_DeadAIUI_C_JigSetLootContent Parms{};

	Parms.Widget = Widget;
	Parms.Name_0 = std::move(Name_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_DeadAIUI.W_DeadAIUI_C.JSICheckStatus
// (Public, BlueprintCallable, BlueprintEvent)

void UW_DeadAIUI_C::JSICheckStatus()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "JSICheckStatus");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_DeadAIUI.W_DeadAIUI_C.JSIOnWeightUpdated
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  NewWeight                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::JSIOnWeightUpdated(double NewWeight)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "JSIOnWeightUpdated");

	Params::W_DeadAIUI_C_JSIOnWeightUpdated Parms{};

	Parms.NewWeight = NewWeight;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_DeadAIUI.W_DeadAIUI_C.OnCreatedFromUtility
// (Public, BlueprintCallable, BlueprintEvent)

void UW_DeadAIUI_C::OnCreatedFromUtility()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "OnCreatedFromUtility");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_DeadAIUI.W_DeadAIUI_C.OnInitialized
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UW_DeadAIUI_C::OnInitialized()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "OnInitialized");

	UObject::ProcessEvent(Func, nullptr);
}


// Function W_DeadAIUI.W_DeadAIUI_C.SetActionbarFollower
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSI_Slot_C*                      JigRef                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
// bool*                                   Return                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::SetActionbarFollower(class UJSI_Slot_C* JigRef, bool* Return)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "SetActionbarFollower");

	Params::W_DeadAIUI_C_SetActionbarFollower Parms{};

	Parms.JigRef = JigRef;

	UObject::ProcessEvent(Func, &Parms);

	if (Return != nullptr)
		*Return = Parms.Return;
}


// Function W_DeadAIUI.W_DeadAIUI_C.SetActorOwner
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AActor*                           ActorRef                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::SetActorOwner(class AActor* ActorRef)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "SetActorOwner");

	Params::W_DeadAIUI_C_SetActorOwner Parms{};

	Parms.ActorRef = ActorRef;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_DeadAIUI.W_DeadAIUI_C.SetInspectorRef
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UBP_InspectorWindowWidget_C*      Inspector                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::SetInspectorRef(class UBP_InspectorWindowWidget_C* Inspector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "SetInspectorRef");

	Params::W_DeadAIUI_C_SetInspectorRef Parms{};

	Parms.Inspector = Inspector;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_DeadAIUI.W_DeadAIUI_C.SetItemReference
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UJSI_Slot_C*                      ItemRef                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::SetItemReference(class UJSI_Slot_C* ItemRef)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "SetItemReference");

	Params::W_DeadAIUI_C_SetItemReference Parms{};

	Parms.ItemRef = ItemRef;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_DeadAIUI.W_DeadAIUI_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UW_DeadAIUI_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "Tick");

	Params::W_DeadAIUI_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function W_DeadAIUI.W_DeadAIUI_C.GetListOfContainers
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, Const)
// Parameters:
// TArray<class UJSIContainer_C*>*         Containers                                             (Parm, OutParm, ContainsInstancedReference)

void UW_DeadAIUI_C::GetListOfContainers(TArray<class UJSIContainer_C*>* Containers) const
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("W_DeadAIUI_C", "GetListOfContainers");

	Params::W_DeadAIUI_C_GetListOfContainers Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Containers != nullptr)
		*Containers = std::move(Parms.Containers);
}

}

