﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: UDS_Startup_Interface

#include "Basic.hpp"

#include "UDS_Startup_Interface_classes.hpp"
#include "UDS_Startup_Interface_parameters.hpp"


namespace SDK
{

// Function UDS_Startup_Interface.UDS_Startup_Interface_C.UDS Ending Play
// (Public, BlueprintCallable, BlueprintEvent)

void IUDS_Startup_Interface_C::UDS_Ending_Play()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = AsUObject()->Class->GetFunction("UDS_Startup_Interface_C", "UDS Ending Play");

	AsUObject()->ProcessEvent(Func, nullptr);
}


// Function UDS_Startup_Interface.UDS_Startup_Interface_C.UDS Starting Up
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class AUltra_Dynamic_Sky_C*             UDS                                                    (BlueprintVisible, BlueprintR<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void IUDS_Startup_Interface_C::UDS_Starting_Up(class AUltra_Dynamic_Sky_C* UDS)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = AsUObject()->Class->GetFunction("UDS_Startup_Interface_C", "UDS Starting Up");

	Params::UDS_Startup_Interface_C_UDS_Starting_Up Parms{};

	Parms.UDS = UDS;

	AsUObject()->ProcessEvent(Func, &Parms);
}

}

