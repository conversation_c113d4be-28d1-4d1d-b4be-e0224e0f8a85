﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_BaseProgressBar

#include "Basic.hpp"

#include "Slate_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.ExecuteUbergraph_WB_BaseProgressBar
// 0x0470 (0x0470 - 0x0000)
struct WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_2;                               // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_3;                               // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_4;                               // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x3];                                       // 0x0019(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_5;                               // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_6;                               // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_7;                               // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          K2Node_CustomEvent_BarFillType;                    // 0x0034(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseShader;                     // 0x0035(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          Temp_byte_Variable;                                // 0x0036(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_37[0x1];                                       // 0x0037(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar;     // 0x0038(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_InPercent;                      // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_1;   // 0x0048(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_InColor;                        // 0x0050(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_CustomEvent_Tiling_1;                       // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_2;   // 0x0068(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FProgressBarStyle                      CallFunc_SetFillImageTiling_Output_Get;            // 0x0070(0x0290)()
	struct FVector2D                              K2Node_CustomEvent_FillImageSize;                  // 0x0300(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_3;   // 0x0310(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UObject*                                K2Node_CustomEvent_FillImage;                      // 0x0318(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_4;   // 0x0320(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_5;   // 0x0328(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_InbIsMarquee;                   // 0x0330(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_331[0x7];                                      // 0x0331(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UObject*                                K2Node_CustomEvent_Image;                          // 0x0338(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_6;   // 0x0340(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_8;                               // 0x0348(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34C[0x4];                                      // 0x034C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              K2Node_CustomEvent_Image_Size;                     // 0x0350(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_7;   // 0x0360(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_Color;                          // 0x0368(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_8;   // 0x0378(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	ESlateBrushDrawType                           K2Node_CustomEvent_Draw_As_1;                      // 0x0380(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_381[0x7];                                      // 0x0381(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_9;   // 0x0388(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_CustomEvent_Tiling;                         // 0x0390(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_391[0x7];                                      // 0x0391(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_10;  // 0x0398(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_9;                               // 0x03A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_Tint;                           // 0x03A4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3B4[0x4];                                      // 0x03B4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_11;  // 0x03B8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x03C0(0x0014)()
	uint8                                         Pad_3D4[0x4];                                      // 0x03D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Margin;                         // 0x03D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_12;  // 0x03E0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	ESlateBrushDrawType                           K2Node_CustomEvent_Draw_As;                        // 0x03E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3E9[0x7];                                      // 0x03E9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_13;  // 0x03F0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_10;                              // 0x03F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_11;                              // 0x03FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          Temp_byte_Variable_1;                              // 0x0400(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_401[0x3];                                      // 0x0401(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_12;                              // 0x0404(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_13;                              // 0x0408(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_14;                              // 0x040C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_15;                              // 0x0410(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_16;                              // 0x0414(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0418(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_419[0x3];                                      // 0x0419(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default;                             // 0x041C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_17;                              // 0x0420(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_424[0x4];                                      // 0x0424(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UProgressBar*>                   K2Node_MakeArray_Array;                            // 0x0428(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UProgressBar*                           CallFunc_Array_Get_Item;                           // 0x0438(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0440(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0444(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_445[0x3];                                      // 0x0445(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar_14;  // 0x0448(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_2;                              // 0x0450(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_451[0x3];                                      // 0x0451(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Select_Default_1;                           // 0x0454(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_18;                              // 0x0458(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Select_Default_2;                           // 0x045C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Select_Default_3;                           // 0x0460(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Select_Default_4;                           // 0x0464(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetPercent_InPercent_ImplicitCast;        // 0x0468(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar) == 0x000010, "Wrong alignment on WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar");
static_assert(sizeof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar) == 0x000470, "Wrong size on WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, EntryPoint) == 0x000000, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable) == 0x000004, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_1) == 0x000008, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_2) == 0x00000C, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_3) == 0x000010, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_4) == 0x000014, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_bool_Variable) == 0x000018, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_5) == 0x00001C, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_6) == 0x000020, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Loop_Counter_Variable) == 0x000024, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_Add_IntInt_ReturnValue) == 0x000028, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Array_Index_Variable) == 0x00002C, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_7) == 0x000030, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_7' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_BarFillType) == 0x000034, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_BarFillType' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_bUseShader) == 0x000035, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_bUseShader' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_byte_Variable) == 0x000036, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar) == 0x000038, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_InPercent) == 0x000040, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_InPercent' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_1) == 0x000048, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_1' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_InColor) == 0x000050, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_InColor' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_Tiling_1) == 0x000060, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_Tiling_1' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_2) == 0x000068, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_2' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_SetFillImageTiling_Output_Get) == 0x000070, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_SetFillImageTiling_Output_Get' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_FillImageSize) == 0x000300, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_FillImageSize' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_3) == 0x000310, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_3' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_FillImage) == 0x000318, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_FillImage' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_4) == 0x000320, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_4' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_5) == 0x000328, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_5' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_InbIsMarquee) == 0x000330, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_InbIsMarquee' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_Image) == 0x000338, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_Image' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_6) == 0x000340, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_6' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_8) == 0x000348, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_8' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_Image_Size) == 0x000350, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_Image_Size' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_7) == 0x000360, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_7' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_Color) == 0x000368, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_Color' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_8) == 0x000378, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_8' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_Draw_As_1) == 0x000380, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_Draw_As_1' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_9) == 0x000388, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_9' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_Tiling) == 0x000390, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_Tiling' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_10) == 0x000398, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_10' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_9) == 0x0003A0, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_9' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_Tint) == 0x0003A4, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_Tint' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_11) == 0x0003B8, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_11' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_MakeStruct_SlateColor) == 0x0003C0, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_Margin) == 0x0003D8, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_Margin' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_12) == 0x0003E0, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_12' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_CustomEvent_Draw_As) == 0x0003E8, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_CustomEvent_Draw_As' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_13) == 0x0003F0, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_13' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_10) == 0x0003F8, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_10' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_11) == 0x0003FC, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_11' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_byte_Variable_1) == 0x000400, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_12) == 0x000404, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_12' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_13) == 0x000408, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_13' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_14) == 0x00040C, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_14' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_15) == 0x000410, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_15' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_16) == 0x000414, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_16' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_bool_Variable_1) == 0x000418, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_Select_Default) == 0x00041C, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_17) == 0x000420, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_17' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_MakeArray_Array) == 0x000428, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_Array_Get_Item) == 0x000438, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_Array_Length_ReturnValue) == 0x000440, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_Less_IntInt_ReturnValue) == 0x000444, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_GetCurrentProgressBar_AsProgress_Bar_14) == 0x000448, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_GetCurrentProgressBar_AsProgress_Bar_14' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_bool_Variable_2) == 0x000450, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_Select_Default_1) == 0x000454, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, Temp_int_Variable_18) == 0x000458, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::Temp_int_Variable_18' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_Select_Default_2) == 0x00045C, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_Select_Default_2' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_Select_Default_3) == 0x000460, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_Select_Default_3' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, K2Node_Select_Default_4) == 0x000464, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::K2Node_Select_Default_4' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar, CallFunc_SetPercent_InPercent_ImplicitCast) == 0x000468, "Member 'WB_BaseProgressBar_C_ExecuteUbergraph_WB_BaseProgressBar::CallFunc_SetPercent_InPercent_ImplicitCast' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.GetCurrentProgressBar
// 0x0028 (0x0028 - 0x0000)
struct WB_BaseProgressBar_C_GetCurrentProgressBar final
{
public:
	class UProgressBar*                           AsProgress_Bar;                                    // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetActiveWidgetIndex_ReturnValue;         // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C[0x4];                                        // 0x000C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0010(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UProgressBar*                           K2Node_DynamicCast_AsProgress_Bar;                 // 0x0018(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_GetCurrentProgressBar) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_GetCurrentProgressBar");
static_assert(sizeof(WB_BaseProgressBar_C_GetCurrentProgressBar) == 0x000028, "Wrong size on WB_BaseProgressBar_C_GetCurrentProgressBar");
static_assert(offsetof(WB_BaseProgressBar_C_GetCurrentProgressBar, AsProgress_Bar) == 0x000000, "Member 'WB_BaseProgressBar_C_GetCurrentProgressBar::AsProgress_Bar' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_GetCurrentProgressBar, CallFunc_GetActiveWidgetIndex_ReturnValue) == 0x000008, "Member 'WB_BaseProgressBar_C_GetCurrentProgressBar::CallFunc_GetActiveWidgetIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_GetCurrentProgressBar, CallFunc_GetChildAt_ReturnValue) == 0x000010, "Member 'WB_BaseProgressBar_C_GetCurrentProgressBar::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_GetCurrentProgressBar, K2Node_DynamicCast_AsProgress_Bar) == 0x000018, "Member 'WB_BaseProgressBar_C_GetCurrentProgressBar::K2Node_DynamicCast_AsProgress_Bar' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_GetCurrentProgressBar, K2Node_DynamicCast_bSuccess) == 0x000020, "Member 'WB_BaseProgressBar_C_GetCurrentProgressBar::K2Node_DynamicCast_bSuccess' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.GetPercent
// 0x0028 (0x0028 - 0x0000)
struct WB_BaseProgressBar_C_GetPercent final
{
public:
	double                                        Percent_0;                                         // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UProgressBar*                           CallFunc_GetCurrentProgressBar_AsProgress_Bar;     // 0x0010(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Option_0_ImplicitCast;               // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_GetPercent) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_GetPercent");
static_assert(sizeof(WB_BaseProgressBar_C_GetPercent) == 0x000028, "Wrong size on WB_BaseProgressBar_C_GetPercent");
static_assert(offsetof(WB_BaseProgressBar_C_GetPercent, Percent_0) == 0x000000, "Member 'WB_BaseProgressBar_C_GetPercent::Percent_0' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_GetPercent, Temp_bool_Variable) == 0x000008, "Member 'WB_BaseProgressBar_C_GetPercent::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_GetPercent, CallFunc_GetCurrentProgressBar_AsProgress_Bar) == 0x000010, "Member 'WB_BaseProgressBar_C_GetPercent::CallFunc_GetCurrentProgressBar_AsProgress_Bar' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_GetPercent, K2Node_Select_Default) == 0x000018, "Member 'WB_BaseProgressBar_C_GetPercent::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_GetPercent, K2Node_Select_Option_0_ImplicitCast) == 0x000020, "Member 'WB_BaseProgressBar_C_GetPercent::K2Node_Select_Option_0_ImplicitCast' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetBackgroundTint
// 0x0010 (0x0010 - 0x0000)
struct WB_BaseProgressBar_C_SetBackgroundTint final
{
public:
	struct FLinearColor                           Tint;                                              // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetBackgroundTint) == 0x000004, "Wrong alignment on WB_BaseProgressBar_C_SetBackgroundTint");
static_assert(sizeof(WB_BaseProgressBar_C_SetBackgroundTint) == 0x000010, "Wrong size on WB_BaseProgressBar_C_SetBackgroundTint");
static_assert(offsetof(WB_BaseProgressBar_C_SetBackgroundTint, Tint) == 0x000000, "Member 'WB_BaseProgressBar_C_SetBackgroundTint::Tint' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetBarFillType
// 0x0002 (0x0002 - 0x0000)
struct WB_BaseProgressBar_C_SetBarFillType final
{
public:
	EProgressBarFillType                          BarFillType;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseShader_0;                                      // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetBarFillType) == 0x000001, "Wrong alignment on WB_BaseProgressBar_C_SetBarFillType");
static_assert(sizeof(WB_BaseProgressBar_C_SetBarFillType) == 0x000002, "Wrong size on WB_BaseProgressBar_C_SetBarFillType");
static_assert(offsetof(WB_BaseProgressBar_C_SetBarFillType, BarFillType) == 0x000000, "Member 'WB_BaseProgressBar_C_SetBarFillType::BarFillType' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetBarFillType, bUseShader_0) == 0x000001, "Member 'WB_BaseProgressBar_C_SetBarFillType::bUseShader_0' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillColorAndOpacity
// 0x0010 (0x0010 - 0x0000)
struct WB_BaseProgressBar_C_SetFillColorAndOpacity final
{
public:
	struct FLinearColor                           InColor;                                           // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetFillColorAndOpacity) == 0x000004, "Wrong alignment on WB_BaseProgressBar_C_SetFillColorAndOpacity");
static_assert(sizeof(WB_BaseProgressBar_C_SetFillColorAndOpacity) == 0x000010, "Wrong size on WB_BaseProgressBar_C_SetFillColorAndOpacity");
static_assert(offsetof(WB_BaseProgressBar_C_SetFillColorAndOpacity, InColor) == 0x000000, "Member 'WB_BaseProgressBar_C_SetFillColorAndOpacity::InColor' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImage
// 0x0008 (0x0008 - 0x0000)
struct WB_BaseProgressBar_C_SetFillImage final
{
public:
	class UObject*                                FillImage;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetFillImage) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetFillImage");
static_assert(sizeof(WB_BaseProgressBar_C_SetFillImage) == 0x000008, "Wrong size on WB_BaseProgressBar_C_SetFillImage");
static_assert(offsetof(WB_BaseProgressBar_C_SetFillImage, FillImage) == 0x000000, "Member 'WB_BaseProgressBar_C_SetFillImage::FillImage' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImageDrawAs
// 0x0001 (0x0001 - 0x0000)
struct WB_BaseProgressBar_C_SetFillImageDrawAs final
{
public:
	ESlateBrushDrawType                           Draw_As;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetFillImageDrawAs) == 0x000001, "Wrong alignment on WB_BaseProgressBar_C_SetFillImageDrawAs");
static_assert(sizeof(WB_BaseProgressBar_C_SetFillImageDrawAs) == 0x000001, "Wrong size on WB_BaseProgressBar_C_SetFillImageDrawAs");
static_assert(offsetof(WB_BaseProgressBar_C_SetFillImageDrawAs, Draw_As) == 0x000000, "Member 'WB_BaseProgressBar_C_SetFillImageDrawAs::Draw_As' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImageMargin
// 0x0008 (0x0008 - 0x0000)
struct WB_BaseProgressBar_C_SetFillImageMargin final
{
public:
	double                                        Margin;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetFillImageMargin) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetFillImageMargin");
static_assert(sizeof(WB_BaseProgressBar_C_SetFillImageMargin) == 0x000008, "Wrong size on WB_BaseProgressBar_C_SetFillImageMargin");
static_assert(offsetof(WB_BaseProgressBar_C_SetFillImageMargin, Margin) == 0x000000, "Member 'WB_BaseProgressBar_C_SetFillImageMargin::Margin' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImageSize
// 0x0010 (0x0010 - 0x0000)
struct WB_BaseProgressBar_C_SetFillImageSize final
{
public:
	struct FVector2D                              FillImageSize;                                     // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetFillImageSize) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetFillImageSize");
static_assert(sizeof(WB_BaseProgressBar_C_SetFillImageSize) == 0x000010, "Wrong size on WB_BaseProgressBar_C_SetFillImageSize");
static_assert(offsetof(WB_BaseProgressBar_C_SetFillImageSize, FillImageSize) == 0x000000, "Member 'WB_BaseProgressBar_C_SetFillImageSize::FillImageSize' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetFillImageTiling
// 0x0001 (0x0001 - 0x0000)
struct WB_BaseProgressBar_C_SetFillImageTiling final
{
public:
	ESlateBrushTileType                           Tiling;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetFillImageTiling) == 0x000001, "Wrong alignment on WB_BaseProgressBar_C_SetFillImageTiling");
static_assert(sizeof(WB_BaseProgressBar_C_SetFillImageTiling) == 0x000001, "Wrong size on WB_BaseProgressBar_C_SetFillImageTiling");
static_assert(offsetof(WB_BaseProgressBar_C_SetFillImageTiling, Tiling) == 0x000000, "Member 'WB_BaseProgressBar_C_SetFillImageTiling::Tiling' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetImageBlendMask
// 0x0020 (0x0020 - 0x0000)
struct WB_BaseProgressBar_C_SetImageBlendMask final
{
public:
	class UObject*                                BlendMask;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture*                               Mask;                                              // 0x0008(0x0008)(Edit, BlueprintVisible, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture*                               K2Node_DynamicCast_AsTexture;                      // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetImageBlendMask) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetImageBlendMask");
static_assert(sizeof(WB_BaseProgressBar_C_SetImageBlendMask) == 0x000020, "Wrong size on WB_BaseProgressBar_C_SetImageBlendMask");
static_assert(offsetof(WB_BaseProgressBar_C_SetImageBlendMask, BlendMask) == 0x000000, "Member 'WB_BaseProgressBar_C_SetImageBlendMask::BlendMask' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetImageBlendMask, Mask) == 0x000008, "Member 'WB_BaseProgressBar_C_SetImageBlendMask::Mask' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetImageBlendMask, K2Node_DynamicCast_AsTexture) == 0x000010, "Member 'WB_BaseProgressBar_C_SetImageBlendMask::K2Node_DynamicCast_AsTexture' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetImageBlendMask, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'WB_BaseProgressBar_C_SetImageBlendMask::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetImageBlendMask, K2Node_SwitchEnum_CmpSuccess) == 0x000019, "Member 'WB_BaseProgressBar_C_SetImageBlendMask::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetImagePercent
// 0x0010 (0x0010 - 0x0000)
struct WB_BaseProgressBar_C_SetImagePercent final
{
public:
	double                                        Percent_0;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetImagePercent) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetImagePercent");
static_assert(sizeof(WB_BaseProgressBar_C_SetImagePercent) == 0x000010, "Wrong size on WB_BaseProgressBar_C_SetImagePercent");
static_assert(offsetof(WB_BaseProgressBar_C_SetImagePercent, Percent_0) == 0x000000, "Member 'WB_BaseProgressBar_C_SetImagePercent::Percent_0' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetImagePercent, K2Node_SwitchEnum_CmpSuccess) == 0x000008, "Member 'WB_BaseProgressBar_C_SetImagePercent::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetImgsEnabled
// 0x0001 (0x0001 - 0x0000)
struct WB_BaseProgressBar_C_SetImgsEnabled final
{
public:
	bool                                          bInIsEnabled;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetImgsEnabled) == 0x000001, "Wrong alignment on WB_BaseProgressBar_C_SetImgsEnabled");
static_assert(sizeof(WB_BaseProgressBar_C_SetImgsEnabled) == 0x000001, "Wrong size on WB_BaseProgressBar_C_SetImgsEnabled");
static_assert(offsetof(WB_BaseProgressBar_C_SetImgsEnabled, bInIsEnabled) == 0x000000, "Member 'WB_BaseProgressBar_C_SetImgsEnabled::bInIsEnabled' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetInstanceBlendMask
// 0x0010 (0x0010 - 0x0000)
struct WB_BaseProgressBar_C_SetInstanceBlendMask final
{
public:
	class UImage*                                 Img;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class UTexture*                               BlendMask;                                         // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetInstanceBlendMask) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetInstanceBlendMask");
static_assert(sizeof(WB_BaseProgressBar_C_SetInstanceBlendMask) == 0x000010, "Wrong size on WB_BaseProgressBar_C_SetInstanceBlendMask");
static_assert(offsetof(WB_BaseProgressBar_C_SetInstanceBlendMask, Img) == 0x000000, "Member 'WB_BaseProgressBar_C_SetInstanceBlendMask::Img' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetInstanceBlendMask, BlendMask) == 0x000008, "Member 'WB_BaseProgressBar_C_SetInstanceBlendMask::BlendMask' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetInstancePercent
// 0x0010 (0x0010 - 0x0000)
struct WB_BaseProgressBar_C_SetInstancePercent final
{
public:
	class UImage*                                 Img;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        Percent_0;                                         // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetInstancePercent) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetInstancePercent");
static_assert(sizeof(WB_BaseProgressBar_C_SetInstancePercent) == 0x000010, "Wrong size on WB_BaseProgressBar_C_SetInstancePercent");
static_assert(offsetof(WB_BaseProgressBar_C_SetInstancePercent, Img) == 0x000000, "Member 'WB_BaseProgressBar_C_SetInstancePercent::Img' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetInstancePercent, Percent_0) == 0x000008, "Member 'WB_BaseProgressBar_C_SetInstancePercent::Percent_0' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetIsMarquee
// 0x0001 (0x0001 - 0x0000)
struct WB_BaseProgressBar_C_SetIsMarquee final
{
public:
	bool                                          InbIsMarquee;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetIsMarquee) == 0x000001, "Wrong alignment on WB_BaseProgressBar_C_SetIsMarquee");
static_assert(sizeof(WB_BaseProgressBar_C_SetIsMarquee) == 0x000001, "Wrong size on WB_BaseProgressBar_C_SetIsMarquee");
static_assert(offsetof(WB_BaseProgressBar_C_SetIsMarquee, InbIsMarquee) == 0x000000, "Member 'WB_BaseProgressBar_C_SetIsMarquee::InbIsMarquee' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeDrawAs
// 0x0001 (0x0001 - 0x0000)
struct WB_BaseProgressBar_C_SetMarqueeDrawAs final
{
public:
	ESlateBrushDrawType                           Draw_As;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetMarqueeDrawAs) == 0x000001, "Wrong alignment on WB_BaseProgressBar_C_SetMarqueeDrawAs");
static_assert(sizeof(WB_BaseProgressBar_C_SetMarqueeDrawAs) == 0x000001, "Wrong size on WB_BaseProgressBar_C_SetMarqueeDrawAs");
static_assert(offsetof(WB_BaseProgressBar_C_SetMarqueeDrawAs, Draw_As) == 0x000000, "Member 'WB_BaseProgressBar_C_SetMarqueeDrawAs::Draw_As' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeImage
// 0x0008 (0x0008 - 0x0000)
struct WB_BaseProgressBar_C_SetMarqueeImage final
{
public:
	class UObject*                                Image;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetMarqueeImage) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetMarqueeImage");
static_assert(sizeof(WB_BaseProgressBar_C_SetMarqueeImage) == 0x000008, "Wrong size on WB_BaseProgressBar_C_SetMarqueeImage");
static_assert(offsetof(WB_BaseProgressBar_C_SetMarqueeImage, Image) == 0x000000, "Member 'WB_BaseProgressBar_C_SetMarqueeImage::Image' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeImageSize
// 0x0010 (0x0010 - 0x0000)
struct WB_BaseProgressBar_C_SetMarqueeImageSize final
{
public:
	struct FVector2D                              Image_Size;                                        // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetMarqueeImageSize) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetMarqueeImageSize");
static_assert(sizeof(WB_BaseProgressBar_C_SetMarqueeImageSize) == 0x000010, "Wrong size on WB_BaseProgressBar_C_SetMarqueeImageSize");
static_assert(offsetof(WB_BaseProgressBar_C_SetMarqueeImageSize, Image_Size) == 0x000000, "Member 'WB_BaseProgressBar_C_SetMarqueeImageSize::Image_Size' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeTiling
// 0x0001 (0x0001 - 0x0000)
struct WB_BaseProgressBar_C_SetMarqueeTiling final
{
public:
	ESlateBrushTileType                           Tiling;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetMarqueeTiling) == 0x000001, "Wrong alignment on WB_BaseProgressBar_C_SetMarqueeTiling");
static_assert(sizeof(WB_BaseProgressBar_C_SetMarqueeTiling) == 0x000001, "Wrong size on WB_BaseProgressBar_C_SetMarqueeTiling");
static_assert(offsetof(WB_BaseProgressBar_C_SetMarqueeTiling, Tiling) == 0x000000, "Member 'WB_BaseProgressBar_C_SetMarqueeTiling::Tiling' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetMarqueeTint
// 0x0010 (0x0010 - 0x0000)
struct WB_BaseProgressBar_C_SetMarqueeTint final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetMarqueeTint) == 0x000004, "Wrong alignment on WB_BaseProgressBar_C_SetMarqueeTint");
static_assert(sizeof(WB_BaseProgressBar_C_SetMarqueeTint) == 0x000010, "Wrong size on WB_BaseProgressBar_C_SetMarqueeTint");
static_assert(offsetof(WB_BaseProgressBar_C_SetMarqueeTint, Color) == 0x000000, "Member 'WB_BaseProgressBar_C_SetMarqueeTint::Color' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetPBsEnabled
// 0x0001 (0x0001 - 0x0000)
struct WB_BaseProgressBar_C_SetPBsEnabled final
{
public:
	bool                                          bInIsEnabled;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetPBsEnabled) == 0x000001, "Wrong alignment on WB_BaseProgressBar_C_SetPBsEnabled");
static_assert(sizeof(WB_BaseProgressBar_C_SetPBsEnabled) == 0x000001, "Wrong size on WB_BaseProgressBar_C_SetPBsEnabled");
static_assert(offsetof(WB_BaseProgressBar_C_SetPBsEnabled, bInIsEnabled) == 0x000000, "Member 'WB_BaseProgressBar_C_SetPBsEnabled::bInIsEnabled' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_BaseProgressBar_C_SetPercent final
{
public:
	double                                        InPercent;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetPercent) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetPercent");
static_assert(sizeof(WB_BaseProgressBar_C_SetPercent) == 0x000008, "Wrong size on WB_BaseProgressBar_C_SetPercent");
static_assert(offsetof(WB_BaseProgressBar_C_SetPercent, InPercent) == 0x000000, "Member 'WB_BaseProgressBar_C_SetPercent::InPercent' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetScalarParameter
// 0x0028 (0x0028 - 0x0000)
struct WB_BaseProgressBar_C_SetScalarParameter final
{
public:
	class UImage*                                 Img;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FName                                   ParameterName;                                     // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Value;                                             // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetDynamicMaterial_ReturnValue;           // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_SetScalarParameterValue_Value_ImplicitCast; // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetScalarParameter) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetScalarParameter");
static_assert(sizeof(WB_BaseProgressBar_C_SetScalarParameter) == 0x000028, "Wrong size on WB_BaseProgressBar_C_SetScalarParameter");
static_assert(offsetof(WB_BaseProgressBar_C_SetScalarParameter, Img) == 0x000000, "Member 'WB_BaseProgressBar_C_SetScalarParameter::Img' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetScalarParameter, ParameterName) == 0x000008, "Member 'WB_BaseProgressBar_C_SetScalarParameter::ParameterName' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetScalarParameter, Value) == 0x000010, "Member 'WB_BaseProgressBar_C_SetScalarParameter::Value' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetScalarParameter, CallFunc_GetDynamicMaterial_ReturnValue) == 0x000018, "Member 'WB_BaseProgressBar_C_SetScalarParameter::CallFunc_GetDynamicMaterial_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetScalarParameter, CallFunc_SetScalarParameterValue_Value_ImplicitCast) == 0x000020, "Member 'WB_BaseProgressBar_C_SetScalarParameter::CallFunc_SetScalarParameterValue_Value_ImplicitCast' has a wrong offset!");

// Function WB_BaseProgressBar.WB_BaseProgressBar_C.SetTextureParameter
// 0x0020 (0x0020 - 0x0000)
struct WB_BaseProgressBar_C_SetTextureParameter final
{
public:
	class UImage*                                 Img;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	class FName                                   ParameterName;                                     // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTexture*                               Value;                                             // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UMaterialInstanceDynamic*               CallFunc_GetDynamicMaterial_ReturnValue;           // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_BaseProgressBar_C_SetTextureParameter) == 0x000008, "Wrong alignment on WB_BaseProgressBar_C_SetTextureParameter");
static_assert(sizeof(WB_BaseProgressBar_C_SetTextureParameter) == 0x000020, "Wrong size on WB_BaseProgressBar_C_SetTextureParameter");
static_assert(offsetof(WB_BaseProgressBar_C_SetTextureParameter, Img) == 0x000000, "Member 'WB_BaseProgressBar_C_SetTextureParameter::Img' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetTextureParameter, ParameterName) == 0x000008, "Member 'WB_BaseProgressBar_C_SetTextureParameter::ParameterName' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetTextureParameter, Value) == 0x000010, "Member 'WB_BaseProgressBar_C_SetTextureParameter::Value' has a wrong offset!");
static_assert(offsetof(WB_BaseProgressBar_C_SetTextureParameter, CallFunc_GetDynamicMaterial_ReturnValue) == 0x000018, "Member 'WB_BaseProgressBar_C_SetTextureParameter::CallFunc_GetDynamicMaterial_ReturnValue' has a wrong offset!");

}

