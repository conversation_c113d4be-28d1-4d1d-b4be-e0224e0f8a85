﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Container_Linear

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "EMarqueeMethod_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "Slate_structs.hpp"
#include "EMarqueeMask_structs.hpp"
#include "EProgressMethod_structs.hpp"


namespace SDK::Params
{

// Function WB_Container_Linear.WB_Container_Linear_C.AddBackground
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Linear_C_AddBackground final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseBackgroundBlur;                                // 0x0010(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        BlurStrength;                                      // 0x0018(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_AddBackground) == 0x000008, "Wrong alignment on WB_Container_Linear_C_AddBackground");
static_assert(sizeof(WB_Container_Linear_C_AddBackground) == 0x000020, "Wrong size on WB_Container_Linear_C_AddBackground");
static_assert(offsetof(WB_Container_Linear_C_AddBackground, Color) == 0x000000, "Member 'WB_Container_Linear_C_AddBackground::Color' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_AddBackground, bUseBackgroundBlur) == 0x000010, "Member 'WB_Container_Linear_C_AddBackground::bUseBackgroundBlur' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_AddBackground, BlurStrength) == 0x000018, "Member 'WB_Container_Linear_C_AddBackground::BlurStrength' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.AddSegments
// 0x0038 (0x0038 - 0x0000)
struct WB_Container_Linear_C_AddSegments final
{
public:
	int32                                         NumSegments;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Spacing;                                           // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              Size_0;                                            // 0x0010(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           FillColor_0;                                       // 0x0020(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          FillType;                                          // 0x0030(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsFillFromCenter;                                 // 0x0031(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseShader;                                        // 0x0032(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_AddSegments) == 0x000008, "Wrong alignment on WB_Container_Linear_C_AddSegments");
static_assert(sizeof(WB_Container_Linear_C_AddSegments) == 0x000038, "Wrong size on WB_Container_Linear_C_AddSegments");
static_assert(offsetof(WB_Container_Linear_C_AddSegments, NumSegments) == 0x000000, "Member 'WB_Container_Linear_C_AddSegments::NumSegments' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_AddSegments, Spacing) == 0x000008, "Member 'WB_Container_Linear_C_AddSegments::Spacing' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_AddSegments, Size_0) == 0x000010, "Member 'WB_Container_Linear_C_AddSegments::Size_0' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_AddSegments, FillColor_0) == 0x000020, "Member 'WB_Container_Linear_C_AddSegments::FillColor_0' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_AddSegments, FillType) == 0x000030, "Member 'WB_Container_Linear_C_AddSegments::FillType' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_AddSegments, bIsFillFromCenter) == 0x000031, "Member 'WB_Container_Linear_C_AddSegments::bIsFillFromCenter' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_AddSegments, bUseShader) == 0x000032, "Member 'WB_Container_Linear_C_AddSegments::bUseShader' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.ExecuteUbergraph_WB_Container_Linear
// 0x0CA0 (0x0CA0 - 0x0000)
struct WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_IsMarquee;                      // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_CustomEvent_Image;                          // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_12[0x6];                                       // 0x0012(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              K2Node_CustomEvent_Image_Size;                     // 0x0018(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_Color_1;                        // 0x0028(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushDrawType                           K2Node_CustomEvent_Draw_As;                        // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x003C(0x0014)()
	ESlateBrushTileType                           K2Node_CustomEvent_Tiling;                         // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_1;                     // 0x0051(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_52[0x2];                                       // 0x0052(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_CustomEvent_InColorAndOpacity;              // 0x0054(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseSeparation;                 // 0x0064(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_65[0x3];                                       // 0x0065(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_CustomEvent_NumSegments;                    // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Spacing;                        // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              K2Node_CustomEvent_Size;                           // 0x0078(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_FillColor;                      // 0x0088(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          K2Node_CustomEvent_FillType_1;                     // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bIsFillFromCenter;              // 0x0099(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseShader_1;                   // 0x009A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9B[0x5];                                       // 0x009B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_FindHighestPercentValue_ReturnValue;      // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsMarqueeMethod_ReturnValue;              // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A9[0x3];                                       // 0x00A9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_CustomEvent_Color;                          // 0x00AC(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseBackgroundBlur;             // 0x00BC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_BD[0x3];                                       // 0x00BD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_BlurStrength;                   // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsMarqueeMethod_ReturnValue_1;            // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C9[0x7];                                       // 0x00C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Percent;                        // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsMarqueeMethod_ReturnValue_2;            // 0x00D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D9[0x7];                                       // 0x00D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Marquee_Linear_C*                   CallFunc_FindMarquee_ReturnValue;                  // 0x00E0(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_Percent_1;                      // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bAbsoluteFill;                  // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x00F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x00F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressBarFillType                          K2Node_CustomEvent_FillType;                       // 0x00F3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bUseShader;                     // 0x00F4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F5[0x3];                                       // 0x00F5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Marquee_Linear_C*                   CallFunc_FindMarquee_ReturnValue_1;                // 0x00F8(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_1;               // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_101[0x7];                                      // 0x0101(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Marquee_Linear_C*                   CallFunc_FindMarquee_ReturnValue_2;                // 0x0108(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_ProgressChangeColor;            // 0x0110(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0120(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x0158(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x015C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMarqueeMask                                  K2Node_CustomEvent_MaskType;                       // 0x015D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_15E[0x2];                                      // 0x015E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_CustomEvent_CustomMask;                     // 0x0160(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_CustomEvent_MaskTiling;                     // 0x0168(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_169[0x7];                                      // 0x0169(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_Speed;                          // 0x0170(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_bIsMarquee;                     // 0x0178(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMarqueeMethod                                K2Node_CustomEvent_MarqueeMethod;                  // 0x0179(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_17A[0x6];                                      // 0x017A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             K2Node_CustomEvent_GradientTexture;                // 0x0180(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UObject*                                K2Node_CustomEvent_Value;                          // 0x0188(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_2;                    // 0x0190(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_2;                     // 0x0191(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EProgressMethod                               K2Node_CustomEvent_ProgressMethod;                 // 0x0192(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_193[0x5];                                      // 0x0193(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetAbsoluteTargetPercent_ReturnValue;     // 0x0198(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_2;               // 0x01A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsNegativeFillValue_ReturnValue;          // 0x01A1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A2[0x2];                                      // 0x01A2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_FindTargetFillColor_ReturnValue;          // 0x01A4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1B4[0x4];                                      // 0x01B4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetAbsoluteTargetPercent_ReturnValue_1;   // 0x01B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_3;                     // 0x01C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_UseTargetPercent;               // 0x01C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C2[0xE];                                      // 0x01C2(0x000E)(Fixing Size After Last Property [ Dumper-7 ])
	struct FProgressBarStyle                      K2Node_MakeStruct_ProgressBarStyle;                // 0x01D0(0x0290)()
	struct FLinearColor                           K2Node_CustomEvent_Color_2;                        // 0x0460(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_Color_3;                        // 0x0470(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_TargetPercent;                  // 0x0480(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_3;               // 0x0488(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_489[0x7];                                      // 0x0489(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FProgressBarStyle                      K2Node_MakeStruct_ProgressBarStyle_1;              // 0x0490(0x0290)()
	bool                                          K2Node_CustomEvent_UseGradient;                    // 0x0720(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_721[0x7];                                      // 0x0721(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              K2Node_CustomEvent_Size_1;                         // 0x0728(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_4;                     // 0x0738(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_739[0x7];                                      // 0x0739(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FProgressBarStyle                      K2Node_MakeStruct_ProgressBarStyle_2;              // 0x0740(0x0290)()
	struct FLinearColor                           K2Node_CustomEvent_InColor;                        // 0x09D0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_CustomEvent_GradientPower;                  // 0x09E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           K2Node_CustomEvent_Tiling_1;                       // 0x09E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9E9[0x7];                                      // 0x09E9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_CustomEvent_InPercent;                      // 0x09F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_3;                    // 0x09F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9F9[0x7];                                      // 0x09F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FProgressBarStyle                      K2Node_MakeStruct_ProgressBarStyle_3;              // 0x0A00(0x0290)()
	bool                                          CallFunc_IsProgressMethodInterpolate_ReturnValue;  // 0x0C90(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0C91(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0C92(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_4;                    // 0x0C93(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_5;                    // 0x0C94(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_6;                    // 0x0C95(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_7;                    // 0x0C96(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_4;               // 0x0C97(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear) == 0x000010, "Wrong alignment on WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear");
static_assert(sizeof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear) == 0x000CA0, "Wrong size on WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, EntryPoint) == 0x000000, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::EntryPoint' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_IsClosed_Variable) == 0x000004, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_IsMarquee) == 0x000005, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_IsMarquee' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Image) == 0x000008, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Image' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_SwitchEnum_CmpSuccess) == 0x000010, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_IsValid_ReturnValue) == 0x000011, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Image_Size) == 0x000018, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Image_Size' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Color_1) == 0x000028, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Color_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Draw_As) == 0x000038, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Draw_As' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_MakeStruct_SlateColor) == 0x00003C, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Tiling) == 0x000050, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Tiling' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_IsClosed_Variable_1) == 0x000051, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_IsClosed_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_InColorAndOpacity) == 0x000054, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_InColorAndOpacity' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_bUseSeparation) == 0x000064, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_bUseSeparation' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_NumSegments) == 0x000068, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_NumSegments' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Spacing) == 0x000070, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Spacing' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Size) == 0x000078, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Size' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_FillColor) == 0x000088, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_FillColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_FillType_1) == 0x000098, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_FillType_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_bIsFillFromCenter) == 0x000099, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_bIsFillFromCenter' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_bUseShader_1) == 0x00009A, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_bUseShader_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_FindHighestPercentValue_ReturnValue) == 0x0000A0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_FindHighestPercentValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_IsMarqueeMethod_ReturnValue) == 0x0000A8, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_IsMarqueeMethod_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Color) == 0x0000AC, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Color' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_bUseBackgroundBlur) == 0x0000BC, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_bUseBackgroundBlur' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_BlurStrength) == 0x0000C0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_BlurStrength' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_IsMarqueeMethod_ReturnValue_1) == 0x0000C8, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_IsMarqueeMethod_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Percent) == 0x0000D0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Percent' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_IsMarqueeMethod_ReturnValue_2) == 0x0000D8, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_IsMarqueeMethod_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_FindMarquee_ReturnValue) == 0x0000E0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_FindMarquee_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Percent_1) == 0x0000E8, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Percent_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_bAbsoluteFill) == 0x0000F0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_bAbsoluteFill' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_SwitchEnum_CmpSuccess_1) == 0x0000F1, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_Has_Been_Initd_Variable) == 0x0000F2, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_FillType) == 0x0000F3, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_FillType' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_bUseShader) == 0x0000F4, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_bUseShader' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_FindMarquee_ReturnValue_1) == 0x0000F8, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_FindMarquee_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_Has_Been_Initd_Variable_1) == 0x000100, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_Has_Been_Initd_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_FindMarquee_ReturnValue_2) == 0x000108, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_FindMarquee_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_ProgressChangeColor) == 0x000110, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_ProgressChangeColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_Event_MyGeometry) == 0x000120, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_Event_InDeltaTime) == 0x000158, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_Event_IsDesignTime) == 0x00015C, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_MaskType) == 0x00015D, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_MaskType' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_CustomMask) == 0x000160, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_CustomMask' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_MaskTiling) == 0x000168, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_MaskTiling' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Speed) == 0x000170, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Speed' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_bIsMarquee) == 0x000178, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_bIsMarquee' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_MarqueeMethod) == 0x000179, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_MarqueeMethod' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_GradientTexture) == 0x000180, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_GradientTexture' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Value) == 0x000188, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Value' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_SwitchEnum_CmpSuccess_2) == 0x000190, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_SwitchEnum_CmpSuccess_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_IsClosed_Variable_2) == 0x000191, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_IsClosed_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_ProgressMethod) == 0x000192, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_ProgressMethod' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_GetAbsoluteTargetPercent_ReturnValue) == 0x000198, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_GetAbsoluteTargetPercent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_Has_Been_Initd_Variable_2) == 0x0001A0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_Has_Been_Initd_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_IsNegativeFillValue_ReturnValue) == 0x0001A1, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_IsNegativeFillValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_FindTargetFillColor_ReturnValue) == 0x0001A4, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_FindTargetFillColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_GetAbsoluteTargetPercent_ReturnValue_1) == 0x0001B8, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_GetAbsoluteTargetPercent_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_IsClosed_Variable_3) == 0x0001C0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_IsClosed_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_UseTargetPercent) == 0x0001C1, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_UseTargetPercent' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_MakeStruct_ProgressBarStyle) == 0x0001D0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_MakeStruct_ProgressBarStyle' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Color_2) == 0x000460, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Color_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Color_3) == 0x000470, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Color_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_TargetPercent) == 0x000480, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_TargetPercent' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_Has_Been_Initd_Variable_3) == 0x000488, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_Has_Been_Initd_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_MakeStruct_ProgressBarStyle_1) == 0x000490, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_MakeStruct_ProgressBarStyle_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_UseGradient) == 0x000720, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_UseGradient' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Size_1) == 0x000728, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Size_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_IsClosed_Variable_4) == 0x000738, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_IsClosed_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_MakeStruct_ProgressBarStyle_2) == 0x000740, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_MakeStruct_ProgressBarStyle_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_InColor) == 0x0009D0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_InColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_GradientPower) == 0x0009E0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_GradientPower' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_Tiling_1) == 0x0009E8, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_Tiling_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_CustomEvent_InPercent) == 0x0009F0, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_CustomEvent_InPercent' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_SwitchEnum_CmpSuccess_3) == 0x0009F8, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_SwitchEnum_CmpSuccess_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_MakeStruct_ProgressBarStyle_3) == 0x000A00, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_MakeStruct_ProgressBarStyle_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_IsProgressMethodInterpolate_ReturnValue) == 0x000C90, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_IsProgressMethodInterpolate_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_Not_PreBool_ReturnValue) == 0x000C91, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, CallFunc_BooleanAND_ReturnValue) == 0x000C92, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_SwitchEnum_CmpSuccess_4) == 0x000C93, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_SwitchEnum_CmpSuccess_4' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_SwitchEnum_CmpSuccess_5) == 0x000C94, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_SwitchEnum_CmpSuccess_5' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_SwitchEnum_CmpSuccess_6) == 0x000C95, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_SwitchEnum_CmpSuccess_6' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, K2Node_SwitchEnum_CmpSuccess_7) == 0x000C96, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::K2Node_SwitchEnum_CmpSuccess_7' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear, Temp_bool_Has_Been_Initd_Variable_4) == 0x000C97, "Member 'WB_Container_Linear_C_ExecuteUbergraph_WB_Container_Linear::Temp_bool_Has_Been_Initd_Variable_4' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.FindHighestPercentValue
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Linear_C_FindHighestPercentValue final
{
public:
	double                                        ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DoubleDouble_ReturnValue;         // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A[0x6];                                        // 0x000A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_FindHighestPercentValue) == 0x000008, "Wrong alignment on WB_Container_Linear_C_FindHighestPercentValue");
static_assert(sizeof(WB_Container_Linear_C_FindHighestPercentValue) == 0x000018, "Wrong size on WB_Container_Linear_C_FindHighestPercentValue");
static_assert(offsetof(WB_Container_Linear_C_FindHighestPercentValue, ReturnValue) == 0x000000, "Member 'WB_Container_Linear_C_FindHighestPercentValue::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindHighestPercentValue, Temp_bool_Variable) == 0x000008, "Member 'WB_Container_Linear_C_FindHighestPercentValue::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindHighestPercentValue, CallFunc_Greater_DoubleDouble_ReturnValue) == 0x000009, "Member 'WB_Container_Linear_C_FindHighestPercentValue::CallFunc_Greater_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindHighestPercentValue, K2Node_Select_Default) == 0x000010, "Member 'WB_Container_Linear_C_FindHighestPercentValue::K2Node_Select_Default' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.FindMarquee
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Linear_C_FindMarquee final
{
public:
	class UWB_Marquee_Linear_C*                   ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, NoDestructor, HasGetValueTypeHash)
	EMarqueeMethod                                Temp_byte_Variable;                                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWB_Marquee_Linear_C*                   K2Node_Select_Default;                             // 0x0010(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_FindMarquee) == 0x000008, "Wrong alignment on WB_Container_Linear_C_FindMarquee");
static_assert(sizeof(WB_Container_Linear_C_FindMarquee) == 0x000018, "Wrong size on WB_Container_Linear_C_FindMarquee");
static_assert(offsetof(WB_Container_Linear_C_FindMarquee, ReturnValue) == 0x000000, "Member 'WB_Container_Linear_C_FindMarquee::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindMarquee, Temp_byte_Variable) == 0x000008, "Member 'WB_Container_Linear_C_FindMarquee::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindMarquee, K2Node_Select_Default) == 0x000010, "Member 'WB_Container_Linear_C_FindMarquee::K2Node_Select_Default' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.FindTargetFillColor
// 0x0024 (0x0024 - 0x0000)
struct WB_Container_Linear_C_FindTargetFillColor final
{
public:
	struct FLinearColor                           ReturnValue;                                       // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x3];                                       // 0x0011(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_Select_Default;                             // 0x0014(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_FindTargetFillColor) == 0x000004, "Wrong alignment on WB_Container_Linear_C_FindTargetFillColor");
static_assert(sizeof(WB_Container_Linear_C_FindTargetFillColor) == 0x000024, "Wrong size on WB_Container_Linear_C_FindTargetFillColor");
static_assert(offsetof(WB_Container_Linear_C_FindTargetFillColor, ReturnValue) == 0x000000, "Member 'WB_Container_Linear_C_FindTargetFillColor::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetFillColor, Temp_bool_Variable) == 0x000010, "Member 'WB_Container_Linear_C_FindTargetFillColor::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetFillColor, K2Node_Select_Default) == 0x000014, "Member 'WB_Container_Linear_C_FindTargetFillColor::K2Node_Select_Default' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.FindTargetPercentValue
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Linear_C_FindTargetPercentValue final
{
public:
	double                                        ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetPercent_Percent;                       // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Abs_ReturnValue;                          // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_FindTargetPercentValue) == 0x000008, "Wrong alignment on WB_Container_Linear_C_FindTargetPercentValue");
static_assert(sizeof(WB_Container_Linear_C_FindTargetPercentValue) == 0x000020, "Wrong size on WB_Container_Linear_C_FindTargetPercentValue");
static_assert(offsetof(WB_Container_Linear_C_FindTargetPercentValue, ReturnValue) == 0x000000, "Member 'WB_Container_Linear_C_FindTargetPercentValue::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetPercentValue, CallFunc_GetPercent_Percent) == 0x000008, "Member 'WB_Container_Linear_C_FindTargetPercentValue::CallFunc_GetPercent_Percent' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetPercentValue, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000010, "Member 'WB_Container_Linear_C_FindTargetPercentValue::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetPercentValue, CallFunc_Abs_ReturnValue) == 0x000018, "Member 'WB_Container_Linear_C_FindTargetPercentValue::CallFunc_Abs_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.FindTargetProgressBarPosition
// 0x0148 (0x0148 - 0x0000)
struct WB_Container_Linear_C_FindTargetProgressBarPosition final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        LocalPercent;                                      // 0x0008(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_1;                              // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_2;                              // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_3;                              // 0x0031(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_32[0x6];                                       // 0x0032(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_2;                              // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_4;                              // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable_3;                              // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_5;                              // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51[0x7];                                       // 0x0051(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0058(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x0068(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_6;                              // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0079(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7A[0x6];                                       // 0x007A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_GetSizeY_Current_Size_Y;                  // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_1;                           // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue;              // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue;        // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_2;                           // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_2;               // 0x00B0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetSizeX_Current_Size_X;                  // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_3;               // 0x00C8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_3;                           // 0x00D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_1;            // 0x00E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_4;               // 0x00E8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_5;               // 0x00F8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetSizeX_Current_Size_X_1;                // 0x0108(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_4;                           // 0x0110(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_5;                           // 0x0118(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_MapRangeClamped_ReturnValue_2;            // 0x0120(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Multiply_DoubleDouble_ReturnValue_1;      // 0x0128(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default_6;                           // 0x0130(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_6;               // 0x0138(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_FindTargetProgressBarPosition) == 0x000008, "Wrong alignment on WB_Container_Linear_C_FindTargetProgressBarPosition");
static_assert(sizeof(WB_Container_Linear_C_FindTargetProgressBarPosition) == 0x000148, "Wrong size on WB_Container_Linear_C_FindTargetProgressBarPosition");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Percent) == 0x000000, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Percent' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, LocalPercent) == 0x000008, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::LocalPercent' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_bool_Variable) == 0x000010, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_real_Variable) == 0x000018, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_bool_Variable_1) == 0x000020, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_real_Variable_1) == 0x000028, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_bool_Variable_2) == 0x000030, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_bool_Variable_3) == 0x000031, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_bool_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_real_Variable_2) == 0x000038, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_real_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_bool_Variable_4) == 0x000040, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_bool_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_real_Variable_3) == 0x000048, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_real_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_bool_Variable_5) == 0x000050, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_bool_Variable_5' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MakeVector2D_ReturnValue) == 0x000058, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MakeVector2D_ReturnValue_1) == 0x000068, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, Temp_bool_Variable_6) == 0x000078, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::Temp_bool_Variable_6' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, K2Node_SwitchEnum_CmpSuccess) == 0x000079, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_GetSizeY_Current_Size_Y) == 0x000080, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_GetSizeY_Current_Size_Y' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, K2Node_Select_Default) == 0x000088, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, K2Node_Select_Default_1) == 0x000090, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MapRangeClamped_ReturnValue) == 0x000098, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_Multiply_DoubleDouble_ReturnValue) == 0x0000A0, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_Multiply_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, K2Node_Select_Default_2) == 0x0000A8, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::K2Node_Select_Default_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MakeVector2D_ReturnValue_2) == 0x0000B0, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MakeVector2D_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_GetSizeX_Current_Size_X) == 0x0000C0, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_GetSizeX_Current_Size_X' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MakeVector2D_ReturnValue_3) == 0x0000C8, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MakeVector2D_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, K2Node_Select_Default_3) == 0x0000D8, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::K2Node_Select_Default_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MapRangeClamped_ReturnValue_1) == 0x0000E0, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MapRangeClamped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MakeVector2D_ReturnValue_4) == 0x0000E8, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MakeVector2D_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MakeVector2D_ReturnValue_5) == 0x0000F8, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MakeVector2D_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_GetSizeX_Current_Size_X_1) == 0x000108, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_GetSizeX_Current_Size_X_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, K2Node_Select_Default_4) == 0x000110, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::K2Node_Select_Default_4' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, K2Node_Select_Default_5) == 0x000118, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::K2Node_Select_Default_5' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MapRangeClamped_ReturnValue_2) == 0x000120, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MapRangeClamped_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_Multiply_DoubleDouble_ReturnValue_1) == 0x000128, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_Multiply_DoubleDouble_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, K2Node_Select_Default_6) == 0x000130, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::K2Node_Select_Default_6' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_FindTargetProgressBarPosition, CallFunc_MakeVector2D_ReturnValue_6) == 0x000138, "Member 'WB_Container_Linear_C_FindTargetProgressBarPosition::CallFunc_MakeVector2D_ReturnValue_6' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.GetAbsoluteTargetPercent
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Linear_C_GetAbsoluteTargetPercent final
{
public:
	double                                        ReturnValue;                                       // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_Abs_ReturnValue;                          // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_GetAbsoluteTargetPercent) == 0x000008, "Wrong alignment on WB_Container_Linear_C_GetAbsoluteTargetPercent");
static_assert(sizeof(WB_Container_Linear_C_GetAbsoluteTargetPercent) == 0x000018, "Wrong size on WB_Container_Linear_C_GetAbsoluteTargetPercent");
static_assert(offsetof(WB_Container_Linear_C_GetAbsoluteTargetPercent, ReturnValue) == 0x000000, "Member 'WB_Container_Linear_C_GetAbsoluteTargetPercent::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_GetAbsoluteTargetPercent, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000008, "Member 'WB_Container_Linear_C_GetAbsoluteTargetPercent::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_GetAbsoluteTargetPercent, CallFunc_Abs_ReturnValue) == 0x000010, "Member 'WB_Container_Linear_C_GetAbsoluteTargetPercent::CallFunc_Abs_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.GetPercent
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Linear_C_GetPercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_GetPercent_Percent;                       // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_GetPercent) == 0x000008, "Wrong alignment on WB_Container_Linear_C_GetPercent");
static_assert(sizeof(WB_Container_Linear_C_GetPercent) == 0x000010, "Wrong size on WB_Container_Linear_C_GetPercent");
static_assert(offsetof(WB_Container_Linear_C_GetPercent, Percent) == 0x000000, "Member 'WB_Container_Linear_C_GetPercent::Percent' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_GetPercent, CallFunc_GetPercent_Percent) == 0x000008, "Member 'WB_Container_Linear_C_GetPercent::CallFunc_GetPercent_Percent' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.GetPercent_Separated
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_GetPercent_Separated final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_GetPercent_Separated) == 0x000008, "Wrong alignment on WB_Container_Linear_C_GetPercent_Separated");
static_assert(sizeof(WB_Container_Linear_C_GetPercent_Separated) == 0x000008, "Wrong size on WB_Container_Linear_C_GetPercent_Separated");
static_assert(offsetof(WB_Container_Linear_C_GetPercent_Separated, Percent) == 0x000000, "Member 'WB_Container_Linear_C_GetPercent_Separated::Percent' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.GetSizeX
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Linear_C_GetSizeX final
{
public:
	double                                        Current_Size_X;                                    // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_GetSizeX) == 0x000008, "Wrong alignment on WB_Container_Linear_C_GetSizeX");
static_assert(sizeof(WB_Container_Linear_C_GetSizeX) == 0x000018, "Wrong size on WB_Container_Linear_C_GetSizeX");
static_assert(offsetof(WB_Container_Linear_C_GetSizeX, Current_Size_X) == 0x000000, "Member 'WB_Container_Linear_C_GetSizeX::Current_Size_X' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_GetSizeX, CallFunc_BreakVector2D_X) == 0x000008, "Member 'WB_Container_Linear_C_GetSizeX::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_GetSizeX, CallFunc_BreakVector2D_Y) == 0x000010, "Member 'WB_Container_Linear_C_GetSizeX::CallFunc_BreakVector2D_Y' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.GetSizeY
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Linear_C_GetSizeY final
{
public:
	double                                        Current_Size_Y;                                    // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_GetSizeY) == 0x000008, "Wrong alignment on WB_Container_Linear_C_GetSizeY");
static_assert(sizeof(WB_Container_Linear_C_GetSizeY) == 0x000018, "Wrong size on WB_Container_Linear_C_GetSizeY");
static_assert(offsetof(WB_Container_Linear_C_GetSizeY, Current_Size_Y) == 0x000000, "Member 'WB_Container_Linear_C_GetSizeY::Current_Size_Y' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_GetSizeY, CallFunc_BreakVector2D_X) == 0x000008, "Member 'WB_Container_Linear_C_GetSizeY::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_GetSizeY, CallFunc_BreakVector2D_Y) == 0x000010, "Member 'WB_Container_Linear_C_GetSizeY::CallFunc_BreakVector2D_Y' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.GetTargetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_GetTargetPercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_GetTargetPercent) == 0x000008, "Wrong alignment on WB_Container_Linear_C_GetTargetPercent");
static_assert(sizeof(WB_Container_Linear_C_GetTargetPercent) == 0x000008, "Wrong size on WB_Container_Linear_C_GetTargetPercent");
static_assert(offsetof(WB_Container_Linear_C_GetTargetPercent, Percent) == 0x000000, "Member 'WB_Container_Linear_C_GetTargetPercent::Percent' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.Handle_SetUseSeparation
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Linear_C_Handle_SetUseSeparation final
{
public:
	bool                                          bUseSeparation_0;                                  // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2[0x2];                                        // 0x0002(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Select_Default;                             // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_Handle_SetUseSeparation) == 0x000004, "Wrong alignment on WB_Container_Linear_C_Handle_SetUseSeparation");
static_assert(sizeof(WB_Container_Linear_C_Handle_SetUseSeparation) == 0x000010, "Wrong size on WB_Container_Linear_C_Handle_SetUseSeparation");
static_assert(offsetof(WB_Container_Linear_C_Handle_SetUseSeparation, bUseSeparation_0) == 0x000000, "Member 'WB_Container_Linear_C_Handle_SetUseSeparation::bUseSeparation_0' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Handle_SetUseSeparation, Temp_bool_Variable) == 0x000001, "Member 'WB_Container_Linear_C_Handle_SetUseSeparation::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Handle_SetUseSeparation, Temp_int_Variable) == 0x000004, "Member 'WB_Container_Linear_C_Handle_SetUseSeparation::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Handle_SetUseSeparation, Temp_int_Variable_1) == 0x000008, "Member 'WB_Container_Linear_C_Handle_SetUseSeparation::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Handle_SetUseSeparation, K2Node_Select_Default) == 0x00000C, "Member 'WB_Container_Linear_C_Handle_SetUseSeparation::K2Node_Select_Default' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.Interp_BasePB_Color
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Linear_C_Interp_BasePB_Color final
{
public:
	struct FLinearColor                           Target;                                            // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        InterpSpeed;                                       // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bIsChanging;                                       // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_Interp_BasePB_Color) == 0x000008, "Wrong alignment on WB_Container_Linear_C_Interp_BasePB_Color");
static_assert(sizeof(WB_Container_Linear_C_Interp_BasePB_Color) == 0x000020, "Wrong size on WB_Container_Linear_C_Interp_BasePB_Color");
static_assert(offsetof(WB_Container_Linear_C_Interp_BasePB_Color, Target) == 0x000000, "Member 'WB_Container_Linear_C_Interp_BasePB_Color::Target' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Interp_BasePB_Color, InterpSpeed) == 0x000010, "Member 'WB_Container_Linear_C_Interp_BasePB_Color::InterpSpeed' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Interp_BasePB_Color, bIsChanging) == 0x000018, "Member 'WB_Container_Linear_C_Interp_BasePB_Color::bIsChanging' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.IsMarqueeMethod
// 0x0003 (0x0003 - 0x0000)
struct WB_Container_Linear_C_IsMarqueeMethod final
{
public:
	EMarqueeMethod                                Method;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ReturnValue;                                       // 0x0001(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_IsMarqueeMethod) == 0x000001, "Wrong alignment on WB_Container_Linear_C_IsMarqueeMethod");
static_assert(sizeof(WB_Container_Linear_C_IsMarqueeMethod) == 0x000003, "Wrong size on WB_Container_Linear_C_IsMarqueeMethod");
static_assert(offsetof(WB_Container_Linear_C_IsMarqueeMethod, Method) == 0x000000, "Member 'WB_Container_Linear_C_IsMarqueeMethod::Method' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_IsMarqueeMethod, ReturnValue) == 0x000001, "Member 'WB_Container_Linear_C_IsMarqueeMethod::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_IsMarqueeMethod, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000002, "Member 'WB_Container_Linear_C_IsMarqueeMethod::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.IsNegativeFillValue
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Linear_C_IsNegativeFillValue final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        CallFunc_Subtract_DoubleDouble_ReturnValue;        // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_DoubleDouble_ReturnValue;            // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_IsNegativeFillValue) == 0x000008, "Wrong alignment on WB_Container_Linear_C_IsNegativeFillValue");
static_assert(sizeof(WB_Container_Linear_C_IsNegativeFillValue) == 0x000018, "Wrong size on WB_Container_Linear_C_IsNegativeFillValue");
static_assert(offsetof(WB_Container_Linear_C_IsNegativeFillValue, ReturnValue) == 0x000000, "Member 'WB_Container_Linear_C_IsNegativeFillValue::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_IsNegativeFillValue, CallFunc_Subtract_DoubleDouble_ReturnValue) == 0x000008, "Member 'WB_Container_Linear_C_IsNegativeFillValue::CallFunc_Subtract_DoubleDouble_ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_IsNegativeFillValue, CallFunc_Less_DoubleDouble_ReturnValue) == 0x000010, "Member 'WB_Container_Linear_C_IsNegativeFillValue::CallFunc_Less_DoubleDouble_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.IsProgressMethodInterpolate
// 0x0002 (0x0002 - 0x0000)
struct WB_Container_Linear_C_IsProgressMethodInterpolate final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_IsProgressMethodInterpolate) == 0x000001, "Wrong alignment on WB_Container_Linear_C_IsProgressMethodInterpolate");
static_assert(sizeof(WB_Container_Linear_C_IsProgressMethodInterpolate) == 0x000002, "Wrong size on WB_Container_Linear_C_IsProgressMethodInterpolate");
static_assert(offsetof(WB_Container_Linear_C_IsProgressMethodInterpolate, ReturnValue) == 0x000000, "Member 'WB_Container_Linear_C_IsProgressMethodInterpolate::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_IsProgressMethodInterpolate, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000001, "Member 'WB_Container_Linear_C_IsProgressMethodInterpolate::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.IsProgressMethodStatic
// 0x0002 (0x0002 - 0x0000)
struct WB_Container_Linear_C_IsProgressMethodStatic final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_IsProgressMethodStatic) == 0x000001, "Wrong alignment on WB_Container_Linear_C_IsProgressMethodStatic");
static_assert(sizeof(WB_Container_Linear_C_IsProgressMethodStatic) == 0x000002, "Wrong size on WB_Container_Linear_C_IsProgressMethodStatic");
static_assert(offsetof(WB_Container_Linear_C_IsProgressMethodStatic, ReturnValue) == 0x000000, "Member 'WB_Container_Linear_C_IsProgressMethodStatic::ReturnValue' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_IsProgressMethodStatic, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000001, "Member 'WB_Container_Linear_C_IsProgressMethodStatic::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_PreConstruct) == 0x000001, "Wrong alignment on WB_Container_Linear_C_PreConstruct");
static_assert(sizeof(WB_Container_Linear_C_PreConstruct) == 0x000001, "Wrong size on WB_Container_Linear_C_PreConstruct");
static_assert(offsetof(WB_Container_Linear_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WB_Container_Linear_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.Set_DefaultMarquee_Visibility
// 0x0005 (0x0005 - 0x0000)
struct WB_Container_Linear_C_Set_DefaultMarquee_Visibility final
{
public:
	bool                                          bVisible;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable_1;               // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_IsClosed_Variable_1;                     // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_Set_DefaultMarquee_Visibility) == 0x000001, "Wrong alignment on WB_Container_Linear_C_Set_DefaultMarquee_Visibility");
static_assert(sizeof(WB_Container_Linear_C_Set_DefaultMarquee_Visibility) == 0x000005, "Wrong size on WB_Container_Linear_C_Set_DefaultMarquee_Visibility");
static_assert(offsetof(WB_Container_Linear_C_Set_DefaultMarquee_Visibility, bVisible) == 0x000000, "Member 'WB_Container_Linear_C_Set_DefaultMarquee_Visibility::bVisible' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_DefaultMarquee_Visibility, Temp_bool_Has_Been_Initd_Variable) == 0x000001, "Member 'WB_Container_Linear_C_Set_DefaultMarquee_Visibility::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_DefaultMarquee_Visibility, Temp_bool_IsClosed_Variable) == 0x000002, "Member 'WB_Container_Linear_C_Set_DefaultMarquee_Visibility::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_DefaultMarquee_Visibility, Temp_bool_Has_Been_Initd_Variable_1) == 0x000003, "Member 'WB_Container_Linear_C_Set_DefaultMarquee_Visibility::Temp_bool_Has_Been_Initd_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_DefaultMarquee_Visibility, Temp_bool_IsClosed_Variable_1) == 0x000004, "Member 'WB_Container_Linear_C_Set_DefaultMarquee_Visibility::Temp_bool_IsClosed_Variable_1' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.Set_MarqueeMask
// 0x0050 (0x0050 - 0x0000)
struct WB_Container_Linear_C_Set_MarqueeMask final
{
public:
	EMarqueeMask                                  MaskType;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             CustomMask;                                        // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	EMarqueeMask                                  Temp_byte_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             Temp_object_Variable;                              // 0x0018(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Temp_object_Variable_1;                            // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Temp_object_Variable_2;                            // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Temp_object_Variable_3;                            // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             Temp_object_Variable_4;                            // 0x0038(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UTexture2D*                             K2Node_Select_Default;                             // 0x0040(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class UWB_Marquee_Linear_C*                   CallFunc_FindMarquee_ReturnValue;                  // 0x0048(0x0008)(ZeroConstructor, InstancedReference, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_Set_MarqueeMask) == 0x000008, "Wrong alignment on WB_Container_Linear_C_Set_MarqueeMask");
static_assert(sizeof(WB_Container_Linear_C_Set_MarqueeMask) == 0x000050, "Wrong size on WB_Container_Linear_C_Set_MarqueeMask");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, MaskType) == 0x000000, "Member 'WB_Container_Linear_C_Set_MarqueeMask::MaskType' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, CustomMask) == 0x000008, "Member 'WB_Container_Linear_C_Set_MarqueeMask::CustomMask' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, Temp_byte_Variable) == 0x000010, "Member 'WB_Container_Linear_C_Set_MarqueeMask::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, Temp_object_Variable) == 0x000018, "Member 'WB_Container_Linear_C_Set_MarqueeMask::Temp_object_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, Temp_object_Variable_1) == 0x000020, "Member 'WB_Container_Linear_C_Set_MarqueeMask::Temp_object_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, Temp_object_Variable_2) == 0x000028, "Member 'WB_Container_Linear_C_Set_MarqueeMask::Temp_object_Variable_2' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, Temp_object_Variable_3) == 0x000030, "Member 'WB_Container_Linear_C_Set_MarqueeMask::Temp_object_Variable_3' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, Temp_object_Variable_4) == 0x000038, "Member 'WB_Container_Linear_C_Set_MarqueeMask::Temp_object_Variable_4' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, K2Node_Select_Default) == 0x000040, "Member 'WB_Container_Linear_C_Set_MarqueeMask::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_MarqueeMask, CallFunc_FindMarquee_ReturnValue) == 0x000048, "Member 'WB_Container_Linear_C_Set_MarqueeMask::CallFunc_FindMarquee_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.Set_Mirror_OV_BasePB_Marquee
// 0x0048 (0x0048 - 0x0000)
struct WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee final
{
public:
	bool                                          Mirror;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0038(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee) == 0x000008, "Wrong alignment on WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee");
static_assert(sizeof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee) == 0x000048, "Wrong size on WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee, Mirror) == 0x000000, "Member 'WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee::Mirror' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee, Temp_real_Variable) == 0x000008, "Member 'WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee, Temp_real_Variable_1) == 0x000010, "Member 'WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee, Temp_bool_Variable) == 0x000018, "Member 'WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee, K2Node_Select_Default) == 0x000020, "Member 'WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee, CallFunc_BreakVector2D_X) == 0x000028, "Member 'WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee, CallFunc_BreakVector2D_Y) == 0x000030, "Member 'WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee, CallFunc_MakeVector2D_ReturnValue) == 0x000038, "Member 'WB_Container_Linear_C_Set_Mirror_OV_BasePB_Marquee::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.Set_Mirror_OV_OnTopMarquee
// 0x0048 (0x0048 - 0x0000)
struct WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee final
{
public:
	bool                                          Mirror;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0038(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee) == 0x000008, "Wrong alignment on WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee");
static_assert(sizeof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee) == 0x000048, "Wrong size on WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee, Mirror) == 0x000000, "Member 'WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee::Mirror' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee, Temp_real_Variable) == 0x000008, "Member 'WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee, Temp_real_Variable_1) == 0x000010, "Member 'WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee, Temp_bool_Variable) == 0x000018, "Member 'WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee, K2Node_Select_Default) == 0x000020, "Member 'WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee, CallFunc_BreakVector2D_X) == 0x000028, "Member 'WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee, CallFunc_BreakVector2D_Y) == 0x000030, "Member 'WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee, CallFunc_MakeVector2D_ReturnValue) == 0x000038, "Member 'WB_Container_Linear_C_Set_Mirror_OV_OnTopMarquee::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.Set_Mirror_OV_TargetPB_Marquee
// 0x0048 (0x0048 - 0x0000)
struct WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee final
{
public:
	bool                                          Mirror;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        K2Node_Select_Default;                             // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0038(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee) == 0x000008, "Wrong alignment on WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee");
static_assert(sizeof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee) == 0x000048, "Wrong size on WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee, Mirror) == 0x000000, "Member 'WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee::Mirror' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee, Temp_real_Variable) == 0x000008, "Member 'WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee, Temp_real_Variable_1) == 0x000010, "Member 'WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee, Temp_bool_Variable) == 0x000018, "Member 'WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee, K2Node_Select_Default) == 0x000020, "Member 'WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee, CallFunc_BreakVector2D_X) == 0x000028, "Member 'WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee, CallFunc_BreakVector2D_Y) == 0x000030, "Member 'WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee, CallFunc_MakeVector2D_ReturnValue) == 0x000038, "Member 'WB_Container_Linear_C_Set_Mirror_OV_TargetPB_Marquee::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetBarFillType
// 0x0002 (0x0002 - 0x0000)
struct WB_Container_Linear_C_SetBarFillType final
{
public:
	EProgressBarFillType                          FillType;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          bUseShader;                                        // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetBarFillType) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetBarFillType");
static_assert(sizeof(WB_Container_Linear_C_SetBarFillType) == 0x000002, "Wrong size on WB_Container_Linear_C_SetBarFillType");
static_assert(offsetof(WB_Container_Linear_C_SetBarFillType, FillType) == 0x000000, "Member 'WB_Container_Linear_C_SetBarFillType::FillType' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetBarFillType, bUseShader) == 0x000001, "Member 'WB_Container_Linear_C_SetBarFillType::bUseShader' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueeAppearance
// 0x0002 (0x0002 - 0x0000)
struct WB_Container_Linear_C_SetCustomMarqueeAppearance final
{
public:
	bool                                          bIsMarquee;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMarqueeMethod                                MarqueeMethod_0;                                   // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetCustomMarqueeAppearance) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetCustomMarqueeAppearance");
static_assert(sizeof(WB_Container_Linear_C_SetCustomMarqueeAppearance) == 0x000002, "Wrong size on WB_Container_Linear_C_SetCustomMarqueeAppearance");
static_assert(offsetof(WB_Container_Linear_C_SetCustomMarqueeAppearance, bIsMarquee) == 0x000000, "Member 'WB_Container_Linear_C_SetCustomMarqueeAppearance::bIsMarquee' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetCustomMarqueeAppearance, MarqueeMethod_0) == 0x000001, "Member 'WB_Container_Linear_C_SetCustomMarqueeAppearance::MarqueeMethod_0' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueeColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Linear_C_SetCustomMarqueeColor final
{
public:
	struct FLinearColor                           InColorAndOpacity;                                 // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetCustomMarqueeColor) == 0x000004, "Wrong alignment on WB_Container_Linear_C_SetCustomMarqueeColor");
static_assert(sizeof(WB_Container_Linear_C_SetCustomMarqueeColor) == 0x000010, "Wrong size on WB_Container_Linear_C_SetCustomMarqueeColor");
static_assert(offsetof(WB_Container_Linear_C_SetCustomMarqueeColor, InColorAndOpacity) == 0x000000, "Member 'WB_Container_Linear_C_SetCustomMarqueeColor::InColorAndOpacity' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueeMask
// 0x0018 (0x0018 - 0x0000)
struct WB_Container_Linear_C_SetCustomMarqueeMask final
{
public:
	EMarqueeMask                                  MaskType;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTexture2D*                             CustomMask;                                        // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           MaskTiling;                                        // 0x0010(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetCustomMarqueeMask) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetCustomMarqueeMask");
static_assert(sizeof(WB_Container_Linear_C_SetCustomMarqueeMask) == 0x000018, "Wrong size on WB_Container_Linear_C_SetCustomMarqueeMask");
static_assert(offsetof(WB_Container_Linear_C_SetCustomMarqueeMask, MaskType) == 0x000000, "Member 'WB_Container_Linear_C_SetCustomMarqueeMask::MaskType' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetCustomMarqueeMask, CustomMask) == 0x000008, "Member 'WB_Container_Linear_C_SetCustomMarqueeMask::CustomMask' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetCustomMarqueeMask, MaskTiling) == 0x000010, "Member 'WB_Container_Linear_C_SetCustomMarqueeMask::MaskTiling' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueePercent_Target
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_SetCustomMarqueePercent_Target final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetCustomMarqueePercent_Target) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetCustomMarqueePercent_Target");
static_assert(sizeof(WB_Container_Linear_C_SetCustomMarqueePercent_Target) == 0x000008, "Wrong size on WB_Container_Linear_C_SetCustomMarqueePercent_Target");
static_assert(offsetof(WB_Container_Linear_C_SetCustomMarqueePercent_Target, Percent) == 0x000000, "Member 'WB_Container_Linear_C_SetCustomMarqueePercent_Target::Percent' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetCustomMarqueeSpeed
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_SetCustomMarqueeSpeed final
{
public:
	double                                        Speed;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetCustomMarqueeSpeed) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetCustomMarqueeSpeed");
static_assert(sizeof(WB_Container_Linear_C_SetCustomMarqueeSpeed) == 0x000008, "Wrong size on WB_Container_Linear_C_SetCustomMarqueeSpeed");
static_assert(offsetof(WB_Container_Linear_C_SetCustomMarqueeSpeed, Speed) == 0x000000, "Member 'WB_Container_Linear_C_SetCustomMarqueeSpeed::Speed' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_DrawAs
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_SetDefaultMarquee_DrawAs final
{
public:
	ESlateBrushDrawType                           Draw_As;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetDefaultMarquee_DrawAs) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetDefaultMarquee_DrawAs");
static_assert(sizeof(WB_Container_Linear_C_SetDefaultMarquee_DrawAs) == 0x000001, "Wrong size on WB_Container_Linear_C_SetDefaultMarquee_DrawAs");
static_assert(offsetof(WB_Container_Linear_C_SetDefaultMarquee_DrawAs, Draw_As) == 0x000000, "Member 'WB_Container_Linear_C_SetDefaultMarquee_DrawAs::Draw_As' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_Image
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_SetDefaultMarquee_Image final
{
public:
	class UTexture2D*                             Image;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetDefaultMarquee_Image) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetDefaultMarquee_Image");
static_assert(sizeof(WB_Container_Linear_C_SetDefaultMarquee_Image) == 0x000008, "Wrong size on WB_Container_Linear_C_SetDefaultMarquee_Image");
static_assert(offsetof(WB_Container_Linear_C_SetDefaultMarquee_Image, Image) == 0x000000, "Member 'WB_Container_Linear_C_SetDefaultMarquee_Image::Image' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_ImageSize
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Linear_C_SetDefaultMarquee_ImageSize final
{
public:
	struct FVector2D                              Image_Size;                                        // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetDefaultMarquee_ImageSize) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetDefaultMarquee_ImageSize");
static_assert(sizeof(WB_Container_Linear_C_SetDefaultMarquee_ImageSize) == 0x000010, "Wrong size on WB_Container_Linear_C_SetDefaultMarquee_ImageSize");
static_assert(offsetof(WB_Container_Linear_C_SetDefaultMarquee_ImageSize, Image_Size) == 0x000000, "Member 'WB_Container_Linear_C_SetDefaultMarquee_ImageSize::Image_Size' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_IsMarquee
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_SetDefaultMarquee_IsMarquee final
{
public:
	bool                                          IsMarquee;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetDefaultMarquee_IsMarquee) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetDefaultMarquee_IsMarquee");
static_assert(sizeof(WB_Container_Linear_C_SetDefaultMarquee_IsMarquee) == 0x000001, "Wrong size on WB_Container_Linear_C_SetDefaultMarquee_IsMarquee");
static_assert(offsetof(WB_Container_Linear_C_SetDefaultMarquee_IsMarquee, IsMarquee) == 0x000000, "Member 'WB_Container_Linear_C_SetDefaultMarquee_IsMarquee::IsMarquee' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_Tiling
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_SetDefaultMarquee_Tiling final
{
public:
	ESlateBrushTileType                           Tiling;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetDefaultMarquee_Tiling) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetDefaultMarquee_Tiling");
static_assert(sizeof(WB_Container_Linear_C_SetDefaultMarquee_Tiling) == 0x000001, "Wrong size on WB_Container_Linear_C_SetDefaultMarquee_Tiling");
static_assert(offsetof(WB_Container_Linear_C_SetDefaultMarquee_Tiling, Tiling) == 0x000000, "Member 'WB_Container_Linear_C_SetDefaultMarquee_Tiling::Tiling' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetDefaultMarquee_Tint
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Linear_C_SetDefaultMarquee_Tint final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetDefaultMarquee_Tint) == 0x000004, "Wrong alignment on WB_Container_Linear_C_SetDefaultMarquee_Tint");
static_assert(sizeof(WB_Container_Linear_C_SetDefaultMarquee_Tint) == 0x000010, "Wrong size on WB_Container_Linear_C_SetDefaultMarquee_Tint");
static_assert(offsetof(WB_Container_Linear_C_SetDefaultMarquee_Tint, Color) == 0x000000, "Member 'WB_Container_Linear_C_SetDefaultMarquee_Tint::Color' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetFillColor
// 0x0020 (0x0020 - 0x0000)
struct WB_Container_Linear_C_SetFillColor final
{
public:
	struct FLinearColor                           InColor;                                           // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        GradientPower;                                     // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateBrushTileType                           Tiling;                                            // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetFillColor) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetFillColor");
static_assert(sizeof(WB_Container_Linear_C_SetFillColor) == 0x000020, "Wrong size on WB_Container_Linear_C_SetFillColor");
static_assert(offsetof(WB_Container_Linear_C_SetFillColor, InColor) == 0x000000, "Member 'WB_Container_Linear_C_SetFillColor::InColor' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetFillColor, GradientPower) == 0x000010, "Member 'WB_Container_Linear_C_SetFillColor::GradientPower' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetFillColor, Tiling) == 0x000018, "Member 'WB_Container_Linear_C_SetFillColor::Tiling' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetFillColorMask
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_SetFillColorMask final
{
public:
	class UObject*                                Value;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetFillColorMask) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetFillColorMask");
static_assert(sizeof(WB_Container_Linear_C_SetFillColorMask) == 0x000008, "Wrong size on WB_Container_Linear_C_SetFillColorMask");
static_assert(offsetof(WB_Container_Linear_C_SetFillColorMask, Value) == 0x000000, "Member 'WB_Container_Linear_C_SetFillColorMask::Value' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetGradientMask
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_SetGradientMask final
{
public:
	class UTexture2D*                             GradientTexture;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetGradientMask) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetGradientMask");
static_assert(sizeof(WB_Container_Linear_C_SetGradientMask) == 0x000008, "Wrong size on WB_Container_Linear_C_SetGradientMask");
static_assert(offsetof(WB_Container_Linear_C_SetGradientMask, GradientTexture) == 0x000000, "Member 'WB_Container_Linear_C_SetGradientMask::GradientTexture' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetMirrorX_OV_Target
// 0x0040 (0x0040 - 0x0000)
struct WB_Container_Linear_C_SetMirrorX_OV_Target final
{
public:
	bool                                          MirrorX;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2[0x6];                                        // 0x0002(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0030(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetMirrorX_OV_Target) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetMirrorX_OV_Target");
static_assert(sizeof(WB_Container_Linear_C_SetMirrorX_OV_Target) == 0x000040, "Wrong size on WB_Container_Linear_C_SetMirrorX_OV_Target");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorX_OV_Target, MirrorX) == 0x000000, "Member 'WB_Container_Linear_C_SetMirrorX_OV_Target::MirrorX' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorX_OV_Target, Temp_bool_Variable) == 0x000001, "Member 'WB_Container_Linear_C_SetMirrorX_OV_Target::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorX_OV_Target, Temp_real_Variable) == 0x000008, "Member 'WB_Container_Linear_C_SetMirrorX_OV_Target::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorX_OV_Target, Temp_real_Variable_1) == 0x000010, "Member 'WB_Container_Linear_C_SetMirrorX_OV_Target::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorX_OV_Target, K2Node_Select_Default) == 0x000018, "Member 'WB_Container_Linear_C_SetMirrorX_OV_Target::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorX_OV_Target, CallFunc_BreakVector2D_X) == 0x000020, "Member 'WB_Container_Linear_C_SetMirrorX_OV_Target::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorX_OV_Target, CallFunc_BreakVector2D_Y) == 0x000028, "Member 'WB_Container_Linear_C_SetMirrorX_OV_Target::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorX_OV_Target, CallFunc_MakeVector2D_ReturnValue) == 0x000030, "Member 'WB_Container_Linear_C_SetMirrorX_OV_Target::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetMirrorY_OV_Target
// 0x0040 (0x0040 - 0x0000)
struct WB_Container_Linear_C_SetMirrorY_OV_Target final
{
public:
	bool                                          MirrorY;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2[0x6];                                        // 0x0002(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Temp_real_Variable;                                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Temp_real_Variable_1;                              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        K2Node_Select_Default;                             // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_X;                          // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        CallFunc_BreakVector2D_Y;                          // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0030(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetMirrorY_OV_Target) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetMirrorY_OV_Target");
static_assert(sizeof(WB_Container_Linear_C_SetMirrorY_OV_Target) == 0x000040, "Wrong size on WB_Container_Linear_C_SetMirrorY_OV_Target");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorY_OV_Target, MirrorY) == 0x000000, "Member 'WB_Container_Linear_C_SetMirrorY_OV_Target::MirrorY' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorY_OV_Target, Temp_bool_Variable) == 0x000001, "Member 'WB_Container_Linear_C_SetMirrorY_OV_Target::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorY_OV_Target, Temp_real_Variable) == 0x000008, "Member 'WB_Container_Linear_C_SetMirrorY_OV_Target::Temp_real_Variable' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorY_OV_Target, Temp_real_Variable_1) == 0x000010, "Member 'WB_Container_Linear_C_SetMirrorY_OV_Target::Temp_real_Variable_1' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorY_OV_Target, K2Node_Select_Default) == 0x000018, "Member 'WB_Container_Linear_C_SetMirrorY_OV_Target::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorY_OV_Target, CallFunc_BreakVector2D_X) == 0x000020, "Member 'WB_Container_Linear_C_SetMirrorY_OV_Target::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorY_OV_Target, CallFunc_BreakVector2D_Y) == 0x000028, "Member 'WB_Container_Linear_C_SetMirrorY_OV_Target::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_SetMirrorY_OV_Target, CallFunc_MakeVector2D_ReturnValue) == 0x000030, "Member 'WB_Container_Linear_C_SetMirrorY_OV_Target::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_SetPercent final
{
public:
	double                                        InPercent;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetPercent) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetPercent");
static_assert(sizeof(WB_Container_Linear_C_SetPercent) == 0x000008, "Wrong size on WB_Container_Linear_C_SetPercent");
static_assert(offsetof(WB_Container_Linear_C_SetPercent, InPercent) == 0x000000, "Member 'WB_Container_Linear_C_SetPercent::InPercent' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetProgressMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_SetProgressMethod final
{
public:
	EProgressMethod                               ProgressMethod_0;                                  // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetProgressMethod) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetProgressMethod");
static_assert(sizeof(WB_Container_Linear_C_SetProgressMethod) == 0x000001, "Wrong size on WB_Container_Linear_C_SetProgressMethod");
static_assert(offsetof(WB_Container_Linear_C_SetProgressMethod, ProgressMethod_0) == 0x000000, "Member 'WB_Container_Linear_C_SetProgressMethod::ProgressMethod_0' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetSize
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Linear_C_SetSize final
{
public:
	struct FVector2D                              Size_0;                                            // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetSize) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetSize");
static_assert(sizeof(WB_Container_Linear_C_SetSize) == 0x000010, "Wrong size on WB_Container_Linear_C_SetSize");
static_assert(offsetof(WB_Container_Linear_C_SetSize, Size_0) == 0x000000, "Member 'WB_Container_Linear_C_SetSize::Size_0' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetTargetFillColorNegative
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Linear_C_SetTargetFillColorNegative final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetTargetFillColorNegative) == 0x000004, "Wrong alignment on WB_Container_Linear_C_SetTargetFillColorNegative");
static_assert(sizeof(WB_Container_Linear_C_SetTargetFillColorNegative) == 0x000010, "Wrong size on WB_Container_Linear_C_SetTargetFillColorNegative");
static_assert(offsetof(WB_Container_Linear_C_SetTargetFillColorNegative, Color) == 0x000000, "Member 'WB_Container_Linear_C_SetTargetFillColorNegative::Color' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetTargetFillColorPositive
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Linear_C_SetTargetFillColorPositive final
{
public:
	struct FLinearColor                           Color;                                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetTargetFillColorPositive) == 0x000004, "Wrong alignment on WB_Container_Linear_C_SetTargetFillColorPositive");
static_assert(sizeof(WB_Container_Linear_C_SetTargetFillColorPositive) == 0x000010, "Wrong size on WB_Container_Linear_C_SetTargetFillColorPositive");
static_assert(offsetof(WB_Container_Linear_C_SetTargetFillColorPositive, Color) == 0x000000, "Member 'WB_Container_Linear_C_SetTargetFillColorPositive::Color' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetTargetPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_SetTargetPercent final
{
public:
	double                                        TargetPercent_0;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetTargetPercent) == 0x000008, "Wrong alignment on WB_Container_Linear_C_SetTargetPercent");
static_assert(sizeof(WB_Container_Linear_C_SetTargetPercent) == 0x000008, "Wrong size on WB_Container_Linear_C_SetTargetPercent");
static_assert(offsetof(WB_Container_Linear_C_SetTargetPercent, TargetPercent_0) == 0x000000, "Member 'WB_Container_Linear_C_SetTargetPercent::TargetPercent_0' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetUseAbsoluteFillMethod
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_SetUseAbsoluteFillMethod final
{
public:
	bool                                          bAbsoluteFill;                                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetUseAbsoluteFillMethod) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetUseAbsoluteFillMethod");
static_assert(sizeof(WB_Container_Linear_C_SetUseAbsoluteFillMethod) == 0x000001, "Wrong size on WB_Container_Linear_C_SetUseAbsoluteFillMethod");
static_assert(offsetof(WB_Container_Linear_C_SetUseAbsoluteFillMethod, bAbsoluteFill) == 0x000000, "Member 'WB_Container_Linear_C_SetUseAbsoluteFillMethod::bAbsoluteFill' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetUseGradient
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_SetUseGradient final
{
public:
	bool                                          UseGradient;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetUseGradient) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetUseGradient");
static_assert(sizeof(WB_Container_Linear_C_SetUseGradient) == 0x000001, "Wrong size on WB_Container_Linear_C_SetUseGradient");
static_assert(offsetof(WB_Container_Linear_C_SetUseGradient, UseGradient) == 0x000000, "Member 'WB_Container_Linear_C_SetUseGradient::UseGradient' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetUseSeparation
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_SetUseSeparation final
{
public:
	bool                                          bUseSeparation_0;                                  // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetUseSeparation) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetUseSeparation");
static_assert(sizeof(WB_Container_Linear_C_SetUseSeparation) == 0x000001, "Wrong size on WB_Container_Linear_C_SetUseSeparation");
static_assert(offsetof(WB_Container_Linear_C_SetUseSeparation, bUseSeparation_0) == 0x000000, "Member 'WB_Container_Linear_C_SetUseSeparation::bUseSeparation_0' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.SetUseTargetPercent
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_SetUseTargetPercent final
{
public:
	bool                                          UseTargetPercent;                                  // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_SetUseTargetPercent) == 0x000001, "Wrong alignment on WB_Container_Linear_C_SetUseTargetPercent");
static_assert(sizeof(WB_Container_Linear_C_SetUseTargetPercent) == 0x000001, "Wrong size on WB_Container_Linear_C_SetUseTargetPercent");
static_assert(offsetof(WB_Container_Linear_C_SetUseTargetPercent, UseTargetPercent) == 0x000000, "Member 'WB_Container_Linear_C_SetUseTargetPercent::UseTargetPercent' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.StartTriggerProgressChangeColor
// 0x0010 (0x0010 - 0x0000)
struct WB_Container_Linear_C_StartTriggerProgressChangeColor final
{
public:
	struct FLinearColor                           ProgressChangeColor_0;                             // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_StartTriggerProgressChangeColor) == 0x000004, "Wrong alignment on WB_Container_Linear_C_StartTriggerProgressChangeColor");
static_assert(sizeof(WB_Container_Linear_C_StartTriggerProgressChangeColor) == 0x000010, "Wrong size on WB_Container_Linear_C_StartTriggerProgressChangeColor");
static_assert(offsetof(WB_Container_Linear_C_StartTriggerProgressChangeColor, ProgressChangeColor_0) == 0x000000, "Member 'WB_Container_Linear_C_StartTriggerProgressChangeColor::ProgressChangeColor_0' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.Tick
// 0x003C (0x003C - 0x0000)
struct WB_Container_Linear_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_Tick) == 0x000004, "Wrong alignment on WB_Container_Linear_C_Tick");
static_assert(sizeof(WB_Container_Linear_C_Tick) == 0x00003C, "Wrong size on WB_Container_Linear_C_Tick");
static_assert(offsetof(WB_Container_Linear_C_Tick, MyGeometry) == 0x000000, "Member 'WB_Container_Linear_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WB_Container_Linear_C_Tick, InDeltaTime) == 0x000038, "Member 'WB_Container_Linear_C_Tick::InDeltaTime' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.UpdateSeparationPercent
// 0x0008 (0x0008 - 0x0000)
struct WB_Container_Linear_C_UpdateSeparationPercent final
{
public:
	double                                        Percent;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_UpdateSeparationPercent) == 0x000008, "Wrong alignment on WB_Container_Linear_C_UpdateSeparationPercent");
static_assert(sizeof(WB_Container_Linear_C_UpdateSeparationPercent) == 0x000008, "Wrong size on WB_Container_Linear_C_UpdateSeparationPercent");
static_assert(offsetof(WB_Container_Linear_C_UpdateSeparationPercent, Percent) == 0x000000, "Member 'WB_Container_Linear_C_UpdateSeparationPercent::Percent' has a wrong offset!");

// Function WB_Container_Linear.WB_Container_Linear_C.UpdateStaticPercent
// 0x0001 (0x0001 - 0x0000)
struct WB_Container_Linear_C_UpdateStaticPercent final
{
public:
	bool                                          CallFunc_IsProgressMethodStatic_ReturnValue;       // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WB_Container_Linear_C_UpdateStaticPercent) == 0x000001, "Wrong alignment on WB_Container_Linear_C_UpdateStaticPercent");
static_assert(sizeof(WB_Container_Linear_C_UpdateStaticPercent) == 0x000001, "Wrong size on WB_Container_Linear_C_UpdateStaticPercent");
static_assert(offsetof(WB_Container_Linear_C_UpdateStaticPercent, CallFunc_IsProgressMethodStatic_ReturnValue) == 0x000000, "Member 'WB_Container_Linear_C_UpdateStaticPercent::CallFunc_IsProgressMethodStatic_ReturnValue' has a wrong offset!");

}

