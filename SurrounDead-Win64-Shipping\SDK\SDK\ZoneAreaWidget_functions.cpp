﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: ZoneAreaWidget

#include "Basic.hpp"

#include "ZoneAreaWidget_classes.hpp"
#include "ZoneAreaWidget_parameters.hpp"


namespace SDK
{

// Function ZoneAreaWidget.ZoneAreaWidget_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UZoneAreaWidget_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZoneAreaWidget_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function ZoneAreaWidget.ZoneAreaWidget_C.ExecuteUbergraph_ZoneAreaWidget
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, <PERSON>rm, ZeroConstructor, IsPlainOldData, NoD<PERSON>ru<PERSON>, HasGetValueTypeHash)

void UZoneAreaWidget_C::ExecuteUbergraph_ZoneAreaWidget(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZoneAreaWidget_C", "ExecuteUbergraph_ZoneAreaWidget");

	Params::ZoneAreaWidget_C_ExecuteUbergraph_ZoneAreaWidget Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function ZoneAreaWidget.ZoneAreaWidget_C.FadeStartFinished
// (BlueprintCallable, BlueprintEvent)

void UZoneAreaWidget_C::FadeStartFinished()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZoneAreaWidget_C", "FadeStartFinished");

	UObject::ProcessEvent(Func, nullptr);
}


// Function ZoneAreaWidget.ZoneAreaWidget_C.SetName
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      Name_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm)
// class UTexture*                         Texture                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Entering_                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UZoneAreaWidget_C::SetName(const class FText& Name_0, class UTexture* Texture, const struct FLinearColor& Color, bool Entering_)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("ZoneAreaWidget_C", "SetName");

	Params::ZoneAreaWidget_C_SetName Parms{};

	Parms.Name_0 = std::move(Name_0);
	Parms.Texture = Texture;
	Parms.Color = std::move(Color);
	Parms.Entering_ = Entering_;

	UObject::ProcessEvent(Func, &Parms);
}

}

