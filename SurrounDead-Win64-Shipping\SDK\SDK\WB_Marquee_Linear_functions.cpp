﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WB_Marquee_Linear

#include "Basic.hpp"

#include "WB_Marquee_Linear_classes.hpp"
#include "WB_Marquee_Linear_parameters.hpp"


namespace SDK
{

// Function WB_Marquee_Linear.WB_Marquee_Linear_C.ExecuteUbergraph_WB_Marquee_Linear
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Marquee_Linear_C::ExecuteUbergraph_WB_Marquee_Linear(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Marquee_Linear_C", "ExecuteUbergraph_WB_Marquee_Linear");

	Params::WB_Marquee_Linear_C_ExecuteUbergraph_WB_Marquee_Linear Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeMask
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTexture2D*                       Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)

void UWB_Marquee_Linear_C::SetMarqueeMask(class UTexture2D* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Marquee_Linear_C", "SetMarqueeMask");

	Params::WB_Marquee_Linear_C_SetMarqueeMask Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeMethod
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EMarqueeMethod                          MarqueeMethod                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Marquee_Linear_C::SetMarqueeMethod(EMarqueeMethod MarqueeMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Marquee_Linear_C", "SetMarqueeMethod");

	Params::WB_Marquee_Linear_C_SetMarqueeMethod Parms{};

	Parms.MarqueeMethod = MarqueeMethod;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeSize
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 Size_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Marquee_Linear_C::SetMarqueeSize(const struct FVector2D& Size_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Marquee_Linear_C", "SetMarqueeSize");

	Params::WB_Marquee_Linear_C_SetMarqueeSize Parms{};

	Parms.Size_0 = std::move(Size_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeSpeed
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Marquee_Linear_C::SetMarqueeSpeed(double Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Marquee_Linear_C", "SetMarqueeSpeed");

	Params::WB_Marquee_Linear_C_SetMarqueeSpeed Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetMarqueeTiling
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESlateBrushTileType                     Tiling                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Marquee_Linear_C::SetMarqueeTiling(ESlateBrushTileType Tiling)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Marquee_Linear_C", "SetMarqueeTiling");

	Params::WB_Marquee_Linear_C_SetMarqueeTiling Parms{};

	Parms.Tiling = Tiling;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WB_Marquee_Linear.WB_Marquee_Linear_C.SetPercent
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// double                                  Percent                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWB_Marquee_Linear_C::SetPercent(double Percent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WB_Marquee_Linear_C", "SetPercent");

	Params::WB_Marquee_Linear_C_SetPercent Parms{};

	Parms.Percent = Percent;

	UObject::ProcessEvent(Func, &Parms);
}

}

