﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: Ultra_Dynamic_Weather_Interface

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "UDS_and_UDW_State_structs.hpp"


namespace SDK::Params
{

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.Editor Tick
// 0x0040 (0x0040 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_Editor_Tick final
{
public:
	struct FVector                                Editor_Camera_Location;                            // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               Editor_Camera_Rotation;                            // 0x0018(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	double                                        Delta_Time;                                        // 0x0030(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Completed;                                         // 0x0038(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_Editor_Tick) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_Editor_Tick");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_Editor_Tick) == 0x000040, "Wrong size on Ultra_Dynamic_Weather_Interface_C_Editor_Tick");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Editor_Tick, Editor_Camera_Location) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_Editor_Tick::Editor_Camera_Location' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Editor_Tick, Editor_Camera_Rotation) == 0x000018, "Member 'Ultra_Dynamic_Weather_Interface_C_Editor_Tick::Editor_Camera_Rotation' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Editor_Tick, Delta_Time) == 0x000030, "Member 'Ultra_Dynamic_Weather_Interface_C_Editor_Tick::Delta_Time' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Editor_Tick, Completed) == 0x000038, "Member 'Ultra_Dynamic_Weather_Interface_C_Editor_Tick::Completed' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.Get Control Point Location
// 0x0018 (0x0018 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_Get_Control_Point_Location final
{
public:
	struct FVector                                Location;                                          // 0x0000(0x0018)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_Get_Control_Point_Location) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_Get_Control_Point_Location");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_Get_Control_Point_Location) == 0x000018, "Wrong size on Ultra_Dynamic_Weather_Interface_C_Get_Control_Point_Location");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_Control_Point_Location, Location) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_Control_Point_Location::Location' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.Get Local Weather State Values
// 0x0038 (0x0038 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values final
{
public:
	double                                        Cloud_Coverage;                                    // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Wind_Intensity;                                    // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Rain;                                              // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Snow;                                              // 0x0018(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust;                                              // 0x0020(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Fog;                                               // 0x0028(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Lightning;                                         // 0x0030(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values) == 0x000038, "Wrong size on Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values, Cloud_Coverage) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values::Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values, Wind_Intensity) == 0x000008, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values::Wind_Intensity' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values, Rain) == 0x000010, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values::Rain' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values, Snow) == 0x000018, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values::Snow' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values, Dust) == 0x000020, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values::Dust' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values, Fog) == 0x000028, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values::Fog' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values, Lightning) == 0x000030, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_Local_Weather_State_Values::Lightning' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.Get UDS Values Controlled by UDW
// 0x0030 (0x0030 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW final
{
public:
	double                                        Cloud_Coverage;                                    // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Fog;                                               // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Dust_Amount;                                       // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Cloud_Direction;                                   // 0x0018(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Wind_Speed_Multiplier;                             // 0x0020(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	double                                        Fog_Vertical_Velocity;                             // 0x0028(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW) == 0x000030, "Wrong size on Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW, Cloud_Coverage) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW::Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW, Fog) == 0x000008, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW::Fog' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW, Dust_Amount) == 0x000010, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW::Dust_Amount' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW, Cloud_Direction) == 0x000018, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW::Cloud_Direction' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW, Wind_Speed_Multiplier) == 0x000020, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW::Wind_Speed_Multiplier' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW, Fog_Vertical_Velocity) == 0x000028, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDS_Values_Controlled_by_UDW::Fog_Vertical_Velocity' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.Get UDS Weather Override Bool
// 0x0003 (0x0003 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool final
{
public:
	bool                                          Cloud_Coverage;                                    // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Fog;                                               // 0x0001(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Dust;                                              // 0x0002(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool) == 0x000001, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool) == 0x000003, "Wrong size on Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool, Cloud_Coverage) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool::Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool, Fog) == 0x000001, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool::Fog' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool, Dust) == 0x000002, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDS_Weather_Override_Bool::Dust' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.Get UDW State for Saving
// 0x0200 (0x0200 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_Get_UDW_State_for_Saving final
{
public:
	struct FUDS_and_UDW_State                     UDW_State;                                         // 0x0000(0x0200)(Parm, OutParm, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_Get_UDW_State_for_Saving) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_Get_UDW_State_for_Saving");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_Get_UDW_State_for_Saving) == 0x000200, "Wrong size on Ultra_Dynamic_Weather_Interface_C_Get_UDW_State_for_Saving");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Get_UDW_State_for_Saving, UDW_State) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_Get_UDW_State_for_Saving::UDW_State' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.Initialize Weather
// 0x0008 (0x0008 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_Initialize_Weather final
{
public:
	class AUltra_Dynamic_Sky_C*                   UDS;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_Initialize_Weather) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_Initialize_Weather");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_Initialize_Weather) == 0x000008, "Wrong size on Ultra_Dynamic_Weather_Interface_C_Initialize_Weather");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Initialize_Weather, UDS) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_Initialize_Weather::UDS' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.Report Removed Radial Storm
// 0x0010 (0x0010 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_Report_Removed_Radial_Storm final
{
public:
	class AActor*                                 Storm;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          Success;                                           // 0x0008(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_Report_Removed_Radial_Storm) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_Report_Removed_Radial_Storm");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_Report_Removed_Radial_Storm) == 0x000010, "Wrong size on Ultra_Dynamic_Weather_Interface_C_Report_Removed_Radial_Storm");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Report_Removed_Radial_Storm, Storm) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_Report_Removed_Radial_Storm::Storm' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_Report_Removed_Radial_Storm, Success) == 0x000008, "Member 'Ultra_Dynamic_Weather_Interface_C_Report_Removed_Radial_Storm::Success' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.UDS Reconstruct
// 0x0001 (0x0001 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_UDS_Reconstruct final
{
public:
	bool                                          Success;                                           // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_UDS_Reconstruct) == 0x000001, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_UDS_Reconstruct");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_UDS_Reconstruct) == 0x000001, "Wrong size on Ultra_Dynamic_Weather_Interface_C_UDS_Reconstruct");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDS_Reconstruct, Success) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_UDS_Reconstruct::Success' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.UDS Weather Variable Overrides
// 0x0038 (0x0038 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides final
{
public:
	bool                                          Override_Clouds;                                   // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Cloud_Coverage;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Override_Fog;                                      // 0x0010(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Fog;                                               // 0x0018(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Override_Dust;                                     // 0x0020(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	double                                        Dust;                                              // 0x0028(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Success;                                           // 0x0030(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides) == 0x000038, "Wrong size on Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides, Override_Clouds) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides::Override_Clouds' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides, Cloud_Coverage) == 0x000008, "Member 'Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides::Cloud_Coverage' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides, Override_Fog) == 0x000010, "Member 'Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides::Override_Fog' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides, Fog) == 0x000018, "Member 'Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides::Fog' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides, Override_Dust) == 0x000020, "Member 'Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides::Override_Dust' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides, Dust) == 0x000028, "Member 'Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides::Dust' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides, Success) == 0x000030, "Member 'Ultra_Dynamic_Weather_Interface_C_UDS_Weather_Variable_Overrides::Success' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.UDW Runtime Tick
// 0x0008 (0x0008 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_UDW_Runtime_Tick final
{
public:
	double                                        Delta_Time;                                        // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_UDW_Runtime_Tick) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_UDW_Runtime_Tick");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_UDW_Runtime_Tick) == 0x000008, "Wrong size on Ultra_Dynamic_Weather_Interface_C_UDW_Runtime_Tick");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDW_Runtime_Tick, Delta_Time) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_UDW_Runtime_Tick::Delta_Time' has a wrong offset!");

// Function Ultra_Dynamic_Weather_Interface.Ultra_Dynamic_Weather_Interface_C.UDW State Apply
// 0x0208 (0x0208 - 0x0000)
struct Ultra_Dynamic_Weather_Interface_C_UDW_State_Apply final
{
public:
	struct FUDS_and_UDW_State                     State;                                             // 0x0000(0x0200)(BlueprintVisible, BlueprintReadOnly, Parm, HasGetValueTypeHash)
	bool                                          Completed;                                         // 0x0200(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(Ultra_Dynamic_Weather_Interface_C_UDW_State_Apply) == 0x000008, "Wrong alignment on Ultra_Dynamic_Weather_Interface_C_UDW_State_Apply");
static_assert(sizeof(Ultra_Dynamic_Weather_Interface_C_UDW_State_Apply) == 0x000208, "Wrong size on Ultra_Dynamic_Weather_Interface_C_UDW_State_Apply");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDW_State_Apply, State) == 0x000000, "Member 'Ultra_Dynamic_Weather_Interface_C_UDW_State_Apply::State' has a wrong offset!");
static_assert(offsetof(Ultra_Dynamic_Weather_Interface_C_UDW_State_Apply, Completed) == 0x000200, "Member 'Ultra_Dynamic_Weather_Interface_C_UDW_State_Apply::Completed' has a wrong offset!");

}

